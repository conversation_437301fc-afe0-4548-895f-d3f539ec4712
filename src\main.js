import Vue from 'vue';
import App from './App';
import store from './store';
import router from './router';
import '@/permission'; // 路由守卫

/**样式相关内容*/
import 'normalize.css/normalize.css';
import ElementUI from 'element-eoss';
import 'element-eoss/lib/theme-chalk/index.css';
import '@/styles/index.scss'; // global css
import '@/styles/reset.css'; //清除默认样式
import '@/assets/iconfont/iconfont.css'; // 阿里图标库
// set ElementUI lang to EN
// Vue.use(ElementUI, { locale });
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI);

Vue.config.productionTip = false;

import Video from 'video.js';
import 'video.js/dist/video-js.css';
Vue.prototype.$video = Video; //引入Video播放器

/**全局组件挂载*/
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';
Vue.use(VueAwesomeSwiper);
import Empty from '@/components/empty/index.vue'; // 空状态
Vue.component('Empty', Empty);
import AlIcon from '@/components/al_icon/index.vue';
Vue.component('AlIcon', AlIcon);
import headAvator from '@/components/img-render';
Vue.component('headAvator', headAvator);
import directives from './utils/directive'; // 部分指令
directives.forEach(({ key, value }) => {
	Vue.directive(key, value);
});

/**全局挂载函数工具*/
import api from '@/api/index.js'; // 引入全局请求，并挂载
import isShopLogin from '@/utils/is-login.js';
import getEnumeration from '@/utils/enumeration';
import {
	getSiteId,
	getDydUserinfo,
	judgeFile,
	changeImageSize,
	addStorageEvent,
	judgeImg,
	loginOut,
	tenantId,
	activeUrl,
	hasEcomPermission
} from '@/utils/global_method';
import breadCrumb from '@/utils/bread_crumb';
import qs from 'qs';
// import WujieVue from 'wujie-vue2';
// Vue.use(WujieVue);
Vue.prototype.isShopLogin = isShopLogin; // 判断是否登录
Vue.prototype.$api = api; // 全局请求api
Vue.prototype.getSiteId = getSiteId; //获取站点
Vue.prototype.$getDydUserinfo = getDydUserinfo; //获取大预定的用户信息
Vue.prototype.$judgeImg = judgeImg; //拼接接口返回的图片地址 this.$judgeImg(url,isId)
Vue.prototype.$judgeFile = judgeFile; //拼接接口返回的附件地址
Vue.prototype.changeImageSize = changeImageSize; //裁剪图片地址
Vue.prototype.$addStorageEvent = addStorageEvent; // 川货搬过来的存储工具
Vue.prototype.$getEnumeration = getEnumeration; //获取枚举
Vue.prototype.$loginOut = loginOut; //退出登录
Vue.prototype.$addToBreadCrumb = breadCrumb; // 单个添加面包屑
Vue.prototype.$tenantId = tenantId(); // 获取租户
Vue.prototype.$activeUrl = activeUrl; // 根据地址栏动态拼接当前域名资源url
Vue.prototype.$hasEcomPermission = hasEcomPermission; // 根据地址栏动态拼接当前域名资源url
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
// 	const { mockXHR } = require('../mock');
// 	mockXHR();
// }
/**混入每个页面的数据、方法*/
Vue.mixin({
	computed: {
		// 用户信息
		_userinfo() {
			return store.getters.userinfo;
		}
	},
	methods: {
		/**获取ownId*/
		getOwnId(url) {
			if (!url) return '';
			let ownId = url.split('?')[1];
			ownId = qs.parse(ownId).adjunctId;
			return ownId;
		}
	}
});

/**挂载*/
window._VUE = new Vue({
	el: '#app',
	router,
	store,
	render: h => h(App)
});
