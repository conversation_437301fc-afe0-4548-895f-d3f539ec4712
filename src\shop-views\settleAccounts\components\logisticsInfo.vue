<template>
	<div class="logistics-info">
		<div class="logistics-info-header">
			<div class="icon"></div>
			<div class="title">物流配送信息</div>
		</div>
		<div class="logistics-info-offline">
			<el-form label-width="100px" :model="formLabelAlign">
				<el-form-item v-if="type != 1" label="配送方式：">
					<div class="distribution">
						<div
							v-for="item of distributionModeList"
							:key="item"
							:class="[
								'distribution-mode',
								formLabelAlign.distributionType == item ? 'distribution-active' : ''
							]"
							@click="distributionClick(item)"
						>
							<span>{{ item == 1 ? '买家自提' : '卖家配送' }}</span>
							<div v-show="formLabelAlign.distributionType == item" class="img"></div>
							<img class="check-img" src="@/assets/shop-images/check.png" alt="" />
						</div>
						<!-- <div
							:class="[
								'distribution-mode',
								formLabelAlign.distributionType === 2 ? 'distribution-active' : ''
							]"
							@click="distributionClick(2)"
						>
							<span>买家自提</span>
							<div v-show="formLabelAlign.distributionType === 2" class="img">
								<img src="@/assets/shop-images/check.png" alt="" />
							</div>
						</div> -->
					</div>
				</el-form-item>
				<el-form-item v-if="formLabelAlign.distributionType == 1" label="提货人：">
					<div>
						<span v-show="!list.length" class="switch-user">
							<span>
								<i class="el-icon-s-custom"></i>
							</span>
							<a href="javascript:void(0)" class="add-location" @click="dialog('add')">
								<span>新增提货人</span>
							</a>
						</span>
						<div v-for="item of list" v-show="item.active" :key="item.id" @click="activeSite(item)">
							<span class="address-list">
								<span :class="['address', 'user-address']">
									<span class="site">{{ item.contact }}</span>
									<span class="location">
										{{ item.telno }}
									</span>
								</span>
								<span class="switch-user">
									<span>
										<i class="el-icon-s-custom"></i>
									</span>
									<a href="javascript:void(0)" class="add-location" @click="dialog">
										<span>更换提货人</span>
									</a>
								</span>
							</span>
						</div>
					</div>
				</el-form-item>
				<el-form-item v-if="formLabelAlign.distributionType == 1" label="自提地址：">
					<div class="site-list">
						<div
							v-for="item of storeList"
							:key="item.id"
							:class="['site-list-item', item.checked ? 'site-list-item-active' : '']"
						>
							<div>
								<el-checkbox v-model="item.checked" @change="checkedChange(item)">
									{{ item.name }}
								</el-checkbox>
							</div>
							<div class="site-list-item-nav">
								<i class="el-icon-location"></i>
								<div class="site-list-item-nav-location">{{ item.addrDetail }}</div>
							</div>
						</div>
					</div>
				</el-form-item>
				<el-form-item v-if="formLabelAlign.distributionType == 2" label="收货地址：">
					<div>
						<div v-for="item of list" :key="item.id" class="address-list">
							<span
								:class="['address', item.active ? 'address-active' : '']"
								@click="activeAddress(item)"
							>
								<i class="el-icon-location"></i>
								<span class="site">{{ item.contact }}</span>

								<span class="location">
									{{ item.detailaddress }}
								</span>
								<span class="location">
									{{ item.telno }}
								</span>
							</span>
							<span v-show="item.type == 1" class="default">默认地址</span>
							<a href="javascript:void(0)">
								<span
									v-show="item.active && item.type == 2"
									class="default"
									@click="updateSite(item, 1)"
								>
									设为默认地址
								</span>
							</a>
							<a href="javascript:void(0)">
								<span class="default" @click="setDialogFormVisible(item)">编辑</span>
							</a>
						</div>
						<a href="javascript:void(0)" class="add-location" @click="addDialogFormVisible">
							+ 新增收货地址
						</a>
					</div>
				</el-form-item>
				<div v-if="restsList.length">
					<el-form-item label="其他信息："></el-form-item>
					<el-form-item v-for="(item, index) of restsList" :key="index" :label="`${item.text}：`">
						<el-input
							v-model="formLabelAlign[item.key]"
							placeholder="请输入内容"
							class="rests-input"
						></el-input>
					</el-form-item>
				</div>
			</el-form>
		</div>
		<PickUpDialog
			v-if="dialogUserVisible"
			ref="PickUpDialog"
			:dialog-user-visible="dialogUserVisible"
			:arr-list="arrList"
		/>
		<TakeDialog
			v-if="dialogFormVisible"
			:dialog-form-visible="dialogFormVisible"
			:set-form="form"
		/>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';
import PickUpDialog from './pickUpDialog.vue';
import TakeDialog from './takeDialog.vue';

export default {
	name: 'LogisticsInfo',
	components: { PickUpDialog, TakeDialog },
	props: {
		distributionModeList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		let phone = (rule, value, callback) => {
			var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('联系人电话不能为空'));
			} else if (!reg.test(value)) {
				return callback(new Error('请输入正确的电话'));
			}
			callback();
		};
		return {
			restsList: [],
			formLabelAlign: {
				distributionType: 2
			},
			dialogUserVisible: false,
			form: {
				telNo: null
			},
			dialogFormVisible: false,
			checked: false,
			storeList: [],
			isAdd: false,
			title: '选择提货人',
			list: [],
			rules: {
				contact: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
				addr: [{ required: true, message: '请选择所在地区', trigger: 'blur' }],
				detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
				telNo: [{ required: true, validator: phone, trigger: 'blur' }]
			},
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			type: 1,
			arrList: [],
			getDialogFormVisible: 1,
			userId: getCookie('user_id'),
			info: {}
		};
	},
	watch: {
		dialogFormVisible(newName, oldName) {
			// this.form = {};
		}
	},
	created() {
		this.userId = this.isShopLogin();
		this.type = this.$route.query.type;
		// if (this.type == 1) {
		// 	this.formLabelAlign.distributionType = 2;
		// }
		console.log(this.type, '++++++++++++--------------------------------------------+++');
		this.getUserAddrList();
		// this.getStoreList()
	},
	methods: {
		getStoreList(sellerId) {
			this.$parent.loading = true;
			this.$api.shop_api.getShopOfflineStoreList({ sellerId }).then(res => {
				this.storeList = res?.result || [];
				this.$parent.loading = false;
			});
		},
		// 收货地址列表
		getUserAddrList() {
			// this.$parent.loading = true;
			let data = {
				offset: 0,
				psize: 10,
				userId: this.userId
			};
			this.$api.shop_api.getUserAddrList(data).then(res => {
				if (res.state) {
					this.list = res.result;
					this.arrList = [];
					if (this.list && this.list.length) {
						this.list = this.list.map(item => {
							if (item.type == 1) {
								this.info = item;
								this.info.addressType = 1;
								this.$emit('Address', this.info);
							}
							return {
								...item,
								active: item.type == 1
							};
						});
						this.list[0].active = true;
						this.arrList = this.list;
						this.$emit('Address', this.arrList[0]);
					}
					this.$nextTick(() => {
						if (this.$refs.PickUpDialog) {
							this.$refs.PickUpDialog.list = this.arrList;
						}
					});
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				// this.$parent.loading = false;
				this.skeleton = false;
			});
		},
		setDialogFormVisible(item) {
			this.getDialogFormVisible = 2;
			this.dialogFormVisible = true;
			this.form = {};
			let addr = [];
			addr.push(item.provinceid);
			addr.push(item.cityid);
			addr.push(item.countyid);
			this.form = { ...item, telNo: item?.telno || null, addr, detailAddress: item.detailaddress };
		},
		addDialogFormVisible() {
			this.form = {};
			this.dialogFormVisible = true;
		},
		dialog(type) {
			this.dialogUserVisible = true;
		},
		updateSite(item, type) {
			let provinceId = '';
			let cityId = '';
			let countyId = '';
			provinceId = type ? item.provinceid : item.provinceId;
			cityId = type ? item.cityid : item.cityId;
			countyId = type ? item.countyid : item.countyId;
			let data = {
				...item,
				telNo: item?.telNo || item.telno,
				type: type ? type : item.type,
				detailAddress: item?.detailAddress || item?.detailaddress || ' ',
				userId: this.userId,
				cityId,
				countyId,
				provinceId
			};
			this.$api.shop_api.saveUserAddr(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message({
						message: res.msg || '添加成功',
						type: 'success'
					});
					this.dialogFormVisible = false;
					this.getUserAddrList();
				} else {
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},

		activeAddress(item) {
			item.distributionType = 2; //收货地址
			this.$emit('Address', item, 2);
			this.list = this.list.map(event => {
				return {
					...event,
					active: item.id == event.id
				};
			});
		},
		activeSite(item) {
			item.distributionType = 1; //提货人：
			this.$emit('Address', item, 3);
			this.list = this.list.map(event => {
				return {
					...event,
					active: item.id == event.id
				};
			});
		},
		checkedChange(item) {
			item.distributionType = 1; // 自提地址
			console.log(item);
			this.$emit('Address', item, 1);
			this.storeList = this.storeList.map(event => {
				return {
					...event,
					checked: item.id == event.id
				};
			});
		},
		distributionClick(item) {
			console.log(item, 'asdasd');
			this.$parent.distributionModeType = item;
			if (item == 2) {
				this.$parent.$refs.aggregateAmount.show = true;
			} else {
				this.$parent.$refs.aggregateAmount.show = false;
			}
			this.formLabelAlign.distributionType = item;
			this.info.distributionType = item;
			if (this.arrList && this.arrList.length) {
				this.arrList.forEach(event => {
					if (event.active) {
						this.info = { ...event, distributionType: item };
					}
				});
			}
			this.$emit('Address', this.info);
		}
	}
};
</script>
<style lang="scss" scoped>
.logistics-info {
	background-color: #fff;
	padding: 16px 20px;
	&-header {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-offline {
		margin-top: 15px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		padding: 16px 0px;
		.distribution {
			cursor: pointer;
			display: flex;
		}
		.distribution-mode {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid #d9d9d9;
			padding: 8px 18px;
			margin-right: 10px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 22px;
			overflow: hidden;
			position: relative;
			.check-img {
				position: absolute;
				bottom: 0px;
				right: 0px;
			}
			.img {
				position: absolute;
				height: 27px;
				width: 15px;
				bottom: -10px;
				right: -3px;
				background: var(--brand-6, '#ca3f3b');
				-moz-transform: rotate(45deg);
				-webkit-transform: rotate(45deg);
				-o-transform: rotate(45deg);
				transform: rotate(45deg);
			}
		}
		.distribution-active {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			color: var(--brand-6, '#ca3f3b');
		}
		.site {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #262626;
			line-height: 20px;
		}
		.location {
			margin-left: 5px;
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
		}
		.default {
			margin-left: 16px;
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 20px;
		}
		.address {
			display: flex;
			align-items: center;
			padding: 5px 19px 5px 5px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid #eeeeee;
			cursor: pointer;
		}
		.address-active {
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			i {
				color: var(--brand-6, '#ca3f3b');
			}
		}
		.add-location {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 20px;
		}
		.site-list {
			display: flex;
			flex-wrap: wrap;
			&-item {
				height: 72px;
				margin-bottom: 16px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #d9d9d9;
				padding: 0px 20px;
				margin-right: 8px;
				&-nav {
					display: flex;
					align-items: center;
					i {
						color: #8c8c8c;
					}
					&-location {
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
			.site-list-item-active {
				border: 1px solid var(--brand-6, '#ca3f3b');
				::v-deep .el-checkbox__label {
					color: var(--brand-6, '#ca3f3b');
				}
				.site-list-item-nav {
					display: flex;
					align-items: center;
					i {
						color: var(--brand-6, '#ca3f3b');
					}
					.site-list-item-nav-location {
						color: var(--brand-6, '#ca3f3b');
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
		}
	}
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
	background-color: var(--brand-6, '#ca3f3b');
	border-color: var(--brand-6, '#ca3f3b');
}
.user-address {
	border: none !important;
}
.switch-user {
	padding: 5px 8px;
	margin-left: 20px;
	height: 30px;
	background: #ffffff;
	line-height: 0px;
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	border: 1px solid var(--brand-6, '#ca3f3b');
	font-size: 12px;
	font-family: Source Han Sans SC-Normal, Source Han Sans SC;
	font-weight: 400;
	color: var(--brand-6, '#ca3f3b');
	span {
		margin-left: 4px;
	}
}
.switch-user-from {
	padding: 5px 8px;
	width: 110px;
	margin-top: 10px;
	background: #ffffff;
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	border: 1px solid var(--brand-6, '#ca3f3b');
	font-size: 12px;
	font-family: Source Han Sans SC-Normal, Source Han Sans SC;
	font-weight: 400;
	color: var(--brand-6, '#ca3f3b');
	span {
		margin-left: 4px;
	}
}
.list {
	max-height: 400px;
	overflow-y: auto;
	&-item_active {
		overflow: hidden;
		border: 1px solid var(--brand-6, '#ca3f3b') !important;
		position: relative;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		margin-bottom: 10px;
		.img {
			position: absolute;
			height: 46px;
			width: 20px;
			top: -15px;
			left: -7px;
			background: var(--brand-6, '#ca3f3b');
			transform: rotate(45deg);
		}
		.check-img {
			position: absolute;
			top: 0px;
			left: 0px;
			width: 16px;
			height: 16px;
		}
	}
	&-item {
		position: relative;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		margin-bottom: 10px;
		height: 93px;
		&-title {
			display: flex;
			justify-content: space-between;
			font-size: 18px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
			div {
				padding: 10px 20px;
			}
			i {
				margin-top: 12px;
				margin-right: 9px;
			}
		}
		&-dec {
			display: flex;
			padding: 10px 21px;
			justify-content: space-between;
			div {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #9da5b7;
				.phone {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
				}
			}
			.form-button {
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				cursor: pointer;
				span {
					padding: 0 20px;
				}
			}
		}
	}
}
.address-list {
	display: flex;
	align-items: center;
}
.rests-input {
	width: 416px;
}
</style>
