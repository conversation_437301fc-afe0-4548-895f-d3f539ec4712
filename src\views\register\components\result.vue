<!--
 * @Description: 注册成功
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-04-17 15:38:08
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-06-19 13:41:46
-->
<template>
	<div class="result">
		<div class="result-icon"><i class="iconfont icon-check-circle-filled"></i></div>
		<div class="result-text">注册成功</div>
		<div class="result-info">恭喜您完成注册</div>
		<div>
			<el-button class="result-btn" type="primary" ghost @click="handleToHome">去往登录</el-button>
		</div>
	</div>
</template>
<script>
export default {
	name: 'RegisterForm',
	methods: {
		handleToHome() {
			this.$router.push({ path: '/login', query: this.$route.query });
		}
	}
};
</script>
<style lang="scss" scoped>
.result {
	text-align: center;
	&-icon {
		i {
			font-size: 63px;
			color: #76bf6a;
		}
		margin-bottom: 16px;
	}
	&-text {
		height: 44px;
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 44px;
		margin-bottom: 5px;
	}
	&-info {
		height: 24px;
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
		margin-bottom: 32px;
	}
	&-btn {
		width: 180px;
		height: 38px;
		font-size: 14px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
	}
}
</style>
