<template>
	<div class="market">
		<!-- top -->
		<div class="top">
			<div class="left">
				<img src="" alt="" />
				<span>平台公示</span>
			</div>
			<div class="right">
				<el-carousel height="20px" direction="vertical">
					<el-carousel-item v-for="item in 3" :key="item">
						<a class="medium">{{ item }}</a>
					</el-carousel-item>
				</el-carousel>
				<div class="link">
					<a href="">更多></a>
				</div>
			</div>
		</div>
		<div class="conten">
			<!-- title -->
			<div class="title">
				<div class="left">
					<img src="" alt="" />
					<span>市场行情</span>
				</div>
				<div class="right">
					<a href="">更多></a>
				</div>
			</div>
			<!-- search -->
			<div class="search">
				<el-select v-model="value1" multiple placeholder="请选择">
					<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>

				<el-select
					v-model="value2"
					multiple
					collapse-tags
					style="margin-left: 7px"
					placeholder="请选择"
				>
					<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
				<el-select
					v-model="value2"
					multiple
					collapse-tags
					style="margin-left: 7px"
					placeholder="请选择"
				>
					<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
			</div>
			<!-- chart -->
			<div class="chart">
				<chart-block :option="rchartsOption" />
			</div>
		</div>
	</div>
</template>

<script>
import ChartBlock from '@/components/public/ChartBlock.vue';
export default {
	name: 'Market',
	components: { ChartBlock },
	data() {
		return {
			options: [
				{
					value: '选项1',
					label: '黄金糕'
				},
				{
					value: '选项2',
					label: '双皮奶'
				},
				{
					value: '选项3',
					label: '蚵仔煎'
				},
				{
					value: '选项4',
					label: '龙须面'
				},
				{
					value: '选项5',
					label: '北京烤鸭'
				}
			],
			value1: [],
			value2: [],
			rchartsOption: {
				title: {
					text: ''
				},
				legend: {
					bottom: 10,
					data: ['成都市 大豆 七天内行情趋势图']
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						name: '成都市 大豆 七天内行情趋势图',
						type: 'line',
						smooth: true,
						areaStyle: {},
						data: [5, 20, 36, 10, 10, 20]
					}
				]
			}
		};
	}
};
</script>

<style lang="scss" scoped>
.market {
	.top {
		width: 352px;
		height: 42px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		position: relative;

		.left {
			width: 118px;
			padding: 15px;
			font-size: 14px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #ca3f3b;
		}
		.right {
			width: 234px;
			position: absolute;
			left: 118px;
			padding: 15px;
			top: 0px;

			::v-deep .el-carousel__item {
				a {
					font-size: 12px;
					font-family: Source Han Sans SC-Normal, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
				}
			}
			::v-deep .el-carousel__indicators {
				display: none;
			}
			.link {
				font-size: 12px;
				font-family: Inter-Regular, Inter;
				font-weight: 400;
				color: #8c8c8c;
				padding: 15px;
				position: absolute;
				right: 0px;
				top: 0px;
			}
		}
	}
	.conten {
		width: 352px;
		height: 417px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		.title {
			margin-top: 8px;
			position: relative;
			padding: 10px 15px;
			width: 350px;
			height: 42px;
			background: linear-gradient(91deg, #fff7ea 0%, rgba(252, 233, 233, 0) 100%);
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			.left {
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #262626;
			}
			.right {
				position: absolute;
				right: 0px;
				top: 0px;
				padding: 10px 15px;
				font-size: 12px;
				font-family: Inter-Regular, Inter;
				font-weight: 400;
				color: #8c8c8c;
			}
		}
		.search {
			padding: 9px 11px;
			::v-deep .el-input {
				width: 104px;
			}
		}
		.chart {
			height: 316px;
		}
	}
}
</style>
