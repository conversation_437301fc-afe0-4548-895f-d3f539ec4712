<template>
	<div
		class="points"
		:style="
			isMain ? 'width:1200px;margin-top: 7px' : 'width:100%;background: #ffffff;padding: 20px;'
		"
	>
		<points-header @openAllPoint="openAllPoint"></points-header>
		<tabs @getList="getList"></tabs>
		<goods-list
			ref="goodsList"
			v-loading="loading"
			:list="list"
			:total="total"
			@changePage="changePage"
		></goods-list>
		<all-point :visible.sync="visible"></all-point>
	</div>
</template>

<script>
import pointsHeader from '@/shop-views/pointsMall/components/header';
import tabs from '@/shop-views/pointsMall/components/tabs';
import goodsList from '@/shop-views/pointsMall/components/list';
import allPoint from '@/shop-views/pointsMall/components/allPoint';
export default {
	name: 'Index',
	components: {
		pointsHeader,
		tabs,
		goodsList,
		allPoint
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			list: [],
			loading: false,
			visible: false,
			params: {
				pageNum: 1, //当前页(默认1)
				pageSize: 8, //页大小(默认10)
				categoryCode: '', //商品类型:
				sortRule: '', //排序规则:
				sellPointMin: '', //所需最少积分
				sellPointMax: '' //所需最多积分
				//categoryId: '' //商品类型编号
			},
			total: 0
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/**该表页码大小*/
		changePage(page, size) {
			this.$set(this.params, 'pageNum', page);
			this.$set(this.params, 'pageSize', size);
			this.getList();
		},
		/**打开收支记录弹窗*/
		openAllPoint() {
			this.visible = true;
		},
		/**获取积分商品列表*/
		getList(data) {
			this.loading = true;
			if (data) {
				// 如果搜索条件改变，要重置页码以及数据
				this.params.pageNum = 1;
				this.params.pageSize = 8;
				this.$refs.goodsList.reset(); // 重置充值分页组件
				Object.keys(this.params).forEach(key => {
					if (data[key] !== undefined) {
						this.params[key] = data[key];
					}
				});
			}
			let params = {
				rentId: this.getSiteId(),
				...this.params
			};
			this.$api.shop_api.productSku(params).then(res => {
				this.loading = false;
				if (res.code !== 200) return;
				this.list = res.data?.records;
				this.total = res.data.total;
			});
		}
	}
};
</script>

<style scoped lang="scss">
.points {
	margin: 0 auto;
}
</style>
