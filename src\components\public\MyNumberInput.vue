<template>
	<input
		class="my-number-input"
		:style="{ textAlign: textAlign }"
		:step="numberStep"
		type="number"
		:placeholder="placeholder"
		v-model.number="inputModel"
		@input="inputHandle"
		oninput="value=value.replace(/[^\d]/g,'')"
	/>
</template>
<script type="text/ecmascript-6">
export default {
  components: {},
  props: {
    // 允许输入几位小数
    point:{
      default: 0
    },
    // 允许输入最大值
    max:Number,
    // placeholder
    placeholder:String,
    value: {
      default: null
    },
    textAlign:{
        type:String,
        default: 'left'
    }
  },
  computed:{
    inputModel: {
      get:function() {
        // 父组件==>子组件 发消息
        return this.value;
      },
      set:function (value) {
        // 子组件==>父组件 发消息
        let val = this.$el.value;
        let len = val.length;
        // 解决首位直接输入 '0开头的数字'问题
        if(len==2 && val.charAt(0)==0 && val.charAt(1)!='.'){
          this.$el.value = val.charAt(1);
          this.setParentModeVal(this.$el.value);
          return;
        }
        // 解决数字键盘可以输入输入多个小数点问题
        if(Math.abs(this.value) > 0 && val==='' && value ===''){
          if(this.keyDownDel){
            this.$el.value = '';// 正常删除
          }else {
            this.$el.value = this.value;// 多次输入小数点时
          }
          this.setParentModeVal(this.$el.value);
          return ;
        }

        // 解决开始就输入点问题
        if(this.value === '' && val === '' && value === ''){
          this.$el.value = '';
          this.setParentModeVal('');
          return ;
        }

        // 解决负数问题
        if(this.value<0){
          this.$el.value = '';
          this.setParentModeVal('');
          return ;
        }

        // 解决保留小数问题
        if(val){
          let pointIndex =  val.indexOf('.');
          if(pointIndex>0 && (len - pointIndex)>(this.point+1)){
            this.$el.value = val.substr(0,pointIndex + this.point +1);
            this.setParentModeVal(this.$el.value);
            return ;
          }
        }

        // 解决输入最大值问题
        if(this.max>0 && val>this.max){
          this.$el.value = val.substr(0,len-1);
          this.setParentModeVal(this.$el.value);
          return;
        }

        this.setParentModeVal(val);
        return;
      }
    },
    numberStep:function() {
      let step=1
      if(this.point>0){
        step=step/Math.pow(10,this.point)
      }
      return step
    }
  },
  data() {
    return {
      keyDownDel:true,// 判断键盘输入值
    }
  },
  mounted() {
    // 判断键盘是否是删除动作
    // window.document.onkeydown = function(event){
    //   let e = event || window.event || arguments.callee.caller.arguments[0];
    //   if(e.keyCode==8||e.keyCode==46){
    //     that.keyDownDel = true;
    //   }else {
    //     that.keyDownDel = false;
    //   }
    // };
  },
  methods: {
      inputHandle(e){
        const noPoint = !this.point && e.data === '.' // 不能輸入小数点
        const havePoint = this.point && e.target.value.indexOf('.') !== -1 && e.data === '.' // 只能输入一个小数点
        if(noPoint || havePoint){
            this.setParentModeVal('');
            return
        }
      },
      setParentModeVal(value){
        // this.value = value;
        // 解决负数问题
        if(value<0){
          this.$el.value = 0;
          this.setParentModeVal(0);
          return ;
        }
        this.$emit('input', value);
      }
  },
}
</script>

<style lang="scss" scoped>
.my-number-input {
	height: 32px;
	user-select: auto;
	outline: none; // 去除默认选中状态边框
	background: #ffffff;
	border-radius: 2px;
	border: 1px solid #e5e5e5;
	padding-left: 15px;
	box-sizing: border-box;
	color: #222222;
	&:disabled {
		background-color: #f5f7fa;
		border-color: #e5e5e5;
		color: #bfbfbf;
		cursor: not-allowed;
	}
}
</style>
