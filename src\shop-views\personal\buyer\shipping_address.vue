<template>
	<div
		v-loading="loading"
		class="shipping_address"
		:style="{ width: isMain ? '1200px' : '100%', marginBottom: isMain ? '40px' : '' }"
	>
		<div class="head">
			<el-button type="primary" icon="el-icon-plus" @click="openDia('添加收货地址')">
				新增收货地址
			</el-button>
			<div class="tips">您已创建 {{ list.length }} 个收货地址，最多可创建10个</div>
		</div>
		<el-skeleton :loading="skeleton" animated>
			<div v-for="(item, index) in list" :key="index" class="list">
				<div v-if="item.type != 1" class="close" @click="handleClose(item.id)">
					<AlIcon name="icon-close" color="rgba(217, 217, 217, 1)" size="16px"></AlIcon>
				</div>
				<div v-show="item.type == 1" class="tag">
					<el-tag type="danger">默认地址</el-tag>
				</div>
				<div class="name">{{ item.contact }}</div>
				<div class="box">
					<div class="cell">
						<div class="lf">收货人：</div>
						<div class="rf">{{ item.contact || '-' }}</div>
					</div>
					<div class="cell">
						<div class="lf">地址：</div>
						<div class="rf">
							{{
								`${item.provinceName || ''} ${item.cityName || ''} ${item.countyName || ''} ${
									item.detailaddress || ''
								}`
							}}
						</div>
					</div>
					<div class="cell">
						<div class="lf">手机：</div>
						<div class="rf">{{ item.telno || '-' }}</div>
					</div>
					<div class="btn-box">
						<el-link
							v-if="item.type != 1"
							type="primary"
							:underline="false"
							@click="handleTyp(item)"
						>
							设为默认
						</el-link>
						<el-link type="primary" :underline="false" @click="openDia('编辑收货地址', item)">
							编辑
						</el-link>
					</div>
				</div>
			</div>
			<el-empty v-if="list.length == 0" description="暂无数据"></el-empty>
		</el-skeleton>
		<el-dialog
			:key="dialogVisible"
			v-loading="diaLoading"
			:title="diaTitle"
			:visible.sync="dialogVisible"
			width="800px"
			:append-to-body="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			destroy-on-close
			@close="
				() => {
					$refs.form.clearValidate();
				}
			"
		>
			<el-form ref="form" :model="form" :rules="rules" label-width="100px" class="form">
				<el-form-item label="收货人" prop="contact">
					<el-input
						v-model="form.contact"
						placeholder="请输入收货人"
						@change="inputChange()"
					></el-input>
				</el-form-item>
				<el-form-item label="手机号码" prop="telNo">
					<el-input
						v-model="form.telNo"
						placeholder="请输入手机号码"
						@change="inputChange()"
					></el-input>
				</el-form-item>
				<el-form-item label="身份证号" prop="idCard">
					<el-input
						v-model="form.idCard"
						placeholder="请输入身份证号"
						@change="inputChange()"
					></el-input>
				</el-form-item>
				<el-form-item label="邮政编码" prop="postCode">
					<el-input
						v-model="form.postCode"
						placeholder="请输入邮政编码"
						@change="inputChange()"
					></el-input>
				</el-form-item>
				<el-form-item label="所在地区" prop="addr">
					<el-cascader
						v-model="form.addr"
						:options="addresOptions"
						placeholder="请选择所在地区"
						:props="{ label: 'name', value: 'value' }"
					></el-cascader>
				</el-form-item>
				<el-form-item label="详细地址" prop="detailAddress">
					<el-input
						v-model="form.detailAddress"
						placeholder="请输入详细地址"
						@change="inputChange()"
					></el-input>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="form.remark"
						placeholder="请输入备注"
						@change="inputChange()"
					></el-input>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="saveUserAddr">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';

export default {
	name: 'ShippingAddress',
	components: {
		// Empty
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		let phone = (rule, value, callback) => {
			var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('联系人电话不能为空'));
			} else if (!reg.test(value)) {
				return callback(new Error('请输入正确的电话'));
			}
			callback();
		};
		return {
			diaTitle: '添加收货地址',
			dialogVisible: false,
			form: {
				contact: '',
				detailAddress: '',
				telNo: '',
				type: '',
				idCard: '',
				postCode: '',
				remark: '',
				addr: []
			},
			rules: {
				contact: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
				idCard: [
					{
						pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
						message: '请输入正确的身份证号',
						trigger: 'blur'
					}
				],
				postCode: [
					{
						pattern: /^[0-9]{6}$/,
						message: '请输入正确的邮政编码',
						trigger: 'blur'
					}
				],
				addr: [{ required: true, message: '请选择所在地区', trigger: 'change' }],
				detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
				telNo: [{ required: true, validator: phone, trigger: 'blur' }]
			},
			list: [],
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			loading: false,
			skeleton: true,
			userId: getCookie('user_id'),
			diaLoading: false
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getUserAddrList();
		}
	},
	methods: {
		// 解决弹窗表单不能输入的问题
		inputChange() {
			this.$forceUpdate();
		},
		// 设为默认
		handleTyp(t) {
			this.loading = true;
			const data = {
				userId: this.userId,
				id: t.id,
				contact: t.contact,
				telNo: t.telno,
				detailAddress: t.detailaddress,
				type: '1',
				idCard: t.idCard,
				postCode: t.postCode,
				remark: t.remark,
				provinceId: t.provinceid,
				cityId: t.cityid,
				countyId: t.countyid
			};
			this.$api.shop_api.saveUserAddr(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message({
						message: res.msg || '设置成功',
						type: 'success'
					});
					this.getUserAddrList();
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 新增收货地址
		saveUserAddr() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.diaLoading = true;
					const provinceId = this.form.addr[0];
					const cityId = this.form.addr[1];
					const countyId = this.form.addr[2];
					let type = this.form.type || 2;
					if (this.list.length == 0) {
						type = 1;
					}
					const data = {
						...this.form,
						provinceId,
						cityId,
						countyId,
						userId: this.userId,
						type: type
					};
					delete data.addr;
					this.$api.shop_api.saveUserAddr(data).then(res => {
						if (res.state) {
							this.$message({
								message: res.msg || '添加成功',
								type: 'success'
							});
							this.dialogVisible = false;
							this.getUserAddrList();
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
						this.diaLoading = false;
					});
				} else {
					this.diaLoading = false;
					return false;
				}
			});
		},
		// 收货地址列表
		getUserAddrList() {
			let data = {
				offset: 0,
				psize: 10,
				userId: this.userId
			};
			this.$api.shop_api.getUserAddrList(data).then(res => {
				if (res.state) {
					this.list = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.skeleton = false;
			});
		},
		openDia(t, row) {
			this.$message.close();
			if (this.list.length >= 10) {
				this.$message.error('收货地址最多创建10个！');
				return;
			}
			this.diaTitle = t;
			if (this.diaTitle == '编辑收货地址') {
				this.form.contact = row.contact || '';
				this.form.id = row.id;
				this.form.detailAddress = row.detailaddress || '';
				this.form.telNo = row.telno || '';
				this.form.type = row.type;
				this.form.addr = [row.provinceid || '', row.cityid || '', row.countyid || ''];
				this.form.idCard = row.idCard || '';
				this.form.postCode = row.postCode || '';
				this.form.remark = row.remark || '';
			} else {
				this.form = {
					name: '',
					detailAddress: '',
					telNo: '',
					type: '',
					addr: [],
					idCard: '',
					postCode: '',
					remark: ''
				};
			}
			this.dialogVisible = true;
		},
		handleClose(id) {
			this.$confirm('确定要删除当前地址？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.shop_api.delAddr({ id }).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '删除成功',
								type: 'success'
							});
							this.getUserAddrList();
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
						this.loading = false;
					});
				})
				.catch(() => {});
		}
	}
};
</script>

<style lang="scss" scoped>
.shipping_address {
	margin: 0 auto;
	padding: 20px;
	background: #ffffff;
	.head {
		display: flex;
		align-items: flex-end;
		.tips {
			margin-left: 6px;
			font-size: 12px;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
		}
	}
	.list {
		position: relative;
		margin-top: 22px;
		border: 1px solid #d9d9d9;
		padding: 12px 19px 23px 21px;
		.close {
			position: absolute;
			top: 12px;
			right: 23px;
			cursor: pointer;
		}
		.tag {
			position: absolute;
			top: 0;
			right: 0;
		}
		.name {
			font-size: 18px;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
		}
		.box {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-between;
			.cell {
				margin-top: 9px;
				width: 46%;
				display: flex;
				align-items: center;
				.lf {
					width: 74px;
					height: 22px;
					font-size: 14px;
					font-weight: 400;
					color: #9da5b7;
					line-height: 22px;
				}
				.rf {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
				}
			}
			.btn-box {
				display: flex;
				width: 15%;
				justify-content: space-between;
			}
		}
	}
	::v-deep.el-dialog__body {
		padding: 12px;
		border-top: 1px solid #eeeeee;
		border-bottom: 1px solid #eeeeee;
		.el-input {
			width: 380px;
		}
	}
}
</style>
