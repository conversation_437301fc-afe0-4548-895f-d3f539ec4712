<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 社区成员审核
 @author: WH
 @date: 2023/9/20
 -->
<template>
	<el-dialog title="审核成员" :visible.sync="visible" :before-close="beforeClose">
		<el-form ref="form" :rules="rules" :model="form" label-width="140px">
			<el-form-item label="审核结果" prop="isAudit">
				<el-radio-group v-model="form.isAudit">
					<el-radio label="1">同意</el-radio>
					<el-radio label="2">驳回</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="备注">
				<el-input v-model="form.remark" type="textarea"></el-input>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" :loading="loading" @click="submit">审 核</el-button>
			<el-button @click="beforeClose">取 消</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	props: {
		memberId: {
			type: String,
			required: true
		},
		communityId: {
			type: String,
			required: true
		},
		visible: {
			type: Boolean,
			required: true
		}
	},
	data() {
		return {
			loading: false,
			form: {},
			rules: {
				isAudit: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
			}
		};
	},

	methods: {
		submit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.addPhotos();
				}
			});
		},
		beforeClose() {
			this.$emit('update:visible', false);
		},
		//新增社区相册——————————————————————————————
		async addPhotos() {
			this.loading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.auditMember({
					...this.form,
					memberId: this.memberId,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.form = {};
					this.beforeClose();
					this.$emit('crudMember', 'add');
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.dialog-footer {
	text-align: center;
}
</style>
