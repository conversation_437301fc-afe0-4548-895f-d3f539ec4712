<template>
	<div>
		<div v-show="total != 0" v-loading="loading" class="goods">
			<div class="item">
				<a
					v-for="item of list"
					:key="item.productId"
					href="javascript:void(0)"
					@click="details(item)"
				>
					<div class="item-nav">
						<img :src="$judgeFile(item.coverUrl)" alt="" />
						<div class="name">
							<span class="title">
								<span v-show="item.recommendNames" class="tag">{{ item.recommendNames }}</span>
								{{ item.productName }}
							</span>
						</div>
						<div class="price">
							<span>¥</span>
							<template v-if="classifyInfo.type === 'knowledge'">
								{{ item.isFree === '1' ? '免费' : item.sellPrice.toFixed(2) }}
							</template>
							<template v-else>
								{{ item.sellPrice.toFixed(2) }}
							</template>
						</div>
						<div v-if="item.shopLogo || item.shopName" class="store-name">
							<img v-if="item.shopLogo" :src="$judgeFile(item.shopLogo)" alt="" />
							<img v-else src="@/assets/shop-images/Group-1205.png" alt="" />
							{{ item.shopName }}
						</div>
					</div>
				</a>
			</div>
			<div class="pagination">
				<pagination
					:fixed="'left'"
					:current-page="page"
					:page-size="limit"
					:total="total"
					:layout="'total,->,prev,pager,next,jumper'"
					@paginationChange="paginationChange"
				/>
			</div>
		</div>
		<Empty v-show="total == 0" :tips="'暂无商品信息'" />
	</div>
</template>

<script>
import Pagination from '@/components/public/Pagination.vue';
export default {
	components: { Pagination },
	props: {
		classifyInfo: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			page: 1, // 当前页
			limit: 12, // 每页显示的条数
			total: 0, // 总条数
			list: [],
			offset: 0,
			psize: 12,
			loading: false,
			type: 1
		};
	},

	created() {},
	mounted() {
		// this.getList();
	},
	methods: {
		paginationChange(item) {
			if (item.type == 1) {
				this.offset = 0;
				this.psize = 12;
			} else {
				this.offset = this.psize * (item.value - 1);
				this.psize = 12;
			}
			this.$parent.loading = true;
			this.getList(this.classifyInfo);
		},
		details(t) {
			if (this.classifyInfo.type === 'knowledge') {
				this.$router.push({
					path: '/freecourses',
					query: {
						id: t.id || ''
					}
				});
			} else {
				let url = this.$route.query?.url || '';
				this.$router.push({
					path: '/shopDetail',
					query: {
						id: t.productId || t.id,
						type: this.classifyInfo.type,
						url: url
					}
				});
			}
		},
		getList() {
			this.type = this.classifyInfo.type1;
			this.loading = true;
			// 技术技能培训
			if (this.classifyInfo.type === 'knowledge') {
				const { typeId, keywords, orderRule } = this.classifyInfo;
				// all-1:综合，通过评分排序 time-4: 新品,发布时间 跟新时间 saledNum-2: 人气，学习人数
				const orderByColumn = orderRule === 'all' ? 1 : orderRule === 'time' ? 4 : 2;
				const data = {
					courseClassIds: typeId ? [typeId] : [],
					name: keywords,
					isAsc: 'desc',
					orderByColumn,
					pageNum: this.offset / this.psize + 1,
					pageSize: this.psize
				};
				this.$api.study_api.pageList(data).then(res => {
					this.list = (res.data.items || []).map(v => {
						Object.assign(v, {
							productId: v.id,
							coverUrl: v.coverImg,
							productName: v.name,
							sellPrice: v.currentPrice
						});
						return v;
					});
					this.total = Number(res.data.total);
					this.$parent.loading = false;
				});
			} else {
				let data = {
					siteId: this.getSiteId(),
					offset: this.offset,
					psize: this.psize,
					...this.classifyInfo
					// categoryId: this.info.categoryId
				};
				this.$api.shop_api.getAllProduct(data).then(res => {
					this.list = res?.result || [];
					this.total = Number(res.totalNum);
					this.$parent.loading = false;
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.goods {
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	margin-top: 16px;
	padding: 38px 20px;
	.item {
		display: flex;
		flex-wrap: wrap;
		&-nav:hover {
			.title {
				color: var(--brand-6, '#ca3f3b') !important;
			}
		}
		&-nav {
			margin: 8px 12px 0;
			width: 263px;
			height: 367px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			padding: 8px 6px;
			img {
				width: 250px;
				height: 250px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				object-fit: cover;
			}
			.name {
				margin-top: 7px;
				.tag {
					padding: 2px 8px;
					background: #ffeeee;
					border-radius: 2px 2px 2px 2px;
					opacity: 1;
					font-size: 12px;
					font-family: Source Han Sans SC-Normal, Source Han Sans SC;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
					line-height: 20px;
					width: 40px;
					margin-right: 5px;
				}
				.title {
					height: 48px;
					font-size: 16px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #404040;
					line-height: 24px;
					overflow: hidden; //超出的文本隐藏
					text-overflow: ellipsis; //溢出用省略号显示
					display: -webkit-box;
					-webkit-line-clamp: 2; // 超出多少行
					-webkit-box-orient: vertical;
				}
			}
			.price {
				width: 79px;
				height: 26px;
				font-size: 15px;
				font-family: Rany-Medium, Rany;
				font-weight: 500;
				color: var(--brand-6, '#ca3f3b');
				line-height: 20px;
				span {
					font-size: 11px;
				}
			}
			.store-name {
				font-size: 12px;
				font-family: Source Han Sans SC-Normal, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
				display: flex;
				align-items: center;
				img {
					width: 12px;
					height: 12px;
					margin-right: 5px;
					object-fit: cover;
				}
			}
		}
	}
	.pagination {
		margin-top: 73px;
	}
}
::v-deep .empty {
	margin-top: 60px;
}
</style>
