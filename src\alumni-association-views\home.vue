<template>
	<div class="alumni-association-box">
		<!-- 校友会页面 -->
		<!-- 顶部大图部分 -->
		<div v-loading="bannerLoading" class="top-box">
			<!-- <div class="top-title-box"></div> -->
			<div v-if="bannerList && bannerList.length" class="swiper">
				<el-carousel
					arrow="never"
					:interval="5000"
					:autoplay="true"
					indicator-position="outside"
					height="400px"
				>
					<el-carousel-item v-for="(item, index) in bannerList" :key="index">
						<img :src="$judgeFile(item.url)" alt="" class="carousel-img" />
					</el-carousel-item>
				</el-carousel>
			</div>
			<Empty v-else :tips="'暂无数据'" />
		</div>
		<!-- 中间内容区域 -->
		<!-- 校友动态区域 -->
		<div v-loading="newsListLoading" class="content-box latest-box">
			<div class="title-box">
				<span class="title">校友动态</span>
				<span class="title-desc" @click="toList('news')">
					<i>更多</i>
					<i class="el-icon-arrow-right iconDistance"></i>
					<i class="el-icon-arrow-right iconDistance"></i>
				</span>
			</div>
			<!-- 内容区域 -->
			<div v-if="newsList && newsList.length" class="latest-content">
				<!-- 左边区域 -->
				<div class="content-left">
					<div class="carousel-box">
						<el-carousel
							ref="carousel"
							trigger="click"
							height="440px"
							arrow="never"
							:autoplay="false"
							indicator-position="none"
							@change="carouselChange"
						>
							<el-carousel-item v-for="(item, index) in newsList.slice(0, 3)" :key="index">
								<img class="item-carousel-img" :src="getYbzyImg(item.coverImg)" alt="" />
							</el-carousel-item>
						</el-carousel>
						<!-- 展示的底部内容 -->
						<div class="carousel-bottom">
							<div class="carousel-left">
								<span class="day">{{ newsList[carouselIndex].publishTime | dateShow('day') }}</span>
								<span class="time">
									{{ newsList[carouselIndex].publishTime | dateShow('mouths', '.') }}
								</span>
							</div>
							<div class="carousel-poits">
								<template v-for="(item, index) in newsList.slice(0, 3)">
									<span
										:key="index"
										:class="[index == carouselIndex ? 'poit-active' : '', 'poit']"
										@click="poitClick(item)"
									></span>
								</template>
							</div>
							<div class="carousel-right">
								<span class="prev" title="上一个" @click="carouselPrevNext('prev')">
									<!-- <i class="el-icon-back carousel-icon"></i> -->
								</span>
								<span class="next" title="下一个" @click="carouselPrevNext('next')">
									<!-- <i class="el-icon-right carousel-icon"></i> -->
								</span>
							</div>
						</div>
					</div>
					<div class="text-content" @click="toDetail(newsList[carouselIndex].id)">
						<div class="text-title u-line-1">{{ newsList[carouselIndex].title }}</div>
						<div class="text-desc u-line-2">{{ newsList[carouselIndex].abstract }}</div>
					</div>
				</div>
				<!-- 右边区域 -->
				<div class="content-right">
					<div
						v-for="(item, index) in newsList.slice(3, 8)"
						:key="index"
						class="item-card"
						@click="toDetail(item.id)"
					>
						<div class="time-box">
							<span class="time-day">{{ item.publishTime | dateShow('day') }}</span>
							<span class="time-mouth">{{ item.publishTime | dateShow }}</span>
						</div>
						<div class="text-content">
							<div class="text-title u-line-1">{{ item.title }}</div>
							<div class="text-desc u-line-2">{{ item.abstract }}</div>
						</div>
					</div>
				</div>
			</div>
			<Empty v-else :tips="'暂无数据'" />
		</div>
		<!-- 职教视界 || 校友之声 || 通知公告 -->
		<div class="content-box alumni-news-box">
			<!-- 内容区域 -->
			<div class="alumni-news-content">
				<div class="alumni-news-content-item alumni-news-content-item-right-padding">
					<div class="title-box-by-parent">
						<span class="title">职教视界</span>
						<span class="title-desc" @click="toList('education')">
							<i>更多</i>
							<i class="el-icon-arrow-right iconDistance"></i>
							<i class="el-icon-arrow-right iconDistance"></i>
						</span>
					</div>
					<!-- 职教视界内容区域 -->
					<div
						v-if="alumnaEducationViewList && alumnaEducationViewList.length"
						v-loading="alumnaEducationViewListLoading"
						class="alumni-news-content-item-content"
					>
						<homeEducationBoundary
							:alumna-education-view-list="alumnaEducationViewList"
						></homeEducationBoundary>
					</div>
					<Empty v-else :tips="'暂无数据'" />
				</div>
				<div class="alumni-news-content-item alumni-news-content-item-right-padding">
					<div class="title-box-by-parent">
						<span class="title">校友之声</span>
						<span class="title-desc" @click="toList('voice')">
							<i>更多</i>
							<i class="el-icon-arrow-right iconDistance"></i>
							<i class="el-icon-arrow-right iconDistance"></i>
						</span>
					</div>
					<!-- 校友之声内容区域 -->
					<div
						v-if="alumnaVocalityList && alumnaVocalityList.length"
						v-loading="alumnaVocalityListLoading"
						class="alumni-news-content-item-content"
					>
						<homeAlumniVoice :alumna-vocality-list="alumnaVocalityList"></homeAlumniVoice>
					</div>
					<Empty v-else :tips="'暂无数据'" />
				</div>
				<div class="alumni-news-content-item">
					<div class="title-box-by-parent">
						<span class="title">通知公告</span>
						<span class="title-desc" @click="toList('notice')">
							<i>更多</i>
							<i class="el-icon-arrow-right iconDistance"></i>
							<i class="el-icon-arrow-right iconDistance"></i>
						</span>
					</div>
					<!-- 通知公告内容区域 -->
					<div
						v-if="alumnaNoticeList && alumnaNoticeList.length"
						v-loading="alumnaNoticeListLoading"
						class="alumni-news-content-item-content"
					>
						<homeNotice :alumna-notice-list="alumnaNoticeList"></homeNotice>
					</div>
					<Empty v-else :tips="'暂无数据'" />
				</div>
			</div>
			<!-- <Empty v-else :tips="'暂无数据'" /> -->
		</div>
		<!-- 校友活动 -->
		<div v-loading="alumniActiveloading" class="content-box alumni-active-box">
			<div class="alumni-active-content">
				<div class="title-box">
					<span class="title">校友活动</span>
					<span class="title-desc" @click="toList('activeList')">
						<i>更多</i>
						<i class="el-icon-arrow-right iconDistance"></i>
						<i class="el-icon-arrow-right iconDistance"></i>
					</span>
				</div>
			</div>
			<!-- 校友活动内容 -->
			<div v-if="alumniActiveList && alumniActiveList.length" class="alumni-active-box">
				<homeAlumniActive :alumni-active-list="alumniActiveList"></homeAlumniActive>
			</div>
			<Empty v-else :tips="'暂无数据'" />
		</div>
		<!-- 校友风采 -->
		<div class="content-box alumni-show-box">
			<div class="alumni-show-content">
				<div class="title-box-by-parent">
					<span class="title">校友风采</span>
					<span class="title-desc" @click="toListShow('show')">
						<i>更多</i>
						<i class="el-icon-arrow-right iconDistance"></i>
						<i class="el-icon-arrow-right iconDistance"></i>
					</span>
				</div>
			</div>
			<!-- 校友风采内容 -->
			<div class="alumni-show-box">
				<homeFallsImgs></homeFallsImgs>
			</div>
			<!-- <Empty v-else :tips="'暂无数据'" /> -->
		</div>
	</div>
</template>
<script>
import homeEducationBoundary from './components/home-education-boundary';
import homeAlumniVoice from './components/home-alumni-voice';
import homeNotice from './components/home-notice';
import homeAlumniActive from './components/home-alumni-active';
import homeFallsImgs from './components/home-falls-imgs';
import { baseUrl } from '@/config';
export default {
	components: {
		homeEducationBoundary,
		homeAlumniVoice,
		homeNotice,
		homeAlumniActive,
		homeFallsImgs
	},
	filters: {
		// 用于处理正常时间格式换成年月与日分开
		dateShow(str, type, space) {
			let strTime = '';
			let dateArray = str.split(' ')[0].split('-');
			if (type == 'day') {
				strTime = dateArray[2];
			} else {
				if (space == '.') {
					strTime = dateArray[0] + '.' + dateArray[1];
				} else {
					strTime = dateArray[0] + '年' + dateArray[1] + '月';
				}
			}
			return strTime;
		}
	},
	data() {
		return {
			bannerLoading: false,
			bannerList: [],
			baseUrl,
			carouselIndex: 0, // 轮播下标
			newsList: [], //最新动态数据
			alumnaEducationViewList: [], //职教视界数据
			alumniActiveList: [], //校友活动数据
			educationList: [], //职教视界数据
			alumnaVocalityList: [], //校友之声数据
			alumnaNoticeList: [], //通知公告数据
			alumniActiveloading: false, //校友活动
			newsListLoading: false, //加载动画--校友动态
			alumnaEducationViewListLoading: false, //加载动画--职教视界
			alumnaVocalityListLoading: false, //加载动画--校友之声
			alumnaNoticeListLoading: false, //加载动画--通知公告
			alumniActive: true //加载动画--职教视界
		};
	},
	mounted() {
		this.getAdvert();
		this.getInterfaceDataList('alumniActive', 'alumniActive', '6');
		this.getInformation('alumnaDynamic', 'newsList', 8);
		this.getInformation('alumnaEducationView', 'alumnaEducationViewList', 5);
		this.getInformation('alumnaVocality', 'alumnaVocalityList', 8);
		this.getInformation('alumnaNotice', 'alumnaNoticeList', 5);
	},
	methods: {
		/**
		 * @description 获取列表
		 * */
		getInformation(code, list, listSize) {
			this[list + `Loading`] = true;
			let data = {
				nodeCode: code,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				pageNum: 1,
				pageSize: listSize || 10
			};
			this.$api.information_api.paging(data).then(res => {
				this[list] = res?.results?.records || [];
				this[list + `Loading`] = false;
			});
		},
		/**获取广告*/
		getAdvert() {
			this.bannerLoading = true;
			this.$api.shop_api
				.getAdvertsByCode({
					siteId: this.getSiteId(),
					sysCode: 'pc_addservice_index'
				})
				.then(res => {
					this.bannerLoading = false;
					this.bannerList = res.result?.adData || [];
				});
		},
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		/**
		 * @description 幻灯片轮播触发事件
		 * */
		carouselChange(index) {
			this.carouselIndex = index;
		},
		// 自定义轮播点点击事件，主动触发轮播的切换
		poitClick(index) {
			this.$refs.carousel.setActiveItem(index);
		},
		// 自定义轮播前后点击事件，主动触发轮播前一个和后一个的切换
		carouselPrevNext(type) {
			if (type == 'prev') {
				this.$refs.carousel.prev();
			} else {
				this.$refs.carousel.next();
			}
		},
		/**
		 * @description 获取列表
		 * */
		getInterfaceDataList(code, list, listSize) {
			this[list + `loading`] = true;
			// let newData = new FormData();
			// newData.append('pageNumber', 1);
			let data = {
				'pager.pageNumber': 1,
				'pager.pageSize': '6',
				'queryParams.status': '0,1,2',
				'queryParams.tenementId': this._userinfo.tenantId || this.$tenantId,
				'queryParams.plateCode': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
				// 'queryParams.activityType': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
			};

			this.$api.alumni_association_api.alumniActiveList(data).then(res => {
				this.alumniActiveList = res.data.list;
				this[list + `loading`] = false;
			});
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		// 跳转校友动态详情页面
		toDetail(id) {
			this.jumpPage(`alumni-news-detail?updateTitle=校友动态详情页&id=${id}&code=alumnaDynamic`);
		},
		// 跳转校友活动列表
		toList(code) {
			// if (code == 'activeList') {
			// 	this.jumpPage(`/alumni-association-list?code=${code}`);
			// } else {
			// 	this.jumpPage(`alumni-news-list?code=${code}`);
			// }
			switch (code) {
				case 'activeList':
					this.jumpPage(`/alumni-association-list?code=${code}`);
					break;
				case 'news':
					this.jumpPage(`/alumni-news-list?code=alumnaDynamic&updateTitle=校友动态`);
					break;
				case 'education':
					this.jumpPage(`/alumni-news-list?code=alumnaEducationView&updateTitle=职教视界`);
					break;
				case 'voice':
					this.jumpPage(`/alumni-news-list?code=alumnaVocality&updateTitle=校友之声`);
					break;
				case 'notice':
					this.jumpPage(`/alumni-news-list?code=alumnaNotice&updateTitle=通知公告`);
					break;
				default:
					break;
			}
		},
		// 跳转校友风采列表
		toListShow(code) {
			this.jumpPage(`/alumni-show-list?code=${code}`);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
@text-desc {
	font-size: 14px;
	font-weight: 400;
	color: #333333;
}
.alumni-association-box {
	background: #ffffff;
}

// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.top-box {
	width: 100%;
	height: 400px;
	.carousel-img {
		width: 100%;
		object-fit: cover;
	}
}
// 内容区域标题统一样式
.title-box {
	width: $max-width;
	margin: 0 auto 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 90px;
	line-height: 90px;
	.title {
		display: block;
		font-size: 24px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #000000;
	}
	.title-desc {
		cursor: pointer;
		display: block;
		width: 70px;
		height: 36px;
		background: #e8eaf0;
		border-radius: 18px;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #747d85;
		line-height: 36px;
		text-align: center;
		.iconDistance {
			width: 8px;
		}
	}
}
.title-box-by-parent {
	width: 100%;
	margin: 0 auto 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 90px;
	line-height: 90px;
	.title {
		display: block;
		font-size: 24px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #000000;
	}
	.title-desc {
		cursor: pointer;
		display: block;
		width: 70px;
		height: 36px;
		background: #ffffff;
		border-radius: 18px;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #747d85;
		line-height: 36px;
		text-align: center;
		.iconDistance {
			width: 8px;
		}
	}
}
.content-box {
	width: $max-width;
	margin: 0 auto;
	border-top: 0.1px solid transparent;
}
// 查看更多按钮
.more-btn {
	width: 140px;
	height: 48px;
	background: var(--brand-6, #0076e8);
	border-radius: 24px;
	font-size: 16px;
	color: #ffffff;
	line-height: 48px;
	text-align: center;
	display: inline-block;
	cursor: pointer;
}
.latest-box {
	position: relative;
	.latest-more {
		position: absolute;
		bottom: 50px;
		right: 0;
	}
}
.latest-content {
	display: flex;
	justify-content: space-between;
	margin-top: 0;
	.content-left {
		width: 660px;
		padding-bottom: 70px;
		.carousel-box {
			position: relative;
		}
		.item-carousel-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		.carousel-bottom {
			width: 100%;
			height: 68px;
			background: rgba(0, 0, 0, 0.3);
			position: absolute;
			bottom: 0;
			left: 0;
			z-index: 2;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.carousel-left {
				width: 113px;
				height: 68px;
				// background: #2386ee;
				background: url('~@/assets/info-images/carousel-left.png') center;
				background-size: cover;
				color: #ffffff;
				display: flex;
				flex-direction: column;
				justify-content: center;
				// align-items: center;
				padding-left: 20px;
				.day {
					display: block;
					font-size: 26px;
				}
				.time {
					font-size: 14px;
				}
			}
			.carousel-poits {
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.poit {
				width: 12px;
				height: 12px;
				background: #ffffff;
				border-radius: 50%;
				margin: 0 4px;
				cursor: pointer;
			}
			.poit-active {
				background: var(--brand-6, #0076e8);
			}
			.carousel-right {
				width: 122px;
				height: 45px;
				display: flex;
				align-items: center;
				background: url('~@/assets/info-images/carousel-right.png') center;
				background-size: cover;
				.prev,
				.next {
					display: inline-block;
					width: 100%;
					height: 100%;
					text-align: center;
					color: #ffffff;
					line-height: 45px;
					cursor: pointer;
				}
				.prev {
					// background: #ff9f34;
				}
				.next {
					// background: #0076e8;
				}
			}
		}
		.text-content {
			margin-top: 27px;
			.text-title {
				cursor: pointer;
				font-size: 18px;
				color: #5b5b5b;
			}
			.text-desc {
				font-size: 14px;
				color: #7a8392;
				margin-top: 27px;
			}
		}
	}
	.content-right {
		width: 560px;
		.item-card {
			cursor: pointer;
			display: flex;
			height: 70px;
			margin-bottom: 50px;
			align-items: center;
		}
		.time-box {
			display: flex;
			width: 100px;
			height: 66px;
			padding-right: 12px;
			border-right: 2px solid #e5e5e5;
			text-align: center;
			flex-direction: column;
			flex-shrink: 0;
			.time-day {
				height: 49px;
				font-size: 60px;
				font-family: FZLanTingHeiS-UL-GB;
				font-weight: 400;
				color: #e40001;
				line-height: 44px;
			}
			.time-mouth {
				font-size: 14px;
				color: #999999;
				display: inline-block;
				margin-top: 9px;
			}
		}
		.text-content {
			margin-left: 22px;
			width: calc(100% - 100px - 22px);
			.text-title {
				font-size: 16px;
				color: #5b5b5b;
			}
			.text-desc {
				font-size: 14px;
				color: #7a8392;
				margin-top: 19px;
			}
		}
	}
}
.alumni-news-box {
	width: 100%;
	background: #f5f5f5;
	background-size: cover;
	.alumni-news-content {
		display: flex;
		width: $max-width;
		margin: 0 auto;
		margin-top: 0;
		height: 720px;
		.alumni-news-content-item {
			flex: 1;
			height: 100%;
			.alumni-news-content-item-content {
				height: 630px;
			}
		}
		.alumni-news-content-item-right-padding {
			padding: 0 30px 0 0;
		}
	}
}

.alumni-active-box {
	width: 100%;
	background-size: cover;
	background: #ffffff;
	.alumni-active-content {
		display: flex;
		width: $max-width;
		margin: 0 auto;
		margin-top: 0;
	}
	.alumni-active-box {
		margin: 0 auto;
		width: $max-width;
	}
}
.alumni-show-box {
	width: 100%;
	background-size: cover;
	background: #f5f5f5;
	.alumni-show-content {
		display: flex;
		width: $max-width;
		margin: 0 auto;
		margin-top: 0;
	}
	.alumni-show-box {
		margin: 0 auto;
		width: $max-width;
		height: 870px;
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: var(--brand-6, #0076e8);
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #ffffff;
	border-radius: 50%;
}
::v-deep .el-carousel__indicators--outside {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 0;
	bottom: 0;
	height: 40px;
	width: 100%;
}
.view-icon {
	margin-right: 9px;
}
</style>
