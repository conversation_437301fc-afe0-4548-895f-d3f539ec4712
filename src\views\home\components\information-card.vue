<!--
 @desc:职教视野（资讯）卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<el-image class="img" :src="cardData.coverImg" lazy fit="cover"></el-image>
		<div class="content-box">
			<div class="center">
				<span class="u-line-1">{{ cardData.title }}</span>
				<p class="u-line-2">{{ cardData.abstract }}</p>
			</div>
			<footer>
				<span>
					<i class="el-icon-time"></i>
					{{ cardData.publishTime }}
				</span>
				<span>
					<i class="el-icon-view"></i>
					{{ cardData.viewNum }}
				</span>
			</footer>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'information', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(space-between);
	width: 616px;
	height: 150px;
	border-radius: 8px;
	font-family: Microsoft YaHei;
	cursor: pointer;
	.img {
		width: 228px;
		height: 100%;
		border-radius: 8px;
		object-fit: cover;
		// border: 1px solid red;
	}
	.content-box {
		@include flexBox(space-between, flex-start);
		flex-direction: column;
		height: 100%;
		width: calc(100% - 248px);
		.center {
			width: 100%;
			& > span {
				font-size: 14px;
				font-weight: bold;
				color: #333333;
				width: 100%;
				display: inline-block;
			}
			& > p {
				width: 100%;
				margin-top: 16px;
				font-size: 14px;
				color: #7a8392;
				overflow: hidden;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				text-overflow: ellipsis;
				-webkit-line-clamp: 2;
			}
		}

		footer {
			@include flexBox(flex-start);
			width: 100%;
			span {
				font-size: 14px;
				color: #7a8392;
				margin-right: 34px;
			}
		}
	}
}
</style>
