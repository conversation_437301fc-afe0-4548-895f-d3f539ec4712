<!--
 * @Description: 公告
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-04-27 10:57:01
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-05-05 12:01:44
-->
<template>
	<div class="message-center" :style="{ width: isMain ? '1200px' : '100%' }">
		<div class="content">
			<div v-for="item in noticeList" :key="item.id" class="content-list">
				<div class="content-title">
					<div class="content-title_name">
						<!-- <img class="content-title_icon" :src="waapp01" alt="" /> -->
						<span class="content-title_text">{{ item.title }}</span>
					</div>
					<div class="content-title_date">{{ item.publishTime }}</div>
				</div>
				<div class="content-text">
					{{ getPlainText(item.detail).slice(0, 140) }}
				</div>
				<div class="content-btn">
					<el-button type="primary" ghost @click="handlerToDetail(item)">查看详情</el-button>
				</div>
			</div>
		</div>
		<Empty v-if="noticeList.length === 0"></Empty>
		<div v-else class="content-page">
			<el-pagination
				:current-page="page.current"
				:page-sizes="[10, 100, 200, 300, 400]"
				:page-size="page.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import { getPlainText } from '@/utils';
export default {
	name: 'EnterpriseInfo',
	components: {},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			total: 0,
			noticeList: [],
			page: {
				current: 1,
				size: 10,
				state: null
			},
			getPlainText
		};
	},
	mounted() {
		this.handleGetMyMessage();
	},
	methods: {
		// 获取晓消息
		handleGetMyMessage() {
			this.$api.personal_api
				.findCmsInfo({
					nodeCode: 'businessCenterNotice',
					tenantId: this.$tenantId,
					pageNum: this.page.current,
					pageSize: this.page.size
				})
				.then(res => {
					this.noticeList = res.results.records;
					// 当前类型下的消息数量
					this.total = res.results.total;
				});
		},
		// 条数变化
		handleSizeChange(val) {
			this.page.size = val;
			this.page.current = 1;
			this.handleGetMyMessage();
		},
		// 页数变化
		handleCurrentChange(val) {
			this.page.current = val;
			this.handleGetMyMessage();
		},
		handlerToDetail({ id, nodeCode }) {
			if (this.isMain) {
				this.$router.push({
					path: '/notice/detail',
					query: { id, nodeCode, tenantId: this.$tenantId }
				});
			} else {
				this.$emit('changePage', 'noticeList', {
					detail: true,
					id,
					nodeCode,
					tenantId: this.$tenantId
				});
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.message-center {
	margin: 0 auto 36px;
	background-color: #fff;
}
.content {
	padding: 20px;
	&-list {
		margin-bottom: 12px;
		background: #ffffff;
		padding: 16px 12px;
		border: 1px solid #eeeeee;
	}
	&-title {
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 24px;
		margin-bottom: 7px;
		display: flex;
		justify-content: space-between;
		&_date {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #bfbfbf;
			line-height: 22px;
		}
		&_icon {
			width: 30px;
			height: 30px;
			margin-right: 4px;
		}
		&_name {
			display: flex;
			align-items: center;
		}
	}
	&-text {
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 22px;
		margin-bottom: 7px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	&-btn {
		display: flex;
		justify-content: flex-end;
	}
	&-page {
		padding: 0 20px 20px;
	}
}
::v-deep .el-pagination {
	text-align: right;
}
</style>
