<template>
	<div>
		<div class="coupon-con-subTabs">
			<div
				v-for="(item, index) of subTabs"
				:key="index"
				class="tabs"
				:class="subTab === index ? 'select' : ''"
				@click="subTab = index"
			>
				{{ item }}
			</div>
		</div>
		<div class="list">
			<div
				v-for="(item, index) of list"
				:key="index"
				:style="{
					backgroundImage: `url(${bg})`,
					marginRight: (index + 1) % 3 === 0 ? '0' : ''
				}"
				class="list-item"
			>
				<div class="list-item-icon">店铺优惠券</div>
				<div class="money">
					{{ item.couponType == 2 ? `${item.quota}折` : `￥${item.quota}` }}
				</div>
				<div class="name">
					<div class="name-top">满{{ item.basic }}{{ item.couponType == 2 ? '享' : '使用' }}</div>
					<div class="name-bottom">有效期{{ item.effectiveStart }}至{{ item.effectiveEnd }}</div>
				</div>
				<div class="line"></div>
				<div class="right">
					<div
						class="button"
						:class="couponIndexArr.includes(item.id) ? 'select-button' : ''"
						@click="coupon(item, index)"
					>
						<AlIcon
							v-show="couponIndexArr.includes(item.id)"
							name="el-icon-check"
							color="#ffffff"
						></AlIcon>
					</div>
				</div>
			</div>
			<Empty v-if="list.length === 0"></Empty>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CouponList',
	props: {
		couponsInfo: {
			type: Object,
			default: () => {
				return {
					availableList: [],
					unavailableList: []
				};
			}
		}
	},
	data() {
		return {
			subTabs: ['可用优惠券', '不可用优惠券'],
			subTab: 0,
			bg: require('@/assets/shop-images/coupons-bg.png'),
			couponIndexArr: [] // 选中的优惠券
		};
	},
	computed: {
		list() {
			return this.subTab === 0 ? this.couponsInfo.availableList : this.couponsInfo.unavailableList;
		}
	},
	methods: {
		/**领取优惠券*/
		coupon(item) {
			if (this.couponIndexArr.includes(item.id)) {
				let index = this.couponIndexArr.indexOf(item.id);
				this.couponIndexArr.splice(index, 1);
			} else {
				/**判断优惠券是否可以共享*/
				if (this.couponIndexArr.length > 0 && item.discountShare) {
					this.$message.error('该优惠券不可以和其他优惠券一起使用！');
				} else {
					this.couponIndexArr.push(item.id);
				}
			}
			this.$emit('changeCoupon', this.couponIndexArr.join(','));
		}
	}
};
</script>

<style scoped lang="scss">
.coupon-con-subTabs {
	margin-top: 18px;
	display: flex;
	align-items: center;
	.tabs {
		width: 133px;
		height: 32px;
		font-size: 16px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		line-height: 32px;
		color: #404040;
		text-align: center;
		background: transparent;
		cursor: pointer;
	}
	.select {
		color: #ffffff;

		background: var(--brand-6, #0076e8);
		border-radius: 21px;
	}
}
.list {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin-top: 16px;
	&-item {
		width: 311px;
		height: 78px;
		background-size: 100% 100%;
		position: relative;
		padding: 21px 0 18px 12px;
		display: flex;
		align-items: center;
		margin: 0 15px 26px 0;
		&-icon {
			font-size: 10px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #3274e0;
			line-height: 14px;
			position: absolute;
			top: 0;
			left: 0;
			width: 66px;
			height: 14px;
			background: #ccdffd;
			border-radius: 8px 0px 8px 0px;
			text-align: center;
		}
		.money {
			flex-shrink: 0;
			min-width: 66px;
			font-size: 24px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #f95f55;
			margin-right: 14px;
			text-align: center;
		}
		.name {
			flex: 1;
			&-top {
				font-size: 15px;
				font-family: PingFang SC-Heavy, PingFang SC;
				font-weight: 800;
				color: #404040;
				line-height: 26px;
				margin-bottom: 5px;
			}
			&-bottom {
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 17px;
				word-wrap: anywhere;
			}
		}
		.line {
			width: 1px;
			height: 65px;
			background: #bdd3f9;
			margin-left: 8px;
		}
		.right {
			flex-shrink: 0;
			width: 60px;
			display: flex;
			align-items: center;
			justify-content: center;
			.button {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #3274e0;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
			}
			.select-button {
				background: #3274e0;
			}
		}
	}
}
::v-deep .empty {
	padding: 30px 0;
}
</style>
