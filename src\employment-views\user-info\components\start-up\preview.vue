<!--
 @desc:个人中心 就业创业 简历预览
 @author: WH
 @date: 2023/9/6
 -->
<template>
	<div class="preview-box">
		<header>
			<img src="@/assets/images/home/<USER>" alt="" />
			<div class="btn-box">
				<div>
					<i class="el-icon-printer"></i>
					打印
				</div>
				<div>
					<i class="el-icon-s-unfold"></i>
					导出
				</div>
			</div>
		</header>
		<div class="title">
			<h1>个人简历</h1>
			<h2>Personal resume</h2>
		</div>
		<div class="info-box">
			<div class="mini-title"><span>教育背景</span></div>
			<div class="info">
				<img src="@/assets/images/home/<USER>" alt="" />
				<ul>
					<li>{{ info.userName }}</li>
					<li>
						<p>
							<i class="el-icon-date"></i>
							{{ info.age }}岁
						</p>
						<p>
							<i class="el-icon-s-user"></i>
							{{ info.sex }}
						</p>
						<p>
							<i class="el-icon-s-suitcase"></i>
							{{ info.wordYears }}年经验
						</p>
						<p>
							<i class="el-icon-collection-tag"></i>
							{{ info.education }}
						</p>
					</li>
					<li>
						<p>
							<i class="el-icon-phone-outline"></i>
							{{ info.phone }}
						</p>
						<p>
							<i class="el-icon-location-information"></i>
							{{ info.side }}
						</p>
					</li>
				</ul>
			</div>
			<div class="mini-title"><span>求职期望</span></div>
			<div class="expect">
				<ul v-for="(item, index) in expectList" :key="index">
					<li>期望职位：{{ item.position }}</li>
					<li>期望薪资：{{ item.salary }}</li>
					<li>期望工作城市：{{ item.workCity }}</li>
				</ul>
			</div>
			<div class="mini-title"><span>工作经历</span></div>
			<div class="experience">
				<el-timeline>
					<el-timeline-item
						v-for="(item, index) in experienceList"
						:key="index"
						:timestamp="item.workTime"
						placement="top"
					>
						<el-card>
							<h4>{{ item.schoolName }}</h4>
							<ul class="row">
								<li>职位名称：{{ item.position }}</li>
								<li>公司行业：{{ item.type }}</li>
								<li>工作地点：{{ item.side }}</li>
							</ul>
						</el-card>
					</el-timeline-item>
				</el-timeline>
			</div>
			<div class="mini-title"><span>教育经历</span></div>
			<div class="experience">
				<el-timeline>
					<el-timeline-item
						v-for="(item, index) in educationList"
						:key="index"
						:timestamp="item.workTime"
						placement="top"
					>
						<el-card>
							<h4>{{ item.schoolName }}</h4>
							<ul class="row">
								<li>学历：{{ item.education }}</li>
								<li>专业：{{ item.major }}</li>
							</ul>
						</el-card>
					</el-timeline-item>
				</el-timeline>
			</div>
			<div class="mini-title"><span>个人优势</span></div>
			<div class="merit">
				<p>{{ merit.text }}</p>
				<div class="tags">
					<span>个人标签：</span>
					<ul>
						<li v-for="(item, index) in merit.tags" :key="index">{{ item }}</li>
					</ul>
				</div>
			</div>
		</div>
		<div class="info-box">
			<div class="mini-title"><span>我的证书</span></div>
			<div class="certificate">
				<img src="@/assets/images/home/<USER>" alt="" />
				<p>XXX荣誉证书</p>
			</div>

			<footer>好就业，上技状元数字化校园</footer>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CurriculumPreview',

	data() {
		return {
			info: {
				userName: '张三', //学生姓名
				schoolName: '', //所属院校
				age: '20', //年龄
				sex: '男', //性别
				email: '', //电子邮箱
				wordYears: '3',
				education: '本科',
				phone: '13000001111', //联系方式
				side: '四川省宜宾市翠屏区新村74号' //现在位置
			},
			expectList: [
				{
					position: '产品经理',
					salary: '14k~16k',
					workCity: '宜宾'
				},
				{
					position: '产品经理',
					salary: '14k~16k',
					workCity: '宜宾'
				}
			],
			experienceList: [
				{
					workTime: '2019.09 ~ 2021.07',
					schoolName: '宜宾职业技术学院',
					position: '商务总监',
					type: '互联网',
					side: '四川省宜宾市翠屏区新村74号'
				},
				{
					workTime: '2019.09 ~ 2021.07',
					schoolName: '宜宾职业技术学院',
					position: '商务总监',
					type: '互联网',
					side: '四川省宜宾市翠屏区新村74号'
				}
			],
			educationList: [
				{
					workTime: '2019.09 ~ 2021.07',
					schoolName: '宜宾职业技术学院',
					education: '大专',
					major: '汽车与轨道交通'
				},
				{
					workTime: '2019.09 ~ 2021.07',
					schoolName: '宜宾职业技术学院',
					education: '大专',
					major: '汽车与轨道交通'
				}
			],
			merit: {
				text: '个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息。',
				tags: ['认真负责', '积极乐观', '坚韧不拔']
			}
		};
	},

	methods: {}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.preview-box {
	header {
		@include flexBox(space-between);
		img {
			width: 138px;
			height: 50px;
		}
		.btn-box {
			@include flexBox();
			div {
				margin-left: 20px;
				color: #0076e8;
				cursor: pointer;
			}
		}
	}
	.title {
		width: 100%;
		height: 430px;
		padding: 100px 80px;
		font-weight: 400;
		color: #ffffff;
		background: linear-gradient(45deg, #3497fc, #1b8bfc);
		h1 {
			font-size: 96px;
		}
		h2 {
			font-size: 36px;
			margin-top: 30px;
		}
	}
	.info-box {
		border: 1px solid #dcdcdc;
		padding: 40px;
		.info {
			@include flexBox(flex-start);
			height: 260px;
			padding: 40px;
			img {
				width: 180px;
				height: 180px;
				border-radius: 50%;
				border: 1px solid red;
			}
			ul {
				li {
					@include flexBox(flex-start);
					font-size: 14px;
					&:nth-child(1) {
						padding-left: 20px;
						margin-bottom: 40px;
						font-size: 36px;
						font-weight: bold;
					}
					&:nth-child(2) {
						margin-bottom: 24px;
					}
					p {
						padding: 0 20px;
						border-right: 1px solid #eeeeee;
					}
				}
			}
		}
		.expect {
			padding: 40px;
			ul {
				@include flexBox(flex-start);
				&::before {
					display: inline-block;
					content: '';
					width: 10px;
					height: 10px;
					margin-right: 10px;
					border-radius: 50%;
					background: #0076e8;
				}
				&:nth-child(1) {
					margin-bottom: 40px;
				}
				li {
					margin-right: 126px;
				}
			}
		}
		.experience {
			padding: 40px;
			color: #61687c;
			h4 {
				font-size: 18px;
				font-weight: bold;
				margin-bottom: 22px;
			}

			.row {
				@include flexBox(flex-start);
				font-size: 14px;
				font-weight: 400;
				li {
					margin-right: 120px;
				}
			}
		}
		.merit {
			padding: 40px;
			color: #61687c;
			font-size: 14px;
			line-height: 22px;
			.tags {
				span {
					display: inline-block;
					margin: 40px 0 20px 0;
				}
				ul {
					@include flexBox(flex-start);
					li {
						padding: 6px 14px;
						border-radius: 2px;
						margin-right: 10px;
						background: #f1f3f8;
					}
				}
			}
		}
		.certificate {
			img {
				width: 150px;
				height: 200px;
				margin-bottom: 20px;
				border: 1px solid red;
			}
		}
		footer {
			height: 60px;
			padding: 0 40px;
			margin-top: 80px;
			line-height: 60px;
			text-align: right;
			color: #fff;
			background: #0076e8;
		}
	}
}
.mini-title {
	margin-bottom: 20px;
	font-size: 14px;
	font-weight: bold;
	border-bottom: 1px solid #e8eaf0;
	color: #ffffff;

	span {
		position: relative;
		width: 120px;
		padding: 8px 0 8px 20px;
		display: block;
		// text-align: center;
		background: #0076e8;
		&::before,
		&::after {
			position: absolute;
			right: 0px;
			top: 0;
			display: inline-block;
			content: '';
			width: 16px;
			height: 100%;
			background: #479cee;
			transform: skew(30deg, 0);
		}
		&::after {
			right: -16px;
			background: #b2d6f8;
		}
	}
}
::v-deep .el-timeline-item__node {
	background: radial-gradient(#fff, 60%, #0076e8);
}
::v-deep .el-timeline-item__tail {
	border-left: 2px dashed #6c7285;
}
</style>
