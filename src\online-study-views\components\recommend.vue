<template>
	<div>
		<Title :title="'为您推荐'" />
		<div class="list">
			<a v-for="item in 15" :key="item" href="">
				<div class="item">
					<img src="" alt="" />
					<div class="title">
						<span class="title-text">玉米-农华101</span>
						<span class="tags">川货</span>
					</div>
					<div class="num">
						<span class="text">库存</span>
						<span class="text1">200.00吨</span>
					</div>
					<div class="site">
						<span class="text">存货地</span>
						<span class="text1">云南</span>
					</div>
					<div class="price">
						<span>¥1298.00</span>
						<span class="text">/吨</span>
					</div>
				</div>
			</a>
		</div>
	</div>
</template>

<script>
import Title from './Title.vue';

export default {
	name: 'Recommend',
	components: {
		Title
	}
};
</script>

<style lang="scss" scoped>
.list {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	.item {
		margin-top: 15px;
		width: 232px;
		height: 344px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		padding: 8px 6px 0;
		img {
			width: 220px;
			height: 220px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.text {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #9aa3ba;
			margin-right: 5px;
		}
		.text1 {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
		}
		.title {
			margin-top: 6px;
			.title-text {
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
				margin-right: 5px;
			}
			.tags {
				width: 40px;
				height: 24px;
				background: #ffeeee;
				border-radius: 2px 2px 2px 2px;
				opacity: 1;
				text-align: center;
				font-size: 12px;
				font-family: Source Han Sans SC-Normal, Source Han Sans SC;
				font-weight: 400;
				color: #ca3f3b;
			}
		}
		.num {
			margin-top: 6px;
		}
		.site {
			margin-top: 6px;
		}
		.price {
			margin-top: 6px;
			font-size: 11px;
			font-family: Rany-Medium, Rany;
			font-weight: 500;
			color: #ca3f3b;
			line-height: 20px;
		}
	}
}
</style>
