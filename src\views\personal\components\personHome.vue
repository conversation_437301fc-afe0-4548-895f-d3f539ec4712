<template>
	<div class="person">
		<div class="person-left">
			<!--			<div class="person-left-top">-->
			<!--				<img class="img" src="@/assets/shop-images/user_center_frame.png" alt="" />-->
			<!--				<div class="title">个人中心</div>-->
			<!--			</div>-->
			<div :key="key" class="person-left-con">
				<el-menu
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
				>
					<!--					<el-submenu index="1">-->
					<!--						<template slot="title">-->
					<!--							<i class="el-icon-location"></i>-->
					<!--							<span>导航一</span>-->
					<!--						</template>-->
					<!--						<el-menu-item-group>-->
					<!--							<el-menu-item index="1-1">选项1</el-menu-item>-->
					<!--							<el-menu-item index="1-2">选项2</el-menu-item>-->
					<!--						</el-menu-item-group>-->
					<!--					</el-submenu>-->
					<el-menu-item v-for="menu of menus" :key="menu.index" :index="menu.index">
						<span slot="title">{{ menu.name }}</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb>
			<personHeader v-if="activeMenu !== '1-1'" :title="activeName"></personHeader>
			<component
				:is="activeComponent"
				:is-main="false"
				:params="params"
				@changePage="changePage"
			></component>
		</div>
		<roleTip ref="roleTip" :tip-text="tipText" :button="buttonText" @onClick="clickTip"></roleTip>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import baseInfo from '@/views/personal/components/baseInfo';
import Home from '@/views/personal/components/home';
import noticeList from '@/views/notice/index';
import noticeDetail from '@/views/notice/detail';
import personHeader from '@/components/person-header';
import password from '@/views/personal/components/password';
import roleTip from '@/components/role-tip';
import enterprise from '@/views/enterprise-auth';
import application from '@/views/personal/components/application';
import { mapGetters } from 'vuex';
export default {
	name: 'PersonHome',
	components: {
		subBreadcrumb,
		baseInfo,
		Home,
		noticeList,
		noticeDetail,
		personHeader,
		password,
		roleTip,
		enterprise,
		application
	},
	props: {
		subMenu: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			activeMenu: '1-1',
			beforeActiveMenu: '1-1', // 保存点击菜单前的选项，如果没有权限就重置为之前
			key: 0, // 菜单组件的key，保持更新
			activeComponent: 'home',
			activeName: '',
			tipText: '', // 没有权限的提示
			buttonText: '', // 提示弹窗里面的按钮

			eventType: '', // 事件类型
			params: {} // 进入组件页面带的参数
		};
	},
	computed: {
		...mapGetters(['roles']),
		menus() {
			let arr = [
				{
					name: '首页',
					index: '1-1',
					path: 'home'
				},
				{
					name: '基本信息',
					index: '2-1',
					path: 'baseInfo'
				},
				{
					name: '安全设置',
					index: '3-1',
					path: 'password'
				},
				{
					name: '系统公告/消息',
					index: '4-1',
					path: 'noticeList',
					detailPath: 'noticeDetail'
				},
				{
					name: '认证中心',
					index: '5-1',
					// role: [1],
					path: 'enterprise'
				},
				// {
				// 	name: '应用中心',
				// 	index: '6-1',
				// 	path: 'application'
				// }
				// {
				// 	name: '职教服务大厅',
				// 	index: '7-1',
				// 	link: '/independentPersonal/vocational',
				// 	role: [2]
				// },
				{
					name: '企业管理中心',
					index: '8-1',
					link: '/independentPersonal/enterprise'
					// role: [3, 4],
					// tip: '您还未进行企业认证，相关功能服务暂无权限！',
					// button: '立即前往企业认证',
					// eventType: '5-1'
				}
			];
			// arr = arr.filter(item => {
			//   console.log(item);
			// 	item.role = item.role || [];
			// 	let isRole = true;
			// 	for (let role of this.roles) {
			// 		if (item.role.includes(role)) {
			// 			isRole = false;
			// 		}
			// 	}
			// 	return isRole;
			// });
			return arr;
		}
	},
	watch: {
		subMenu(newVal) {
			if (newVal) {
				this.toSubMenu(newVal);
			}
		}
	},
	created() {
		if (this.subMenu) {
			this.toSubMenu(this.subMenu);
		}
	},
	methods: {
		/**提示弹窗中的按钮触发事件*/
		clickTip() {
			this.toSubMenu(this.eventType);
		},
		/**根据路由跳到对应的组件*/
		toSubMenu(subMenu) {
			let obj = this.menus.find(item => {
				return item.index === subMenu;
			});
			this.activeMenu = obj.index;
			this.activeComponent = obj.path;
			this.activeName = obj.name;
		},
		/**跳转到对应的组件*/
		changePage(path, params) {
			let obj = this.menus.find(item => {
				return item.path === path;
			});
			this.activeMenu = obj.index;
			this.activeName = obj.name;
			// 如果打开详情就用详情
			if (params && params.detail) {
				this.activeComponent = obj.detailPath;
				this.params = params;
			} else {
				this.params = {};
				this.activeComponent = obj.path;
			}
		},
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.menus.find(item => {
				return item.index === index;
			});

			console.log(currentObj);
			// 判断用户对当前菜单是否有权限
			let isRole = false;
			if (currentObj.role) {
				for (let role of this.roles) {
					if (currentObj.role.includes(role)) {
						isRole = true;
					}
				}
			} else {
				isRole = true;
			}
			// 如果有权限
			if (isRole) {
				// 如果是内置组件直接替换组件
				if (currentObj.path) {
					this.beforeActiveMenu = this.activeMenu = currentObj.index;
					this.activeName = currentObj.name;
					this.activeComponent = currentObj.path;
				}
				// 如果是打开页面
				else {
					this.activeMenu = this.beforeActiveMenu; // 打开的是新页面，所以当前选择回到之前
					this.key += 1;
					this.$router.push(currentObj.link);
				}
			} else {
				this.buttonText = currentObj.button || ''; // 提示框中的按钮
				this.eventType = currentObj.eventType || ''; // 按钮事件
				this.activeMenu = this.beforeActiveMenu; // 有提示窗的话，菜单选中重置到之前
				this.key += 1;
				this.tipText = currentObj.tip || '您还没有相关权限！';
				this.$refs.roleTip.visible = true;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
		&-top {
			background: linear-gradient(136deg, #dee9ff 0%, #ffffff 100%);
			width: 100%;
			height: 66px;
			font-size: 20px;
			font-family: Source Han Sans SC-Bold, Source Han Sans SC;
			font-weight: bold;
			color: #2b99fe;
			line-height: 28px;
			display: flex;
			align-items: center;
			justify-content: center;
			.img {
				width: 32px;
				height: 32px;
			}
		}
	}
	&-right {
		width: calc(100% - 236px);
	}
}
::v-deep .el-menu-item {
	padding-left: 40px !important;
}
::v-deep .is-active {
	position: relative;
	&::before {
		content: '';
		display: inline-block;
		position: absolute;
		left: 20px;
		top: calc(50% - 7px);
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background: #3274e0;
	}
}
</style>
