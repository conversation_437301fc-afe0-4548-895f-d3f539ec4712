<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 卡片
 @author: WH
 @date: 2023/9/12
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- <img class="is-img" src="@/assets/employment-images/tool-enterprise.png" alt="" /> -->
		<div
			:class="[
				'tag',
				cardData.isAudit == 0 && 'tag-wait',
				cardData.isAudit == 1 && 'tag-success',
				(cardData.isAudit == 2 || cardData.isAudit == 3) && 'tag-fail'
			]"
		>
			{{ auditArr[cardData.isAudit || 0] }}
		</div>
		<!-- 审核中的遮罩，让用户不能点击任何操作 -->
		<div v-if="cardData.isAudit == 0" class="masker"></div>
		<img class="is-img" :src="getImgUrl(cardData.communityLogo)" alt="" />
		<header>
			<div class="hr-info">
				<span>{{ cardData.communityName }}</span>
				<p>{{ cardData.capacityNum }}人</p>
			</div>

			<div v-if="showBtn === 'inform'" class="card-btn">
				<p v-if="cardData.isAudit == 1" @click.stop="btnHandle('addInform')">
					<i class="el-icon-bell"></i>
					发布通知
				</p>
				<p @click.stop="btnHandle('informControl')">
					<i class="el-icon-setting"></i>
					通知管理
				</p>
				<p @click.stop="btnHandle('update')">
					<i class="el-icon-edit-outline"></i>
					编辑资料
				</p>
			</div>
			<div v-if="showBtn === 'invite'" class="card-btn">
				<p @click.stop="btnHandle('update')">
					<i class="el-icon-edit-outline"></i>
					编辑资料
				</p>
				<p @click.stop="btnHandle('Invite')">
					<i class="el-icon-user"></i>
					邀请成员
				</p>
			</div>
		</header>
		<article>
			<ul class="tags-box">
				<li v-for="(item, index) in cardData.labelNames" :key="index">{{ item }}</li>
			</ul>
			<div class="content-box">
				<el-popover
					v-model="visible"
					placement="top-start"
					width="600"
					trigger="hover"
					:content="cardData.introduce"
				>
					<!-- <el-button slot="reference" @click="visible = !visible">手动激活</el-button> -->
					<span slot="reference" class="text" @click="visible = !visible">
						{{ cardData.introduce }}
					</span>
				</el-popover>
			</div>
			<div class="chat-heads">
				<div v-for="(item, index) in cardData.memberList" :key="index" class="head-img">
					<!-- <img src="@/assets/shop-images/default-avatar.png" alt="" /> -->
					<!-- <img class="member-img" :src="getOwnId(item.id)" alt="" /> -->
					<headAvator :own-id="item.userId" class="member-img" />
				</div>
				<div class="more">
					<i class="el-icon-more"></i>
				</div>
			</div>
		</article>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';

export default {
	props: {
		showBtn: {
			type: [String, null],
			default: null
		},
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			visible: false,
			auditArr: ['待审核', '审核通过', '审核未通过', '禁用']
		};
	},

	methods: {
		btnHandle(btnName) {
			this.$emit('btnHandle', { btnName, cardData: this.cardData });
		},
		clickCard() {
			if (this.cardData.isAudit == 0) {
				return this.$message.warning('社区审核中');
			}
			this.$emit('clickCard', { ...this.cardData });
		},
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	position: relative;
	width: 100%;
	height: 200px;
	padding: 20px 0 20px 260px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	overflow: hidden;
	.tag {
		position: absolute;
		top: 20px;
		left: 0;
		padding: 6px 14px;
		z-index: 5;
		color: #fff;
		border-radius: 10px 0 10px 0;
	}
	.tag-success {
		background: #0076e8;
	}
	.tag-wait {
		background: rgba(0, 0, 0, 0.3);
	}
	.tag-fail {
		background: #e80022bd;
	}
	.masker {
		position: absolute;
		top: 20px;
		left: 0;
		width: 100%;
		height: calc(100% - 40px);
		z-index: 4;
		border-radius: 10px;
		// background: rgba(0, 0, 0, 0.1);
	}
	// border: 1px solid red;
	.is-img {
		@include flexBox();
		position: absolute;
		top: 50%;
		left: 0;
		margin-top: -80px;
		width: 240px;
		height: 160px;
		border-radius: 10px;
		object-fit: cover;
		// border: 1px solid red;
	}
	header {
		@include flexBox(space-between);
		// border: 1px solid red;
		margin-bottom: 10px;
		.hr-info {
			@include flexBox(flex-start);
			p {
				font-size: 16px;
				color: #0076e8;
			}
			span {
				display: inline-block;
				margin-right: 10px;
				font-size: 14px;
				color: #8390a3;
			}
		}
		.card-btn {
			@include flexBox(flex-start);
			margin-right: 10px;
			font-size: 14px;
			color: #0076e8;
			p {
				margin-left: 16px;
			}
		}
	}
	article {
		.tags-box {
			@include flexBox(flex-start);
			li {
				margin-right: 8px;
				font-size: 12px;
				padding: 8px 10px;
				border-radius: 2px;
				background: #fff8eb;
				color: #b38f48;
			}
		}
		.content-box {
			// width: 460px;
			font-size: 14px;
			margin: 14px 0;
			// overflow: hidden;
			// text-overflow: ellipsis;
			// white-space: nowrap;
			color: #333333;
			.text {
				width: 460px;
				display: block;

				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color: #666666;
			}
		}
		.chat-heads {
			@include flexBox(flex-start);
			.head-img {
				@include flexBox();
				width: 37px;
				height: 37px;
				border-radius: 50%;
				margin-left: -6px;
				border: 1px solid #e8eaec;
				background: #fff;
				&:nth-child(1) {
					margin-left: 0;
				}
				.member-img {
					width: 30px;
					height: 30px;
					border-radius: 50%;
				}
				// &:nth-child(3) {
				// 	margin-left: -6px;
				// }
				// &:nth-child(4) {
				// 	margin-left: -6px;
				// }
			}
			.more {
				@include flexBox();
				width: 37px;
				height: 37px;
				margin-left: -6px;
				border-radius: 50%;
				border: 1px solid #e8eaec;

				i {
					text-align: center;
					line-height: 30px;
					display: inline-block;
					width: 30px;
					height: 30px;
					border-radius: 50%;
					background: #e5e5e5;
					color: #999999;
				}
			}
		}
	}
}
</style>
