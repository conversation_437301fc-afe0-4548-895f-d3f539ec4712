<template>
	<div class="app-wrapper">
		<!--  头部  -->
		<div :class="{ 'fixed-header': fixedHeader }">
			<SelfHeader></SelfHeader>
		</div>
		<!--  面包屑  -->
		<subBreadcrumb v-show="!$route.meta.noShowBreadCrumb"></subBreadcrumb>
		<!--  路由页面  -->
		<app-main />
		<!--  底部  -->
		<SelfFooter />
	</div>
</template>

<script>
import { SelfHeader, AppMain, SelfFooter } from './components';
import ResizeMixin from './mixin/ResizeHandler';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';

export default {
	name: 'Layout',
	components: {
		AppMain,
		SelfHeader,
		SelfFooter,
		subBreadcrumb
	},
	mixins: [ResizeMixin],
	computed: {
		fixedHeader() {
			return this.$store.state.settings.fixedHeader;
		}
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.app-wrapper {
	// background: #f9f9f9;
	position: relative;
	// padding-top: 150px;
}
</style>
