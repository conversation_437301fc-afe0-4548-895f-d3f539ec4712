<!--
 @desc:个人中心 就业创业 我的简历
 @author: WH
 @date: 2023/9/5
 -->
<template>
	<div v-loading="loading" class="user-info-box">
		<div class="mini-title">
			<span>基本信息</span>
		</div>
		<el-form ref="form" :model="form" :rules="rules" label-width="140px">
			<div class="business-info">
				<el-form-item label="姓名" prop="name">
					<el-input
						v-model="form.name"
						placeholder="请输入姓名"
						type="input"
						suffix-icon="el-icon-edit"
					></el-input>
				</el-form-item>
				<el-form-item label="头像" prop="head">
					<uploadItem
						class="avatar-uploader"
						:file-number="1"
						:code="headCode"
						:own-id="uploadId"
						@fileChange="fileChange"
					/>
				</el-form-item>
				<!-- <el-form-item label="所属院校" prop="university">
					<el-input
						v-model="form.university"
						placeholder="请输入院校名称"
						type="input"
						suffix-icon="el-icon-edit"
					></el-input>
				</el-form-item> -->
				<el-form-item label="出生年月" prop="birth">
					<el-date-picker
						v-model="form.birth"
						type="date"
						format="yyyy-M-d"
						align="right"
						placeholder="请选择出生年月"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="性别" prop="sex">
					<el-radio-group v-model="form.sex">
						<el-radio label="1">男</el-radio>
						<el-radio label="0">女</el-radio>
					</el-radio-group>
				</el-form-item>

				<!-- <el-form-item label="电子邮箱" prop="email">
					<el-input v-model="form.email" placeholder="请输入电子邮箱" type="input"></el-input>
				</el-form-item>
				<el-form-item label="联系方式" prop="phone">
					<el-input v-model="form.phone" placeholder="请输入联系方式" type="input"></el-input>
				</el-form-item> -->
				<el-form-item label="现居地址" prop="address">
					<el-input v-model="form.address" placeholder="请输入现居地址" type="input"></el-input>
				</el-form-item>
			</div>
		</el-form>
		<div class="mini-title"><span>教育背景</span></div>
		<Education ref="education" :table-data="form.educations" />
		<div class="mini-title"><span>实习经历</span></div>
		<Internship ref="internship" :table-data="form.internships" />
		<div class="mini-title"><span>导入简历</span></div>
		<div class="import-box">
			<uploadItem
				accept=".pdf, .doc, .docx"
				class="avatar-uploader"
				:file-number="10"
				:code="resumeCode"
				:own-id="uploadId"
				@fileChange="resumeFileChange"
			/>
		</div>
		<div class="info-save">
			<el-button class="info-save-confirm" type="primary" size="large" @click="handlerSubmit(0)">
				保存
			</el-button>
			<el-button class="info-save-staging" size="large" @click="preview">预览</el-button>
			<el-button class="info-save-staging" size="large" @click="handlerSubmit(9)">取消</el-button>
		</div>
		<el-dialog title="简历预览" :visible.sync="previewVisible" :fullscreen="true">
			<div class="preview">
				<Preview />
				<!-- <div slot="footer" class="dialog-footer">
					<el-button @click="previewVisible = false">取 消</el-button>
					<el-button type="primary" @click="previewVisible = false">确 定</el-button>
				</div> -->
			</div>
		</el-dialog>
	</div>
</template>

<script>
import uploadItem from '@/alumni-association-views/user-info/components/uploadItem.vue';
import Education from './curriculum/education.vue';
import Internship from './curriculum/internship.vue';
import Preview from './curriculum/preview.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
	components: { uploadItem, Education, Internship, Preview },

	data() {
		return {
			headCode: 'job_student_resume_face',
			resumeCode: 'job_student_resume_adjunct',
			uploadId: uuidv4(),
			loading: false,
			form: {
				sex: ''
				// name: '张三', //学生姓名
				// university: '宜宾职院', //所属院校
				// birth: '', //年龄
				// sex: '1', //性别
				// email: '<EMAIL>', //电子邮箱
				// phone: '13011110000', //联系方式
				// address: '四川省宜宾市翠屏区新村74号' //现在位置
			},
			// form: {
			// 	name: '', //学生姓名
			// 	university: '', //所属院校
			// 	birth: '', //年龄
			// 	sex: '', //性别
			// 	email: '', //电子邮箱
			// 	phone: '', //联系方式
			// 	address: '' //现在位置
			// },
			rules: {
				name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
				// university: [{ required: true, message: '请输入', trigger: 'blur' }],
				birth: [{ required: true, message: '请选择出身年月', trigger: 'change' }],
				sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
				head: [{ required: true, message: '请上传头像', trigger: 'change' }],
				// email: [
				// 	{
				// 		required: true,
				// 		trigger: 'blur',
				// 		validator: (rule, value, callback) => {
				// 			if (!value) {
				// 				return callback(new Error('电子邮箱不能为空'));
				// 			}
				// 			if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
				// 				callback();
				// 			} else {
				// 				callback(new Error('请输入正确的电子邮箱'));
				// 			}
				// 		}
				// 	}
				// ],
				// phone: [
				// 	{
				// 		required: true,
				// 		trigger: 'blur',
				// 		validator: (rule, value, callback) => {
				// 			if (!value) {
				// 				return callback(new Error('联系方式不能为空'));
				// 			}
				// 			if (/^1[3456789]\d{9}$/.test(value)) {
				// 				callback();
				// 			} else {
				// 				callback(new Error('请输入正确手机格式'));
				// 			}
				// 		}
				// 	}
				// ],
				address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
			},
			previewVisible: false
		};
	},

	mounted() {
		// this.handlerGetRegion();
		// this.handlerQueryDict();
		this.getCurriculumInfo();
	},
	beforeDestroy() {
		window.localStorage.removeItem('curriculumData');
	},
	methods: {
		//跳转预览
		preview() {
			this.previewVisible = true;
		},
		// 提交数据
		handlerSubmit(status) {
			let education = this.$refs.education;
			let internship = this.$refs.internship;
			this.$refs.form.validate(valid => {
				if (valid) {
					this.form.educations = [...education.list];
					this.form.internships = [...internship.list];
					this.form.id = this.uploadId;
					this.save();
				}
			});
		},

		async queryDict() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.queryDict();
				if (rCode == 0) {
					this.uploadId = results.id;
					results.sex = results.sex + '';
					results.educations.forEach(item => {
						item.time = `${item.startTime}-${item.endTime}`;
					});
					results.internships.forEach(item => {
						item.time = `${item.startTime}-${item.endTime}`;
					});
					this.form = { ...results };
					this.$message.success(msg);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		async getCurriculumInfo() {
			try {
				this.loading = true;
				let { rCode, results } = await this.$api.employment_api.getCurriculumInfo();
				//有id代表已经提交过 修改
				if (rCode == 0 && results?.id) {
					this.uploadId = results.id;
					results.sex = results.sex + '';
					results.educations.forEach(item => {
						item.time = `${item.startTime}-${item.endTime}`;
					});
					results.internships.forEach(item => {
						item.time = `${item.startTime}-${item.endTime}`;
					});
					this.form = { ...results };
					// this.$message.success(msg);
					window.localStorage.setItem('curriculumData', JSON.stringify({ ...results }));
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.success(error);
			} finally {
				this.loading = false;
			}
		},
		async save() {
			try {
				this.loading = true;
				let { rCode, msg } = await this.$api.employment_api.saveCurriculum(this.form);
				if (rCode == 0) {
					// this.communityTags = results;
					this.$message.success(msg);
					this.getCurriculumInfo();
					// this.getUserAuth();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		fileChange(file) {
			if (file) {
				this.$nextTick(() => {
					this.$set(this.form, 'head', file[0]?.adjunctId);
					this.$refs.form.validateField('head');
				});
			}
		},
		resumeFileChange(file) {
			if (file) {
				this.$nextTick(() => {
					let files = file.map(item => item?.adjunctId);
					this.$set(this.form, 'attachment', files?.join(',') || '');
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

@import '@/styles/alumni-association.scss';
.user-info-box {
	padding: 20px;
	background: #fff;

	.el-input,
	.el-select {
		width: 300px;
	}
}
.info-save {
	width: 100%;
	padding-top: 40px;
	border-top: 1px solid #d9d9d9;
	text-align: center;
	&-staging {
		margin-right: 30px;
		padding-left: 56px;
		padding-right: 56px;
	}
	&-confirm {
		padding-left: 56px;
		padding-right: 56px;
	}
}
.preview {
	width: 1260px;
	height: 100%;
	overflow: auto;
	margin: 0 auto;
}
</style>
