<!--
 @desc:收藏  公司卡片
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="card" @click="clickCard">
		<div class="conpany-img">
			<img src="@/assets/employment-images/tool-enterprise.png" alt="" />
		</div>
		<header>
			<div class="hr-info">
				<span>{{ cardData.corp_name }}</span>
				<p>
					<i>[</i>
					{{ cardData.corp_industry }}·{{ cardData.people_num_code }}
					<i>]</i>
				</p>
			</div>
			<p class="hr-state">
				<i class="el-icon-star-on"></i>
				{{ stateText }}
			</p>
		</header>
		<article>
			<div class="content-box">
				经营范围：
				<span>{{ cardData.business_scope }}</span>
			</div>
			<ul class="tags-box">
				<li v-for="(item, index) in cardData.tags" :key="index">{{ item }}</li>
			</ul>
		</article>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			const TEXT = ['取消收藏'];
			return TEXT[this.cardData.state - 0];
		}
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	position: relative;
	width: 100%;
	height: 120px;
	margin-bottom: 20px;
	padding-left: 88px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	overflow: hidden;
	// border: 1px solid red;
	.conpany-img {
		@include flexBox();
		position: absolute;
		top: 0;
		left: 0;
		width: 72px;
		height: 72px;
		border: 1px solid #f0f3f7;
		border-radius: 5px;
		img {
			width: 46px;
			height: 46px;
			border-radius: 6px;
		}
	}
	header {
		@include flexBox(space-between);
		// border: 1px solid red;
		margin-top: 8px;
		.hr-info {
			@include flexBox(flex-start);
			p {
				font-size: 14px;
				color: #666666;
			}

			span {
				display: inline-block;
				font-size: 16px;
				color: #333333;
				margin-right: 10px;
			}
		}
		.hr-state {
			font-size: 14px;
			font-weight: 400;
			color: #0076e8;
		}
	}
	article {
		.content-box {
			width: 460px;
			font-size: 14px;
			margin: 14px 0;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			color: #333333;
			span {
				color: #666666;
			}
		}
	}
}
.tags-box {
	@include flexBox(flex-start);
	li {
		margin-right: 10px;
		font-size: 14px;
		padding: 4px 10px;
		background: #f1f3f8;
		color: #999;
		border-radius: 2px;
	}
}
</style>
