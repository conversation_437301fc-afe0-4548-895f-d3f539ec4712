/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-09-18 08:45:47
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-09-18 14:54:37
 * @FilePath: /ybzy-zhxy-pc/src/api/index.js
 * @Description:
 */
import * as user_api from '@/api/user.js';
import * as shop_api from './model/shop.js';
import * as treasure_api from './model/skill-treasure.js';
import * as study_api from './model/online-study.js';
import * as information_api from './model/information.js';
import * as alumni_association_api from './model/alumni-association.js';
import * as vocational_education_hall_api from './model/vocational-education-hall.js';
import * as personal_api from './model/personal.js';
import * as alumni_api from './model/alumni-association.js';
import * as employment_api from './model/employment.js';
import * as enterprise_center from './model/enterprise-center.js'; // 企业中心 企业管理

export default {
	...user_api,
	shop_api,
	treasure_api,
	study_api,
	information_api,
	personal_api,
	alumni_api,
	alumni_association_api,
	vocational_education_hall_api,
	employment_api,
	enterprise_center
};
