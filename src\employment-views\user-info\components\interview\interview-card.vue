<!--
 @desc:面试邀请卡片
 @author: WH
 @date: 2023/9/6
 -->
<template>
	<div class="card" @click="clickCard">
		<header>
			<div class="hr-info">
				<img
					:src="
						cardData.hrHeadImg
							? getImgUrl(cardData.hrHeadImg)
							: require('@/assets/shop-images/default-avatar.png')
					"
					alt=""
				/>
				<span>{{ cardData.createUserName }}</span>
				<span>HR</span>
			</div>
			<p :class="{ 'hr-state': true, close: cardData.state == 2 }">
				{{ cardData.interviewStatusDes }}
			</p>
		</header>
		<article>
			<div class="title-box">
				<div class="title">
					<p>
						{{ cardData.postName }}
					</p>
					<span>{{ cardData.postSalary }}</span>
				</div>
				<p>面试时间：{{ cardData.interviewDate + ' ' + cardData.interviewTime }}</p>
			</div>
			<div class="conpany-box">
				<div class="company-info">
					<img src="@/assets/employment-images/tool-enterprise.png" alt="" />
					<p>{{ cardData.enterpriseNameAndDes }}</p>
					<!-- <p>
						<i>[</i>
						{{ cardData.tags }}
						<i>]</i>
					</p> -->
				</div>
				<p>面试地址：{{ cardData.interviewAddress }}</p>
			</div>
		</article>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	mixins: [PreviewAdjunctMixin],
	props: {
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return { alumniUrl };
	},
	computed: {
		hrName() {
			let sex = this.cardData.hr.sex == 1 ? '先生' : '女士';
			return this.cardData.hr.username.substring(0, 1) + sex;
		},
		stateText() {
			const TEXT = ['待面试', '已面试', '职位已关闭'];
			return TEXT[this.cardData.state - 0];
		}
	},
	methods: {
		/**获取logo*/
		// getImgUrl(url) {
		// 	if (url.includes('http')) {
		// 		return url;
		// 	}
		// 	return `${alumniUrl}${url}`;
		// },
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 100%;
	height: 150px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	border-bottom: 1px solid #e8eaf0;
	header {
		@include flexBox(space-between);
		margin-bottom: 18px;
		// border: 1px solid red;
		.hr-info {
			@include flexBox(flex-start);

			img {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				margin-right: 6px;
			}
			span {
				display: inline-block;
				font-size: 16px;
				color: #8390a3;
				padding: 0 10px;
				&:nth-child(2) {
					border-right: 1px solid #8390a3;
				}
			}
		}
		.hr-state {
			font-size: 16px;
			color: #333333;
		}
		.close {
			color: #999999;
		}
	}
	article {
		.title-box {
			@include flexBox(space-between);
			& > p {
				width: 270px;
				margin-right: 46px;
				font-size: 14px;
				color: #8390a3;
			}
			.title {
				@include flexBox(flex-start);
				margin-bottom: 20px;
				font-size: 16px;
				color: #0076e8;
				span {
					margin-left: 10px;
					font-size: 18px;
					color: #fe574a;
				}
			}
		}
		.conpany-box {
			@include flexBox(space-between);
			font-size: 14px;
			color: #8390a3;
			& > p {
				width: 270px;
				margin-right: 46px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.company-info {
				@include flexBox(flex-start);

				img {
					width: 24px;
					height: 24px;
				}
				p {
					&:nth-of-type(1) {
						margin: 0 16px;
					}
				}
			}
		}
	}
}
</style>
