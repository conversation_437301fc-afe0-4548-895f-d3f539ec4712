<template>
	<div class="need_list">
		<div class="tab-box">
			<div class="tab" :class="tabIndex == -1 ? 'act' : ''" @click="handleTab(-1)">全部需求</div>
			<div class="tab" :class="tabIndex == 3 ? 'act' : ''" @click="handleTab(3)">已发布</div>
			<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">待审核</div>
			<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">未通过</div>
			<div class="tab" :class="tabIndex == 6 ? 'act' : ''" @click="handleTab(6)">已取消</div>
			<div class="tab" :class="tabIndex == 5 ? 'act' : ''" @click="handleTab(5)">已成交</div>
			<div class="line" :style="{ left: lineLeft(tabIndex) }"></div>
		</div>
		<div class="head">
			<div class="lf">筛选查询</div>
			<div class="rf">
				<el-button icon="el-icon-search" @click="handleSearch()">查询</el-button>
			</div>
		</div>
		<el-form :inline="true" :model="search" class="form">
			<el-form-item label="商品分类：">
				<el-cascader
					v-model="search.goodsType"
					:options="goodsOptions"
					placeholder="请选择"
					clearable
					style="width: 185px"
					:props="{ label: 'name', value: 'id', checkStrictly: true }"
				></el-cascader>
			</el-form-item>
			<el-form-item label="期望货源地：">
				<el-cascader
					v-model="search.expectedAddr"
					:options="addresOptions"
					placeholder="请选择"
					clearable
					style="width: 185px"
					:props="{ label: 'name', value: 'value' }"
				></el-cascader>
			</el-form-item>
			<el-form-item label="发布时间：">
				<el-date-picker
					v-model="search.time"
					type="daterange"
					clearable
					range-separator="至"
					value-format="yyyy-MM-dd"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<div class="btn-box">
			<el-button type="primary" icon="el-icon-plus" @click="goAdd">发布需求</el-button>
		</div>
		<div class="table">
			<el-table v-loading="loading" :data="tableData" style="width: 100%">
				<template slot="empty">
					<el-empty description="暂无数据"></el-empty>
				</template>
				<el-table-column
					prop="goodsTypeDesc"
					label="品种"
					align="center"
					width="180px"
				></el-table-column>
				<el-table-column prop="quantity" label="采购量" align="center"></el-table-column>
				<el-table-column prop="receiveAddrDesc" label="收货地" align="center"></el-table-column>
				<el-table-column prop="quotedNum" label="报价情况" align="center"></el-table-column>
				<el-table-column prop="validityDay" label="剩余时间" align="center"></el-table-column>
				<el-table-column prop="publishTime" label="发布时间" align="center"></el-table-column>
				<el-table-column prop="stateDesc" label="状态" align="center">
					<template slot-scope="scope">
						<el-tooltip v-if="scope.row.state == 1" class="item" effect="dark" placement="top">
							<div slot="content" class="tooltip">
								{{ scope.row.auditOpinion }}
							</div>
							<span>
								{{ scope.row.stateDesc }}
							</span>
						</el-tooltip>
						<span v-else>
							{{ scope.row.stateDesc }}
						</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="150">
					<template slot-scope="scope">
						<el-button @click="goDetails(scope.row.id)">查看详情</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import { personal } from '@/api';
import { Loading } from 'element-eoss';
import { setCookie1, delCookie } from '@/utils/auth';
export default {
	name: 'NeedList',
	// components: {
	// 	Empty
	// },
	data() {
		return {
			loading: false,
			search: {},
			page: 1,
			size: 10,
			total: 0,
			tableData: [],
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			goodsOptions: JSON.parse(sessionStorage.getItem('bulkClassifyList')),
			tabIndex: -1
		};
	},
	created() {
		this.pagingOfBuyer();
	},
	methods: {
		// 跳转至新增
		async goAdd() {
			this.isShopLogin();
			let options = {
				target: 'app'
			};
			let loadingInstance = Loading.service(options);
			const res = await personal.scswlLogin({ authType: 'onlyCheck' });
			loadingInstance.close();
			if (res.isSeller) {
				setCookie1('isSeller', 'true');
			} else {
				delCookie('isSeller');
			}
			if (res.isEnterpriseAuth) {
				setCookie1('isEnterpriseAuth', 'true');
			} else {
				delCookie('isEnterpriseAuth');
			}
			if (res.isEnterpriseAuth) {
				this.$router.push({
					path: `/add_need`
				});
			} else {
				this.$confirm('必须完成企业认证，才能发布需求, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {})
					.catch(() => {});
			}
		},
		// tab切换
		handleTab(i) {
			if (i !== this.tabIndex) {
				this.option = 0;
				this.tabIndex = i;
				this.pagingOfBuyer();
			}
		},
		// 左侧高度
		lineLeft(i) {
			let num = 0;
			if (i == -1) {
				num = 0;
			} else if (i == 3) {
				num = 1;
			} else if (i == 0) {
				num = 2;
			} else if (i == 1) {
				num = 3;
			} else if (i == 6) {
				num = 4;
			} else {
				num = 5;
			}
			return 100 * num + 'px';
		},
		// 查询
		handleSearch() {
			this.page = 1;
			this.pagingOfBuyer();
		},
		// 获取列表
		pagingOfBuyer() {
			this.loading = true;
			const { goodsType, expectedAddr } = this.search;
			const goodsType1 = goodsType && goodsType.length > 0 ? goodsType[goodsType.length - 1] : '';
			const expectedAddr1 = expectedAddr && expectedAddr.length > 0 ? expectedAddr[1] : '';
			const publishSTime = this.search.time ? this.search.time[0] : '';
			const publishETime = this.search.time ? this.search.time[1] : '';
			const data = {
				pageNum: this.page,
				pageSize: this.size,
				goodsType: goodsType1,
				expectedAddr: expectedAddr1,
				publishSTime,
				publishETime,
				state: this.tabIndex == -1 ? '' : this.tabIndex
			};
			personal.pagingOfBuyer(data).then(res => {
				if (res.code == 200) {
					this.tableData = res.results.records;
					this.total = res.results.total;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 跳转至详情
		goDetails(i) {
			this.$router.push({
				path: `/personal/need_details?id=${i}`
			});
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.pagingOfBuyer();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.pagingOfBuyer();
		}
	}
};
</script>

<style lang="scss" scoped>
.need_list {
	width: 100%;
	.tab-box {
		margin-bottom: 10px;
		height: 40px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		position: relative;
		.tab {
			width: 60px;
			font-size: 14px;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: #404040;
			margin-right: 40px;
			cursor: pointer;
			text-align: center;
		}
		.act {
			color: var(--brand-6, '#ca3f3b');
		}
		.line {
			width: 60px;
			height: 1px;
			background: var(--brand-6, '#ca3f3b');
			position: absolute;
			left: 0;
			bottom: -1px;
			transition: all 0.3s;
		}
	}
	.head {
		height: 48px;
		background: #f4f4f4;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		::v-deep .el-button--primary {
			background: var(--brand-6, '#ca3f3b');
			border-color: var(--brand-6, '#ca3f3b');
		}
		.lf {
			padding-left: 18px;
			font-size: 14px;
			font-weight: 500;
			color: #404040;
		}
		.rf {
			margin-left: auto;
			padding-right: 12px;
		}
	}
	.form {
		display: flex;
		padding-top: 19px;
		justify-content: center;
		box-sizing: border-box;
		height: 79px;
		border: 1px solid #d9d9d9;
		border-top: none;
		::v-deep .el-date-editor {
			width: 255px;
		}
	}
	.btn-box {
		margin: 28px 0;
	}
	.table {
		min-height: 800px;
		::v-deep .el-table th {
			background: #f6f6f6;
		}
	}
	.page {
		margin-top: 41px;
		::v-deep .el-pagination {
			display: flex;
			.btn-prev {
				margin-left: auto;
			}
		}
	}
}
.tooltip {
	width: 300px;
}
</style>
