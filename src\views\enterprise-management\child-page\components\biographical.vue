<template>
	<el-dialog title="个人简历" width="1000px" :visible.sync="showModel" @close="closeDialog">
		<div class="content">
			<p class="title">基本信息</p>
			<div class="person-info">
				<el-image
					class="img"
					:src="`${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${detail.head}`"
					lazy
					alt=""
				/>
				<p>
					<span class="name">{{ detail.name }}</span>
					<span v-if="detail.sexStr" class="info-item">{{ detail.sexStr }}</span>
					<span v-if="detail.birth" class="info-item">{{ detail.birth || '-' }}</span>
					<span v-if="detail.college" class="info-item">{{ detail.college || '-' }}</span>
					<span v-if="detail.phone" class="info-item">{{ detail.phone || '-' }}</span>
					<span v-if="detail.email" class="info-item">{{ detail.email || '-' }}</span>
					<span v-if="detail.address" class="info-item">{{ detail.address || '-' }}</span>
				</p>
			</div>
			<!-- <p class="title">求职期望</p> -->
			<!-- <el-table
				:data="expectationsList"
				style="width: 100%"
				align="center"
				header-align="center"
				stripe
			>
				<el-table-column prop="name" label="期望职位"></el-table-column>
				<el-table-column prop="price" label="期望薪资"></el-table-column>
				<el-table-column prop="address" label="期望工作城市"></el-table-column>
			</el-table> -->
			<p class="title">工作经历</p>
			<el-table
				:data="experienceList"
				style="width: 100%"
				align="center"
				header-align="center"
				stripe
			>
				<el-table-column prop="companyName" label="公司名称"></el-table-column>
				<el-table-column prop="startTime" label="开始时间"></el-table-column>
				<el-table-column prop="endTime" label="结束时间"></el-table-column>
				<el-table-column prop="post" label="职位名称"></el-table-column>
				<!-- <el-table-column prop="companyIndustry" label="公司行业"></el-table-column>
				<el-table-column prop="address" label="工作地点">
					<template #default="{ row }">
						{{ row.address || '-' }}
					</template>
</el-table-column> -->
			</el-table>
			<p class="title">教育经历</p>
			<el-table
				:data="educationList"
				style="width: 100%"
				align="center"
				header-align="center"
				stripe
			>
				<el-table-column prop="university" label="学校名称"></el-table-column>
				<el-table-column prop="startTime" label="开始时间"></el-table-column>
				<el-table-column prop="endTime" label="结束时间"></el-table-column>
				<el-table-column prop="education" label="学历"></el-table-column>
				<el-table-column prop="major" label="专业"></el-table-column>
			</el-table>
			<template v-if="detail.introduction">
				<p class="title">个人简介：</p>
				<p>{{ detail.introduction || '-' }}</p>
			</template>
			<template v-if="detail.tags">
				<p class="title">个人标签：</p>
				<p>
					<span v-for="tag in detail.tags" :key="tag" class="label">{{ tag }}</span>
				</p>
			</template>
		</div>
		<div class="info-save">
			<el-button size="large" @click="closeDialog">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script>
import { baseUrl } from '@/config';

export default {
	data() {
		return {
			baseUrl,
			showModel: false,
			detail: {
				name: '王小丫',
				age: '24岁',
				sex: '男',
				education: '大专',
				experience: '5年经验',
				tel: '1563728192',
				address: '宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾',
				content:
					'个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优势信息个人优',
				labels: ['认真负责', '积极乐观', '适应力强'],
				img: 'http://dayuding.eimm.wisesoft.net.cn:8099/group1/M00/01/60/rBAJQmM2kleARSewAAL2daeaN-811.jpeg'
			},
			expectationsList: [
				{
					name: '项目经理',
					price: '10k',
					address: '宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾'
				}
			],
			experienceList: [
				{
					name: '宜宾职业技术学院',
					time: '2019.09 ~ 2021.07',
					industry: '互联网',
					position: '项目经理',
					address: '宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾宜宾'
				}
			],
			educationList: [
				{
					name: '宜宾职业技术学院',
					time: '2019.09 ~ 2021.07',
					education: '大专',
					speciality: '汽车与轨道交通'
				},
				{
					name: '宜宾职业技术学院',
					time: '2019.09 ~ 2021.07',
					education: '大专',
					speciality: '汽车与轨道交通'
				}
			]
		};
	},
	methods: {
		async show(row) {
			const res = await this.$api.enterprise_center.getResumeInfo({
				id: row.resumeId,
				postResumeId: row.id
			});
			if (res.success && res.results) {
				const { results } = res;
				this.detail = {
					...results,
					tags: results.tag?.split(',')
				};
				this.educationList = results.educations;
				this.experienceList = results.internships;
				this.showModel = true;
			} else {
				this.$message.warning(!res.results ? '暂无数据' : res.msg || '获取失败');
			}
		},
		closeDialog() {
			this.showModel = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.content {
	margin-top: -20px;
	margin-bottom: 10px;
}

.title {
	font-size: 18px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 20px;
	padding-left: 13px;
	position: relative;
	margin-top: 20px;
	margin-bottom: 15px;

	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 0;
	}
}

.person-info {
	display: flex;
	margin-bottom: 10px;

	.img {
		width: 80px;
		height: 80px;
		border-radius: 50%;
		flex-shrink: 0;
		margin-right: 10px;
	}

	.name {
		font-size: 18px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		display: block;
	}

	.info-item {
		display: inline-block;
		font-size: 14px;
		font-weight: 400;
		margin: 10px 20px 0;
		position: relative;

		&::after {
			content: '';
			width: 1px;
			height: 10px;
			background: #d8d8d8;
			display: inline-block;
			right: -20px;
			position: absolute;
			top: 5px;
		}
	}
}

.label {
	display: inline-block;
	padding: 6px 14px;
	font-size: 14px;
	margin-right: 10px;
	color: #999999;
	background: #f1f3f8;
	border-radius: 2px;
}

.info-save {
	text-align: center;
}
</style>
