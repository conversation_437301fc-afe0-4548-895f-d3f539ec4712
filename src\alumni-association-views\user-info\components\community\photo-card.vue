<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 相册管理
 @author: WH
 @date: 2023/9/14
 -->

<template>
	<div v-loading="loading" class="card">
		<header>
			<img class="is-img" :src="getImgUrl(form.coverImg)" alt="" />
			<div class="head">
				<span>
					{{ form.albumName }}
				</span>

				<p v-if="userInfo.id === createUserId" @click.stop="visible = true">
					<i class="el-icon-picture"></i>
					上传图片
				</p>
			</div>
			<div class="head-main">
				<p>相册用途：{{ form.introduce }}</p>
				<p>{{ form.createUserName }} 于{{ form.createTime }}创建</p>
			</div>
		</header>
		<article v-loading="imgLoading">
			<div
				v-for="(item, index) in imgList"
				:key="index"
				:class="{ 'is-img': true, 'mg-r0': (index + 1) % 4 == 0 }"
			>
				<el-image
					:ref="'preview' + index"
					class="is-img"
					:src="getImgUrl(item.photoUrl)"
					:preview-src-list="[getImgUrl(item.photoUrl)]"
				></el-image>
				<div class="shade"></div>
				<div :class="userInfo.id === createUserId ? 'btn-box' : 'btn-box2'">
					<div>
						<i class="el-icon-view" @click="handleShow(index)"></i>
						<p>查看</p>
					</div>
					<div v-if="userInfo.id === createUserId">
						<i class="el-icon-delete" @click.stop="delPhoto(item.id)"></i>
						<p>删除</p>
					</div>
				</div>
			</div>
		</article>
		<el-dialog title="上传照片" :visible.sync="visible">
			<uploadItem
				class="avatar-uploader"
				:file-number="1000"
				:code="dataCode"
				:own-id="uploadId"
				@fileChange="fileChange"
			/>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submit">确 定</el-button>
				<el-button @click="visible = false">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
import uploadItem from '../uploadItem.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
	components: { uploadItem },
	props: {
		photoId: {
			type: [String, null],
			required: true
		}
	},
	data() {
		return {
			loading: false,
			imgLoading: false,
			dataCode: 'alumna_community_album',
			uploadId: uuidv4(),
			visible: false,
			form: {},
			photoUrl: '',
			imgList: [],
			showViewer: false,
			userInfo: {},
			createUserId: ''
		};
	},
	mounted() {
		this.getPhotoInfo();
		this.getPhoto();
	},
	methods: {
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					let adjunctId = file.map(item => item?.adjunctId);
					this.photoUrl = adjunctId.join(',');
				});
			}
		},
		//查询详情社区相册——————————————————————————————
		async getPhotoInfo() {
			this.loading = true;
			//获取登录用户信息
			this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
			try {
				let { rCode, results } = await this.$api.alumni_api.getPhotoInfo(this.photoId);
				if (rCode == 0) {
					this.form = results;
					console.log('results>>>>>>>>', results);
					this.createUserId = results.createUserId;
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//分页查询社区相册—————————————————
		async getPhoto() {
			this.imgLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getPhoto({
					pageNum: 1,
					pageSize: 20,
					albumId: this.photoId
				});
				if (rCode == 0) {
					this.imgList = results?.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.imgLoading = false;
			}
		},
		//新增社区相册——————————————————————————————
		async submit() {
			this.loading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.addPhoto({
					photoUrl: this.photoUrl,
					albumId: this.photoId
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.visible = false;
					this.getPhoto();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//删除社区相册图片——————————————————————————————
		async delPhoto(id) {
			this.imgLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.delPhoto(id);
				if (rCode == 0) {
					this.$message.success(msg);
					this.getPhoto();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.imgLoading = false;
			}
		},
		//图片预览
		handleShow(index) {
			console.log(22222222222, this.$refs['preview' + index][0]);
			this.$refs['preview' + index][0].clickHandler();
		},
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		btnHandle(btnName) {
			this.$emit('btnHandle', btnName);
		},
		InviteFn() {
			this.$emit('InviteFn');
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 100%;
	height: 100%;
}
header {
	position: relative;
	width: 100%;
	height: 126px;
	padding: 0 0 4px 168px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	overflow: hidden;
	.is-img {
		@include flexBox();
		position: absolute;
		top: 50%;
		left: 0;
		margin-top: -48px;
		width: 148px;
		height: 96px;
		background: #d5e9fc;
		border-radius: 4px;
	}
	.head {
		@include flexBox(space-between);
		// border: 1px solid red;
		span {
			font-size: 16px;
			font-weight: bold;
			color: #7a8392;
		}
		p {
			font-size: 14px;
			margin-right: 10px;
			color: #0076e8;
		}
	}
	.head-main {
		p {
			font-size: 14px;
			color: #8390a3;
			&:nth-of-type(1) {
				margin: 20px 0 12px 0;
			}
		}
	}
}
article {
	@include flexBox(flex-start, flex-start);
	flex-wrap: wrap;
	width: 100%;
	min-height: 180px;
	overflow-x: hidden;
	.is-img {
		width: 226px;
		height: 150px;
		margin: 0 20px 20px 0;
		border-radius: 4px;
		background: #8390a3;
		img {
			width: 100%;
			height: 100%;
		}
		position: relative;
		&:hover {
			.shade {
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.8);
				border: 1px solid #e8eaec;
			}
			.btn-box {
				opacity: 1;
			}
			.btn-box2 {
				opacity: 1;
			}
		}
		.is-img {
			width: 226px;
			height: 150px;
			border-top-left-radius: 4px;
			border-top-right-radius: 4px;
		}

		.shade {
			position: absolute;
			z-index: 1;
			top: 1px;
			left: 1px;
			border-radius: 4px;
		}
		// 只有查看时
		.btn-box {
			// @include flexBox();
			text-align: center;
			position: absolute;
			z-index: 3;
			top: 50%;
			left: 50%;
			margin: -20px 0 0 -45px;
			width: auto;
			height: 60px;
			font-size: 14px;
			opacity: 0;
			color: #ffffff;
			cursor: pointer;
			i {
				margin-bottom: 10px;
			}
			display: flex;
		}
		// 查看和删除操作都存在时
		.btn-box2 {
			// @include flexBox();
			text-align: center;
			position: absolute;
			z-index: 3;
			top: 50%;
			left: 50%;
			margin: -20px 0 0 -15px;
			width: auto;
			height: 60px;
			font-size: 14px;
			opacity: 0;
			color: #ffffff;
			cursor: pointer;
			i {
				margin-bottom: 10px;
			}
			display: flex;
		}
		.btn-box div {
			margin: 0 10px 0 10px;
		}
		.imgReview {
			width: 60%;
			height: 80%;
			margin: 0 auto;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.mg-r0 {
		margin-right: 0;
	}
}
</style>
