<template>
	<div>
		<div class="steps">
			<Steps :status="2"></Steps>
		</div>
		<div class="irf-content">
			<div class="irf-img"><i class="iconfont icon-close-circle-filled"></i></div>
			<div class="irf-status">审核不通过</div>
			<div class="irf-info">
				审核意见：
				<div>
					{{ epData.auditComment }}
				</div>
			</div>
			<div class="irf-im">
				若有疑问，可
				<base-wx-kefu :kfid="kfid"><a href="javascript:void(0)">咨询客服</a></base-wx-kefu>
			</div>
		</div>
		<div class="info-save">
			<el-button class="info-save-staging" size="large" @click="handleToAuth">
				返回修改企业信息
			</el-button>
		</div>
	</div>
</template>

<script>
import Steps from '@/components/auth-center/steps';
import { kfid } from '@/config';
export default {
	name: 'ReviewFailed',
	components: { Steps },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
	},
	data() {
		return {
			kfid
		};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	methods: {
		handleToAuth() {
			// 切换到表单
			this.$emit('compChange', 'Form', true);
		}
	}
};
</script>
<style lang="scss" scoped>
.info-save {
	width: 100%;
	padding-top: 40px;
	border-top: 1px solid #d9d9d9;
	text-align: center;
	&-staging {
		margin-right: 30px;
		padding-left: 56px;
		padding-right: 56px;
	}
	&-confirm {
		padding-left: 56px;
		padding-right: 56px;
	}
}
.irf {
	&-content {
		padding-top: 37px;
		padding-bottom: 27px;
		text-align: center;
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
		margin: 0 auto;
	}
	&-img i {
		font-size: 63px;
		color: #ff4936;
	}
	&-status {
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 44px;
		margin: 10px 0 65px 0;
	}
	&-info {
		margin-bottom: 25px;
		text-align: left;
		padding: 11px 29px;
		background: linear-gradient(91deg, #fff7ea 0%, rgba(252, 233, 233, 0) 100%);
		font-size: 18px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #f2a665;
		line-height: 32px;
		p {
			margin: 0;
			color: #f2a665;
			line-height: 32px;
		}
	}
	&-im a {
		color: var(--brand-6, #0076e8);
	}
}
</style>
