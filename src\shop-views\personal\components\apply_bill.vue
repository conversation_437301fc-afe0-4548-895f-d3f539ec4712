<template>
	<div class="apply_bill">
		<el-dialog
			v-loading="loading"
			class="dia"
			:visible.sync="visible"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			append-to-body
			:popper-append-to-body="false"
			lock-scroll
			title="申请开票"
			width="800px"
			@open="open"
			@close="close"
		>
			<div v-if="visible" class="box">
				<el-form ref="form" :model="form" :rules="rules" label-width="80px">
					<div class="data">
						<div class="cell">
							<div class="lf">订单编号：</div>
							<div class="rf">{{ item.ORDER_ID || '-' }}</div>
						</div>
						<div class="cell">
							<div class="lf">订单金额：</div>
							<div class="rf red">￥ {{ item.TOTAL_MONEY || '0.00' }}</div>
						</div>
					</div>
					<el-form-item label="抬头类型">
						<el-radio-group v-model="form.head" @change="radioChange">
							<el-radio label="3">个人</el-radio>
							<el-radio label="2">企业</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item label="发票类型" prop="type">
						<el-select v-model="form.type" placeholder="请选择发票类型" @change="selectChange">
							<el-option v-show="form.head == 2" label="单位增值税发票" :value="1"></el-option>
							<el-option v-show="form.head == 2" label="单位普通发票" :value="2"></el-option>
							<el-option v-show="form.head == 3" label="个人发票" :value="3"></el-option>
						</el-select>
					</el-form-item>
					<!-- <el-form-item label="抬头类型">
					<el-select v-model="form.type" placeholder="请选择发票抬头类型">
						<el-option label="个人" value="个人"></el-option>
						<el-option label="单位" value="单位"></el-option>
					</el-select>
				</el-form-item> -->
					<el-form-item label="发票抬头" prop="billHeader">
						<el-input
							v-model="form.billHeader"
							placeholder="请填写需要开具发票的姓名或公司"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item label="邮箱地址" prop="receiveEmail">
						<el-input
							v-model="form.receiveEmail"
							placeholder="填写接收电子发票的邮箱地址"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item v-if="form.head == 2" label="税号" prop="dutyParagraph">
						<el-input
							v-model="form.dutyParagraph"
							placeholder="请填写纳税人识别号"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item v-if="form.head == 2" label="开户银行" prop="bank">
						<el-input
							v-model="form.bank"
							placeholder="请填写开户银行"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item v-if="form.head == 2" label="银行账户" prop="bankAccount">
						<el-input
							v-model="form.bankAccount"
							placeholder="请填写银行账号"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item v-if="form.head == 2" label="企业地址" prop="regAddress">
						<el-input
							v-model="form.regAddress"
							placeholder="请填写企业地址"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item v-if="form.head == 2" label="企业电话" prop="regPhoneNumber">
						<el-input
							v-model="form.regPhoneNumber"
							placeholder="请填写企业电话"
							@input="$forceUpdate()"
						></el-input>
					</el-form-item>
					<el-form-item label="发票内容">
						<div class="btn">商品明细</div>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button type="primary" @click="saveBillApply">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	name: 'ApplyBill',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		},
		id: {
			type: String,
			default: ''
		}
	},
	data() {
		let phone = (rule, value, callback) => {
			// var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('企业电话不能为空'));
			} else if (!/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
				return callback(new Error('请输入正确的企业电话'));
			}
			callback();
		};
		return {
			form: {},
			rules: {
				type: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
				billHeader: [
					{ required: true, message: '请填写需要开具发票的姓名或公司', trigger: 'blur' }
				],
				bank: [{ required: true, message: '请填写开户银行', trigger: 'blur' }],
				regAddress: [{ required: true, message: '请填写企业地址', trigger: 'blur' }],
				bankAccount: [{ required: true, message: '请填写银行帐户', trigger: 'blur' }],
				dutyParagraph: [{ required: true, message: '请填写纳税人识别号', trigger: 'blur' }],
				regPhoneNumber: [{ required: true, validator: phone, trigger: 'blur' }],
				receiveEmail: [{ required: true, message: '填写接收电子发票的邮箱地址', trigger: 'blur' }]
			},
			visible: false,
			loading: false
		};
	},
	methods: {
		getBillApplyById() {
			this.form = {
				head: '3',
				type: '',
				billHeader: '',
				receiveEmail: '',
				dutyParagraph: '',
				bank: '',
				bankAccount: '',
				regAddress: '',
				regPhoneNumber: '',
				id: ''
			};
			this.loading = true;
			this.$api.shop_api.getBillApplyById({ id: this.item.billApplyId }).then(res => {
				if (res.state) {
					this.form.type = res.result.type;
					this.form.head = res.result.type == '3' ? '3' : '2';
					this.form.billHeader = res.result.bill_header || '';
					this.form.receiveEmail = res.result.receive_email || '';
					this.form.dutyParagraph = res.result.duty_paragraph || '';
					this.form.bank = res.result.bank || '';
					this.form.regAddress = res.result.reg_address || '';
					this.form.regPhoneNumber = res.result.reg_phone_number || '';
					this.form.id = res.result.id || '';
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		radioChange(i) {
			this.form.type = '';
			this.$forceUpdate();
		},
		selectChange(i) {
			this.form.type = i;
			this.$forceUpdate();
		},
		open() {
			if (this.item.billApplyId) {
				this.getBillApplyById();
			} else {
				this.form = {
					head: '3',
					type: '',
					billHeader: '',
					receiveEmail: '',
					dutyParagraph: '',
					bank: '',
					bankAccount: '',
					regAddress: '',
					regPhoneNumber: '',
					id: ''
				};
			}
		},
		close() {},
		saveBillApply() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					const {
						type,
						billHeader,
						dutyParagraph,
						regAddress,
						regPhoneNumber,
						bank,
						bankAccount,
						receiveEmail
					} = this.form;
					let data = {
						type,
						billHeader,
						dutyParagraph,
						regAddress,
						regPhoneNumber,
						bank,
						bankAccount,
						receiveEmail,
						orderId: this.item.ORDER_ID,
						userId: this.isShopLogin(),
						status: '0',
						id: this.form.id
					};
					this.$api.shop_api.saveBillApply(data).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: '申请成功',
								type: 'success'
							});
							this.loading = false;
							this.visible = false;
							this.$emit('success');
						} else {
							this.loading = false;
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				} else {
					return false;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.dia {
	.btn {
		width: 90px;
		height: 40px;
		background: #ffffff;
		border-radius: 3px;
		opacity: 1;
		border: 1px solid var(--brand-6, '#ca3f3b');
		font-size: 14px;
		font-weight: 400;
		color: var(--brand-6, '#ca3f3b');
		line-height: 40px;
		text-align: center;
	}
	.data {
		height: 52px;
		background: #f4f5f8;
		border-radius: 4px 4px 4px 4px;
		display: flex;
		align-items: center;
		padding-left: 9px;
		margin-bottom: 17px;
		.cell {
			display: flex;
			align-items: center;
			margin-right: 67px;
			.lf {
				font-size: 14px;
				font-weight: 400;
				color: #9da5b7;
				line-height: 22px;
			}
			.rf {
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				line-height: 22px;
			}
			.red {
				color: var(--brand-6, '#ca3f3b');
			}
		}
	}
	.tips {
		padding: 10px 20px;
		text-align: center;
	}
	.box {
		padding: 10px 20px;
		border-top: 1px solid #eeeeee;
		border-bottom: 1px solid #eeeeee;
	}
	::v-deep.el-dialog__body {
		padding: 0 0;
		.el-form-item {
			margin-bottom: 19px;
		}
		.el-form-item__label {
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			line-height: 40px;
		}
		.el-input {
			width: 380px;
		}
		.el-radio__input.is-checked .el-radio__inner {
			background: var(--brand-4, '#ca3f3b');
		}
	}
}
</style>
