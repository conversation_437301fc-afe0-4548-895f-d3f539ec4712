import { delCookie, getCookie, removeToken } from '@/utils/auth';
import store from '../store';
import config from '@/config';
import Cookies from 'js-cookie';
import request from '@/utils/request';
import { Message } from 'element-eoss';
/**获取站点*/
export function getSiteId() {
	let siteId = '';
	siteId = getCookie('rentId');
	return siteId || config.siteId;
}
/**获取大预定的用户信息*/
export function getDydUserinfo() {
	let userinfo = localStorage.getItem('dydUserInfo');
	userinfo = userinfo ? JSON.parse(userinfo) : {};
	return userinfo;
}
/** 拼接接口返回的图片地址 isId为true时根据id找图片*/
// 是否有电商登录权限

/**
 * 判断是否具有电商权限
 * @returns {boolean} 是否有权限
 */
export function hasEcomPermission() {
	const href = window.location.href;
	const allowedDomains = ['localhost', 'ybzy.cn']; // 允许的域名列表
	return allowedDomains.some(domain => href.includes(domain)) || false;
}
// 动态地址
function toolOrigin() {
	const isDebServe =
		window.location.href.includes('localhost') || window.location.href.includes('192.168');
	const origin = window.location.origin + '/';
	// 判断是否为本地运行localhost，本地加代理
	return isDebServe ? origin + 'dev-api/' : origin;
}
export function judgeImg(url, isId) {
	if (!url) {
		return require('@/assets/images/defaultImg.png');
	}
	if (url.indexOf('http') !== -1) {
		return url;
	}
	const host = toolOrigin();
	if (isId !== true) {
		return host + '/' + url;
	}
	const imageIdArray = url.split('adjunctId=');

	return `${host}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${
		imageIdArray[imageIdArray.length - 1]
	}`;
}
/**拼接接口根据环境动态返回的附件地址*/
export function judgeFile(url) {
	if (!url) {
		return '';
	}
	if (url.indexOf('http') !== -1) {
		return url;
	}
	const origin = window.location.origin + '/';
	// 判断是否为本地运行localhost，本地加代理
	const host =
		origin.includes('localhost') || origin.includes('10.40') ? origin + 'dev-api/' : origin;
	return host + url;
}
/**裁剪图片尺寸方法 仅限(trip-api、trip-seller电商板块）*/
export function changeImageSize(src, width = 100, height = 100) {
	console.log(src, 'src');
	if (src.includes('https://dayuding.wisesoft.net.cn/')) {
		let idx = src.lastIndexOf('.');
		let prefix = src.substring(0, idx);
		let ext = src.substring(idx + 1);
		let fulpath = '';
		if (ext !== 'webp') {
			fulpath = `${prefix}_${width}x${height}.${ext}`;
			return fulpath;
		} else {
			return src;
		}
	}
	return src;
}
/**川货搬过来的存储工具*/
export function addStorageEvent(type, key, data) {
	// localStorage
	if (type === 1) {
		// 创建一个StorageEvent事件
		let newStorageEvent = document.createEvent('StorageEvent');
		const storage = {
			setItem: function (k, val) {
				localStorage.setItem(k, val);
				// 初始化创建的事件
				newStorageEvent.initStorageEvent('setItem', false, false, k, null, val, null, null);
				// 派发对象
				window.dispatchEvent(newStorageEvent);
			}
		};
		return storage.setItem(key, data);
	} else {
		// sessionStorage
		// 创建一个StorageEvent事件
		let newStorageEvent = document.createEvent('StorageEvent');
		const storage = {
			setItem: function (k, val) {
				sessionStorage.setItem(k, val);
				// 初始化创建的事件
				newStorageEvent.initStorageEvent('setItem', false, false, k, null, val, null, null);
				// 派发对象
				window.dispatchEvent(newStorageEvent);
			}
		};
		return storage.setItem(key, data);
	}
}

// 根据地址栏动态拼接当前域名资源url
export function activeUrl(url) {
	const basePath = config.imgUrl;
	// const basePath = '/dev-api/';
	try {
		// 尝试创建一个新的 URL 对象来解析传入的 URL
		const parsedUrl = new URL(url);

		// 获取并返回路径、查询字符串和片段标识符（如果有）
		let path = parsedUrl.pathname;
		if (parsedUrl.search) {
			path += parsedUrl.search;
		}
		if (parsedUrl.hash) {
			path += parsedUrl.hash;
		}

		return basePath + path;
	} catch (error) {
		// 如果提供的不是有效的URL，则认为它是相对路径，并拼接基础路径
		console.error('Invalid URL:', error);
		// 确保相对路径以斜杠开头
		if (!url.startsWith('/')) {
			url = '/' + url;
		}
		// 拼接基础路径与相对路径
		return basePath + url;
	}
}

// 获取租户id
export function tenantId() {
	// debugger;
	const tenantIdFromStorage = localStorage.getItem('tenantId') || '';
	const tenantIdFromDefault = 'ca0b39c476a94d0a8627f2d7adcefce4';
	return tenantIdFromStorage || tenantIdFromDefault;
}

/**退出登录*/
export function loginOut(isLogin = false) {
	if (isLogin) {
		clearCache();
		return;
	}
	request({
		url: '/ybzy/front/platuser/loginOut',
		method: 'post'
	}).then(res => {
		if (res.rCode == 0) {
			clearCache();
		} else {
			Message({
				message: res.message || res.msg,
				type: 'warning'
			});
		}
	});
}
// 清空缓存
function clearCache() {
	const tenantIdOld = tenantId();
	/**清楚cookie里面的内容*/
	removeToken(); // 宜宾反的
	// delCookie('user_id'); // 宜宾反的
	// delCookie('token'); // 大预订电商返回的
	var cookies = Cookies.get();
	// 然后，遍历所有的 Cookie，并分别删除它们
	for (var cookieName in cookies) {
		delCookie(cookieName);
	}
	localStorage.removeItem('dydUserInfo'); // 大预订一机游的信息

	// 清除所有的缓存
	localStorage.clear();
	sessionStorage.clear();

	/**清除local缓存以及vuex里面的信息*/
	store.commit('user/SET_USER_INFO', {});
	store.commit('user/SET_ROLES', []);
	store.commit('user/SET_IDENTITY', []);

	// 租户id不用清除
	localStorage.setItem('tenantId', tenantIdOld);

	// 跳转到首页
	window.location.hash = '#/login';
}
