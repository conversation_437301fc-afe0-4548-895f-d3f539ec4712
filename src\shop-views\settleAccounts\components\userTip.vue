<template>
	<div class="tip">
		<div class="tip-top">
			<div class="line"></div>
			<div calss="tile">购买须知</div>
		</div>
		<div class="tip-con">
			<!-- eslint-disable-next-line vue/no-v-html -->
			<div v-if="skus[0]" v-html="skus[0].SKU_DESC"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'UserTip',
	props: {
		skus: {
			type: Array,
			default: () => {
				return [];
			}
		}
	}
};
</script>

<style scoped lang="scss">
.tip {
	margin: 16px 0;
	padding: 16px 23px 20px 20px;
	background: #ffffff;
	&-top {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		.line {
			width: 6px;
			height: 20px;
			background: var(--brand-6, #0076e8);
			margin-right: 7px;
		}
		.title {
			font-size: 16px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-con {
		background: #ffffff;
		border: 1px solid #d9d9d9;
		padding: 20px;
	}
}
</style>
