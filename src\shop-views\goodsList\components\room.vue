<template>
	<div class="room">
		<div class="room-title">
			<div
				v-for="(item, index) of titleList"
				:key="index"
				class="room-title-item"
				:style="{ width: item.width, paddingLeft: item.paddingLeft }"
			>
				{{ item.name }}
			</div>
		</div>
		<div class="room-list">
			<div v-for="(sku, i) of info.skus" :key="i" class="room-list-item">
				<div
					class="room-list-item-pic"
					:style="{ width: titleList[0].width, paddingLeft: titleList[0].paddingLeft }"
				>
					<img class="img" :src="$judgeFile(sku.URL)" alt="" />
				</div>
				<div class="line"></div>
				<div
					class="room-list-item-product"
					:style="{ width: titleList[1].width, paddingLeft: titleList[1].paddingLeft }"
				>
					<div class="name">{{ sku.SKU_NAME }}</div>
					<div class="sku">{{ sku.skuLabels }}</div>
				</div>
				<div
					class="room-list-item-types"
					:style="{ width: titleList[2].width, paddingLeft: titleList[2].paddingLeft }"
				>
					{{ sku.ATTRS.join('/') }}
				</div>
				<div
					class="room-list-item-price"
					:style="{ width: titleList[3].width, paddingLeft: titleList[3].paddingLeft }"
				>
					￥{{ sku.REAL_PRICE }}
				</div>
				<div class="submit">
					<div class="submit-button" @click="toBuy(sku.ID)">
						<img src="@/assets/shop-images/submit-icon.png" class="icon" alt="" />
						立即预订
					</div>
					<div class="submit-tip">在线支付</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Room',
	props: {
		info: {
			type: Object,
			default: () => {
				return { skus: [] };
			}
		}
	},
	data() {
		return {
			titleList: [
				{
					name: '房型信息',
					width: '262px',
					paddingLeft: '38px'
				},
				{
					name: '产品名称',
					width: '294px',
					paddingLeft: '25px'
				},
				{
					name: '床型/早餐/取消规则/确认政策',
					width: '333px',
					paddingLeft: ''
				},
				{
					name: '日均价',
					width: '98px',
					paddingLeft: ''
				}
			]
		};
	},
	methods: {
		toBuy(id) {
			this.$emit('toBuy', id);
		}
	}
};
</script>

<style scoped lang="scss">
.room {
	margin: 20px;
	&-title {
		height: 44px;
		background: #e4f0fd;
		border: 1px solid #ebebeb;
		display: flex;
		align-items: center;
		font-size: 14px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #697082;
	}
	&-list {
		&-item {
			display: flex;
			padding: 20px 30px 20px 0;
			height: 158px;
			align-items: center;
			box-sizing: border-box;
			border-bottom: 1px solid #f0f0f0;
			&-pic {
				display: flex;
				.img {
					width: 108px;
					height: 108px;
					margin-right: 7px;
				}
			}
			.line {
				height: 100%;
				width: 1px;
				background: #f0f0f0;
			}
			&-product {
				.name {
					font-size: 14px;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #404040;
					line-height: 20px;
					margin-bottom: 6px;
				}
				.sku {
					font-size: 12px;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #8c8c8c;
					line-height: 20px;
				}
			}
			&-types {
				font-size: 14px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #404040;
				line-height: 20px;
			}
			&-price {
				font-size: 22px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #f95f55;
				line-height: 20px;
			}
			.submit {
				&-button {
					cursor: pointer;
					width: 136px;
					height: 40px;
					background: var(--brand-6, #0076e8);
					border-radius: 4px;
					margin-bottom: 5px;
					font-size: 16px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 24px;
					display: flex;
					align-items: center;
					justify-content: center;
					.icon {
						margin-right: 8px;
						width: 14px;
						height: 14px;
					}
				}
				&-tip {
					font-size: 12px;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #bfbfbf;
					line-height: 20px;
				}
			}
		}
	}
}
</style>
