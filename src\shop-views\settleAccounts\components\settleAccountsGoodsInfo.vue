<template>
	<div class="logistics-info">
		<div class="logistics-info-header">
			<div class="icon"></div>
			<div class="title">商品清单</div>
		</div>
		<div class="conter">
			<el-table :data="list" style="width: 100%" :header-cell-style="{ background: '#F4F4F4' }">
				<el-table-column label="商品图片" width="180">
					<template slot-scope="scope">
						<img
							v-if="scope.row.cover_url"
							class="img"
							:src="$judgeFile(scope.row.cover_url)"
							alt=""
						/>
					</template>
				</el-table-column>
				<el-table-column prop="product_name" label="商品" width="180"></el-table-column>
				<el-table-column prop="sku_name" label="规格"></el-table-column>
				<el-table-column
					v-if="shopType === 'scene'"
					prop="startDate"
					label="游玩时间"
				></el-table-column>
				<el-table-column
					v-if="shopType === 'hotel'"
					prop="startDate"
					label="入住时间"
				></el-table-column>
				<el-table-column
					v-if="shopType === 'hotel'"
					prop="endDate"
					label="离店时间"
				></el-table-column>
				<el-table-column
					v-if="shopType === 'hotel'"
					prop="count_night"
					label="居住天数"
				></el-table-column>
				<el-table-column prop="sell_price" label="单价">
					<template slot-scope="scope">
						<span>{{ scope.row.sell_price.toFixed(2) }}</span>
					</template>
				</el-table-column>
				<el-table-column v-if="isPointGoods" prop="sell_point" label="积分"></el-table-column>
				<el-table-column prop="sku_num" label="数量"></el-table-column>
				<el-table-column label="优惠">
					<template slot-scope="">
						<div class="price">0</div>
					</template>
				</el-table-column>
				<el-table-column prop="price" label="小计">
					<template slot-scope="scope">
						<div class="price">
							<span v-if="isPointGoods">
								{{ setBigNumber(Number(scope.row.sell_point), Number(scope.row.sku_num)) }}积分+
							</span>
							<span v-if="shopType === 'hotel'">{{ scope.row.totalPrices.toFixed(2) }}</span>
							<span v-else>
								¥{{ setBigNumber(Number(scope.row.sell_price), Number(scope.row.sku_num)) }}
							</span>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<div class="goods-remark">
				<div class="goods-remark-label">商品备注：</div>
				<el-input v-model="remark" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
			</div>
		</div>
	</div>
</template>

<script>
import BigNumber from 'bignumber.js';
import { Loading } from 'element-eoss';
import dateInterval from '@/utils/dateInterval';

export default {
	name: 'SettleAccountsGoodsInfo',
	props: {
		/**是否是积分商品*/
		isPointGoods: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		/**开始时间*/
		startDate: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**结束时间*/
		endDate: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**商品类型*/
		shopType: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			remark: '',
			type: 1,
			ids: '',
			list: [],
			channel: '',
			info: {},
			skuNum: 1,
			countNight: 1
		};
	},
	watch: {
		startDate(newVal) {
			this.countNight = dateInterval(this.startDate, this.endDate);
		},
		endDate(newVal) {
			this.countNight = dateInterval(this.startDate, this.endDate);
		}
	},
	created() {
		this.type = this.$route.query.type;

		this.channel = this.$route.query.channel;
		this.ids = this.$route.query.ids;
		this.skuId = this.$route.query.skuId;
		this.skuNum = this.$route.query.skuNum;
		if (this.shopType === 'hotel') {
			this.countNight = dateInterval(this.startDate, this.endDate);
		}
		if (this.channel == 'market') {
			this.getSku();
			this.getInfo();
			return;
		}
		if (this.type == 1) {
			this.getList();
		} else {
			this.getSku();
			this.getDetails();
		}
	},
	methods: {
		/**购物车获取商品列表*/
		getList() {
			this.$parent.loading = true;
			let data = {
				ids: this.ids
			};
			this.$api.shop_api.getProductInfoByIds(data).then(res => {
				this.list = res?.result || [];
				this.total = Number(res.totalNum);
				this.list = this.list.map(item => {
					return {
						...item,
						totalPrices: BigNumber(item.sell_price).times(item.sku_num).toString()
						// preferentialPrice: BigNumber(item.vip_price).times(item.sku_num).toString()
					};
				});
				this.$emit('distributionMode', [2]);
				this.$parent.loading = false;
				this.$emit('totalPrice', this.list);
			});
		},
		getSku() {
			let options = {
				target: 'app'
			};
			let loadingInstance = Loading.service(options);
			this.$parent.loading = true;
			this.$api.shop_api.skuDetails({ id: this.skuId }).then(res => {
				if (res.state) {
					let info = res.result;
					let arr = [];
					try {
						if (info.contactsResult.needContacts) {
							info.contactsResult.contacts.forEach(item => {
								if (
									item.key != 'contact' &&
									item.key != 'phoneNum' &&
									item.key != 'provinceId' &&
									item.key != 'cityId' &&
									item.key != 'countyId' &&
									item.key != 'personAddress'
								) {
									arr.push(item);
								}
							});
						}
						this.$emit('setRestsList', arr);
					} catch (error) {
						console.log(error);
					}
					console.log(res.result, '6666666666666666');
					this.$emit('distributionMode', res.result.otherRules?.distributionMode?.distributionMode);
					this.$emit('transportFee', res.result.otherRules.transportFee);
				}
				loadingInstance.close();
				this.$parent.loading = false;
			});
		},
		getInfo() {
			this.$parent.loading = true;
			let data = {
				id: this.ids
			};
			this.$api.shop_api.detailInfo(data).then(res => {
				let info = res?.results || {};
				this.info = info;
				this.list = [];
				let obj = {
					product_name: info.spuName,
					sku_name: info.goodsName,
					sell_price: info.price,
					sku_num: info.quantity,
					vip_price: info.price,
					sku_id: info.skuId,
					cover_url: info.goodsCoverImg
				};
				this.list.push(obj);
				this.list = this.list.map(item => {
					return {
						...item,
						totalPrices: BigNumber(item.sell_price).times(item.sku_num).toString(),
						preferentialPrice: BigNumber(item.vip_price).times(item.sku_num).toString()
					};
				});
				this.$emit('getStoreList', this.info.sellerId);
				this.$emit('distributionMode', info?.pickUpTypes);
				this.$emit('totalPrice', this.list);
				this.$parent.loading = false;
			});
		},
		/**针对酒店商品，数量变化重新计算价格*/
		resetPrice(skuNum) {
			this.skuNum = skuNum;
			this.list = this.list.map(item => {
				item.sku_num = skuNum;
				item.count_night = this.countNight;
				return {
					...item,
					totalPrices: BigNumber(item.sell_price)
						.times(item.sku_num * item.count_night)
						.toString(),
					totalPoint: BigNumber(item.sell_point)
						.times(item.sku_num * item.count_night)
						.toString()
				};
			});
			this.$emit('totalPrice', this.list, true);
		},
		/**获取商品详情并计算单个商品的价格*/
		getDetails() {
			this.$parent.loading = true;
			this.$api.shop_api.getAllProductDetail({ id: this.ids }).then(res => {
				let info = res?.result || {};
				this.info = info;
				let sku = {};
				info.skus.forEach(element => {
					if (element.ID === this.skuId) {
						sku = element;
					}
				});
				let obj = {
					product_name: info.baseInfo.PRODUCT_NAME,
					sku_name: sku.SKU_NAME,
					sell_price: sku.REAL_PRICE,
					sell_point: sku.REAL_POINT,
					sku_num: this.skuNum,
					vip_price: sku.REAL_VIPPRICE,
					sku_id: this.skuId,
					spu_id: sku.ITEM_ID,
					count_night: 1, // 默认一晚上
					cover_url: info.baseInfo.COVER_URL
				};
				// 酒店
				if (this.shopType === 'hotel') {
					obj.count_night = this.countNight;
					obj.startDate = this.startDate;
					obj.endDate = this.endDate;
				}
				// 门票
				if (this.shopType === 'scene') {
					obj.startDate = this.startDate;
				}
				this.list = [];
				this.list.push(obj);

				this.list = this.list.map(item => {
					return {
						...item,
						totalPrices: BigNumber(item.sell_price)
							.times(item.sku_num * item.count_night)
							.toString(),
						totalPoint: BigNumber(item.sell_point)
							.times(item.sku_num * item.count_night)
							.toString()
						// preferentialPrice: BigNumber(item.vip_price).times(item.sku_num).toString()
					};
				});
				this.$parent.loading = false;
				this.$emit('getStoreList', this.info.baseInfo.SELLER_ID);
				this.$emit('totalPrice', this.list);
				this.$emit('setInfo', this.info); // 保存兄弟组件需要用到的信息
			});
		},
		setBigNumber(a, b, item) {
			let totalPrice = BigNumber(a).times(b).toString();
			return Number(totalPrice).toFixed(2);
		},
		distributionClick(item) {
			this.formLabelAlign.distributionType = item;
		}
	}
};
</script>
<style lang="scss" scoped>
.logistics-info {
	background-color: #fff;
	padding: 16px 20px;
	margin-top: 16px;
	&-header {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	.conter {
		margin-top: 15px;
	}
	.price {
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: var(--brand-6, '#ca3f3b');
		line-height: 22px;
	}
	.img {
		width: 70px;
		height: 70px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
	}
	.goods-remark {
		margin-top: 13px;
		width: 100%;
		display: flex;
		padding-left: 20px;
		align-items: center;
		&-label {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
		}
		.el-textarea {
			width: calc(100% - 110px);
			min-height: 55px;
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
	}
}
</style>
