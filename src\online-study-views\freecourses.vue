<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="detail-box">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="banner">
				<div class="inner flex2">
					<div class="info">
						<h3 class="bigTitle">{{ basicInfo.name }}</h3>
						<div class="flex2">
							<h4 class="smallTitle">{{ basicInfo.name }}</h4>
							<p class="flex1">
								<span class="coursePrice">
									<span>¥</span>
									{{ basicInfo.isFree === '1' ? '免费' : (basicInfo.currentPrice || 0).toFixed(2) }}
								</span>
								<span
									v-if="basicInfo.isFree === '0' && !basicInfo.buy"
									class="orderBtn"
									@click="onOrder"
								>
									下单
								</span>
								<span v-if="basicInfo.buy" class="courseBought">已购买</span>
							</p>
						</div>
						<div class="flex2 list">
							<div class="flex1">
								<div class="ele">
									<i class="el-icon el-icon-time ele-icon"></i>
									时长:{{ basicInfo.courseDuration }}
								</div>
								<div class="ele">
									<i class="el-icon el-icon-user ele-icon"></i>
									学习人数:{{ basicInfo.learnPersonNum }}人
								</div>
								<div class="ele">
									<i class="el-icon el-icon-user ele-icon"></i>
									综合评分:{{ basicInfo.score }}分
								</div>
							</div>
							<div class="flex1 icon">
								<div
									class="flex1"
									:class="{ isCollect: basicInfo.collect }"
									@click="collectCourseFn"
								>
									<i class="iconfont el-icon-star-off"></i>
									<div class="text">收藏</div>
								</div>
								<div class="flex1" @click="linkUrl">
									<i class="iconfont el-icon-share"></i>
									<div class="text">分享</div>
								</div>
							</div>
						</div>
					</div>
					<!-- <div class="right-absolut">
						<div class="videoBox">
							<div class="imgBox">
								<img :src="basicInfo.coverImg" />
							</div>
							<div class="btn-box c6">
								<span class="desc">免费</span>
								<el-button class="btn btn1" @click="goPlay">开始学习</el-button>
							</div>
							<div class="know c6">
								<div class="text">课程须知</div>
								<p class="content">{{ basicInfo.notice }}</p>
							</div>
						</div>
					</div> -->
				</div>
			</div>
			<div class="partFour inner flex0">
				<div class="fourLeft">
					<el-tabs v-model="activeName">
						<el-tab-pane label="章节" name="one">
							<div class="tab1" v-html="basicInfo.content"></div>
							<div class="course-list">
								<div v-for="(item, index) in courseDirectory" :key="index" class="course-item">
									<div class="lesson-name">{{ item.name }}</div>
									<div v-for="(ele, i) in item.courseHourList" :key="i" class="lesson flex2">
										<div class="lname flex1" @click="goStudy(ele.id)">
											<i class="el-icon el-icon-caret-right"></i>
											<div class="name">{{ ele.name }}</div>
											<div class="long">
												<i class="el-icon el-icon-time ele-icon"></i>
												{{ ele.courseDuration }}
											</div>
										</div>
									</div>
								</div>
							</div>
						</el-tab-pane>
						<el-tab-pane label="评价" name="three">
							<div class="tab4">
								<div class="tab4top flex1">
									<div class="score">
										<div class="number">{{ basicInfo.score }}</div>
										<el-rate v-model="basicInfo.score" disabled></el-rate>
									</div>
									<div class="label">
										<div class="n1">
											{{ basicInfo.courseAssessmentText }}
										</div>
										<div class="n2 flex1">
											<div v-for="(item, index) in pingAll.tagNums" :key="index" class="item">
												{{ item.value }}
												<span>{{ item.nums }}</span>
											</div>
										</div>
									</div>
								</div>
								<ul v-if="commentDataList.length > 0" class="list">
									<li v-for="(item, index) in commentDataList" :key="index">
										<div class="user flex1">
											<img :src="item.createUser ? item.createUser.avatar : ''" class="touxiang" />
											<div class="flex2">
												<div class="flex1">
													<div class="name">
														{{ item.createUser ? item.createUser.nickName : '' }}
													</div>
													<div class="ps"></div>
													<div class="date flex1">
														<i class="iconfont icon-shijian"></i>
														<span>{{ item.createTime }}</span>
													</div>
												</div>
												<div class="star">
													<el-rate v-model="item.score" disabled></el-rate>
												</div>
											</div>
										</div>
										<div class="text">{{ item.content }}</div>
										<div class="tag flex1">
											<div v-for="(ele, i) in item.tagList" :key="i" class="ele">{{ ele }}</div>
										</div>
										<div v-show="item.replayContent" class="word">{{ item.replayContent }}</div>
									</li>
								</ul>
								<div class="page">
									<el-pagination
										background
										:current-page="pageNum"
										:page-size="12"
										layout="prev, pager, next, jumper"
										:total="totalPage"
										@size-change="handleSizeChange"
										@current-change="handleCurrentChange"
									></el-pagination>
								</div>
							</div>
						</el-tab-pane>
					</el-tabs>
				</div>
				<!-- <div class="fourRight">
					<div class="type1">
						<div class="title">推荐课程</div>
						<div class="list">
							<li
								v-for="(item, index) in tjLesson"
								:key="index"
								class="list-item"
								@click="changLesson(item.id)"
							>
								<img class="leftImg" :src="item.coverImg" alt="" />
								<div class="info flex6">
									<div class="title over2">
										{{ item.name }}
									</div>
								</div>
							</li>
						</div>
					</div>
				</div> -->
			</div>
		</div>
	</div>
</template>
<script>
// import FloatBtn from './components/floatBtn.vue';
import ElementUI from 'element-eoss';
// import config from '@/config';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			activeName: 'one', //当前选中页签code值
			basicInfo: {}, //详情数据
			courseDirectory: [], //课程大纲数据
			commentDataList: [], //评价数据
			pageSize: 10, //课程评价一页展示条数
			pageNum: 1, //课程评价页码数
			totalPage: 0, //总页码数，用于分页组件处理分页数据
			total: 0, //总数据条数
			labelValue: [], //评价标签
			pingAll: {}, //评价标签数量
			tjLesson: [] //推荐好课
		};
	},
	computed: {},
	created() {
		this.getAssessmentTagListFn();
	},
	mounted() {
		this.footmarkAddFn(); //新增浏览足迹
		// this.tjCourseFn();
		this.getLessonInfo(this.$route.query.id); //课程详情
		this.getCourseDirectoryFn(this.$route.query.id); //课程大纲
		this.getAssessmentPageListFn(this.$route.query.id); //课程评价
		this.getAssessmentTagNumsFn(this.$route.query.id); //评价标签数量
	},
	methods: {
		/**
		 * @description 增加浏览足迹
		 * */
		async footmarkAddFn() {
			await this.$api.study_api.footmarkAdd({
				footmarkId: this.$route.query.id,
				userId: this._userinfo.id || '',
				type: 2
			});
		},
		/**
		 * @description 复制
		 * */
		linkUrl() {
			let url = window.location.href; //拿到想要复制的值
			let copyInput = document.createElement('input'); //创建input元素
			document.body.appendChild(copyInput); //向页面底部追加输入框
			copyInput.setAttribute('value', url); //添加属性，将url赋值给input元素的value属性
			copyInput.select(); //选择input元素
			document.execCommand('Copy'); //执行复制命令
			ElementUI.Message.success('链接已复制！'); //弹出提示信息，不同组件可能存在写法不同
			//复制之后再删除元素，否则无法成功赋值
			copyInput.remove(); //删除动态创建的节点
		},
		/**
		 * @description 切换每页展示条数时触发
		 * */
		handleSizeChange(val) {
			this.pageSize = val;
			this.getAssessmentPageListFn(this.$route.query.id);
		},
		/**
		 * @@description 切换分页时触发
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
			this.getAssessmentPageListFn(this.$route.query.id);
		},
		/**
		 * @description 获取课程详情
		 * */
		async getLessonInfo(id) {
			const { data } = await this.$api.study_api.getCourseDetailInfo({ id });
			data.score = Number(data?.score.toFixed(2) || 0);
			this.basicInfo = data;
		},
		//课程大纲
		async getCourseDirectoryFn(id) {
			const { data } = await this.$api.study_api.getCourseDirectory({ courseId: id });
			this.courseDirectory = data;
		},
		//课程评价
		async getAssessmentPageListFn(id) {
			const { data } = await this.$api.study_api.getAssessmentPageList({
				courseId: id,
				pageSize: this.pageSize,
				pageNum: this.pageNum
			});
			this.totalPage = data.totalPage;
			this.total = data.total;
			if (data.items.length > 0) {
				for (let x = 0; x < data.items.length; x++) {
					data.items[x].score = Number(data.items[x].score);
					for (let y = 0; y < data.items[x].tagList.length; y++) {
						data.items[x].tagList[y] = this.getValue(data.items[x].tagList[y]);
					}
				}
				this.commentDataList = data.items;
			}
		},
		//收藏或取消收藏
		async collectCourseFn() {
			const { data } = await this.$api.study_api.collectCourse({
				courseId: this.$route.query.id,
				type: this.basicInfo.collect ? 0 : 1
			});
			console.log('收藏', data);
			this.getLessonInfo(this.$route.query.id);
			ElementUI.Message({
				type: 'success',
				message: '操作成功'
			});
		},
		//获取评价标签
		async getAssessmentTagListFn() {
			const { data } = await this.$api.study_api.getAssessmentTagList();

			this.labelValue = data;
		},
		getValue(value) {
			for (let item of this.labelValue) {
				if (value == item.value) {
					return item.label;
				}
			}
		},
		/**
		 * @description 获取评价标签数量
		 */
		async getAssessmentTagNumsFn(courseId) {
			const { data } = await this.$api.study_api.getAssessmentTagNums({ courseId });
			data.tagNums = data.tagNums.map(item => {
				return {
					value: this.getValue(item.value),
					nums: item.nums
				};
			});
			this.pingAll = data;
		},
		/**
		 * @description 跳转播放页面--章节
		 * @param {lessonId} 章节id
		 * */
		goStudy(lessonId) {
			this.$router.push({
				path: '/video-play',
				query: { id: this.basicInfo.id, lessonId }
			});
		},
		/**
		 * @description 跳转播放页面--课程
		 * */
		goPlay() {
			this.$router.push({
				path: '/video-play',
				query: { id: this.basicInfo.id, lessonId: this.courseDirectory[0].courseHourList[0].id }
			});
		},
		/**
		 * @description 推荐好课数据请求
		 */
		async tjCourseFn() {
			const data = await this.$api.study_api.tjCourse({ courseId: this.$route.query.id });
			this.tjLesson = data.data;
		},
		/**
		 * @description 手动切换课程数据
		 * @param {courseId} 切换的课程id
		 * */
		changLesson(courseId) {
			this.pageNum = 1;
			this.activeName = 'one';
			this.getLessonInfo(courseId);
			this.getCourseDirectoryFn(courseId);
			this.getAssessmentPageListFn(courseId);
		},
		// 下单
		onOrder() {
			if (!this.isShopLogin()) {
				return;
			}
			this.$router.push({
				path: '/freecourses-order',
				query: {
					id: this.basicInfo.id
				}
			});
		}
	}
};
</script>
<style scoped lang="scss">
.detail-box {
	background: #ffffff;
	border-top: 0.1px solid transparent;
}
.top {
	width: 100%;
	height: 40px;
	line-height: 40px;
	background: #ffffff;
	color: #999999;
	font-size: 14px;
	.top-a {
		width: 1200px;
		margin: 0 auto;
	}
	span {
		color: var(--brand-6, #0076e8);
	}
}
.inner {
	width: 1200px;
	margin: 0 auto;
}
.main {
	.banner {
		.inner {
			position: relative;
			background: url('~@/assets/study_images/bluebanner.png') no-repeat center;
			border-radius: 10px;
			margin-top: 10px;
			height: 168px;
		}

		.info {
			// width: 860px;
			width: 100%;
			padding: 30px;
			.bigTitle {
				// padding: 44px 0 24px 0;
				font-size: 30px;
				font-weight: bold;
				color: #ffffff;
			}
			.smallTitle {
				font-size: 18px;
				color: #ffffff;
				margin-top: 20px;
			}
			.smallTitle {
				font-size: 14px;
				font-weight: 400;
				color: #ffffff;
			}
			.coursePrice {
				font-size: 16px;
				font-weight: 500;
				color: #f95f55;

				span {
					font-size: 12px;
				}
			}
			.orderBtn {
				width: 64px;
				height: 32px;
				background: var(--brand-6, #0076e8);
				border-radius: 21px;
				font-size: 16px;
				color: #ffffff;
				line-height: 32px;
				text-align: center;
				cursor: pointer;
				margin-left: 15px;
			}
			.courseBought {
				font-size: 14px;
				color: #bac6df;
				margin-left: 15px;
			}
			.score {
				font-weight: 400;
				color: #ffffff;
				margin-top: 30px;
				font-size: 14px;
				.number {
					padding: 0 15px;
				}
				.study {
				}
			}
			.list {
				font-size: 14px;
				margin-top: 30px;
				font-weight: 400;
				color: #bac6df;
				.ele {
					margin-right: 20px;
				}
				.ele-icon {
					margin-right: 6px;
				}
				.icon {
					text-align: center;
					> div {
						cursor: pointer;
						margin-left: 42px;
					}
					.text {
						margin-left: 6px;
					}
				}
				.isCollect {
					color: rgb(247, 186, 42);
				}
			}
		}
		.right-absolut {
			width: 280px;
			top: 30px;
			right: 30px;
			position: absolute;
		}
		.videoBox {
			height: 338px;
			background: #f7f9fb;
			// box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
			border-radius: 10px;
			box-sizing: border-box;
			padding: 12px;
			overflow-y: auto;
			overflow-x: hidden;
			.imgBox {
				width: 256px;
				height: 160px;
				background: #333333;
				border-radius: 6px;
				position: relative;

				img {
					width: 100%;
					height: 100%;
					border-radius: 6px;
				}
				.play {
					cursor: pointer;
					text-align: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					.iconfont {
						font-size: 54px;
						color: #fff;
						margin-bottom: 10px;
					}
					z-index: 100;
					> div {
						text-align: center;
						color: #fff;
					}
				}
			}
			.price {
				margin-top: 20px;
				.red {
					color: #ff0000;
					font-size: 30px;
					font-weight: 600;
					span {
						color: #ff0000;
						font-size: 14px;
						position: relative;
						bottom: 5px;
					}
				}
				.black {
					position: relative;
					bottom: 4px;
					font-weight: 400;
					text-decoration: line-through;
					color: #999999;
					font-size: 14px;
					margin-left: 10px;
				}
			}
			// .btn {
			// 	width: 280px;
			// 	height: 47px;

			// 	box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
			// 	border-radius: 24px;
			// 	font-size: 18px;
			// 	margin-top: 20px;
			// }
			// .btn1 {
			// 	background: var(--brand-6, #0076e8);
			// 	color: #fff;
			// }
			// .btn2 {
			// 	background: rgba(0, 118, 232, 0.1);
			// 	color: var(--brand-6, #0076e8);
			// 	margin-left: 0;
			// }
			.btn-box {
				display: flex;
				justify-content: space-between;
				margin-top: 16px;
				.desc {
					font-size: 18px;
					font-weight: bold;
					color: #ed2525;
				}
				.btn {
					width: 100px;
					height: 30px;
					border-radius: 15px;
					border: 1px solid #4f85ff;
					font-size: 14px;
					color: #4f85ff;
				}
			}
			.know {
				.text {
					margin-top: 16px;
					margin-left: 9px;
					font-size: 14px;
					position: relative;
					&::after {
						content: '';
						width: 3px;
						height: 12px;
						background: #4f85ff;
						border-radius: 2px;
						position: absolute;
						top: 4px;
						left: -9px;
					}
				}
				.content {
					margin-top: 2px;
				}
			}
		}
	}
}
.fourRight {
	margin-top: 210px;
	margin-left: 30px;
	width: 280px;
	.type1 {
		padding-bottom: 20px;
		width: 100%;
		background: #ffffff;
		.title {
			font-size: 20px;
			font-weight: bold;
			color: #222222;
			position: relative;
			&::after {
				content: '';
				position: absolute;
				width: 40px;
				height: 5px;
				background: #4f85ff;
				border-radius: 3px;
				left: 0;
				bottom: -10px;
			}
		}
		.list {
			margin-top: 30px;
			li {
				margin-bottom: 20px;
				border-radius: 10px;
				cursor: pointer;
				.leftImg {
					width: 100%;
					height: 160px;
					border-radius: 10px;
				}
				.info {
					width: 100%;
					padding: 16px 0 0;
					.title {
						font-size: 16px;
						font-weight: 400;
						color: #222222;
					}
				}
				&:hover {
					box-shadow: 0px 0px 20px 0px rgba(35, 56, 77, 0.4);
				}
			}
		}
	}
}
.partFour {
	margin-top: 20px;
	padding-bottom: 30px;
	min-height: 480px;
	.fourLeft {
		// width: 860px;
		width: 100%;
		background: #fff;
		::v-deep .el-tabs__item {
			font-size: 16px;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #666666;
		}
		::v-deep .el-tabs__item.is-active {
			font-size: 20px;
			font-family: SourceHanSansCN-Bold, SourceHanSansCN;
			font-weight: bold;
			color: #222222;
		}
		::v-deep .el-tabs__active-bar {
			height: 4px;
		}
		::v-deep .el-tabs_header {
			margin: 0;
		}
		// ::v-deep .el-tabs__item {
		// 	font-weight: 500;
		// 	line-height: 50px;
		// 	height: 50px;
		// 	font-weight: bolder;
		// }
		.tab1 {
			background: #ffffff;
			box-sizing: border-box;
			font-size: 14px;
			padding: 20px 0 40px;
			font-weight: 400;
			color: #666666;
			line-height: 24px;
			::v-deep p {
				img {
					width: 100% !important;
				}
			}
		}
		.course-list {
			padding: 0 0 20px;
			.course-item {
				background: #f7f9fb;
				border-radius: 10px;
				padding: 30px;
				margin-top: 10px;
			}
			.lesson-name {
				font-size: 16px;
				font-weight: bold;
				color: #222222;
			}
			.header {
				.blue {
					width: 26px;
					height: 26px;
					text-align: center;
					background: var(--brand-6, #0076e8);
					line-height: 26px;
					color: #fff;
					border-radius: 50%;
				}
				.lessonName {
					color: #313d54;
					font-weight: bold;
					font-size: 18px;
					margin-left: 17px;
					margin-right: 8px;
				}
				.time {
					font-size: 16px;
					font-weight: 400;
					color: #9399a5;
				}
			}
			.lesson {
				color: #313d54;
				margin-left: 60px;
				margin-top: 16px;
				.icon {
					font-size: 18px;
					margin: 0 6px 0 28px;
					color: var(--brand-6, #0076e8);
				}
				.name {
					font-size: 14px;
					font-weight: 400;
					margin-right: 8px;
				}
				.long {
					font-size: 14px;
					flex-shrink: 0;
					font-weight: 400;
					color: #9399a5;
				}
				.right {
					padding-right: 15px;
					.study {
						width: 80px;
						height: 28px;
						background: var(--brand-6, #0076e8);
						border-radius: 14px;
						text-align: center;
						line-height: 28px;
						font-size: 14px;
						color: #fff;
						font-weight: 400;
						display: none;
						cursor: pointer;
					}
				}
			}
			.lesson:hover {
				color: #4f85ff;
				cursor: pointer;
			}
		}
		.tab3 {
			.li {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 192px;
				height: 240px;
				background: linear-gradient(54deg, #f8f8f8 0%, #ffffff 100%);
				img {
					margin-top: 30px;
				}
				.ns {
					color: #666666;
					font-weight: bold;
					margin-top: 12px;
				}
				.ds {
					text-align: center;
					font-size: 12px;
					padding: 11px 31px;
					font-weight: 400;
					color: #999999;
					line-height: 18px;
				}
			}
		}
		.tab4 {
			padding: 0 20px;
			.star {
				padding: 5px 0;
				::v-deep .el-rate__icon {
					margin-right: 0;
				}
			}
			.tab4top {
				border-bottom: 1px solid #e8eaf0;
				padding-bottom: 12px;
				.score {
					text-align: center;
					.number {
						font-weight: bold;
						font-size: 50px;
						color: var(--brand-6, #0076e8);
					}
					::v-deep .el-rate__icon {
						margin-right: 0;
					}
				}
				.label {
					margin-left: 22px;
					.n1 {
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #9399a5;
						margin-bottom: 12px;
						span {
							color: #666666;
						}
					}
					.n2 {
						.item {
							margin-right: 10px;
							height: 26px;
							background: #e5f1fd;
							border-radius: 13px;
							text-align: center;
							line-height: 26px;
							font-size: 12px;
							color: var(--brand-6, #0076e8);
							font-weight: 400;
							padding: 0 10px;
							span {
								margin-right: 2px;
							}
						}
					}
				}
			}
			.list {
				li {
					border-bottom: 1px solid #e8eaf0;
					.user {
						padding: 10px 20px;
						.touxiang {
							width: 60px;
							height: 60px;
							border-radius: 50%;
							margin-right: 10px;
						}
						.flex2 {
							width: 710px;
							margin-top: -30px;
						}
						.name {
							color: #333333;
							font-size: 14px;
							font-weight: 500;
						}
						.ps {
							margin: 0 15px;
							color: #999999;
							font-size: 14px;
						}
						.date {
							font-size: 14px;
							color: #999999;
							.iconfont {
								color: #999999;
							}
							span {
								color: #999999;
								margin-left: 5px;
							}
						}
					}
					.text {
						padding: 5px 5px 10px 90px;
						margin-top: -40px;
						margin-bottom: 15px;
					}
					.tag {
						margin-bottom: 20px;
						padding-left: 90px;
						.ele {
							background: #e8eaf0;
							border: 1px solid #dddddd;
							border-radius: 11px;
							height: 22px;
							text-align: center;
							line-height: 22px;
							font-weight: 400;
							color: #959ba7;
							font-size: 12px;
							padding: 0 7px;
							margin-right: 10px;
						}
					}
					.word {
						background: #f4f5f7;
						padding: 20px;
						margin-left: 90px;
						margin-bottom: 20px;
						font-weight: 400;
						color: #6b7484;
						line-height: 24px;
						border-radius: 6px;
					}
				}
			}

			.page {
				text-align: center;
				padding: 30px 0;
			}
		}
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	margin-top: 10px;
	// background: #fff;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 40px;
	}
}
</style>
