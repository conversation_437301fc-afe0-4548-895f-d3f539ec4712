<template>
	<div class="collect_goods">
		<div v-for="(item, index) in list" :key="index" class="list" @click="handleList(item)">
			<div v-show="multiple" class="multiple" :class="getClass(item)">
				<span class="el-icon-check"></span>
			</div>
			<img class="img" :src="$judgeFile(item.coverUrl)" alt="" />
			<div class="title nth2">{{ item.productName }}</div>
			<div class="price">
				<span v-if="item.pointExchangeProduct == 1">{{ item.sellPoint }}积分+</span>
				¥{{ Number(item.sellPrice || 0).toFixed(2) }}
			</div>
			<div v-show="!multiple" class="del el-icon-delete" @click.stop="handleDelete(item)"></div>
			<div v-show="!multiple" class="btm" @click.stop="goShop(item)">进入店铺</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CollectGoods',
	props: {
		list: {
			type: Array,
			default: () => {
				return [];
			}
		},
		multiple: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			checkList: []
		};
	},
	methods: {
		// 删除
		handleDelete(i) {
			this.$confirm('确定要取消对此商品的关注吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$emit('handleDelete', i);
				})
				.catch(() => {});
		},
		// 进入店铺
		goShop(i) {
			this.$emit('goShop', i);
		},
		// 点击详情
		handleList(i) {
			if (this.multiple) {
				for (let a = 0; a < this.checkList.length; a += 1) {
					if (i.productId === this.checkList[a].productId) {
						this.checkList.splice(a, 1);
						this.$forceUpdate();
						return;
					}
				}
				this.checkList.push(i);
				this.$forceUpdate();
			} else {
				this.$emit('handleList', i);
			}
		},
		// 判断是否选中
		getClass(i) {
			for (let a = 0; a < this.checkList.length; a += 1) {
				if (i.productId == this.checkList[a].productId) {
					return 'act';
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.collect_goods {
	display: flex;
	flex-wrap: wrap;
	.list {
		margin-top: 8px;
		margin-left: 16px;
		width: 210px;
		height: 265px;
		padding: 11px 21px;
		box-sizing: border-box;
		position: relative;
		overflow: hidden;
		.multiple {
			width: 62px;
			height: 57px;
			margin: 0 auto;
			background: conic-gradient(from 135deg, rgba(0, 0, 0, 0.45) 90deg, transparent 90deg);
			top: -29px;
			right: -31px;
			position: absolute;
			transform: rotate(45deg);
			span {
				margin: 38px 0 0 24px;
				color: #fff;
				transform: rotate(-45deg);
				font-weight: 700;
			}
		}
		.act {
			background: conic-gradient(from 135deg, var(--brand-6, '#ca3f3b') 90deg, transparent 90deg);
		}
		img {
			width: 168px;
			height: 168px;
			object-fit: cover;
		}
		.title {
			margin-top: 4px;
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			height: 44px;
		}
		.price {
			margin-top: 2px;
			font-size: 11px;
			font-weight: 500;
			color: #f95f55;
			line-height: 20px;
		}
		.del {
			position: absolute;
			width: 24px;
			height: 24px;
			background: rgba(0, 0, 0, 0.45);
			top: -24px;
			right: 0;
			text-align: center;
			line-height: 24px;
			color: #fff;
			transition: all 0.3s;
			cursor: pointer;
		}
		.btm {
			width: 100%;
			height: 32px;
			background: rgba(0, 0, 0, 0.45);
			bottom: -32px;
			right: 0;
			position: absolute;
			font-size: 14px;
			font-weight: 400;
			color: #ffffff;
			line-height: 32px;
			text-align: center;
			transition: all 0.3s;
			cursor: pointer;
		}
	}
	.list:hover {
		box-shadow: 0px 3px 16px 0px rgba(66, 89, 158, 0.15);
		.del {
			top: 0;
		}
		.btm {
			bottom: 0;
		}
	}
}
</style>
