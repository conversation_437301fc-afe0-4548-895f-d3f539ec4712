<!--
 @desc:个人中心 校友会 校友认证
 @author: WH
 @date: 2023/9/8 
 -->
<template>
	<div v-loading="loading" class="user-info-box">
		<el-form ref="form" :model="form" :rules="rules" label-width="140px">
			<!-- 审核状态 -->
			<div v-if="detail" class="status-box">
				<span v-if="detail.isAudit == 0" class="status-span">审核中</span>
				<span v-if="detail.isAudit == 1" class="status-span status-success">审核通过</span>
				<span v-if="detail.isAudit == 2" class="status-span status-fail">审核未通过</span>
				<span v-if="detail.isAudit == 2" class="status-click" @click="viewAudit">查看审核结果</span>
			</div>
			<div class="business-info">
				<el-form-item label="校友姓名" prop="memberName">
					<el-input
						v-model="form.memberName"
						placeholder="请输入姓名"
						type="input"
						suffix-icon="el-icon-edit"
						:disabled="subBtnState"
					></el-input>
				</el-form-item>
				<el-form-item label="身份证号" prop="idcard">
					<el-input
						v-model="form.idcard"
						placeholder="请输入身份证号"
						type="input"
						:disabled="subBtnState"
					></el-input>
				</el-form-item>
				<el-form-item label="联系方式" prop="phoneNum">
					<el-input
						v-model="form.phoneNum"
						placeholder="请输入联系方式"
						type="input"
						:disabled="subBtnState"
					></el-input>
				</el-form-item>
				<el-form-item label="引荐学院" prop="recommenderCode">
					<el-select
						v-model="form.recommenderCode"
						placeholder="请选择引荐学院"
						:disabled="subBtnState"
					>
						<el-option
							v-for="(item, index) in getOrgList"
							:key="index"
							:label="item.name"
							:value="item.value"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="校友资质" prop="qualifications">
					<uploadItem
						class="avatar-uploader"
						:file-number="5"
						:code="dataCode"
						:own-id="uploadId"
						:only-read="subBtnState"
						@fileChange="fileChange"
					/>
				</el-form-item>
				<el-form-item label="教育经历" prop="educations">
					<el-table :data="form.educations" border style="width: 100%">
						<el-table-column fixed prop="schoolName" label="学校名称">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.schoolName"
									placeholder="请输入学校名称"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="schoolEducation" label="学历">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.schoolEducation"
									placeholder="请输入学历"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="professionalName" label="专业" width="160">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.professionalName"
									placeholder="请输入专业"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="schoolTime" label="在校时间" width="240px">
							<template slot-scope="scope">
								<el-date-picker
									v-model="scope.row.schoolTime"
									type="monthrange"
									align="right"
									unlink-panels
									range-separator="至"
									start-placeholder="开始日期"
									end-placeholder="结束日期"
									:disabled="subBtnState"
								></el-date-picker>
							</template>
						</el-table-column>
						<el-table-column v-if="!subBtnState" fixed="right" label="操作" width="50">
							<template slot-scope="scope">
								<el-popconfirm
									title="确定删除吗？"
									@confirm="educationDelFn('educations', scope.$index)"
								>
									<el-button
										slot="reference"
										icon="el-icon-delete"
										type="text"
										size="small"
									></el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="!subBtnState" class="add-btn">
						<i class="el-icon-circle-plus-outline" @click="addRow('educations')"></i>
						<span @click="addRow('educations')">新增一行</span>
					</div>
				</el-form-item>
				<el-form-item label="工作经历" prop="workExperiences">
					<el-table :data="form.workExperiences" border style="width: 100%">
						<el-table-column prop="companyName" label="公司名称">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.companyName"
									placeholder="请输入公司名称"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="companyIndustry" label="公司行业">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.companyIndustry"
									placeholder="请输入公司行业"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="workTime" label="在职时间" width="240px">
							<template slot-scope="scope">
								<el-date-picker
									v-model="scope.row.workTime"
									type="monthrange"
									align="right"
									unlink-panels
									range-separator="至"
									start-placeholder="开始日期"
									end-placeholder="结束日期"
									:disabled="subBtnState"
								></el-date-picker>
							</template>
						</el-table-column>
						<el-table-column prop="positionName" label="职位名称" width="160">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.positionName"
									placeholder="请输入职位名称"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>
						<el-table-column prop="companyAddr" label="工作地址" width="160">
							<template slot-scope="scope">
								<el-input
									v-model="scope.row.companyAddr"
									placeholder="请输入工作地址"
									type="input"
									:disabled="subBtnState"
								></el-input>
							</template>
						</el-table-column>

						<el-table-column v-if="!subBtnState" fixed="right" label="操作" width="50">
							<template slot-scope="scope">
								<el-popconfirm
									title="确定删除吗？"
									@confirm="educationDelFn('workExperiences', scope.$index)"
								>
									<el-button
										slot="reference"
										icon="el-icon-delete"
										type="text"
										size="small"
									></el-button>
								</el-popconfirm>
							</template>
						</el-table-column>
					</el-table>
					<div v-if="!subBtnState" class="add-btn">
						<i class="el-icon-circle-plus-outline" @click="addRow('workExperiences')"></i>
						<span @click="addRow('workExperiences')">新增一行</span>
					</div>
				</el-form-item>
			</div>
		</el-form>
		<div class="info-save">
			<el-button
				:disabled="subBtnState"
				class="info-save-confirm"
				type="primary"
				size="large"
				:loading="submitLoading"
				@click="handlerSubmit(0)"
			>
				保存
			</el-button>
		</div>
		<roleTip ref="roleTip" :tip-text="tipText" :button="buttonText" @onClick="clickTip"></roleTip>
		<el-dialog v-if="detail" title="查看审核结果" :visible.sync="viewAuditModel" width="400px">
			<div class="audit-dialog">
				<p class="item">
					<span class="label">审核人：</span>
					<span>{{ detail.auditUserName || '-' }}</span>
				</p>
				<p class="item">
					<span class="label">审核时间：</span>
					<span>{{ detail.auditTime || '-' }}</span>
				</p>
				<p class="item">
					<span class="label">审核状态：</span>
					<span>{{ detail.isAudit == 2 ? '审核未通过' : '-' }}</span>
				</p>
				<p class="item">
					<span class="label">审核意见：</span>
					<span>{{ detail.auditContent || '-' }}</span>
				</p>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import uploadItem from './uploadItem.vue';
import roleTip from '@/components/role-tip';
import { v4 as uuidv4 } from 'uuid';
import { parseTime } from '@/utils';

export default {
	components: { uploadItem, roleTip },

	data() {
		return {
			loading: false,
			submitLoading: false,
			uploadId: uuidv4(),
			dataCode: 'alumna_member_qualifications',
			tipText: '您还未进行企业认证，相关功能服务暂无权限！',
			buttonText: '',
			subBtnState: true,
			detail: null, //认证内容
			getOrgList: [], //引荐学院
			viewAuditModel: false, //是否展示认证结果的弹窗
			form: {
				educations: [], //默认数组得加上
				workExperiences: [] //默认数组得加上
			},
			// form: {
			// 	memberName: '', //学生姓名
			// 	idcard: '', //身份证号
			// 	phoneNum: '', //联系方式
			// 	qualifications: '', //校友资质
			// 	//教育经历
			// 	educations: [
			// 		// { schoolName: 'schoolName', atSchlloTime: '' }
			// 	],
			// 	//工作经历
			// 	workExperiences: []
			// },
			// form: {
			// 	memberName: '张三', //学生姓名
			// 	idcard: '510823200001202020', //身份证号
			// 	phoneNum: '13011110000', //联系方式
			// 	qualifications: '', //校友资质
			// 	//教育经历
			// 	educations: [
			// 		{ schoolName: '宜宾职院', schoolEducation: '大专', professionalName: '网络工程' }
			// 	],
			// 	//工作经历
			// 	workExperiences: [
			// 		{
			// 			companyName: '五粮液',
			// 			companyIndustry: '宜宾大道',
			// 			companyIndustry: '制造业',
			// 			positionName: '调酒师'
			// 		}
			// 	]
			// },
			rules: {
				memberName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
				// qualifications: [{ required: true, message: '请上传校友资质', trigger: 'change' }],
				idcard: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!value) {
								return callback(new Error('身份证号不能为空'));
							}
							if (
								/^[1-9]\d{5}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}([0-9]|X)$/.test(
									value
								)
							) {
								callback();
							} else {
								callback(new Error('请输入正确的身份证号格式'));
							}
						}
					}
				],
				phoneNum: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!value) {
								return callback(new Error('联系方式不能为空'));
							}
							if (/^1[3456789]\d{9}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确手机格式'));
							}
						}
					}
				],
				recommenderCode: [{ required: true, message: '请选择引荐学院', trigger: 'change' }], //引荐学院
				educations: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							const KEY = ['schoolName', 'schoolEducation', 'professionalName', 'schoolTime'];
							if (value?.length === 0) {
								return callback(new Error('请填写教育经历'));
							} else {
								for (let i = 0; i < value.length; i++) {
									let item = value[i];
									for (let j = 0; j < KEY.length; j++) {
										const _key = KEY[j];
										if ([undefined, null, ''].includes(item[_key])) {
											return callback(new Error('请完善教育经历内容'));
										}
									}
								}
								callback();
							}
						}
					}
				],
				workExperiences: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							const KEY = [
								'companyName',
								'companyIndustry',
								'workTime',
								'positionName',
								'companyAddr'
							];

							if (value?.length === 0) {
								return callback(new Error('请填写工作经历'));
							} else {
								for (let i = 0; i < value.length; i++) {
									let item = value[i];
									for (let j = 0; j < KEY.length; j++) {
										const _key = KEY[j];
										if ([undefined, null, ''].includes(item[_key])) {
											return callback(new Error('请完善工作经历内容'));
										}
									}
								}
								callback();
							}
						}
					}
				]
			}
		};
	},

	mounted() {
		this.getUserAuth();
		this.getOrgListF();
	},
	methods: {
		//获取校友认证状态——————————————————————————————
		async getUserAuth() {
			try {
				this.loading = true;
				this.formLoading = true;
				let { rCode, msg, results } = await this.$api.alumni_api.getUserAuth();

				if (rCode == 0) {
					//处理时间格式
					this.detail = results;
					let { educations, workExperiences } = results;

					educations?.length > 0 &&
						educations.forEach(item => {
							let isTime = item.schoolTime.split('~');
							item.schoolTime = [isTime[0], isTime[1]];
						});
					workExperiences?.length > 0 &&
						workExperiences.forEach(item => {
							let isTime = item.workTime.split('~');
							item.workTime = [isTime[0], isTime[1]];
						});

					//数据回显
					this.form = { ...results, educations, workExperiences };
					//图片数据回显
					this.uploadId = results.id;
					// 判断审核状态
					if (results.isAudit == 2) {
						// 0待审核 待审核状态不能操作
						// 2未审核未通过
						this.subBtnState = false;
					} else {
						this.subBtnState = true;
					}
				} else {
					this.initTip(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//校友认证
		async userAuthentication() {
			try {
				this.submitLoading = true;
				let data = JSON.parse(JSON.stringify(this.form));
				data.educations.forEach(item => {
					item.schoolTime =
						parseTime(item.schoolTime[0].substring(0, 9), '{y}-{m}') +
						'~' +
						parseTime(item.schoolTime[1].substring(0, 9), '{y}-{m}');
				});
				data.workExperiences.forEach(item => {
					item.workTime =
						parseTime(item.workTime[0].substring(0, 9), '{y}-{m}') +
						'~' +
						parseTime(item.workTime[1].substring(0, 9), '{y}-{m}');
				});
				data.id = this.uploadId;
				// data.educations = JSON.stringify(data.educations);
				// data.workExperiences = JSON.stringify(data.workExperiences);
				let { rCode, msg } = await this.$api.alumni_api.userAuthentication(data);
				if (rCode == 0) {
					// this.communityTags = results;
					this.$message.success(msg);
					this.getUserAuth();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.submitLoading = false;
			}
		},
		// 获取二级学院数据
		getOrgListF() {
			this.$api.personal_api.getOrgList().then(({ rCode, msg, results }) => {
				if (rCode == 0) {
					let list = results || [];
					this.getOrgList = list.reduce((acc, cur) => {
						acc = acc.concat(cur.children || []);
						return acc;
					}, []);
				}
			});
		},
		initTip(msg) {
			// const STATE_KEY = [
			// 	{
			// 		tipText: '您提交的校友认证，还未审核通过！',
			// 		buttonText: ''
			// 	},
			// 	{
			// 		tipText: '您提交的校友认证，匹配不成功，请核实认证信息！',
			// 		buttonText: ''
			// 	},
			// 	{
			// 		tipText: '您还未进行校友认证，相关校友服务暂无权限！',
			// 		buttonText: '立即前往校友认证'
			// 	}
			// ];
			// const { tipText, buttonText } = STATE_KEY[state];

			this.tipText = msg;
			if (msg == '您还未进行校友认证，相关校友服务暂无权限！') {
				this.subBtnState = false;
				this.buttonText = '立即前往校友认证';
			} else {
				this.subBtnState = true;
				this.buttonText = '';
			}
			this.$refs.roleTip.visible = true;
		},
		clickTip() {
			// this.toSubMenu(this.eventType);
			this.$refs.roleTip.visible = false;
		},
		educationDelFn(addTableName, index) {
			this.$delete(this.form[addTableName], index);
		},
		//新增一行
		addRow(addTableName) {
			this.form[addTableName].push({});
		},
		// 提交数据
		handlerSubmit(status) {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.userAuthentication();
				} else {
					return false;
				}
			});
		},
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					this.file = file;
					let idArr = file.map(item => item.adjunctId);
					this.form.qualifications = idArr.join(',');
					// this.$set(this.formData, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('qualifications');
				});
			}
		},
		// 查看审核结果
		viewAudit() {
			this.viewAuditModel = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.user-info-box {
	padding: 20px;
	background: #fff;
	position: relative;
	.add-btn {
		font-size: 14px;
		text-align: center;
		color: #0076e8;
		i,
		span {
			margin-right: 6px;
			cursor: pointer;
		}
	}
	.el-input,
	.el-select {
		width: 300px;
	}
	.el-table {
		.el-input,
		.el-date-editor {
			width: 100%;
		}
	}
	.info-save {
		width: 100%;
		padding-top: 40px;
		border-top: 1px solid #d9d9d9;
		text-align: center;
		&-staging {
			margin-right: 30px;
			padding-left: 56px;
			padding-right: 56px;
		}
		&-confirm {
			padding-left: 56px;
			padding-right: 56px;
		}
	}
	.status-box {
		position: absolute;
		right: 10px;
		top: 10px;
		z-index: 2;
		.status-span {
			display: inline-block;
			padding: 6px 10px;
			color: #0076e8;
			border-radius: 4px;
			margin-right: 6px;
		}
		.status-fail {
			color: #fff;
			background: rgba(0, 0, 0, 0.3);
		}

		.status-click {
			display: inline-block;
			color: #0076e8;
			cursor: pointer;
		}
	}
	.audit-dialog {
		.item {
			margin-bottom: 6px;
		}
		.label {
			display: inline-block;
			width: 80px;
			// text-align: right;
		}
	}
}
::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-range-editor.is-disabled input {
	color: #000;
}
</style>
