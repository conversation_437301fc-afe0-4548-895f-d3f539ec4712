<template>
	<div v-loading="loading" class="appraise">
		<div class="main">
			<div class="title">商品评价</div>
			<div class="content">
				<el-form ref="form" :model="form" :rules="rules" label-width="95px" class="form">
					<!-- <el-form-item label="商品评分：" prop="score2">
						<div class="box">
							<el-rate v-model="form.score" :disabled="isLook"></el-rate>
						</div>
					</el-form-item> -->
					<el-form-item
						:label="details.REG_CODE != 'law' ? '描述相符：' : '专业能力：'"
						prop="score2"
					>
						<div class="box">
							<el-rate v-model="form.score2" :disabled="isLook"></el-rate>
						</div>
					</el-form-item>
					<el-form-item
						:label="details.REG_CODE != 'law' ? '卖家服务：' : '服务态度：'"
						prop="score2"
					>
						<div class="box">
							<el-rate v-model="form.score3" :disabled="isLook"></el-rate>
						</div>
					</el-form-item>
					<el-form-item
						:label="details.REG_CODE != 'law' ? '物流服务：' : '响应速度：'"
						prop="score2"
					>
						<div class="box">
							<el-rate v-model="form.score4" :disabled="isLook"></el-rate>
						</div>
					</el-form-item>
					<el-form-item label="评价内容：" prop="content" class="l-48">
						<el-input
							v-model="form.content"
							type="textarea"
							placeholder="请输入商品纯度、包装、运输方式、品牌需求、货期等信息"
							:rows="4"
							resize="none"
							:disabled="isLook"
						></el-input>
					</el-form-item>
					<el-form-item label="晒图片：" class="l-48">
						<div class="upd-box">
							<div class="tips">晒图片（最多5张）</div>
							<el-upload
								:action="updUrl"
								:disabled="isLook"
								list-type="picture-card"
								:on-remove="handleRemove"
								:file-list="fileList"
								:on-success="success"
								:before-upload="
									() => {
										btnLoading = true;
										if (fileList.length == 5) return false;
									}
								"
								:multiple="false"
								:class="fileList.length === 5 ? 'hide_box' : ''"
								accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PBG,.GIF,.BMP"
							>
								<i class="el-icon-plus"></i>
							</el-upload>
						</div>
					</el-form-item>
					<div class="btn">
						<el-button v-if="!isLook" type="primary" :loading="btnLoading" @click="handleComment">
							提交评价
						</el-button>
						<el-button v-else :disabled="false" @click="$router.go(-1)">返 回</el-button>
					</div>
				</el-form>
				<div class="right">
					<div class="title">订单详情</div>
					<div v-for="(item, index) in details.ORDER_GOODS_DETAILS" :key="index">
						<div class="img-box">
							<img :src="$judgeFile(item.COVER_URL)" alt="" />
							<div class="rf">
								<div class="name nth2">{{ item.NAME }}</div>
								<div class="tit">商品数量：{{ item.NUMBER }}</div>
								<div class="tit">
									商品总价：
									<span>¥{{ item.PRICE.toFixed(2) }}</span>
								</div>
							</div>
						</div>
					</div>
					<div class="data">
						<div class="cell">
							<div class="title">订单编号：</div>
							<div class="val">{{ details.ORDER_ID || '-' }}</div>
						</div>
						<div class="cell">
							<div class="title">订单总额：</div>
							<div class="val">{{ details.TOTAL_MONEY || '0.00' }}</div>
						</div>
						<div class="cell">
							<div class="title">实际支付：</div>
							<div class="val">{{ details.ACTUAL_MONEY || '0.00' }}</div>
						</div>
						<!-- <div class="cell">
							<div class="title">下单人：</div>
							<div class="val">{{ details.passengers ? details.passengers.name : '-' }}</div>
						</div> -->
						<div class="cell">
							<div class="title">下单时间：</div>
							<div class="val">{{ details.ORDER_TIME || '-' }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { baseUrl } from '@/config';

export default {
	name: 'Appraise',
	data() {
		return {
			form: {},
			isLaw: this.$route.query.isLaw,
			rules: {
				score2: [{ required: true, message: '请选择评分', trigger: 'blur' }],
				content: [{ required: true, message: '请输入评价', trigger: 'blur' }]
			},
			id: '',
			updUrl: baseUrl + '/api/supply-web/fileUpload/singleFile',
			details: {
				ORDER_GOODS_DETAILS: []
			},
			fileList: [],
			isLook: false,
			loading: false,
			btnLoading: false,
			userId: ''
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (!this.$route.query.id) {
			this.$message.close();
			this.$message.error('参数错误！');
			this.$router.go(-1);
		} else {
			this.id = this.$route.query.id;
			this.getOrderDetail();
		}
	},
	methods: {
		// 上传成功回调
		success(response, file, fileList) {
			this.fileList = fileList;
			this.btnLoading = false;
		},
		// 提交评价
		handleComment() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					const { content, score2, score3, score4 } = this.form;
					let scores = [];
					const item1 = {
						estimateType: this.details.REG_CODE != 'law' ? 2 : 7,
						title: this.details.REG_CODE != 'law' ? '描述相符' : '专业能力',
						score: score2
					};
					let item2 = {
						estimateType: this.details.REG_CODE != 'law' ? 4 : 8,
						title: this.details.REG_CODE != 'law' ? '卖家服务' : '服务态度',
						score: score3
					};
					let item3 = {
						estimateType: this.details.REG_CODE != 'law' ? 3 : 9,
						title: this.details.REG_CODE != 'law' ? '物流服务' : '响应速度',
						score: score4
					};
					scores.push(item1, item2, item3);
					let images = [];
					if (this.fileList.length > 0) {
						images = this.fileList.map(i => {
							return i.response.results.fileId;
						});
					}
					images = images.toString(',');
					let data = {
						type: 1,
						images,
						content,
						estimatedId: this.details.ORDER_ID,
						userId: this.userId,
						scores: JSON.stringify(scores)
					};
					this.$api.shop_api.comment(data).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '评价成功',
								type: 'success'
							});
							this.loading = false;
							this.isLook = true;
							setTimeout(() => {
								this.$router.go(-1);
							}, 1000);
						} else {
							this.loading = false;
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				} else {
					return false;
				}
			});
		},
		// 获取订单详情
		getOrderDetail() {
			this.loading = true;
			this.$api.shop_api.getOrderDetail({ orderId: this.id }).then(res => {
				if (res.state) {
					this.details = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 移除图片
		handleRemove(file, fileList) {
			this.fileList = fileList;
		}
	}
};
</script>
<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.appraise {
	width: 100%;
	.head {
		width: 1200px;
		display: flex;
		align-items: center;
		margin: 22px auto;
		.icon {
			color: #9aa3ba;
			font-size: 17px;
			margin-right: 8px;
			cursor: pointer;
		}
		.red {
			color: var(--brand-6, '#0076e8');
		}
	}
	.main {
		width: 1200px;
		margin: 0 auto;
		background: #fff;
		padding: 28px 24px 42px 34px;
		box-sizing: border-box;
		.title {
			font-size: 24px;
			font-weight: 500;
			color: #404040;
			line-height: 28px;
		}
	}
	.content {
		margin-top: 26px;
		display: flex;
		.form {
			width: 810px;
			border: 1px solid #d9d9d9;
			padding: 45px 25px;
			box-sizing: border-box;
			::v-deep.el-form-item {
				margin-bottom: 18px;
				.el-form-item__label {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
				}
				.el-textarea__inner {
					border: 1px solid #d9d9d9;
				}
			}
			.box {
				height: 22px;
				display: flex;
				align-items: center;
			}
			.l-48 {
				margin-top: 30px;
			}
			.tips {
				font-size: 14px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
			.btn {
				margin: 95px 0 0 100px;
			}

			.hide_box {
				::v-deep.el-upload--picture-card {
					display: none;
				}
			}
		}
		.right {
			width: 318px;
			margin-left: auto;
			border: 1px solid #d9d9d9;
			padding: 14px 16px;
			box-sizing: border-box;
			.title {
				font-size: 16px;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
			}
			.img-box {
				margin-top: 26px;
				display: flex;
				img {
					width: 72px;
					height: 70px;
				}
				.rf {
					width: 192px;
					margin-left: 6px;
					.name {
						font-size: 16px;
						font-weight: 400;
						color: #404040;
						line-height: 24px;
					}
					.tit {
						margin-top: 6px;
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
						span {
							color: var(--brand-6, '#0076e8');
						}
					}
				}
			}
			.data {
				margin-top: 38px;
				padding: 7px 0;
				background: #f4f5f8;
				.cell {
					display: flex;
					align-items: center;
					.title {
						width: 80px;
						height: 36px;
						font-size: 14px;
						font-weight: 400;
						color: #9da5b7;
						line-height: 36px;
						text-align: right;
					}
					.val {
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #404040;
						line-height: 36px;
					}
				}
			}
		}
	}
}
</style>
