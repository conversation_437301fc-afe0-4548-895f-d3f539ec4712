<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="detail">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<!-- 中心内容展示部分 -->
		<div v-loading="loading" class="detail-box">
			<div class="details-title">
				{{ detail.title || detail.name }}
			</div>
			<div class="details-desc">
				<span v-if="detail.updateUserName">来源：{{ detail.updateUserName }}</span>
				<span>发布时间：{{ detail.updateTime || detail.publishTime }}</span>
				<span>浏览量：{{ detail.viewNum || 0 }}次</span>
			</div>
			<div class="details-tool">
				<p>
					【字体：
					<span
						:class="[activeSize == 'big' ? 'active-size' : '', 'font-size']"
						@click="changeSize('big')"
					>
						大
					</span>
					<span
						:class="[activeSize == 'middle' ? 'active-size' : '', 'font-size']"
						@click="changeSize('middle')"
					>
						中
					</span>
					<span
						:class="[activeSize == 'small' ? 'active-size' : '', 'font-size']"
						@click="changeSize('small')"
					>
						小
					</span>
					】
				</p>
				<p class="tool-btn">
					<span :class="['item-text', isLike && 'actvie-btn']" @click="likeObjects">
						<img
							v-if="isLike"
							src="@/assets/employment-images/liked-gray.png"
							class="btn-icon btn-img"
							alt=""
						/>
						<img
							v-else
							src="@/assets/employment-images/like-gray.png"
							class="btn-icon btn-img"
							alt=""
						/>
						<span>点赞</span>
					</span>
					<span :class="['item-text', isCollect && 'actvie-btn']" @click="collect">
						<!-- <i
							:class="['el-icon', 'btn-icon', isCollect ? 'el-icon-star-on' : 'el-icon-star-off']"
						></i> -->
						<img
							v-if="isCollect"
							src="@/assets/employment-images/collected.png"
							class="btn-icon btn-img"
							alt=""
						/>
						<img
							v-else
							src="@/assets/employment-images/collect-gray.png"
							class="btn-icon btn-img"
							alt=""
						/>
						<!-- <i class="el-icon el-icon-star-on btn-icon"></i> -->
						<span>收藏</span>
					</span>
					<span class="item-text" @click="urlCopy">
						<!-- <i class="el-icon el-icon-share btn-icon"></i> -->
						<img src="@/assets/employment-images/share-gray.png" class="btn-icon btn-img" alt="" />
						<span>分享</span>
					</span>
				</p>
			</div>
			<div class="btns-box">
				<!-- <template v-if="detailType == 'mentor'">
					<el-button type="info" round class="btn online-mentor-btn">与导师在线对话</el-button>
				</template> -->
				<template v-if="detailType != 'mentor'">
					<el-button
						v-if="_userinfo.id != detail.createUserId"
						type="info"
						round
						class="btn online-btn"
						@click="contact"
					>
						在线咨询
					</el-button>
					<el-button type="info" round class="btn contact-btn" @click="showTel">联系企业</el-button>
				</template>
			</div>
			<!-- eslint-disable-next-line vue/no-v-html -->
			<div
				ref="content"
				class="content-box"
				v-html="detail.description || detail.detail || detail.introduction"
			></div>
			<!-- 上一篇下一篇 -->
			<!-- <div class="bottom-box">
				<p v-if="pre" class="bottom-item u-line-1">
					<span>【上一篇】</span>
					<span>{{ pre.title }}</span>
				</p>
				<p v-if="next" class="bottom-item u-line-1">
					<span>【下一篇】</span>
					<span>{{ next.title }}</span>
				</p>
			</div> -->
			<!-- 其他导师推荐 -->
			<!-- <div v-if="detailType == 'mentor'" class="more-mentor">
				<p class="title">其他导师推荐</p>
			</div> -->
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="messageInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import preview from '@/utils/preview';
import urlCopy from '@/utils/url_copy';
import fontSize from '@/utils/font_size';
import contactMessage from '@/components/public/contactMessage.vue';

export default {
	components: {
		subBreadcrumb,
		contactMessage
	},
	data() {
		return {
			dialogMessageVisible: false,
			messageInfo: {},
			detailId: '', //文章id
			code: '', //当前文章所属code
			loading: false, //加载动画
			detail: {}, //详情数据
			activeSize: 'middle', //字号大小
			isCollect: false, //是否收藏
			isLike: false, //是否收藏
			typeList: {
				project: 5,
				mentor: 3
			}, //类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			detailType: '' //文章类型 mentor为导师详情 project为创业项目详情
		};
	},
	mounted() {
		this.detailId = this.$route.query.id || '';
		this.code = this.$route.query.code || '';
		this.detailType = this.$route.query.type || '';
		if (this.detailType == 'project') {
			this.getProjectDetail();
		} else if (this.detailType == 'mentor') {
			this.getMentorDetail();
		} else {
			this.getDetail();
		}

		this.isCollected();
		this.isLiked();
		// this.view();
	},
	methods: {
		/**
		 * @description 获取详情
		 * */
		getDetail() {
			this.loading = true;
			let data = {
				id: this.detailId
			};
			this.$api.employment_api
				.serviceInfo(data)
				.then(res => {
					this.detail = res?.results || {};
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 获取项目详情
		 * */
		getProjectDetail() {
			this.loading = true;
			let data = {
				id: this.detailId
			};
			this.$api.employment_api
				.jobProjectInfo(data)
				.then(res => {
					this.detail = res?.results || {};
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 获取导师详情
		 * */
		getMentorDetail() {
			this.loading = true;
			let data = {
				id: this.detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId
				// code: this.code
			};
			this.$api.information_api
				.detail(data)
				.then(res => {
					this.detail = res?.results || {};
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 添加浏览量
		 * */
		view() {
			let data = {
				objectId: this.detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				objectType: 'info'
			};
			this.$api.information_api
				.view(data)
				.then(res => {})
				.catch(() => {});
		},
		// 是否收藏
		isCollected() {
			const param = {
				id: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.isCollected(param)
				.then(res => {
					if (res.success) {
						this.isCollect = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 收藏
		collect() {
			if (this.isCollect) {
				return this.cancelCollects();
			}
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				collects: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.collect(param)
				.then(res => {
					if (res.success) {
						this.isCollect = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消收藏
		cancelCollects() {
			const param = {
				objectIds: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.cancelCollects(param)
				.then(res => {
					if (res.success) {
						this.isCollect = false;
						this.$message.success('取消收藏');
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 是否收藏
		isLiked() {
			const param = {
				id: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.isLiked(param)
				.then(res => {
					if (res.success) {
						this.isLike = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 点赞
		likeObjects() {
			if (this.isLike) {
				return this.cancelLikes();
			}
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				likes: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.likeObjects(param)
				.then(res => {
					if (res.success) {
						this.isLike = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消点赞
		cancelLikes() {
			const param = {
				objectIds: this.detailId,
				type: this.typeList[this.detailType] || 2
			};
			this.$api.employment_api
				.cancelLikes(param)
				.then(res => {
					if (res.success) {
						this.isLike = false;
						this.$message.success('取消点赞');
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		preview() {
			preview();
		},
		urlCopy() {
			urlCopy();
		},
		changeSize(type) {
			this.activeSize = type;
			fontSize(this.$refs.content, type);
		},
		/**
		 * @description 在线咨询
		 * */
		contact() {
			this.setMessageInfo();
			this.dialogMessageVisible = true;
		},
		/**
		 * @description 展示电话号码
		 */
		showTel() {
			this.$alert(this.detail.telephone, '联系号码');
		},
		setMessageInfo() {
			let obj = {
				SHOP_NAME: this.detail.enterpriseName || this.detail.createUserName,
				SHOP_LOG: this.detail.cover || '',
				SELLER_ID: this.detail.createUserId,
				isGoods: true
			};
			this.messageInfo = obj;
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.detail-box {
	width: $max-width;
	padding: 50px;
	margin: 20px auto;
	// margin-top: 20px;
	margin-bottom: 30px;
	background: #ffffff;
	font-family: Microsoft YaHei;
	.details-title {
		// width: 871px;
		font-size: 24px;
		line-height: 36px;
		font-weight: 400;
		color: #333333;
		text-align: center;
	}
	.details-desc {
		text-align: center;
		font-size: 14px;
		font-weight: 400;
		color: #999999;
		margin-top: 20px;
		> span {
			margin: 0 6px;
		}
	}
	.details-tool {
		font-size: 14px;
		color: #666666;
		line-height: 18px;
		text-align: center;
		margin-top: 17px;
		// > p {
		// 	margin: 0 12px;
		// }
		.font-size {
			margin: 0 4px;
			cursor: pointer;
		}
		.active-size {
			color: var(--brand-6, #0076e8);
		}
		.tool-btn {
			margin-top: 23px;
			display: flex;
			justify-content: center;
			.item-text {
				margin: 0 17px;
				cursor: pointer;
				display: inline-flex;
				align-items: center;
			}
			.btn-icon {
				margin-right: 4px;
				font-size: 18px;
				color: #999999;
			}
			.btn-img {
				width: 16px;
				height: 16px;
			}
			.actvie-btn {
				// color: #ff6b0c;
			}
		}
	}
	.btns-box {
		text-align: center;
		margin-top: 32px;
		.btn {
			width: 140px;
			height: 50px;
			border-radius: 25px;
			border: none;
		}
		.online-btn {
			background: linear-gradient(98deg, #248ef0 0%, #0076e8 100%);
			box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
		}
		.contact-btn {
			background: linear-gradient(160deg, #ff6000 0%, #e63b0d 100%);
			box-shadow: 0px 4px 10px 0px rgba(230, 59, 13, 0.2);
		}
		.online-mentor-btn {
			width: 280px;
			background: linear-gradient(98deg, #248ef0 0%, #0076e8 100%);
			box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
		}
	}
	.content-box {
		width: 100%;
		overflow: hidden;
		margin-top: 60px;
		border-top: 1px solid #e9e9e9;
		// border-bottom: 1px solid #e9e9e9;
		padding-top: 28px;
	}
	.bottom-box {
		font-size: 14px;
		font-weight: 400;
		color: #999999;
		padding-top: 20px;
		.bottom-item {
			color: #666666;
			cursor: pointer;
		}
	}
	.more-mentor {
		width: 1160px;
		height: 284px;
		background: #f4f4f4;
		padding: 30px;
		.title {
			font-size: 24px;
			font-weight: 400;
			color: #333333;
			text-align: center;
		}
	}
}
</style>
