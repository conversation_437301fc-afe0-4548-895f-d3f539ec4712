import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import getPageTitle from '@/utils/get-page-title';
import breadCrumb from '@/utils/bread_crumb';
import { getToken } from '@/utils/auth';
NProgress.configure({ showSpinner: false });
router.beforeEach(async (to, from, next) => {
	NProgress.start();
	document.title = getPageTitle(to.meta.title);
	const hasToken = getToken();
	// 路由配置中需要登录的界面，校验是否登录
	if (to.meta.needLogin && !hasToken) {
		window._VUE.$message.error('未登录，请先登录！');
		setTimeout(() => {
			next(`/login?redirect=${to.fullPath}`);
		}, 1000);
	} else {
		next();
	}
	NProgress.done();
});

router.afterEach((to, from) => {
	if (to.query.updateTitle) {
		to.meta.title = to.query.updateTitle;
	}
	/**根据路由处理面包屑*/
	breadCrumb(to, from);
	NProgress.done();
});
