<template>
	<div class="examination-box">
		<div v-loading="loading" class="main">
			<div class="img-box">
				<span class="img-text">{{ title }}</span>
				<img class="top-img" src="@/assets/study_images/examination-bg.png" alt="" />
			</div>
			<div class="back-box" @click="back">
				<img class="back-img" src="@/assets/study_images/examination-back.png" alt="" />
				<span>返回课程</span>
			</div>
			<!-- 试题内容部分 -->
			<div v-if="list && list.length" class="examination-content">
				<!-- 标题部分 -->
				<div class="title">
					<span class="topic-title">
						{{ list[itemIndex].question }}
						<template v-if="examin">
							<el-input
								v-if="list[itemIndex].type == 4"
								v-model="list[itemIndex].result"
								disabled
								class="topic-input"
							></el-input>
							<p v-if="list[itemIndex].type == 4" class="correct-answer">
								正确答案：
								<span>{{ list[itemIndex].answer }}</span>
							</p>
						</template>
						<template v-else>
							<el-input
								v-if="list[itemIndex].type == 4"
								v-model.trim="resultList[list[itemIndex].id].result"
								:maxlength="list[itemIndex].muns"
								show-word-limit
								placeholder="请输入内容"
								class="topic-input"
							></el-input>
						</template>
					</span>
					<span class="len">
						<span class="item-index">{{ itemIndex + 1 }}</span>
						/{{ list.length }}
					</span>
				</div>
				<!-- 选项部分 -->
				<ul v-if="list[itemIndex].type != 4" class="options-box">
					<li
						v-for="(item, index) in list[itemIndex].items"
						:key="index"
						:class="[
							'option-item',
							examin &&
							list[itemIndex].result.includes(item.item) &&
							!list[itemIndex].answer.includes(item.item)
								? 'error-option'
								: '',
							examin && list[itemIndex].answer.includes(item.item) ? 'primy-option' : '',
							!examin && resultList[list[itemIndex].id].result.includes(item.item)
								? 'primy-option'
								: ''
						]"
						@click="optionClick(list[itemIndex], item)"
					>
						{{ item.item }}{{ item.content }}
						<template v-if="examin">
							<template
								v-if="
									list[itemIndex].result.includes(item.item) &&
									!list[itemIndex].answer.includes(item.item)
								"
							>
								<img class="option-img" src="@/assets/study_images/result-error.png" alt="" />
							</template>
							<template v-if="list[itemIndex].answer.includes(item.item)">
								<img class="option-img" src="@/assets/study_images/result-correct.png" alt="" />
							</template>
						</template>
						<template v-else>
							<!-- <img
								v-if="resultList[list[itemIndex].id].result.includes(item.item)"
								class="option-img"
								src="@/assets/study_images/result-correct.png"
								alt=""
							/> -->
						</template>
					</li>
				</ul>
				<!-- 操作按钮部分 -->
				<div class="btns-box">
					<el-button v-if="itemIndex > 0" type="primary" plain round @click="prev()">
						上一题
					</el-button>
					<el-button v-if="itemIndex < list.length - 1" type="primary" plain round @click="next()">
						下一题
					</el-button>
					<el-button v-if="!examin" type="primary" plain round @click="submitPrev()">
						提交
					</el-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
export default {
	data() {
		return {
			baseUrl,
			loading: false, //加载动画
			itemIndex: 0,
			examinationId: '',
			courseId: '',
			exam: false,
			examFlag: true, //是否完成所有试题
			// 1单选 2多选 3判断 4填空
			list: [],
			examin: false, //是否已经考试过
			resultList: {} //考试答案数据
		};
	},
	mounted() {
		this.examinationId = this.$route.query.examId || '';
		this.courseId = this.$route.query.courseId || '';
		this.title = this.$route.query.title || '考试';
		this.examin = this.$route.query.exam || false;
		if (this.examin == 'true') {
			// 根据传过来的值区分是否已经考试
			this.examin = true;
			this.analysisExam();
		} else {
			this.examin = false;
			this.startExam();
		}
	},
	methods: {
		// 开始答题
		startExam() {
			this.loading = true;
			this.resultList = {};
			this.loading = true;
			let param = {
				examId: this.examinationId
			};
			this.$api.study_api
				.startExam(param)
				.then(({ code, data }) => {
					this.loading = false;
					if (code == 200) {
						this.list = data || [];
						let resultList = this.list.reduce((acc, currt) => {
							let obj = {
								id: currt.id,
								result: currt.type != 4 ? [] : ''
							};
							acc[currt.id] = obj;
							return acc;
						}, {});
						this.$set(this, 'resultList', resultList);
					}
				})
				.catch(() => {
					this.loading = false;
				});
		},
		// 提交之前的判断操作
		submitPrev() {
			this.examFlag = true;
			let itemsList = [];
			for (let item in this.resultList) {
				let obj = {
					id: item,
					result: ''
				};
				if (typeof this.resultList[item].result == 'string') {
					obj.result = this.resultList[item].result;
				} else {
					obj.result = this.resultList[item].result.join(',');
				}
				if (!obj.result) {
					// 判断是否有未答题
					this.examFlag = false;
				}
				itemsList.push(obj);
			}
			if (!this.examFlag) {
				this.$confirm('还有未答题目，是否提交')
					.then(_ => {
						return this.submitExamination(itemsList);
					})
					.catch(() => {
						console.log('中断提交');
						return;
					});
			} else {
				this.submitExamination(itemsList);
			}
		},
		// 提交
		submitExamination(itemsList) {
			console.log('继续提交');
			this.loading = true;
			let param = {
				courseId: this.courseId,
				examId: this.examinationId,
				items: itemsList
			};
			this.$api.study_api
				.submitExamination(param)
				.then(({ code, data }) => {
					if (code == 200) {
						this.$router.push({
							path: '/examination-result',
							query: { examId: this.examinationId, courseId: this.courseId }
						});
					}
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		// 查看解析
		analysisExam() {
			this.loading = true;
			let param = {
				examId: this.examinationId,
				courseId: this.courseId
			};
			this.$api.study_api
				.analysisExam(param)
				.then(({ code, data }) => {
					this.loading = false;
					if (code == 200) {
						this.list = data || [];
					}
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 上一题
		 * */
		prev() {
			if (this.itemIndex == 0) {
				return false;
			}
			this.itemIndex--;
		},
		/**
		 * @description 下一题
		 * */
		next() {
			if (this.itemIndex == this.list.length - 1) {
				return false;
			}
			this.itemIndex++;
		},
		/**
		 * description 选项选中事件
		 * */
		optionClick(item, optionItem) {
			if (this.examin) {
				return;
			}
			// 1单选 2多选 3判断 4填空
			if (item.type == 1 || item.type == 3) {
				let arr = [];
				arr.push(optionItem.item);
				this.resultList[item.id].result = arr;
			} else if (item.type == 2) {
				let optionIndex = this.resultList[item.id].result.indexOf(optionItem.item);
				if (optionIndex >= 0 && this.resultList[item.id].result.length) {
					this.resultList[item.id].result.splice(optionIndex, 1);
				} else {
					this.resultList[item.id].result.push(optionItem.item);
				}
			}
		},
		// 返回课程
		back() {
			this.$router.push({
				path: '/freecourses',
				query: { id: this.$route.query.id }
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.examination-box {
	width: 100%;
	height: 100%;
	background: #ffffff;
	font-family: SourceHanSansCN-Regular, SourceHanSansCN;
	.main {
		width: 1200px;
		margin: 0 auto;
		height: 100%;
		padding: 20px 0;
	}
	.img-box {
		width: 100%;
		height: 180px;
		position: relative;
	}
	.img-text {
		position: absolute;
		display: inline-block;
		font-size: 60px;
		font-family: DOUYUFont;
		color: #378cef;
		line-height: 79px;
		left: 104px;
		top: 60px;
	}
	.top-img {
		width: 100%;
		height: 180px;
	}
	.back-box {
		height: 16px;
		font-size: 16px;
		font-weight: 400;
		color: #0374ec;
		margin-top: 40px;
		cursor: pointer;
	}
	.back-img {
		width: 14px;
		height: 14px;
		margin-right: 9px;
	}
	// 试题内容部分
	.examination-content {
		margin-top: 40px;
	}
	.title {
		font-size: 18px;
		color: #222222;
		display: flex;
		justify-content: space-between;
	}
	.topic-title {
		width: 100%;
	}
	.topic-input {
		width: 300px;
	}
	.correct-answer {
		color: #0076e9;
		margin-top: 10px;
		word-break: break-all;
	}
	.len {
		flex-shrink: 0;
		margin-left: 10px;
		.item-index {
			color: #0076e9;
		}
	}
	.options-box {
		margin-top: 30px;
		.option-item {
			width: 100%;
			padding: 22px 30px;
			background: #f7f9fb;
			border-radius: 10px;
			margin-bottom: 10px;
			cursor: pointer;
			display: flex;
			justify-content: space-between;
			align-items: center;
			word-break: break-all;
			// height: 18px;
			font-size: 18px;
			font-weight: bold;
			min-height: 80px;
		}
		.primy-option {
			background: #eef7ff;
			border: 1px solid #0076e9;
			color: #0076e9;
		}
		.error-option {
			background: #ffeaea;
			border: 1px solid #ff9191;
			color: #ff9191;
		}
		.option-img {
			width: 36px;
			height: 36px;
			flex-shrink: 0;
			margin-left: 10px;
		}
		.option-item-active {
			background: #eef7ff;
			border: 1px solid #0076e9;
		}
		.option-item-error {
			background: #ffeaea;
			border: 1px solid #ff9191;
		}
	}
	.btns-box {
		margin-top: 40px;
		text-align: center;
	}
	::v-deep.el-input.is-disabled .el-input__inner {
		color: #999;
	}
}
</style>
