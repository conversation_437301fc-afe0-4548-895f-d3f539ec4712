<template>
	<div
		v-loading="loading"
		class="shoppingCart"
		:style="{ width: isMain ? '1200px' : '100%', marginBottom: isMain ? '60px' : '' }"
	>
		<div class="total">
			已添加
			<span>{{ total }}</span>
			件商品
		</div>
		<div class="conten">
			<div class="shoppingCart-goods">
				<div class="shoppingCart-goods-item">
					<table class="table-header">
						<tr>
							<th class="checkbox">
								<el-checkbox v-model="allChecked" @change="allChangeChecked">全选</el-checkbox>
							</th>
							<th class="info">商品信息</th>
							<th class="specification">规格</th>
							<th class="price">单价</th>
							<th class="num">数量</th>
							<th class="table-header-total">小计(元)</th>
							<th>操作</th>
						</tr>
					</table>
					<div v-for="(item, index) of list" :key="index" class="shoppingCart-goods-item-list">
						<shopping-cart-list
							:key="item.checked"
							:info="item"
							@changeChecked="changeChecked"
							@onButton="onButton"
						/>
					</div>
				</div>
			</div>
		</div>

		<Empty v-show="total == 0" :tips="'暂无商品信息'" />
		<div v-show="total != 0" class="pagination">
			<pagination
				:fixed="'left'"
				:current-page="page"
				:page-size="limit"
				:total="total"
				:layout="'total,sizes,->,prev,pager,next,jumper'"
				@paginationChange="paginationChange"
			/>
		</div>
		<div class="hold-block"></div>
		<div class="aggregate">
			<div class="aggregate-left">
				<div><el-checkbox v-model="allChecked" @change="allChangeChecked">全选</el-checkbox></div>
				<div>
					<a href="javascript:void(0)" @click="detaleCart('')">删除</a>
				</div>
				<div>
					<a href="javascript:void(0)" @click="collect('')">移入收藏夹</a>
				</div>
			</div>
			<div class="aggregate-right">
				<div class="aggregate-right-nums">
					<span>已选商品</span>
					<span class="aggregate-right-nums_num">{{ activeList.length }}</span>
					<span>件</span>
				</div>
				<div class="aggregate-right-total">
					<span>合计：</span>
					<span class="aggregate-right-total-price">￥{{ Number(totalPrice).toFixed(2) }}</span>
				</div>
				<div class="aggregate-right-button">
					<a href="javascript:void(0)">
						<div @click="createOrderMult">结算</div>
						<!-- <div>结算</div> -->
					</a>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Pagination from '@/components/public/Pagination.vue';
import BigNumber from 'bignumber.js';

import shoppingCartList from './components/shopping-cart-list.vue';
export default {
	name: 'ShoppingCart',
	components: { shoppingCartList, Pagination },
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			allChecked: false,
			page: 1, // 当前页
			limit: 10, // 每页显示的条数
			total: 0, // 总条数,
			offset: 0,
			list: [],
			activeList: [],
			totalPrice: 0,
			loading: false,
			userId: ''
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getList();
		}
	},
	methods: {
		getList() {
			this.loading = true;
			this.list = [];
			let data = {
				rentId: this.getSiteId(),
				memberId: this.userId,
				offset: this.offset,
				psize: this.limit
			};
			this.$api.shop_api
				.getShoppingCar(data)
				.then(res => {
					this.list = res?.result || [];
					this.list = this.setArr(this.list);
					this.list.forEach(event => {
						event.checked = false;
					});
					this.total = Number(res.totalNum);
					let activeList = []; // 可能出现删除或者收藏的情况，重新声明一个数组
					if (this.activeList && this.activeList.length) {
						this.activeList.forEach(event => {
							this.list.forEach(item => {
								item.List.forEach(element => {
									if (event.id == element.id) {
										element.checked = true;
										activeList.push(event);
									}
								});
							});
						});
					}
					this.activeList = activeList;
					this.setStatistics();
					this.loading = false;
				})
				.catch(err => {
					this.loading = false;
				});
		},
		paginationChange(item) {
			if (item.type == 1) {
				this.limit = item.value;
				this.offset = 0;
			} else {
				this.offset = this.limit * (item.value - 1);
			}
			this.getList();
		},
		changeChecked(item, type) {
			let allChecked = true;
			let arr = [];
			this.list.forEach(event => {
				if (event.checked == false) {
					allChecked = false;
				}
				if (event.shop_id == item.shop_id) {
					event = item;
				}
				event.List.forEach(element => {
					if (event.checked) {
						// 选择店铺
						arr.push(element);
					} else {
						// 只选择商品
						if (element.checked) {
							arr.push(element);
						}
					}
				});
			});
			this.allChecked = allChecked;
			this.activeList = [];
			arr.forEach(item => {
				if (item.state) {
					this.activeList.push(item);
				}
			});
			this.setStatistics();
		},
		onButton(type, data) {
			if (type == 1) {
				this.detaleCart(data.id);
			} else {
				this.collect(data.id);
			}
		},
		setIds() {
			let arr = [];

			if (!this.activeList.length) {
				this.$message.close();
				this.$message.warning('请选择数据');
				return;
			}
			this.loading = true;
			arr = this.activeList.map(item => {
				return item.id;
			});
			return arr;
		},
		createOrderMult() {
			let ids = this.setIds();
			if (ids) {
				this.$router.push({
					path: `/settleAccountsIndex?ids=${ids}&type=1`
				});
			} else {
				this.$message.error('请选择商品');
			}
		},
		detaleCart(id) {
			let ids = '';
			if (id) {
				ids = id;
			} else {
				ids = this.setIds().toString();
			}
			this.loading = true;
			let data = {
				ids: ids,
				memberId: this.userId
			};
			this.$api.shop_api.batchDelete(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message.success(res.msg);
				} else {
					this.$message.close();
					this.$message.warning(res.msg);
				}
				this.getList();
				this.loading = false;
			});
		},
		collect(id) {
			let ids = '';
			if (id) {
				ids = id;
			} else {
				ids = this.setIds().toString();
			}
			let data = {
				ids: ids,
				memberId: this.userId
			};
			this.loading = true;
			this.$api.shop_api.moveToFavorite(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message.success(res.msg);
				} else {
					this.$message.close();
					this.$message.warning(res.msg);
				}
				this.getList();
				this.loading = false;
			});
		},
		allChangeChecked(item) {
			let arr = [];
			this.list.forEach(event => {
				event.checked = item;
				event.List &&
					event.List.length &&
					event.List.forEach(emenet => {
						emenet.checked = item;
					});
				if (item) {
					arr = arr.concat(event.List);
				}
			});
			this.activeList = [];
			arr.forEach(item => {
				if (item.state) {
					this.activeList.push(item);
				}
			});
			this.setStatistics();
		},
		setStatistics() {
			console.log(this.activeList);
			this.totalPrice = 0;
			this.activeList.forEach(item => {
				this.totalPrice = BigNumber(this.totalPrice).plus(Number(item.totalPrice)).toString();
			});
		},
		setArr(arr) {
			let dataArr = [];
			arr.map(mapItem => {
				if (dataArr.length == 0) {
					dataArr.push({ shop_id: mapItem.shop_id, shop_name: mapItem.shop_name, List: [mapItem] });
				} else {
					let res = dataArr.some(item => {
						//判断相同店铺，有就添加到当前项
						if (item.shop_id == mapItem.shop_id) {
							item.List.push(mapItem);
							return true;
						}
					});
					if (!res) {
						//如果没找店铺添加一个新对象
						dataArr.push({
							shop_id: mapItem.shop_id,
							shop_name: mapItem.shop_name,
							List: [mapItem]
						});
					}
				}
			});
			return dataArr;
		},
		uniqueFunc(arr, uniId) {
			const res = new Map();
			return arr.filter(item => !res.has(item[uniId]) && res.set(item[uniId], 1));
		}
	}
};
</script>

<style lang="scss" scoped>
.shoppingCart {
	margin: 0 auto;
	overflow: hidden;
	background-color: #fff;
	position: relative;
	min-height: 100%;

	.empty {
		padding-bottom: 40px;
		background-color: #fff;
	}

	.conten {
		padding: 0 20px;
		background: #ffffff;
	}

	.total {
		padding: 20px;
		font-size: 18px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #404040;
		line-height: 32px;

		span {
			color: var(--brand-6, '#ca3f3b');
		}
	}

	&-goods {
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		overflow-x: auto;

		&-item {
			width: 100%;
		}

		&-list {
			margin-top: 20px;
		}

		.table-header {
			width: 100%;
			padding: 12px 13px;
			background: #f4f4f4;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			display: block;

			.checkbox {
				width: 58px;
			}

			.info {
				width: 380px;
			}

			.specification {
				width: 142px;
			}

			.price {
				width: 140px;
			}

			.num {
				width: 130px;
			}

			&-total {
				width: 156px;
			}
		}
	}

	.pagination {
		background: #fff;
		padding: 20px;
	}

	.hold-block {
		height: 95px;
	}

	.aggregate {
		position: absolute;
		bottom: 0;
		width: 100%;
		height: 95px;
		background: #ffffff;
		box-shadow: 0px -1px 10px 0px rgba(0, 0, 0, 0.05);
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border-top: 1px solid #d9d9d9;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 29px 17px 29px 23px;

		.aggregate-left {
			display: flex;
			align-items: center;

			div {
				height: 100%;
				line-height: 22px;
				margin-right: 49px;
			}
		}

		.aggregate-right {
			display: flex;
			align-items: center;

			&-nums {
				margin-right: 56px;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;

				.aggregate-right-nums_num {
					font-size: 20px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: var(--brand-6, '#ca3f3b');
				}
			}

			&-total {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
				margin-right: 30px;

				&-price {
					font-size: 24px;
					font-family: Source Han Sans SC-Heavy, Source Han Sans SC;
					font-weight: 800;
					color: #f95f55;
				}
			}

			&-button {
				width: 68px;
				height: 38px;
				background: var(--brand-6, '#ca3f3b');
				// background: #999;
				border-radius: 36px 36px 36px 36px;
				opacity: 1;
				//border: 1px solid #f5222d;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;

				a {
					color: rgba(255, 255, 255, 0.9);
				}
			}
		}
	}
}
</style>
