<template>
	<div class="detail">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<!-- 中心内容展示部分 -->
		<div v-loading="loading" class="detail-box">
			<div class="details-title">
				{{ detail.title }}
			</div>
			<div class="details-desc">
				<span v-if="detail.origin">来源：{{ detail.origin }}</span>
				<span>发布时间：{{ detail.publishTime }}</span>
				<span>浏览量：{{ detail.viewNum || 0 }}次</span>
			</div>
			<div class="details-tool">
				<span>
					【字体：
					<span
						:class="[activeSize == 'big' ? 'active-size' : '', 'font-size']"
						@click="changeSize('big')"
					>
						大
					</span>
					<span
						:class="[activeSize == 'middle' ? 'active-size' : '', 'font-size']"
						@click="changeSize('middle')"
					>
						中
					</span>
					<span
						:class="[activeSize == 'small' ? 'active-size' : '', 'font-size']"
						@click="changeSize('small')"
					>
						小
					</span>
					】
				</span>
				<span class="item-text" @click="preview">打印</span>
				<!-- <span class="item-text">收藏</span> -->
				<span class="item-text" @click="urlCopy">分享</span>
			</div>
			<!-- eslint-disable-next-line vue/no-v-html -->
			<div ref="content" class="content-box" v-html="detail.detail"></div>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import preview from '@/utils/preview';
import urlCopy from '@/utils/url_copy';
import fontSize from '@/utils/font_size';

export default {
	components: {
		subBreadcrumb
	},
	data() {
		return {
			detailId: '', //文章id
			code: '', //当前文章所属code
			loading: false, //加载动画
			detail: {}, //详情数据
			activeSize: 'middle' //字号大小
		};
	},
	mounted() {
		this.detailId = this.$route.query.id || '';
		this.code = this.$route.query.code || '';
		this.getDetail();
		this.view();
	},
	methods: {
		/**
		 * @description 获取列表
		 * */
		getDetail() {
			this.loading = true;
			let data = {
				id: this.detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				code: this.code
			};
			this.$api.information_api
				.detail(data)
				.then(res => {
					this.detail = res?.results || {};
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 添加浏览量
		 * */
		view() {
			let data = {
				objectId: this.detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				objectType: 'info'
			};
			this.$api.information_api
				.view(data)
				.then(res => {})
				.catch(() => {});
		},
		/**
		 * @description 打印
		 * */
		preview() {
			preview();
		},
		/**
		 * @description 分享
		 * */
		urlCopy() {
			urlCopy();
		},
		/**
		 * @description 改变字号
		 * */
		changeSize(type) {
			this.activeSize = type;
			fontSize(this.$refs.content, type);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.detail-box {
	width: $max-width;
	padding: 40px 50px;
	margin: 20px auto;
	// margin-top: 20px;
	margin-bottom: 30px;
	background: #ffffff;
	font-family: Microsoft YaHei;
	.details-title {
		width: 871px;
		font-size: 24px;
		line-height: 36px;
		font-weight: 400;
		color: #333333;
		text-align: center;
		margin: 9px auto 0;
	}
	.details-desc {
		text-align: center;
		font-size: 14px;
		font-weight: 400;
		color: #999999;
		margin-top: 32px;
		> span {
			margin: 0 6px;
		}
	}
	.details-tool {
		font-size: 14px;
		color: #666666;
		line-height: 18px;
		text-align: center;
		margin-top: 17px;
		> span {
			margin: 0 12px;
		}
		.font-size {
			margin: 0 4px;
			cursor: pointer;
		}
		.active-size {
			color: var(--brand-6, #0076e8);
		}
		.item-text {
			cursor: pointer;
		}
	}
	.content-box {
		width: 100%;
		overflow: hidden;
		margin-top: 37px;
		border-top: 1px solid #e9e9e9;
		// border-bottom: 1px solid #e9e9e9;
		padding-top: 28px;
	}
	.bottom-box {
		font-size: 14px;
		font-weight: 400;
		color: #999999;
		padding-top: 20px;
		.bottom-item {
			color: #666666;
			cursor: pointer;
		}
	}
}
</style>
