<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="list-box">
			<!-- 类型选择部分 -->
			<div class="top-box">
				<!-- 左侧类型区域 -->
				<div class="tab-box">
					<div
						v-for="(item, index) in tabList"
						:key="index"
						:class="['item-tab', activeTab == item.code ? 'active-tab' : '']"
						@click="tabClick(item)"
					>
						{{ item.name }}
					</div>
				</div>
				<!-- 右侧搜索栏部分 -->
				<div class="right-box">
					<el-input v-model="keywords" class="input-search" placeholder="请输入关键词"></el-input>
					<i class="el-icon-search reash-icon" @click="search"></i>
				</div>
			</div>
			<!-- 列表内容区域 -->
			<div class="list-card">
				<Empty v-if="list.length == 0" :tips="'暂无数据'" />
				<div v-for="(item, index) in list" :key="index" class="item-card">
					<img class="item-img" :src="getYbzyImg(item.coverImg)" alt="" />
					<div class="card-right" @click="toDetail(item)">
						<p class="item-title u-line-1">{{ item.title }}</p>
						<p class="item-time">
							<span>{{ item.publishTime }}</span>
							<span>
								<i class="el-icon-view view-icon"></i>
								{{ item.viewNum || 0 }}
							</span>
						</p>
						<div class="item-content u-line-4">
							{{ item.abstract }}
						</div>
						<!-- 进入详情的按钮 -->
						<div class="detail-btn" title="进入详情">
							<i class="el-icon-right"></i>
						</div>
					</div>
				</div>
			</div>
			<!-- 分页区域 -->
			<el-pagination
				v-if="totalNum"
				class="pagination"
				background
				prev-text="上一页"
				next-text="下一页"
				layout="prev, pager, next, jumper"
				:total="totalNum"
				:page-size="psize"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import { baseUrl } from '@/config';
export default {
	components: {
		subBreadcrumb
	},
	data() {
		return {
			baseUrl,
			list: [], //列表数据
			tabList: [
				{
					name: '最新动态',
					code: 'dynamicNews'
				},
				{
					name: '政策解读',
					code: 'policyReading'
				},
				{
					name: '职教视界',
					code: 'educationView'
				}
			], //类型列表数据
			activeTab: '', //当前选中类型
			pageNum: 1, //当前页码
			psize: 10, ///没页条数
			totalNum: 0, //总条数
			keywords: '' //关键词
		};
	},
	mounted() {
		this.activeTab = this.$route.query.code || 'dynamicNews';
		this.getInformation();
	},
	methods: {
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		// 跳转详情
		toDetail(item) {
			// 显示类型(2:普通、1:外链)
			if (item.infoType == 2) {
				this.jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`);
			} else {
				window.open(item.linkUrl);
			}
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		// 类型tab点击事件
		tabClick(item) {
			this.activeTab = item.code;
			this.search();
		},
		// 搜索栏点击搜索事件
		search() {
			this.pageNum = 1;
			this.list = [];
			this.getInformation();
		},
		/**
		 * @description 获取列表
		 * */
		getInformation() {
			this.loading = true;
			let data = {
				nodeCode: this.activeTab,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				keywords: this.keywords,
				pageNum: this.pageNum,
				pageSize: this.psize
			};
			this.$api.information_api.paging(data).then(res => {
				this.list = res?.results?.records || [];
				this.totalNum = res?.results?.total || 0;
				this.loading = false;
			});
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			this.getInformation();
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1200px;
* {
	padding: 0;
	margin: 0;
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.list-box {
	font-family: Microsoft YaHei;
	width: $max-width;
	margin: 0 auto;
	margin-bottom: 90px;
	.top-box {
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: space-between;
		margin-top: 50px;
		.tab-box {
			display: flex;
			.item-tab {
				width: 200px;
				height: 50px;
				background: #ffffff;
				border: 1px solid #dddddd;
				border-radius: 25px;
				font-size: 18px;
				color: #333333;
				line-height: 50px;
				text-align: center;
				margin-right: 50px;
				cursor: pointer;
			}
			.active-tab {
				background: var(--brand-6, #0076e8);
				color: #ffffff;
			}
		}

		.right-box {
			width: 400px;
			height: 50px;
			background: #ffffff;
			border-radius: 25px;
			display: flex;
			align-items: center;
			padding: 0 20px;
			.input-search {
				::v-deep.el-input__inner {
					border: none;
				}
			}
			.reash-icon {
				width: 18px;
				height: 18px;
				cursor: pointer;
			}
		}
	}
	.item-card {
		width: 100%;
		height: 300px;
		display: flex;
		margin-top: 84px;
		.item-img {
			width: 450px;
			height: 300px;
			flex-shrink: 0;
			object-fit: cover;
		}
		.card-right {
			margin-left: 40px;
			width: calc(100% - 40px - 450px);
			cursor: pointer;
			.item-title {
				font-size: 24px;
				font-weight: bold;
				color: #333333;
			}
			.item-time {
				display: flex;
				justify-content: space-between;
				font-size: 14px;
				color: #666666;
				margin-top: 33px;
			}
			.item-content {
				height: 106px;
				font-size: 16px;
				line-height: 26px;
				color: #999999;
				margin-top: 37px;
			}
			.detail-btn {
				width: 40px;
				height: 19px;
				color: #999999;
				margin-top: 45px;
				cursor: pointer;
			}
		}
	}
	.pagination {
		text-align: center;
		margin-top: 80px;
		::v-deep.btn-prev,
		::v-deep.btn-next {
			width: 70px;
			height: 40px;
			line-height: 40px;
			background: #ffffff;
			border: 1px solid #e9e9e9;
			border-radius: 4px;
			> span {
				line-height: 40px;
			}
		}
		::v-deep.el-pager {
			.number,
			.btn-quickprev,
			.btn-quicknext {
				background: #ffffff;
				border: 1px solid #e9e9e9;
				padding: 0px 12px;
				height: 40px;
				line-height: 40px;
				border-radius: 4px;
			}
		}
		::v-deep.el-pagination__jump {
			height: 40px;
			line-height: 40px;
		}
	}
	.view-icon {
		margin-right: 9px;
	}
}
</style>
