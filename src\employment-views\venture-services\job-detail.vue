<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div v-loading="loading" class="main">
			<!-- 职位信息栏 -->
			<div class="position-box">
				<div class="position-left">
					<div>
						<span class="name">
							{{ detail.name }}
						</span>
						<span class="price">
							{{ detail.salaryStructure }}
							<!-- <span v-if="detail.payment">/{{ detail.payment }}</span> -->
						</span>
						<img
							v-if="detail.isDualSelect"
							class="tag-img"
							:src="require('@/assets/employment-images/tag.png')"
							alt=""
						/>
					</div>
					<p class="label-box">
						<span v-if="detail.areaName" class="label-item">{{ detail.areaName }}</span>
						<span v-if="detail.education" class="label-item">{{ detail.education }}</span>
						<span v-if="detail.jobNature" class="label-item">{{ detail.jobNature }}</span>
						<span v-if="detail.workExperience" class="label-item">{{ detail.workExperience }}</span>
						<span v-for="(item, index) in detail.tags" :key="index" class="label-item">
							{{ item }}
						</span>
						<span v-if="detail.address" class="label-item">{{ detail.address }}</span>
						<el-tag v-if="detail.lineType === 1" size="mini" type="success">线下</el-tag>
						<el-tag v-else size="mini" type="primary">线上</el-tag>
					</p>
					<p v-if="detail.lineType === 1" class="label-box" style="margin-top: 2px">
						<span v-if="detail.areaName" class="label-item">
							进校时间：{{ detail.enterSchoolTime }}
						</span>
						<span v-if="detail.areaName" class="label-item">
							招聘地点：{{ detail.offlinePlace }}
						</span>
					</p>
				</div>
				<div class="position-right">
					<div v-if="detail.lineType === 0" class="btns-box">
						<el-button
							v-loading="sendLoading"
							type="info"
							round
							:class="['deliver-btn', detail.isSend ? 'send-btn' : '']"
							@click="jobSend"
						>
							{{ detail.isSend ? '简历已投递' : '投递简历' }}
						</el-button>
						<!-- <el-button type="info" plain round class="online-btn">在线面试申请</el-button> -->
					</div>
					<div class="more-btn">
						<span
							:class="['collect-btn', isFollowedP && 'actvie-btn']"
							@click="followObjects('0', 'isFollowedP')"
						>
							<img
								v-if="isFollowedP"
								class="btn-icon btn-img"
								:src="require('@/assets/skill-treasure/heart-small-active.png')"
								alt=""
							/>
							<img
								v-else
								class="btn-icon btn-img"
								:src="require('@/assets/skill-treasure/heart-small.png')"
								alt=""
							/>
							<span>职位关注</span>
						</span>
						<span :class="['collect-btn', isCollect && 'actvie-btn']" @click="collect">
							<!-- <i class="el-icon el-icon-star-on btn-icon"></i> -->
							<img
								v-if="isCollect"
								src="@/assets/employment-images/collected.png"
								class="btn-icon btn-img"
								alt=""
							/>
							<img
								v-else
								src="@/assets/employment-images/collect-gray.png"
								class="btn-icon btn-img"
								alt=""
							/>
							<span>职位收藏</span>
						</span>
						<span type="text" class="share-btn" @click="urlCopy">
							<!-- <i class="el-icon el-icon-share btn-icon"></i> -->
							<img
								src="@/assets/employment-images/share-gray.png"
								class="btn-icon btn-img"
								alt=""
							/>
							<span>职位分享</span>
						</span>
					</div>
				</div>
			</div>
			<div class="info-box">
				<div class="position-info">
					<div class="hr-box">
						<div class="hr-info">
							<img
								class="hr-img"
								:src="
									hrDetail.logo
										? $judgeImg(hrDetail.logo)
										: require('@/assets/shop-images/default-avatar.png')
								"
								alt=""
							/>

							<div>
								<p>
									<span class="name">{{ hrDetail.username }}</span>
									<!-- <span class="online-status">{{ hrDetail.onlineStatus }}</span>
									<span class="status">{{ hrDetail.status }}</span> -->
								</p>
								<p class="desc">HR · {{ companyDetail.corpName }}</p>
							</div>
						</div>
						<!-- <el-button type="info" round class="chat-btn" @click="contact">聊一聊</el-button> -->
					</div>
					<!-- 职位介绍 -->
					<div class="job-description">
						<div class="title">职位介绍</div>

						<!-- {{detail.introduce}} -->
						<span v-if="detail.postTypeDes" class="des-name">{{ detail.postTypeDes }}</span>
						<!-- eslint-disable-next-line vue/no-v-html -->
						<div class="description" v-html="detail.introduce"></div>
					</div>
					<!-- 地图展示 -->
					<div class="map">
						<div id="map"></div>
					</div>
				</div>
				<div class="company-info">
					<div class="title">公司信息</div>
					<div class="company-base">
						<img
							v-if="companyDetail.logo"
							class="company-img"
							:src="getImgUrl(companyDetail.logo)"
							alt=""
						/>
						<img
							v-else
							class="company-img"
							:src="require('@/assets/employment-images/com-defalut-img.jpg')"
							alt=""
						/>
						<span class="company-name">{{ companyDetail.corpName }}</span>
						<span
							:class="['interest', isFollowedC && 'interest-active']"
							@click="followObjects('1', 'isFollowedC')"
						>
							关注
						</span>
					</div>
					<ul class="other-base">
						<li>
							<span class="label">企业行业：</span>
							<span class="text">
								{{ companyDetail.industryField | enterpriseTypeStr(corpIndustryList) }}
							</span>
						</li>
						<li>
							<span class="label">人数规模：</span>
							<span class="text">
								{{ companyDetail.peopleNumCode | enterpriseTypeStr(peopleNumCodeList) }}
							</span>
						</li>
						<li>
							<span class="label">企业地址：</span>
							<span class="text u-line-1">{{ companyDetail.contactAddress }}</span>
						</li>
						<li>
							<span class="label">企业成立日期：</span>
							<span class="text">{{ companyDetail.establishDate || '-' }}</span>
						</li>
						<li>
							<span class="label">注册资本：</span>
							<span class="text">{{ companyDetail.registeredCapital }}</span>
						</li>
						<li>
							<span class="label">经营范围：</span>
							<span class="text u-line-4">{{ companyDetail.businessScope }}</span>
						</li>
					</ul>
					<p>
						<span class="detail-btn" @click="jumpPage(`/company-detail?id=${companyDetail.id}`)">
							详情查看>>
						</span>
					</p>
				</div>
			</div>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="messageInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import AMapLoader from '@amap/amap-jsapi-loader';
import { getDictionaryByCode } from '@/utils';
import urlCopy from '@/utils/url_copy';
import contactMessage from '@/components/public/contactMessageEnterprise.vue';
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
export default {
	components: {
		subBreadcrumb,
		contactMessage
	},
	filters: {
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value) {
					str = item.shortName;
				}
			}
			return str || value;
		}
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			loading: false,
			dialogMessageVisible: false,
			messageInfo: {},
			sendLoading: false, //发送简历加载动画，防止重复加载
			id: '', //岗位id
			detail: {}, //岗位信息
			companyDetail: {}, //公司信息部分
			hrDetail: {}, //hr信息部分
			peopleNumCodeList: [], //人数规模
			corpIndustryList: [], //行业
			isCollect: false, //是否收藏职位
			isFollowedP: false, //是否关注职位
			isFollowedC: false //是否关注公司
		};
	},
	mounted() {
		if (!this.isShopLogin()) {
			return;
		}
		this.id = this.$route.query.id;
		this.jobCoPostInfo();
		this.isCollected();
	},
	methods: {
		/**
		 * @description 获取职位详情
		 * */
		jobCoPostInfo() {
			// 岗位id
			this.loading = true;
			let param = { id: this.id };
			this.$api.employment_api
				.jobCoPostInfo(param)
				.then(res => {
					this.detail = res.results || {};
					this.hrDetail = this.detail?.hr || {};
					this.companyDetail = this.detail?.enterprise || {};
					this.findSysCode(); //人数规模 //行业
					this.initMap(this.companyDetail?.lon || '', this.companyDetail?.lat || '');
					this.isFollowed('0', 'isFollowedP'); //是否关注职位
					this.isFollowed('1', 'isFollowedC'); //是否关注公司
				})
				.finally(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode() {
			// let param = {
			// 	sysAppCode: code
			// };
			// this.$api.employment_api.findSysCode(param).then(res => {
			// 	this[list] = res?.results || [];
			// });
			const dicts = await getDictionaryByCode(['post_industry_area', 'post_company_scale']);
			this.peopleNumCodeList = dicts.post_company_scale || [];
			this.corpIndustryList = dicts.post_industry_area || [];
		},
		// 是否收藏
		isCollected() {
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				id: this.id,
				type: '0'
			};
			this.$api.employment_api
				.isCollected(param)
				.then(res => {
					if (res.success) {
						this.isCollect = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 收藏
		collect() {
			if (!this.isShopLogin()) {
				return;
			}
			if (this.isCollect) {
				return this.cancelCollects();
			}
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				collects: this.id,
				type: '0'
			};
			this.$api.employment_api
				.collect(param)
				.then(res => {
					if (res.success) {
						this.isCollect = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消收藏
		cancelCollects() {
			if (!this.isShopLogin()) {
				return;
			}
			const param = {
				objectIds: this.id,
				type: '0'
			};
			this.$api.employment_api
				.cancelCollects(param)
				.then(res => {
					if (res.success) {
						this.isCollect = false;
						this.$message.info('取消收藏');
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 是否关注职位/公司
		isFollowed(type, isFollowed) {
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				id: isFollowed == 'isFollowedC' ? this.companyDetail.id : this.id,
				type: type
			};
			this.$api.employment_api
				.isFollowed(param)
				.then(res => {
					if (res.success) {
						this.isFollowedC = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 关注职位/公司
		followObjects(type, isFollowed) {
			if (!this.isShopLogin()) {
				return;
			}
			if (this[isFollowed]) {
				return this.cancelFollows(type, isFollowed);
			}
			// 关注内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				follows: isFollowed == 'isFollowedC' ? this.companyDetail.id : this.id,
				type: type
			};
			this.$api.employment_api
				.followObjects(param)
				.then(res => {
					if (res.success) {
						this[isFollowed] = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消关注
		cancelFollows(type, isFollowed) {
			if (!this.isShopLogin()) {
				return;
			}
			// 类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				objectIds: isFollowed == 'isFollowedC' ? this.companyDetail.id : this.id,
				type: type
			};
			this.$api.employment_api
				.cancelFollows(param)
				.then(res => {
					if (res.success) {
						this[isFollowed] = false;
						this.$message.info('取消关注');
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		/**
		 * @description 简历投递
		 * */
		jobSend() {
			if (!this.isShopLogin()) {
				return;
			}
			if (this.detail.isSend) {
				return;
			}
			this.sendLoading = true;
			// 岗位id
			const param = {
				postId: this.id
			};

			this.$confirm('确定要投递简历吗？', '简历投递', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.employment_api
						.jobSend(param)
						.then(res => {
							if (res.success) {
								this.$message.success('提交成功');
								this.jobCoPostInfo();
							} else {
								this.$message.error(res.msg);
							}
							this.sendLoading = false;
						})
						.catch(res => {
							this.sendLoading = false;
							this.$message.error('提交失败');
						});
				})
				.catch(() => {
					this.sendLoading = false;
				});
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},

		/**
		 * @description 加载地图
		 * */
		initMap(lonStr, latStr) {
			let lon = lonStr || '104.64';
			let lat = latStr || '28.75';
			AMapLoader.load({
				key: '3a404f75b7278208a942cd4f820ed6b6', // 申请好的Web端开发者Key，首次调用 load 时必填
				version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
				plugins: [] // 需要使用的的插件列表，如比例尺'AMap.Scale'等
			})
				.then(AMap => {
					let map = new AMap.Map('map', {
						zoom: 16, //初始地图级别
						center: [lon, lat] //初始地图中心点 成都国际非物质文化遗产博览园
					});
					var pointer = new AMap.Marker({
						position: [lon, lat]
					});
					map.add(pointer);
				})
				.catch(e => {
					console.log(e);
				});
		},
		/**
		 * @description 复制连接
		 * */
		urlCopy() {
			urlCopy();
		},
		/**
		 * @description 在线咨询
		 * */
		contact() {
			if (!this.isShopLogin()) {
				return;
			}
			this.setMessageInfo();
			this.dialogMessageVisible = true;
		},
		/**
		 * @description 设置对话框信息
		 * */
		setMessageInfo() {
			let obj = {
				SHOP_NAME: this.hrDetail.username,
				SHOP_LOG: this.hrDetail.photoUrl || '',
				SELLER_ID: this.hrDetail.id,
				isGoods: true
			};
			this.messageInfo = obj;
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;

// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;

	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}

.main {
	width: $max-width;
	margin: 20px auto 60px;
}

.position-box {
	width: 100%;
	min-height: 114px;
	padding: 20px;
	background: #ffffff;
	box-shadow: 0px 0px 10px 0px rgba(200, 200, 200, 0.05);
	border-radius: 5px;
	display: flex;

	.position-left {
		width: 100%;
		height: 100%;

		.name {
			font-size: 24px;
			font-weight: bold;
			color: #333333;
			line-height: 24px;
			margin-right: 40px;
			vertical-align: middle;
		}

		.price {
			display: inline-block;
			font-size: 24px;
			font-weight: bold;
			color: #ff6b0c;
			// margin-left: 40px;
			line-height: 24px;
			vertical-align: middle;
		}

		.tag-img {
			margin-left: 26px;
			height: 40px !important;
			vertical-align: middle;
		}

		.label-box {
			margin-top: 16px;
		}

		.label-item {
			font-size: 14px;
			line-height: 14px;
			color: #333333;
			position: relative;
			margin-right: 40px;
			// margin-left: 20px;
			display: inline-block;

			&:nth-child(1) {
				margin-left: 0;
			}

			&:after {
				content: '';
				display: inline-block;
				width: 1px;
				height: 12px;
				background: #999999;
				position: absolute;
				right: -20px;
				top: 1px;
			}

			&:last-child {
				&:after {
					width: 0;
					height: 0;
				}
			}
		}
	}

	.position-right {
		width: 370px;
		height: 100%;
		flex-shrink: 0;

		.btns-box {
			display: flex;
			justify-content: flex-end;

			.deliver-btn,
			.online-btn {
				height: 42px;
				border-radius: 21px;
				font-size: 16px;
				color: #ffffff;
				border: none;
			}

			.deliver-btn {
				width: 162px;
				background: linear-gradient(160deg, #ff6000 0%, #e63b0d 100%);
				box-shadow: 0px 4px 10px 0px rgba(230, 59, 13, 0.2);
			}

			.online-btn {
				width: 160px;
				background: linear-gradient(98deg, #248ef0 0%, #0076e8 100%);
				box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
				margin-left: 20px;
			}

			.send-btn {
				color: #fff;
				background: #999;
				border: 1px solid #999;
				box-shadow: none;
			}
		}

		.more-btn {
			margin-top: 16px;
			height: 12px;
			text-align: right;

			.collect-btn,
			.share-btn {
				padding: 0;
				font-size: 14px;
				line-height: 14px;
				display: inline-flex;
				align-items: center;
				margin-left: 42px;
				cursor: pointer;
				color: #999999;
			}

			.icon-img {
				width: 12px;
				height: 12px;
			}

			.btn-icon {
				width: 14px;
				height: 14px;
				margin-right: 5px;
			}

			.actvie-btn {
				color: #ff6b0c;
			}
		}
	}
}

.info-box {
	margin-top: 20px;
	display: flex;
	justify-content: space-between;

	.position-info {
		width: 877px;

		.hr-box,
		.job-description,
		.map {
			width: 100%;
			background: #ffffff;
			box-shadow: 0px 0px 10px 0px rgba(200, 200, 200, 0.05);
			border-radius: 5px;
			padding: 40px 20px;
		}

		.hr-box {
			height: 144px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.hr-info {
				display: flex;
				align-items: center;
			}

			.hr-img {
				width: 64px;
				height: 64px;
				border-radius: 50%;
				margin-right: 20px;
				object-fit: cover;
			}

			.name {
				font-size: 16px;
				color: #333333;
			}

			.online-status {
				font-size: 14px;
				color: #666666;
				margin-left: 28px;
				position: relative;

				&:after {
					content: '';
					width: 8px;
					height: 8px;
					background: #7ed321;
					border-radius: 50%;
					position: absolute;
					left: -13px;
					top: calc(50% - 4px);
				}
			}

			.status {
				display: inline-block;
				padding: 4px 8px;
				font-size: 14px;
				line-height: 14px;
				color: #ffffff;
				background: #0076e8;
				border-radius: 2px;
				margin-left: 16px;
			}

			.desc {
				font-size: 14px;
				color: #666666;
				margin-top: 15px;
			}

			.chat-btn {
				width: 96px;
				height: 42px;
				background: #0076e8;
				box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
				border: none;
				border-radius: 21px;
				margin-left: 10px;
			}
		}

		.job-description {
			margin-top: 20px;

			.title {
				font-size: 18px;
				color: #333333;
			}

			.des-name {
				display: inline-block;
				margin-top: 20px;
				// width: 68px;
				height: 20px;
				padding: 0 6px;
				background: #f3f5f9;
				border-radius: 2px;
				font-size: 14px;
				color: #999999;
			}

			.description {
				margin-top: 20px;
			}
		}

		.map {
			margin-top: 20px;
			height: 577px;

			#map {
				width: 100%;
				height: 100%;
			}

			// border: 1px solid red;
		}
	}

	.company-info {
		width: 362px;
		height: 448px;
		padding: 20px;
		background: #ffffff;
		box-shadow: 0px 0px 10px 0px rgba(200, 200, 200, 0.05);
		border-radius: 5px;

		.title {
			font-size: 18px;
			color: #333333;
		}

		.company-base {
			margin-top: 20px;
			display: flex;
			align-items: center;
		}

		.company-img {
			width: 56px;
			height: 56px;
			padding: 10px;
			flex-shrink: 0;
			border: 1px solid #f0f3f7;
			object-fit: contain;
		}

		.company-name {
			margin-left: 20px;
			font-size: 14px;
			color: #333333;
			width: 100%;
			display: inline-block;
		}

		.interest {
			font-size: 14px;
			color: #999999;
			flex-shrink: 0;
			margin-left: 10px;
			cursor: pointer;
		}

		.interest-active {
			color: #ff6b0c;
		}

		.other-base {
			margin-top: 20px;

			> li {
				margin-bottom: 6px;
				display: flex;
			}

			.label {
				color: #333333;
				flex-shrink: 0;
			}

			.text {
				color: #666666;
			}
		}

		.detail-btn {
			float: right;
			font-size: 14px;
			color: #0076e8;
			cursor: pointer;
		}
	}
}
</style>
