<template>
	<div style="background: #f4f6fb">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="mycar inner">
				<div class="mycarTop flex1">
					<div class="ns">我的购物车</div>
					<div class="total">
						(当前购物车共
						<span>{{ carList.length }}</span>
						件)
					</div>
				</div>
				<div class="step flex4">
					<div class="stepItem">
						<div class="number">1</div>
						<div class="ns c6 fs14">我的购物车</div>
					</div>

					<div class="stepItem">
						<div class="number">2</div>
						<div class="ns c6 fs14">成功提交订单</div>
					</div>
				</div>
			</div>
			<div class="shops inner">
				<div class="allChoose flex2">
					<div class="left flex1">
						<el-checkbox v-model="allChecked" class="fs14 bold c74">全选</el-checkbox>
						<div class="fs14 bold c74">课程</div>
					</div>
					<div class="right flex1">
						<span class="fs14 bold c74">金额</span>
						<span class="fs14 bold c74">操作</span>
					</div>
				</div>
				<el-checkbox-group v-model="checkList">
					<div class="content">
						<div v-for="(item, index) in carList" :key="index" class="temp flex2">
							<div class="tempLeft flex0">
								<el-checkbox :label="item.id" class="fs14 bold c74 check">1</el-checkbox>
								<img class="keImg" :src="item.coverImg" alt="" />
								<div class="word">
									<div class="ns">{{ item.name }}</div>
									<!-- <div class="study">1100学习</div> -->
								</div>
							</div>
							<div class="tempRight flex1">
								<div class="price">￥{{ item.currentPrice }}</div>
								<div class="oprate">
									<div class="item" @click="delCartGoodsFn(item.id)">删除</div>
									<!-- <div class="item">移入关注</div> -->
									<div class="item" @click="collectCourseFn(item.courseId)">加入收藏夹</div>
								</div>
							</div>
						</div>
					</div>
				</el-checkbox-group>
				<div v-show="carList.length == 0" class="enpty">空空如也~</div>
			</div>

			<div class="xuanze flex2 inner">
				<div class="xuanzeLeft flex1">
					<el-checkbox v-model="allChecked" class="fs14 bold c74">全选</el-checkbox>
					<div class="btn" @click="delCartGoodsFn(checkList)">删除选中商品</div>
					<!-- <div class="btn">移入关注</div> -->
					<!-- <div class="btn">移入收藏夹</div> -->
				</div>
				<div class="xuanzeRight flex1">
					<div class="item1">
						已选
						<span>{{ checkList.length }}</span>
						件商品
					</div>
					<div class="item1">
						商品总价(不含运费)
						<span>￥{{ totalPrice }}</span>
					</div>
				</div>
			</div>
			<div class="btns inner">
				<router-link to="/lawLectureHall/courseList">
					<div class="btn1 flex1 btn">
						<i class="iconfont icon-gouwuche1-copy-copy"></i>
						<span>继续购物</span>
					</div>
				</router-link>

				<div class="btn2 btn" @click="createOrderFn">生成订单 >></div>
			</div>
		</div>
	</div>
</template>
<script>
import ElementUI from 'element-eoss';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import config from '@/config';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			allChecked: false,
			carList: [],
			checkList: []
		};
	},
	computed: {
		totalPrice: function () {
			let arr = [];
			if (this.checkList.length == 0) return 0;

			for (let x of this.checkList) {
				for (let y of this.carList) {
					if (x == y.id) {
						arr.push(y);
					}
				}
			}
			return arr.reduce((prev, current) => {
				return Number(prev) + Number(current.currentPrice);
			}, 0);
		}
	},
	watch: {
		allChecked(val) {
			if (val) {
				this.checkList = this.carList.map(item => {
					return item.id;
				});
			} else {
				this.checkList = [];
			}
		},
		checkList(arr) {
			if (arr.length == this.carList.length) {
				this.allChecked = true;
			} else {
				this.allChecked = false;
			}
		}
	},
	async mounted() {
		if (!this._userinfo.id) {
			let queryString = '?';
			queryString += ['redirect', location.href].join('=');
			queryString = encodeURIComponent(queryString);
			localStorage.setItem(
				'scwl_homepage',
				`${config.domainUrl}${config.appList.valueAddedServices}`
			);
			window.location.href = `${config.domainUrl}scwl_user_main/#/login${queryString}`;
			return;
		} else {
			// if (!getLessonToken()) {
			// 	await this.loginFrontFn();
			// }
			this.getCartGoodsFn();
		}
	},
	methods: {
		// async loginFrontFn() {
		// 	const data = await this.$api.study_api.loginFront({
		// 		username: this._userinfo.username,
		// 		userId: this._userinfo.id,
		// 		nickname: this._userinfo.nickname
		// 	});
		// 	console.log('token', data.data.access_token);
		// 	setLessonToken(data.data.access_token);
		// },
		handleClick(tab, event) {
			console.log(tab, event);
		},
		handleSizeChange(val) {
			console.log(`每页 ${val} 条`);
		},
		handleCurrentChange(val) {
			console.log(`当前页: ${val}`);
		},
		async getCartGoodsFn() {
			const data = await this.$api.study_api.getCartGoods();
			console.log('查看购物车', data);
			this.carList = data.data || [];
		},
		delCartGoodsFn(ids) {
			let params;
			params = ids;
			if (ids instanceof Array) {
				if (ids.length == 0) {
					this.$message({
						type: 'warning',
						message: '请至少勾选一个课程'
					});
					return;
				}
				params = ids.join(',');
			}
			this.$confirm('确认将该商品删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.study_api.delCartGoods({ ids: params }).then(() => {
						this.getCartGoodsFn();
						this.$message({
							type: 'success',
							message: '删除成功!'
						});
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});

			// let arr = []
		},
		async collectCourseFn(courseId) {
			let data = await this.$api.study_api.collectCourse({ courseId, type: 1 });
			this.$message({
				type: 'success',
				message: data.msg
			});
		},
		async createOrderFn() {
			if (this.checkList.length == 0) {
				this.$message({
					type: 'warning',
					message: '请至少勾选一个课程'
				});
				return;
			}
			let orderList = [];
			for (let x of this.carList) {
				for (let y of this.checkList) {
					if (x.id == y) {
						orderList.push(x.courseId);
					}
				}
			}
			const data = await this.$api.study_api.createOrder({ courseIds: orderList.join(',') });
			if (data.code == 200) {
				ElementUI.Message({
					type: 'success',
					message: '订单生成成功！'
				});
				localStorage.setItem('orderInfo', JSON.stringify(data.data));
				setTimeout(() => {
					this.$router.push({ path: '/payment-center' });
				}, 3000);
			} else {
				ElementUI.Message({
					type: 'warning',
					message: '您已购买过或者有未支付订单'
				});
			}
		}
	}
};
</script>
<style scoped lang="scss">
.top {
	width: 100%;
	height: 40px;
	line-height: 40px;
	background: #ffffff;
	color: #999999;
	font-size: 14px;
	.top-a {
		width: 1200px;
		margin: 0 auto;
	}
	span {
		color: var(--brand-6, #0076e8);
	}
}
.inner {
	width: 1200px;
	margin: 0 auto;
}
.main {
	.mycar {
		margin-top: 20px;
		height: 200px;
		background: #ffffff;

		.mycarTop {
			height: 70px;
			padding-left: 23px;
			border-bottom: 1px solid #f2f2f2;
			.ns {
				font-size: 18px;
				font-weight: 400;
				color: #333333;
			}
			.total {
				margin-left: 13px;
				font-size: 14px;

				font-weight: 400;
				color: #999999;
				span {
					color: #ff0000;
				}
			}
		}
		.step {
			margin-top: 45px;
			justify-content: center;
			.stepItem {
				width: 200px;
				height: 9px;
				background: #e8eaf0;
				position: relative;

				.number {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					text-align: center;
					border-radius: 50%;
					line-height: 36px;
					height: 36px;
					width: 36px;
					font-size: 14px;
					background: #e8eaf0;
					color: #999;
				}
				.ns {
					text-align: center;

					margin-top: 30px;
				}
			}
			.stepItem:nth-child(1) {
				border-radius: 5px 0 0 5px;
				.number {
					background: var(--brand-6, #0076e8);
					font-size: 18px;
					font-weight: 400;
					height: 36px;
					width: 36px;
					color: #fff;
				}
			}
			.stepItem:nth-child(2) {
				border-radius: 0 5px 5px 0;
			}
		}
	}
	.shops {
		margin-top: 10px;
		.allChoose {
			height: 40px;
			background: #e8eaf0;
			padding: 0 20px;
			::v-deep .el-checkbox__input {
				border-radius: 50px !important;
			}
			.left {
				:nth-child(2) {
					margin-right: 70px;
				}
			}
			.right {
				:nth-child(2) {
					margin-left: 80px;
					margin-right: 20px;
				}
			}
		}
		.content {
			.temp {
				border-bottom: 1px solid #e8eaf0;
				padding: 20px;
				background: #fff;
				.tempLeft {
					.keImg {
						margin-left: 5px;
						width: 166px;
						height: 100px;
						border-radius: 6px;
					}
					.word {
						margin-left: 17px;
						.ns {
							padding-top: 10px;
							font-size: 16px;
							font-weight: 400;
							color: #333333;
							padding-bottom: 15px;
						}
						.study {
							font-size: 14px;
							font-weight: 400;
							color: #747d85;
						}
					}
				}
				.tempRight {
					.oprate {
						text-align: center;
						.item {
							cursor: pointer;
							color: #747d85;
							font-size: 14px;
							margin: 10px 0;
							font-weight: 400;
						}
					}
					.price {
						font-size: 14px;
						font-weight: 400;
						color: #747d85;
						width: 140px;
						text-align: center;
						margin-right: 0px;
					}
				}
			}
		}
	}
	.enpty {
		height: 180px;
		text-align: center;
		box-sizing: border-box;
		background: #fff;
		padding-top: 70px;
		color: #999999;
	}
	.xuanze {
		margin-top: 10px;
		height: 80px;
		background: #ffffff;
		padding: 0 20px;
		:nth-child(2) {
			color: #747d85;
		}
		.btn {
			font-size: 14px;
			cursor: pointer;
			font-weight: bold;
			color: #747d85;
			margin-left: 40px;
		}
		.xuanzeRight {
			.item1 {
				font-weight: bold;
				font-size: 14px;
				color: #747d85;
				margin-left: 20px;
				span {
					font-weight: bold;
					font-size: 16px;
					color: #ff0000;
				}
			}
		}
	}
	.btns {
		padding: 20px 0 50px 0;
		text-align: right;
		.btn {
			cursor: pointer;
			display: inline-block;
		}
		.btn1 {
			width: 100px;
			height: 40px;
			background: rgba(0, 118, 232, 0);
			border: 1px solid var(--brand-6, #0076e8);
			border-radius: 6px;
			text-align: center;
			color: var(--brand-6, #0076e8);
			box-sizing: border-box;
			line-height: 40px;
			span {
				font-size: 14px;
			}
		}
		.btn2 {
			margin-left: 20px;
			width: 100px;
			position: relative;
			bottom: 3px;
			line-height: 40px;
			text-align: center;
			height: 40px;
			background: var(--brand-6, #0076e8);
			border-radius: 6px;
			font-size: 14px;
			color: #fff;
		}
	}
}
.check {
	::v-deep.el-checkbox__label {
		display: none;
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 40px;
	}
}
</style>
