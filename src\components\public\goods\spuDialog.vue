<template>
	<Dialog
		:title="'添加服务商品'"
		:dialog-visible="spuDialogVisible"
		:width="'820px'"
		@dialogHandle="dialogHandle"
	>
		<Search
			v-model="searchFormData"
			:search-data="searchData"
			:form-bottom="formBottom"
			class="search"
		></Search>
		<el-table
			ref="table"
			v-loading="loading"
			:data="tableData"
			border
			style="width: 100%"
			@select="handleSelect"
			@select-all="handleSelect"
		>
			<el-table-column type="selection" width="55"></el-table-column>
			<el-table-column prop="proNo" label="产品编码" width="180"></el-table-column>
			<el-table-column
				prop="name"
				label="产品名称"
				width="180"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column prop="areaCodeName" label="城市" show-overflow-tooltip></el-table-column>
			<el-table-column
				prop="classCodesPathName"
				label="产品分类"
				show-overflow-tooltip
			></el-table-column>
			<!-- <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
        </template>
      </el-table-column> -->
		</el-table>
		<Pagination
			:current-page="pageData.page"
			:total="pageData.total"
			:page-size="pageData.limit"
			@paginationChange="handlePageChange"
		></Pagination>
		<template slot="footer">
			<div class="select">
				<!-- <el-button size="mini" @click="dialogHandle(9)">
          {{ `查看已选商品${selectSkuList.length}/1000` }}
        </el-button> -->
			</div>
			<div class="button">
				<el-button @click="dialogHandle">取消</el-button>
				<el-button type="primary" @click="dialogHandle">确定</el-button>
			</div>
		</template>
	</Dialog>
</template>

<script>
import Dialog from '../Dialog';
import Search from '../Search';
import Pagination from '@/components/public/Pagination';
import { coupon } from '@/api';
export default {
	name: 'SpuDialog',
	components: { Search, Dialog, Pagination },

	data() {
		return {
			tableData: [],
			skuList: [],
			couponId: '',
			actionIds: '',
			couponType: '',
			searchFormData: {
				productCodeName: '',
				productTypeCode: '',
				categoryCode: [],
				areaCodes: ''
			},
			selectList: [],
			selectSkuList: [],
			spuDialogVisible: false,
			loading: false,
			// 分页数据
			pageData: {
				page: 1,
				totalPage: 0,
				total: 0,
				limit: 10
			},
			type: '',
			searchData: [
				// {
				//   model: 'productCodeName',
				//   type: 'text',
				//   label: '产品编码：',
				//   placeholder: '请输入产品编码',
				//   width: 228,
				//   maxlength: 20
				// },
				{
					model: 'categoryCode',
					type: 'cascader',
					label: '产品分类：',
					placeholder: '请选择产品分类',
					width: 228,
					maxlength: 100,
					treeData: [],
					cascaderProps: {
						value: 'code',
						label: 'name',
						checkStrictly: true,
						children: 'childrenList'
					}
				},
				{
					model: 'productCodeName',
					type: 'text',
					label: '产品名称：',
					placeholder: '请输入产品名称',
					width: 228,
					maxlength: 20
				},
				{
					model: 'areaCodes',
					type: 'select',
					label: '产品城市：',
					placeholder: '请选择产品城市',
					width: 228,
					maxlength: 100,
					optionLabel: 'name',
					optionValue: 'code',
					options: []
				}
			],
			formBottom: [
				{
					// 自定义按钮
					type: 'button',
					text: '搜索',
					btnType: 'primary',
					handle: this.handleSearch
				},
				{
					// 自定义按钮
					type: 'button',
					text: '重置',
					btnType: '',
					handle: this.reset
				}
			]
		};
	},
	watch: {
		tableData: {
			handler(v) {
				if (v && v.length) {
					this.$nextTick(() => {
						this.selectList.forEach(row => {
							const index = this.tableData.findIndex(item => item.id === row.id);
							index > -1 && this.$refs.table.toggleRowSelection(this.tableData[index], true);
						});
					});
				}
			},
			deep: true
		}
	},
	methods: {
		openSpuDialog(val) {
			this.spuDialogVisible = true;
			this.reset();
			this.pageData.page = 1;
			this.selectList = JSON.parse(JSON.stringify(val));
			this.stie();
			// this.selectList = JSON.parse(JSON.stringify(val))
			this.classify().then(res => {
				this.spu();
			});
		},
		// table 事件
		handleClick(row) {
			if (this.type == 'activityProductList') {
				this.$parent.openDialog(row);
				return;
			}
			if (this.type == 'specialTopicOnline') {
				this.$parent.openDialog(row);
				return;
			}
			this.$refs.skuDialog.openSkuDialog(row);
		},
		selectSku(obj) {
			obj.productType = this.searchFormData.productTypeCode;
			this.$emit('selectSku', obj);
			this.spuDialogVisible = false;
		},
		// 弹窗事件
		dialogHandle(val) {
			this.spuDialogVisible = false;
			this.$emit('dialogHandle', this.selectList);
		},
		// 搜索
		handleSearch(searchFormData) {
			Object.assign(this.searchFormData, searchFormData);
			this.pageData.page = 1;
			this.spu();
		},
		// 清空
		reset() {
			Object.assign(this.searchFormData, this.$options.data().searchFormData);
		},
		//分页
		// 分页事件
		handlePageChange(e) {
			if (e.type === 1) {
				this.pageData.limit = e.value;
				this.pageData.page = 1;
			} else {
				this.pageData.page = e.value;
			}
			this.spu();
		},
		handleSelect(val, row) {
			this.tableData.forEach(row => {
				const exit = this.selectList.some(item => item.id === row.id);
				if (!exit && val.some(item => item.id === row.id)) {
					this.selectList.push(row);
				} else if (exit && !val.some(item => item.id === row.id)) {
					const index = this.selectList.findIndex(item => item.id === row.id);
					index > -1 && this.selectList.splice(index, 1);
				}
			});
		},
		// 获取城市列表
		async stie() {
			try {
				const data = await coupon.marketingCouponStie();
				this.searchData.forEach(item => {
					if (item.label === '产品城市：') {
						item.options = data.cityList;
						item.options.unshift(data.national);
					}
				});
				return data;
			} catch (error) {
				this.$message.close();
				this.$message({
					message: error?.message || '获取失败',
					type: 'fail'
				});
				return Promise.reject();
			}
		},
		spu() {
			this.loading = true;
			const searchFormData = JSON.parse(JSON.stringify(this.searchFormData)); // 拷贝对象

			if (searchFormData.categoryCode && searchFormData.categoryCode.length > 0) {
				searchFormData.categoryCode = searchFormData.categoryCode.pop();
			} else {
				searchFormData.categoryCode = '';
			}
			try {
				let val = {
					productCodeName: searchFormData.productCodeName,
					page: this.pageData.page,
					limit: this.pageData.limit,
					productTypeCode: searchFormData?.productTypeCode || null,
					categoryCode: searchFormData.categoryCode,
					areaCodes: searchFormData.areaCodes
				};
				coupon
					.spuList(val)
					.then(res => {
						if (res.code === 200) {
							this.tableData = res.data.records;
							this.pageData.limit = res.data.limit;
							this.pageData.page = res.data.currentPage;
							this.pageData.total = res.data.totalCount;
							this.loading = false;
							return;
						}
						throw new Error(res.message);
					})
					.catch(err => {
						this.$message.close();
						this.$message.warning(err.message);
						this.loading = false;
						// this.$message.error(err)
					});
			} catch (error) {
				this.$message.close();
				this.$message.warning(error.message);
				this.loading = false;
			}
		},
		// 获取分类数据
		async classify() {
			let val = {
				id: 1,
				nextLevel: 1
			};
			this.loading = true;
			try {
				const data = await coupon.marketingCouponProductCategory(val);
				data &&
					data.length &&
					data.forEach(item => {
						if (item.id == 1) {
							this.searchData.forEach(event => {
								if (event.label === '产品分类：') {
									event.treeData = item.childrenList;
									this.searchFormData.productTypeCode = item.productTypeCode; // 获取商品大类
								}
							});
						}
					});
				this.loading = false;
				return data;
			} catch (error) {
				this.$message.close();
				this.$message({
					message: error?.message || '获取失败',
					type: 'fail'
				});
				this.loading = false;
				return Promise.reject();
			}
		}
	}
};
</script>
<style lang="less" scoped>
.search {
	box-shadow: none !important;
	::v-deep.search-content {
		padding: 0px;
	}
}
::v-deep .el-table-column--selection .cell {
	padding-left: 10px;
	padding-right: 10px;
}
::v-deep .my-pagination {
	padding-top: 20px;
}
</style>
