<template>
	<div class="goods">
		<div>
			<a href="">
				<div class="dec">
					<div>
						<img src="@/assets/img/Group-1206.png" alt="" />
					</div>

					<div :style="{ color: fontColor }" class="text">
						<div class="title">农产品</div>
						<div class="text-dec">精选产品满减活动</div>
						<div class="link">查看更多</div>
					</div>
				</div>
			</a>
		</div>
		<div v-for="item in 9" :key="item">
			<a href="">
				<div class="item">
					<img src="" alt="" />
					<div class="title">潘婷氨基酸乳液修护洗发水1KG强发水1K发水1K韧秀发深层滋...</div>
					<div class="price">
						¥
						<span>92.98</span>
					</div>
				</div>
			</a>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Goods',
	props: {
		fontColor: {
			type: String,
			default: '#76BF6A'
		}
	},
	created() {
		console.log(this.fontColor);
	}
};
</script>

<style lang="scss" scoped>
.goods {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	background-color: #ffffff;
	.dec {
		width: 600px;
		height: 253px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		position: relative;
		.text {
			position: absolute;
			top: 0;
			left: 0;
			padding: 32px 20px;
			.title {
				font-size: 30px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				line-height: 44px;
			}
			.text-dec {
				font-size: 18px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				line-height: 32px;
			}
			.link {
				margin-top: 13px;
				width: 96px;
				height: 34px;
				background: #ffffff;
				border-radius: 6px 6px 6px 6px;
				opacity: 1;
				border: 1px solid;
				text-align: center;
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				line-height: 34px;
			}
		}
	}
	.item {
		width: 200px;
		height: 254px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #eeeeee;
		padding: 11px 20px 0;
		img {
			width: 160px;
			height: 160px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			overflow: hidden; //超出的文本隐藏

			text-overflow: ellipsis; //溢出用省略号显示

			display: -webkit-box;

			-webkit-line-clamp: 2; // 超出多少行

			-webkit-box-orient: vertical;
		}
		.price {
			font-size: 12px;
			font-family: Rany-Medium, Rany;
			font-weight: 500;
			color: #ca3f3b;
			line-height: 20px;
			span {
				font-size: 16px;
			}
		}
	}
}
</style>
