/*
 * @Description: 请求拦截
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-03-30 10:27:48
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-05-29 16:34:52
 */
import axios from 'axios';
import { getCookie } from '@/utils/auth';
import config from '@/config';
// import { baseUrl } from '@/config';
import { error, response } from '@/utils/response-handle';
import store from '@/store';

const getSiteId = () => {
	let siteId = '';
	siteId = getCookie('siteId');
	return siteId || config.siteId;
};
const baseURL = origin.includes('localhost') || origin.includes('10.40') ? '/dev-api' : '';
const service = axios.create({
	baseURL: baseURL,
	withCredentials: true,
	timeout: 30000
});

// 请求对象
service.interceptors.request.use(
	config => {
		console.log(config.url, config?.token);
		if (getSiteId()) {
			config.headers['siteId'] = getSiteId();
		}
		// 设置token
		if (getCookie('token')) {
			// config.headers['scswlcode'] = getCookie('token')
			// config.headers['code'] =  getCookie('token')
			config.headers['token'] = getCookie('token');
		}
		//子系统单点登录token
		if (config?.SSO_name) {
			config.headers['token'] = store.state.ssoToken[`SSO_${config?.SSO_name}`];
		}
		// 如果post请求使用FormData格式传参，处理参数和头部
		if (config.type === 'JSON') {
			config.headers['Content-Type'] = 'application/json; charset=utf-8';
		}
		if (config.type === 'form') {
			config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
			let data = config.data;
			let newData = new FormData();
			Object.keys(data).forEach(key => {
				newData.append(key, data[key]);
			});
			config.data = newData;
		} else if (config.data) {
			config.headers['Content-Type'] = 'application/json; charset=utf-8';
		}
		return config;
	},
	error => {
		console.log(error);
		return Promise.reject(error);
	}
);
// 响应对象
service.interceptors.response.use(response, error);

export default service;
