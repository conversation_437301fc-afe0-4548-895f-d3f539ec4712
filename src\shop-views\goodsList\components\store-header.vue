<template>
	<div class="StoreHeader">
		<div class="left">
			<div class="logo">
				<img v-if="baseInfo.SHOP_LOG" :src="$judgeFile(baseInfo.SHOP_LOG)" alt="" />
				<img v-else src="@/assets/shop-images/SHOP_LOG.png" alt="" />
			</div>
			<div class="name">{{ baseInfo.SHOP_NAME }}</div>
		</div>
		<div class="right">
			<el-button icon="el-icon-service" round @click="dialogMessageVisible = true">
				联系客服
			</el-button>
			<el-button round @click="goShop(baseInfo)">
				<AlIcon name="icon-31dianpu" size="16" color="rgba(0,0,0,.85)"></AlIcon>
				进入店铺
			</el-button>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="baseInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';

import contactMessage from '@/components/public/contactMessage.vue';
export default {
	name: 'StoreHeader',
	components: { contactMessage },
	props: {
		baseInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			dialogMessageVisible: false,
			input: '',
			list: [],
			shopInfo: {},
			userId: getCookie('user_id')
		};
	},
	created() {
		this.getInfo();
	},
	methods: {
		goShop(t) {
			this.$router.push(`/shopHomePage?id=${t.ID}`);
		},
		getInfo() {
			this.loading = true;
			let data = {
				userId: this.userId,
				shopId: this.baseInfo.ID
			};
			this.$api.shop_api.getShopSimpleInfo(data).then(res => {
				this.shopInfo = res?.result || '';
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.StoreHeader {
	width: 100%;
	height: 100px;
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	padding: 21px 16px;
	display: flex;
	justify-content: space-between;
	.left {
		display: flex;
		align-items: center;
		.logo {
			width: 50px;
			height: 50px;
			opacity: 1;
			border: 1px solid #cfd8ec;
			border-radius: 50%;
			margin-right: 10px;
			img {
				width: 50px;
				border-radius: 50%;
				height: 50px;
			}
		}
		.name {
			height: 50px;
			font-size: 18px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #000000;
			line-height: 50px;
			padding-right: 35px;
			overflow: hidden; //超出的文本隐藏
			text-overflow: ellipsis; //溢出用省略号显示
			display: -webkit-box;
			-webkit-line-clamp: 1; // 超出多少行
			-webkit-box-orient: vertical;
		}
	}
	.right {
		display: flex;
		align-items: center;
	}
}
::v-deep .el-button :hover {
	.icon-31dianpu {
		color: var(--brand-6, '#ca3f3b') !important;
	}
}
</style>
