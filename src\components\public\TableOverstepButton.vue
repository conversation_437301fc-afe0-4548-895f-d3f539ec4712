<template>
	<span class="table-popover">
		<span v-if="!newButtonArr.length" class="table-popover-button">
			<el-button
				v-for="(item, index) in buttonArr"
				:key="index"
				:type="item.btnType"
				:icon="item.icon"
				:disabled="item.disabled || false"
				@click="btnHandle(item.handle)"
			>
				{{ item.text }}
			</el-button>
		</span>
		<span v-else>
			<el-button
				:type="buttonArr[0].btnType"
				:icon="buttonArr[0].icon"
				:disabled="buttonArr[0].disabled || false"
				@click="btnHandle(buttonArr[0].handle)"
			>
				{{ buttonArr[0].text }}
			</el-button>
			<el-dropdown :hide-on-click="false" :trigger="trigger">
				<span class="el-dropdown-link">
					更多
					<i class="el-icon-arrow-down el-icon--right"></i>
				</span>
				<el-dropdown-menu slot="dropdown" class="topic-table-button">
					<el-dropdown-item v-for="(item, index) in newButtonArr" :key="index">
						<el-button
							:type="item.btnType"
							:icon="item.icon"
							style="width: 100%; text-align: left"
							:disabled="item.disabled || false"
							@click="btnHandle(item.handle)"
						>
							{{ item.text }}
						</el-button>
					</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</span>
	</span>
</template>

<script>
export default {
	name: '',
	components: {},
	props: {
		row: {
			// table 当前行数据
			type: Object,
			default: () => {}
		},
		buttonArr: {
			// 按钮数据
			type: Array,
			default: () => []
		},
		placement: {
			// popover 显示位置
			type: String,
			default: 'bottom'
		},
		trigger: {
			// popover 打开方式
			type: String,
			default: 'hover'
		}
	},
	data() {
		return {};
	},
	computed: {
		newButtonArr() {
			return this.buttonArr.length > 2 ? this.buttonArr.slice(1) : [];
		}
	},
	methods: {
		// 按钮点击事件
		btnHandle(handle) {
			handle(this.row);
		}
	}
};
</script>
<style lang="less" scoped>
.table-popover {
	.table-popover-button {
		button:last-child {
			margin-left: 13px;
		}
	}
}
.el-dropdown-link {
	cursor: pointer;
	color: #4974f5;
}
::v-deep .el-dropdown {
	margin-left: 10px;
	.el-icon-arrow-down {
		margin-left: 3px;
	}
}
.topic-table-button > li:not(li:nth-child(1)) > .el-button {
	color: #555555 !important;
}
</style>
