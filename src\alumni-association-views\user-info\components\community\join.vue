<!--
 @desc:个人中心 校友会 校友社区 我加入的社区
 @author: WH
 @date: 2023/9/14
 -->
<template>
	<div class="main">
		<div v-loading="loading" class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无社区',
					detail: '你还没有加入相关社区信息',
					clickText: ''
				}"
				@noDataFn="noDataFn"
			/>
			<div v-else-if="list.length !== 0 && !showDetail">
				<set-up-card
					v-for="(item, index) in list"
					:key="index"
					:card-data="item"
					@InviteFn="InviteFn"
					@clickCard="clickCard"
				/>
			</div>
			<div v-else>
				<svg
					t="1706757369916"
					class="icon"
					viewBox="0 0 1024 1024"
					version="1.1"
					xmlns="http://www.w3.org/2000/svg"
					p-id="35434"
					width="20"
					height="20"
					@click="showDetail = false"
				>
					<path
						d="M585.216 255.104H231.168l118.528-118.528a41.792 41.792 0 0 0 0-59.2 41.792 41.792 0 0 0-59.2 0L104.704 263.104c-8.96 8.96-12.8 20.992-12.032 32.64a41.472 41.472 0 0 0 12.032 32.768l185.664 185.664a41.728 41.728 0 0 0 59.264 0 41.792 41.792 0 0 0 0-59.2L233.472 338.88h346.432c146.304 0 269.568 115.2 272.896 261.504a267.84 267.84 0 0 1-267.52 273.728h-53.568l-1.92-0.128H170.304l-1.92 0.128a41.856 41.856 0 0 0-39.36 35.008v13.76c3.2 19.2 19.392 34.048 39.168 35.008h410.816c192.64 0 353.984-152.384 357.504-344.896a351.744 351.744 0 0 0-351.36-357.888z"
						fill="#0076e8"
						p-id="35435"
					></path>
				</svg>
				<join-detail :community-id="clickCommunityID" :community-data="clickCommunityData" />
			</div>
		</div>
		<!-- <div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div> -->

		<invite-dialog :visible.sync="InviteVisible" :community-id="clickCommunityID" />
	</div>
</template>

<script>
import SetUpCard from './set-up-card.vue';
import JoinDetail from './join-detail.vue';
import InviteDialog from './invite-dialog.vue';
import NoData from '../no-data.vue';
import { v4 as uuidv4 } from 'uuid';
export default {
	components: { SetUpCard, InviteDialog, JoinDetail, NoData },
	data() {
		return {
			clickCommunityID: '',
			dataCode: 'alumna_community_cover',
			uploadId: uuidv4(),
			showDetail: false,
			InviteVisible: false,
			formLoading: false,
			submitLoading: false,
			visible: false,
			formData: {
				communityName: '',
				capacityNum: '',
				communityLogo: '',
				labelids: [],
				introduce: ''
			},
			communityTags: [],
			rules: {
				communityName: [{ required: true, message: '请输入社区主题名称', trigger: 'blur' }],
				capacityNum: [{ required: true, message: '请输入社区容纳人数', trigger: 'blur' }],
				communityLogo: [{ required: true, message: '请上传社区封面图片', trigger: 'change' }]
			},
			file: '',
			list: [],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10
		};
	},

	mounted() {
		this.getMyJoinCommunity();
	},
	methods: {
		clickCard(cardData) {
			this.showDetail = true;
			this.clickCommunityID = cardData.id;
			this.clickCommunityData = cardData;
		},

		InviteFn() {
			this.clickCommunityID = this.InviteVisible = true;
		},
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					this.file = file;
					this.formData.communityLogo = file[0]?.adjunctId;
					// this.$set(this.formData, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('communityLogo');
				});
			}
		},

		//申请创建社区——————————————————————————————
		async setUpCommunity(data) {
			this.submitLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.setUpCommunity(data);
				if (rCode == 0) {
					this.$message.success(msg);
					this.formData = {};
					this.visible = false;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.error();
			} finally {
				this.submitLoading = false;
			}
		},
		//查询我加入的社区——————————————————————————————
		async getMyJoinCommunity() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.alumni_api.getMyJoinCommunity({
					pageNum: this.page,
					pageSize: this.size
				});
				if (rCode == 0) {
					this.list = results.records;
					this.total = results.total;
					console.log('>>>>>>>>>>>', results.records);
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.error();
			} finally {
				this.loading = false;
			}
		},

		educationSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					let labelids = [...this.formData.labelids].join(',');
					this.setUpCommunity({ ...this.formData, labelids, id: this.uploadId });
				}
			});
		},
		noDataFn() {},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.$emit('sizeChange', this.size);
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.$emit('pageChange', this.page);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		overflow: auto;
		position: relative;
		.icon {
			position: absolute;
			top: 0px;
			right: 20px;
			z-index: 6;
		}
		.btn {
			@include flexBox();
			width: 160px;
			height: 36px;
			border-radius: 4px;
			font-size: 14px;
			border: 1px solid #dcdfe6;
			color: #ffffff;
			background: #0076e8;
			cursor: pointer;
			i {
				margin-right: 8px;
				font-size: 18px;
			}
		}
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
