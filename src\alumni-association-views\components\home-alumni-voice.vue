<template>
	<div class="alumni-voice">
		<img
			class="pic"
			:src="getYbzyImg(alumnaVocalityList[0].coverImg)"
			alt=""
			@click="toDetail(alumnaVocalityList[0].id)"
		/>
		<div class="top" @click="toDetail(alumnaVocalityList[0].id)">
			<div class="topTitle">{{ alumnaVocalityList[0].title }}</div>
			<div class="textContent">
				{{ alumnaVocalityList[0].abstract }}
			</div>
		</div>
		<div class="bottom">
			<div
				v-for="(item, index) in alumnaVocalityList.slice(1, 8)"
				:key="index"
				class="newsItem"
				@click="toDetail(item.id)"
			>
				<i class="el-icon-menu iconColor"></i>
				<span class="newsItemContent">
					{{ item.title }}
				</span>
				<span class="time">{{ item.publishTime }}</span>
			</div>
		</div>
	</div>
</template>
<script>
import { baseUrl } from '@/config';
export default {
	props: {
		alumnaVocalityList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		// 跳转详情页面
		toDetail(id) {
			this.jumpPage(`alumni-news-detail?updateTitle=校友之声详情页&id=${id}&code=alumnaVocality`);
		},
		jumpPage(url) {
			this.$router.push(url);
		},
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.alumni-voice {
	width: 100%;
	height: 100%;
	overflow: hidden;
	.pic {
		width: 100%;
		height: 230px;
		object-fit: cover;
	}
	.top {
		padding: 0 0 20px 0;
		border-bottom: 1px solid #dcdcdc;
		cursor: pointer;
		.topTitle {
			height: 53px;
			line-height: 53px;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: #333333;
		}
		.textContent {
			height: 40px;
			padding: 0 0 20px 0;
			white-space: normal;
			text-overflow: ellipsis;
			overflow: hidden;
			display: -webkit-box;
			/* 限制在一个块元素显示的文本的行数 */
			/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
			-webkit-line-clamp: 2;
			/* 设置或检索伸缩盒对象的子元素的排列方式 */
			-webkit-box-orient: vertical;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #7a8392;
		}
	}
	.bottom {
		height: calc(100% - 348px);
		padding: 23px 0 0 0;
		.newsItem {
			cursor: pointer;
			margin: 0 0 15px 0;
			align-items: center;
			display: flex;
			height: 20px;
			line-height: 20px;
			.iconColor {
				color: #bfbfbf;
			}
			.newsItemContent {
				padding: 0 17px 0 10px;
				display: block;
				width: 293px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #7a8392;
			}
			.time {
				text-align: right;
				flex: 1;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #7a8392;
			}
		}
	}
}
</style>
