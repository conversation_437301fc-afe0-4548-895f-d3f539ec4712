<template>
	<el-skeleton v-loading="loading" :loading="loading" animated class="details">
		<template slot="template">
			<el-skeleton-item style="margin: 0 1vw; width: 100vw; height: 10vh" />
			<div style="display: flex">
				<el-skeleton-item style="margin: 0 1vw; width: 30vw; height: 100vh" />
				<el-skeleton-item style="margin: 0 1vw; width: 70vw; height: 100vh" />
			</div>
		</template>
		<template>
			<div v-loading="loading">
				<store-header ref="storeHeader" :base-info="info.baseInfo" />
				<goods-info
					ref="goodsInfo"
					:shop-type="shopType"
					:is-point-goods="isPointGoods"
					:base-info="info.baseInfo"
					:distribution-mode="info.distributionMode"
					:skus="info.skus"
					@updateDate="updateDate"
				/>
				<div style="display: flex">
					<GoodStuff
						v-if="shopType === 'scene'"
						v-loading="moreLoading"
						:shop-list="shopList"
						style="margin: 24px 20px 0 0"
					></GoodStuff>
					<div class="details-conten">
						<!-- 菜单切换 -->
						<div class="menu">
							<div
								v-if="shopType === 'hotel'"
								:class="{
									'menu-item': true,
									'menu-active': contentComponent == 'Room'
								}"
								@click="handleMenuClick('Room')"
							>
								<a href="javascript:void(0)">房型价格</a>
								<span></span>
							</div>
							<div
								:class="{
									'menu-item': true,
									'menu-active': contentComponent == 'Product'
								}"
								@click="handleMenuClick('Product')"
							>
								<a href="javascript:void(0)">
									{{ shopType === 'hotel' ? '酒店介绍' : '商品详情' }}
								</a>
								<span></span>
							</div>
							<div
								v-if="shopType === 'hotel'"
								:class="{
									'menu-item': true,
									'menu-active': contentComponent == 'Tool'
								}"
								@click="handleMenuClick('Tool')"
							>
								<a href="javascript:void(0)">酒店设施</a>
								<span></span>
							</div>
							<div
								:class="{
									'menu-item': true,
									'menu-active': contentComponent == 'Estimate'
								}"
								@click="handleMenuClick('Estimate')"
							>
								<a href="javascript:void(0)">
									{{ shopType === 'hotel' ? '用户评价' : '商品评价' }}
								</a>
								<span></span>
							</div>
						</div>
						<!-- 内容区域 -->
						<div id="content">
							<component :is="contentComponent" :info="info" @toBuy="toBuy"></component>
						</div>
					</div>
				</div>
			</div>
		</template>
		<sliderTools ref="sliderTools" :no-type="noType" @openKeFu="openKeFu"></sliderTools>
	</el-skeleton>
</template>

<script>
import GoodStuff from './components/good-stuff.vue';
import Product from './components/product.vue';
import Estimate from './components/estimate.vue';
import ProductMap from './components/product-map.vue';
import GoodsInfo from './components/goods-info.vue';
import storeHeader from './components/store-header.vue';
import Room from './components/room.vue';
import Tool from './components/tool.vue';
import sliderTools from '@/components/slider-tools';
export default {
	name: 'GoodsDetails',
	components: {
		Room,
		Tool,
		storeHeader,
		GoodsInfo,
		GoodStuff,
		Product,
		ProductMap,
		Estimate,
		sliderTools
	},
	data() {
		return {
			contentComponent: '',
			info: {},
			loading: false,
			moreLoading: false,
			shopType: '',
			userId: '',
			shopList: [],
			isPointGoods: false // 是否是积分商品
		};
	},
	computed: {
		noType() {
			// 只有特产才需要购物车
			return this.shopType === 'native' ? [] : ['shop'];
		}
	},
	// watch: {
	// 	'$route.query.id': {
	// 		handler(newVal, oldVal) {
	// 			//判断newVal有没有值监听路由变化
	// 			this.loading = true;
	// 			this.$api.shop_api.getAllProductDetail({ id: newVal }).then(res => {
	// 				this.info = res?.result || [];
	// 				this.loading = false;
	// 			});
	// 		},
	// 		deep: true
	// 	}
	// },
	created() {
		this.shopType = this.$route.query.type || '';
		this.contentComponent = this.shopType === 'hotel' ? 'Room' : 'Product';
		this.loading = true;
		this.isPointGoods = this.$route.query.goodsType === 'point' ? true : false;
		let id = this.$route.query?.id || '';
		let params = {
			id
		};
		// 积分商城的参数
		if (this.$route.query.type) {
			params.siteId = this.getSiteId();
			params.type = this.$route.query.type;
		}
		this.$api.shop_api.getAllProductDetail(params).then(res => {
			this.loading = false;
			this.info = res?.result || [];
			if (!res.state) {
				this.$message({
					message: '商品已下架',
					type: 'warning'
				});
			} else {
				this.getShopList();
			}
		});
	},
	methods: {
		/**刷新购物车数据*/
		updateDate() {
			this.$refs.sliderTools.getShopData();
		},
		/**打开客服*/
		openKeFu() {
			this.$refs.storeHeader.dialogMessageVisible = true;
		},
		/**去预定*/
		toBuy(id) {
			this.$refs.goodsInfo.toBuy(id);
		},
		/**精品好物*/
		getShopList() {
			let data = {
				offset: 0,
				psize: 5,
				shopId: this.info.baseInfo.ID
			};
			this.moreLoading = true;
			this.$api.shop_api.getShopTopProduct(data).then(res => {
				this.shopList = res.result || [];
				this.moreLoading = false;
			});
		},
		handleMenuClick(item) {
			this.contentComponent = item;
		}
	}
};
</script>

<style lang="scss" scoped>
.details {
	width: 1200px;
	margin: 0 auto;
	overflow: hidden;
	.details-conten {
		margin-top: 16px;
		margin-bottom: 60px;
		width: 100%;
		min-height: 600px;
		background: #fff;
		.menu {
			width: 100%;
			height: 70px;
			line-height: 70px;
			border: 1px solid #eeeeee;
			display: flex;
			padding: 0 39px;
			&-item {
				margin-right: 51px;
				position: relative;
				cursor: pointer;
				a {
					color: #262626;
				}
				&:hover {
					color: var(--brand-6, '#ca3f3b');
				}
				& > span {
					position: absolute;
					left: 0;
					bottom: -1px;
					display: none;
					width: 44px;
					height: 6px;
					background: var(--brand-6, '#ca3f3b');
					left: 50%;
					margin-left: -22px;
				}
			}
			&-active {
				a {
					color: var(--brand-6, '#ca3f3b');
				}
				& > span {
					display: inline-block;
				}
			}
		}
	}
}
</style>
