<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 取消报名弹窗
 @author: WH
 @date: 2023/9/15
 -->
<template>
	<el-dialog title="取消报名" :visible.sync="visible" :before-close="beforeClose">
		<el-form ref="form" :rules="rules" :model="form" label-width="140px">
			<el-form-item label="取消原因">
				<el-input v-model="form.title" type="textarea" :rows="6"></el-input>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" :loading="submitLoading" @click="submit">保 存</el-button>
			<el-button @click="beforeClose">取 消</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	props: {
		visible: {
			type: Boolean,
			required: true
		}
	},
	data() {
		return {
			submitLoading: false,
			dataCode: 'alumna_community_notice_attach',
			form: {},
			rules: {
				title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
				content: [{ required: true, message: '请输入正文', trigger: 'blur' }]
			}
		};
	},

	mounted() {},
	methods: {
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					this.file = file;
					this.formData.communityLogo = file[0]?.adjunctId;
					// this.$set(this.formData, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('communityLogo');
				});
			}
		},
		submit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.addInform();
				}
			});
		},
		beforeClose() {
			this.$emit('update:visible', false);
		},
		//申请创建社区——————————————————————————————
		async addInform(data) {
			this.submitLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.addInform(this.form);
				if (rCode == 0) {
					this.$message.success(msg);
					this.formData = {};
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.submitLoading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
ul {
	@include flexBox(space-evenly);
	width: 100%;
}
li {
	text-align: center;
	.img {
		width: 48px;
		height: 48px;
		padding: 6px;
		margin-bottom: 10px;
		background: #f3f5f7;
		img {
			width: 100%;
			height: 100%;
		}
	}
	p {
		font-size: 14px;
		color: #7a8392;
	}
}
</style>
