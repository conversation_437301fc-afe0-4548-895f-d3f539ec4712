<!--
 @desc:积分商城卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<img :src="$judgeFile(cardData.coverUrl)" alt="" />
		<div class="content-box">
			<p>{{ cardData.name }}</p>
			<p>{{ cardData.sellPoint }}&nbsp;积分</p>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					coverUrl: '',
					name: '燃起来燃起来,可以点燃火的宜宾燃面你吃过没',
					sellPoint: '500'
				};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'integral', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 300px;
	height: 290px;
	border-radius: 8px;
	cursor: pointer;
	margin-bottom: 20px;
	background: #ffffff;
	font-family: Microsoft YaHei;
	img {
		width: 300px;
		height: 180px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
	}
	.content-box {
		@include flexBox(space-between, flex-start);
		flex-direction: column;
		padding: 20px;
		height: calc(100% - 180px);
		p {
			font-size: 14px;
			color: #333333;
			line-height: 20px;
			&:nth-child(1) {
				width: 100%;
				overflow: hidden;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				text-overflow: ellipsis;
				-webkit-line-clamp: 2;
			}
			&:nth-child(2) {
				color: #ff0000;
			}
		}
	}
}
</style>
