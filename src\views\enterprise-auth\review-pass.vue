<template>
	<div>
		<div class="steps">
			<Steps :status="1"></Steps>
		</div>
		<div class="ir-content">
			<div class="ir-img"><i class="iconfont icon-check-circle-filled"></i></div>
			<div class="ir-status">认证通过</div>
			<div class="ir-info">尊敬的用户，您已认证成功，可开始交易</div>
		</div>
	</div>
</template>

<script>
import Steps from '@/components/auth-center/steps';

export default {
	name: 'ReviewPass',
	components: { Steps },
	data() {
		return {};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	methods: {}
};
</script>
<style lang="scss" scoped>
.ir {
	&-content {
		width: 633px;
		padding-top: 85px;
		padding-bottom: 111px;
		text-align: center;
		font-size: 16px;
		font-family: Source <PERSON> Sans SC-Regular, Source <PERSON> Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
		margin: 0 auto;
	}
	&-img i {
		font-size: 63px;
		color: #76bf6a;
	}
	&-status {
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 44px;
		margin: 13px 0 9px 0;
	}
	&-info {
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
	}
	&-im a {
		color: var(--brand-6, #0076e8);
	}
}
</style>
