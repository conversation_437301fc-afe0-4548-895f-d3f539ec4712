<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="content">
		<div class="header">
			<div class="indicate"></div>
			<div class="title">{{ active.name }}</div>
		</div>
		<div class="contentHtml" v-html="content"></div>
		<!-- 占位 -->
		<div class="placeholder"></div>
	</div>
</template>

<script>
export default {
	props: {
		active: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			content: '富文本',
			list: []
		};
	},
	watch: {
		active: {
			handler(val) {
				this.list.forEach(i => {
					if (i.title == val.name) {
						this.content = i.detail;
					}
				});
			},
			immediate: true,
			deep: true
		}
	},
	mounted() {
		this.getInformation('copyrightAgreement');
	},
	methods: {
		/**
		 * @description 获取列表
		 * */
		getInformation(nodeCode) {
			this.noticeLoading = true;
			this.loading = true;
			let data = {
				nodeCode,
				tenantId: this._userinfo.tenantId || this.$tenantId
			};
			this.$api.information_api.paging(data).then(res => {
				this.list = res.results.records;
				this.list.forEach(i => {
					if (i.title == this.active.name) {
						this.content = i.detail;
					}
				});
			});
		},

		toBottomInformation(code) {
			this.$router.push(`/bottomInformation?code=${code}`);
		}
	}
};
</script>

<style lang="scss" scoped>
.content {
	margin-left: 20px;
	width: 1140px;
	// background: #fff;
	.header {
		background: #ffffff;
		height: 50px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e8eaf0;
		.indicate {
			border-radius: 2px;
			margin: 0 20px;
			width: 4px;
			height: 15px;
			background: #0076e8;
		}
		.title {
			height: 50px;
			line-height: 50px;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #0076e8;
		}
	}
	.contentHtml {
		background: #ffffff;
		padding: 20px;
	}
	.placeholder {
		width: 100%;
		height: 50px;
	}
}
</style>
