<template>
	<div class="my-pagination" :style="{ 'text-align': fixed }">
		<el-pagination
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			:total="total"
			:page-sizes="pageSlecte"
			:page-size="pageSize"
			popper-class="popperName"
			:layout="layout"
		></el-pagination>
	</div>
</template>
<script>
export default {
	props: {
		// 分页组件位置 left center right
		fixed: {
			type: String,
			default: 'center'
		},
		// 当前页
		currentPage: {
			type: Number,
			default: 1
		},
		// 总条数
		total: {
			type: Number,
			default: 0
		},
		// 每页显示条数
		pageSize: {
			type: Number,
			default: 10
		},
		// 每页显示个数选择器的选项设置
		pageSlecte: {
			type: Array,
			default: () => {
				return [10, 20, 30, 40, 50, 100];
			}
		},
		// 布局
		layout: {
			type: String,
			default: 'total , prev,  pager, next, sizes, jumper'
		}
	},
	methods: {
		handleSizeChange(value) {
			this.$emit('paginationChange', {
				type: 1,
				value,
				description: `切换每页展示条数为${value}条`
			});
		},
		handleCurrentChange(value) {
			this.$emit('paginationChange', {
				type: 2,
				value,
				description: `选择了第${value}页`
			});
		}
	}
};
</script>
<style lang="scss">
.popperName {
	::v-deep .el-select-dropdown__item {
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #555555;
	}
}
</style>
<style lang="scss" scoped></style>
