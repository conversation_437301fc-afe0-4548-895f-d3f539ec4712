<template>
	<div class="list">
		<div class="left">
			<el-menu
				default-active="2"
				class="el-menu-vertical-demo"
				@open="handleOpen"
				@close="handleClose"
			>
				<el-submenu index="1">
					<template slot="title">
						<span>导航一</span>
					</template>
					<el-menu-item-group>
						<template slot="title">分组一</template>
						<el-menu-item index="1-1">选项1</el-menu-item>
						<el-menu-item index="1-2">选项2</el-menu-item>
					</el-menu-item-group>
					<el-menu-item-group title="分组2">
						<el-menu-item index="1-3">选项3</el-menu-item>
					</el-menu-item-group>
					<el-submenu index="1-4">
						<template slot="title">选项4</template>
						<el-menu-item index="1-4-1">选项1</el-menu-item>
					</el-submenu>
				</el-submenu>
				<el-menu-item index="2">
					<span slot="title">导航二</span>
				</el-menu-item>
				<el-menu-item index="3" disabled>
					<span slot="title">导航三</span>
				</el-menu-item>
				<el-menu-item index="4">
					<span slot="title">导航四</span>
				</el-menu-item>
			</el-menu>
		</div>
		<div class="right">
			<div class="right-list">
				<div v-for="item in 6" :key="item" class="right-list-item">
					<div class="logo"></div>
					<div class="title">流动资金贷款</div>
					<div class="dec">平安银行成都分行</div>
					<div class="interest">4.7%-7.5%</div>
					<div class="interest_text">参考年利率</div>
					<div class="flex">
						<div>
							<div class="price">0-200万</div>
							<div class="text">融资额度</div>
						</div>
						<el-divider direction="vertical"></el-divider>
						<div>
							<div class="price">1-12个月</div>
							<div class="text">融资期限</div>
						</div>
					</div>
					<div class="flex-content">
						<a href=""><div class="button">了解详情</div></a>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'FinancingList',
	methods: {
		handleOpen(key, keyPath) {
			console.log(key, keyPath);
		},
		handleClose(key, keyPath) {
			console.log(key, keyPath);
		}
	}
};
</script>
<style lang="scss" scoped>
.list {
	display: flex;
	justify-content: space-between;
}
.left {
	width: 240px;
	border-radius: 0px 0px 0px 0px;
	padding: 0 10px;
	background-color: #fff;
	::v-deep .el-menu-vertical-demo {
		width: 100%;
		border-right: none;
	}
}
.right {
	width: 940px;
	height: 660px;
	background: #f7f8fa;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	overflow-y: auto;
	&-list {
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 293px;
			height: 306px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			padding: 11px 25px 17px;
			margin: 11px 0px 0px 11px;
			opacity: 1;
			text-align: center;
			.logo {
				width: 122px;
				height: 54px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
			}
			.title {
				height: 24px;
				font-size: 18px;
				font-family: Source Han Sans SC-Bold, Source Han Sans SC;
				font-weight: bold;
				color: #262626;
				line-height: 24px;
			}
			.dec {
				height: 22px;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
			.interest {
				margin-top: 16px;
				height: 32px;
				font-size: 24px;
				font-family: Rany-Medium, Rany;
				font-weight: 500;
				color: #f2a665;
				line-height: 32px;
			}
			.interest_text {
				height: 18px;
				font-size: 12px;
				font-family: Noto Sans SC-Regular, Noto Sans SC;
				font-weight: 400;
				color: #404040;
				line-height: 18px;
			}
			.flex {
				margin-top: 20px;
				display: flex;
				justify-content: space-between;
				.price {
					height: 22px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #262626;
					line-height: 22px;
				}
				.text {
					height: 18px;
					font-size: 12px;
					font-family: Noto Sans SC-Regular, Noto Sans SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 18px;
				}
			}
			.flex-content {
				display: flex;
				justify-content: center;
				margin-top: 13px;
			}
			.button {
				width: 244px;
				height: 36px;
				background: #ca3f3b;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #ca3f3b;
				text-align: center;
				font-size: 16px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 32px;
			}
		}
	}
}
</style>
