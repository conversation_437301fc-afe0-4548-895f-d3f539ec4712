<template>
	<div class="login-form">
		<!--  表单左边  -->
		<!-- <div class="login-form-left">
		</div> -->
		<!--  表单右边  -->
		<div class="login-form-right">
			<!-- <div class="login-form-right-title">统一身份认证系统</div> -->
			<div class="login-form-right-tabs">
				<!-- <div class="login-form-right-tabs-tab" @click="type = 'phone'">
					<div class="text" :class="type === 'phone' ? 'select' : ''">手机号登录</div>
					<div :class="type === 'phone' ? 'selectLine' : ''" class="line"></div>
				</div> -->
				<div class="login-form-right-tabs-tab" @click="type = 'user'">
					<div class="text">密码重置</div>
					<div :class="'selectLine'" class="line"></div>
				</div>
			</div>
			<el-form ref="formRef" :model="resetForm" :rules="rules">
				<el-form-item prop="phone">
					<el-input
						v-model="resetForm.phone"
						placeholder="请输入手机号码"
						prefix-icon="el-icon-phone"
					></el-input>
				</el-form-item>
				<el-form-item prop="account">
					<el-input
						v-model="resetForm.account"
						placeholder="请输入教工号/学号"
						prefix-icon="el-icon-s-check"
					></el-input>
				</el-form-item>
				<el-form-item prop="idCard">
					<el-input
						v-model="resetForm.idCard"
						placeholder="请输入身份证号"
						prefix-icon="el-icon-s-check"
					></el-input>
				</el-form-item>
				<el-form-item prop="password">
					<el-input
						v-model="resetForm.password"
						show-password
						:minlength="8"
						:maxlength="16"
						placeholder="请输入新密码"
						prefix-icon="el-icon-lock"
					></el-input>
				</el-form-item>
				<el-form-item prop="isPassword" style="margin-bottom: 18px">
					<el-input
						v-model="resetForm.isPassword"
						show-password
						:minlength="8"
						:maxlength="16"
						placeholder="密码确认"
						prefix-icon="el-icon-lock"
					></el-input>
				</el-form-item>
			</el-form>
			<!-- <div class="password-tip">
				<div class="password-tip-item" :class="passwordLevel === 1 ? 'bad' : ''"></div>
				<div class="password-tip-item" :class="passwordLevel === 2 ? 'good' : ''"></div>
				<div class="password-tip-item" :class="passwordLevel === 3 ? 'best' : ''"></div>
			</div> -->
			<div class="login-form-right-code">
				<div class="code-input">
					<div class="login-form-right-input code-input-con">
						<!-- <img
							class="login-form-right-input-icon"
							src="@/assets/images/login/password-icon.png"
							alt=""right-input
						/> -->
						<svg
							class="login-form-right-input-icon"
							t="1719899627812"
							viewBox="0 0 1024 1024"
							version="1.1"
							xmlns="http://www.w3.org/2000/svg"
							p-id="22435"
							width="200"
							height="200"
						>
							<path
								d="M943.1 172c-2.4-0.2-245.1-25.3-413.8-147.8-5.1-3.7-11-5.6-17.3-5.6-6.2 0-12.2 1.9-17.3 5.6C326.9 146 83.3 171.8 80.9 172c-15.2 1.4-26.6 14.1-26.6 29.3 0 6.7 0.6 165.8 54.8 344.4 32.1 105.8 76.4 196.4 131.9 269.2 70.3 92.3 158.5 156 262 189.2 2.9 0.9 5.9 1.4 9 1.4s6.1-0.5 8.9-1.4c103.6-33.2 191.7-96.8 262-189.2 55.4-72.7 99.8-163.2 131.9-269.2 54.1-178.6 54.8-337.7 54.8-344.4C969.7 186.1 958.3 173.5 943.1 172zM910.1 227.2l-0.1 1.6c-2.9 58.1-13.4 174.4-51.4 299.9-66.7 220.1-183.1 360.1-346 416.1L512 945l-0.6-0.2C349 888.9 232.7 749.4 165.8 530.1c-39.8-130.5-49.4-254.2-51.8-301.4l-0.1-1.6 1.5-0.2c70.6-10.3 250.5-44.8 395.5-142.4l0.9-0.7 1 0.7C658 182.1 837.9 216.6 908.5 227L910.1 227.2z"
								p-id="22436"
								fill="#2778e5"
							></path>
							<path
								d="M641.8 351 467 580.3l-89-76.1c-5.3-4.5-12.1-7-19.1-7-8.6 0-16.8 3.7-22.4 10.3-10.5 12.3-9.1 31 3.3 41.5l112.7 96.4c5.2 4.4 12.4 7 19.6 7 0.9 0 1.8 0 2.7-0.1 8-0.8 15.4-5 20.3-11.4l193.7-254c4.8-6.3 6.8-14 5.7-21.8-1-7.8-5.1-14.7-11.3-19.5C670.1 335.6 651.6 338.1 641.8 351z"
								p-id="22437"
								fill="#2778e5"
							></path>
						</svg>
						<input
							v-model="verifyCode"
							maxlength="6"
							class="input code-input-con-i"
							placeholder="请输入短信验证码"
							type="text"
						/>
					</div>
					<div
						class="code-input-button"
						:style="
							userIsOk && time === 60
								? 'background: #0076e8;color: #ffffff'
								: 'background: #f6f7f9;color: #999999'
						"
						@click="sendCodeMethods"
					>
						{{ time === 60 ? '获取验证码' : time }}
					</div>
				</div>
			</div>
			<div class="login-button">
				<span style="cursor: pointer" @click="goLogin()">已有密码？去登陆</span>
			</div>
			<div class="login-form-right-button" @click="resetPassword">重置密码</div>
			<!-- <div class="login-form-right-tp">
				<div class="radio">
					<div v-if="!sureTp" class="radio-div" @click="changeSureTip"></div>
					<img
						v-else
						class="radio-icon"
						src="@/assets/images/login/sure-icon.png"
						alt=""
						@click="changeSureTip"
					/>
					<div v-if="showTip" class="radio-sure">请阅读并勾选</div>
					<div v-if="showTip" class="tooltip-icon"></div>
				</div>
				我已阅读并同意
				<div class="color" @click="openDialog('隐私政策', '7128298962619994112')">《隐私政策》</div>
				和
				<div class="color" @click="openDialog('用户服务协议', '7128299537076064256')">
					《用户服务协议》
				</div>
			</div> -->
		</div>
	</div>
</template>

<script>
import { debounce } from 'lodash';
import { Loading } from 'element-eoss';
import { baseUrl } from '@/config';
import { validPassWord } from '@/utils/validate';
import { codeNumber, changePassword } from '@/api/model/form-reset.js';
export default {
	name: 'Form',
	data() {
		/**校验密码*/
		const validatePassword = (rule, value, callback) => {
			// 判断密码弱中强
			// const complexityRegexes = [
			// 	// 弱：仅包含字母或数字，或长度小于等于8
			// 	{ regex: /^[a-zA-Z0-9]{0,8}$/, level: 1 },
			// 	// 中：包含大小写字母和数字，长度在9到13之间，但不含特殊字符
			// 	{ regex: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{9,13}$/, level: 2 },
			// 	// 强：包含大小写字母、数字以及至少一种特殊字符，长度大于等于14
			// 	{
			// 		regex: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+`\-={}:";'<>?,./]).{14,}$/,
			// 		level: 3
			// 	}
			// ];

			// // 按顺序检查密码与正则表达式匹配情况，返回第一个匹配到的强度等级
			// for (const { regex, level } of complexityRegexes) {
			// 	if (regex.test(value)) {
			// 		this.passwordLevel = level;
			// 		break;
			// 	}
			// }
			if (!validPassWord(value)) {
				callback(new Error('密码需为8~16位非空字符，并包含数字、大小写字母、特殊字符'));
			} else {
				callback();
			}
		};
		const validateAccount = (rule, value, callback) => {
			const regex = /^[A-Za-z0-9]+$/;
			if (!value) {
				callback(new Error('请输入学工号'));
			} else if (!regex.test(value)) {
				callback(new Error('学工号只能包含字母和数字'));
			} else if (value.length > 30) {
				callback(new Error('学工号不能超过30位'));
			} else {
				callback();
			}
		};
		const validateIsPassword = (rule, value, callback) => {
			if (value != this.resetForm.password) {
				callback(new Error('密码输入不一致'));
			} else {
				callback();
			}
		};
		// 校验手机号
		const validatePhone = (rule, value, callback) => {
			const reg = /^1[3-9]\d{9}$/;
			if (!value) {
				return callback(new Error('手机号不能为空'));
			} else if (!reg.test(value)) {
				callback(new Error('请输入正确的手机号'));
			} else {
				callback();
			}
		};
		// 身份证号校验规则
		const validateIdCard = (rule, value, callback) => {
			const regIdCard =
				/^[1-9]\d{5}(18|19|20|21|22)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|[Xx])$/;
			if (regIdCard.test(value)) {
				callback();
			} else {
				callback(new Error('请输入正确的身份证号码'));
			}
		};
		return {
			loading: null,
			verifyCode: '', //验证码
			passwordLevel: 0,
			resetForm: {}, //重置密码表单
			rules: {
				phone: [{ required: true, trigger: 'change', validator: validatePhone }],
				password: [{ required: true, trigger: 'change', validator: validatePassword }],
				isPassword: [{ required: true, trigger: 'blur', validator: validateIsPassword }],
				account: [{ required: true, trigger: 'blur', validator: validateAccount }],
				idCard: [{ required: true, trigger: 'change', validator: validateIdCard }]
			}, //校验规则g

			time: 60, // 默认60s
			userIsOk: true
		};
	},
	watch: {},
	methods: {
		/**已有密码，去登陆 */
		goLogin() {
			this.$router.push('/login');
		},
		/**重置密码*/
		resetPassword() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					const regex = /^\d{6}$/;
					if (!this.verifyCode) {
						this.$message.warning('请输入验证码');
						return;
					} else if (!regex.test(this.verifyCode)) {
						this.$message.warning('验证码必须位6位数字');
						return;
					}
					const params = {
						phone: this.resetForm.phone,
						idCard: this.resetForm.idCard,
						account: this.resetForm.account,
						password: btoa(this.resetForm.password),
						verifyCode: this.verifyCode
					};
					changePassword(params).then(res => {
						if (res.rCode === 0) {
							this.$message.success('重置成功');
							this.$router.push('/login');
						} else {
							this.$message.error(res.msg);
						}
					});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},
		/**发送验证码*/
		sendCodeMethods: debounce(function () {
			if (this.userIsOk) {
				this.loading = Loading.service({ text: '发送中...', background: 'transparent' });
				codeNumber({
					phone: this.resetForm.phone,
					idCard: this.resetForm.idCard,
					account: this.resetForm.account
				}).then(res => {
					this.loading.close();
					if (res.rCode === 0) {
						this.$message.success('发送成功!');
						this.time = 59;
						this.T = setInterval(() => {
							this.time -= 1;
							if (this.time === 0) {
								clearInterval(this.T);
								this.T = null;
								this.time = 60;
							}
						}, 500);
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}, 500),
		// loginServer和login不同跳转  isJump-是否走正常跳转逻辑
		toolJump(userInfo, isJump = false) {
			if (isJump) {
				// 正常跳转
				if (this.defalutreDirect == '/independentPersonal/vocational') {
					window.open(
						`${baseUrl}/project-ybzy/ybzy/index.html#/home?serverId=ybzyDtcSso&authType=6`,
						'_self'
					);
				}
				this.$router.push(this.redirect ? this.redirect : this.defalutreDirect);
			} else {
				// 走vpn跳转
				if (userInfo.service) {
					window.location.href = userInfo.service;
				}
			}
		}
	}
};
</script>

<style scoped lang="scss">
::v-deep .el-input__inner {
	border-radius: 20px;
	height: 42px;
}
::v-deep .el-input__icon {
	color: #2778e5;
	font-size: 18px;
	line-height: 42px;
}
.login-button {
	display: flex;
	justify-content: flex-end;
	font-size: 14px;
	color: #fff;
	margin-bottom: 20px;
}
.password-tip {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
	// margin-left: 107px;
	.password-tip-item {
		width: 100%;
		height: 5px;
		background: #ebecf0;
		margin-right: 3px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
	}
	.bad {
		background: #ff6552;
		color: #ffffff;
	}
	.good {
		background: var(--brand-6, #0076e8);
		color: #ffffff;
	}
	.best {
		background: #0bdc27;
		color: #ffffff;
	}
}
.login-form {
	background: rgba(0, 0, 0, 0.4);
	border-radius: 16px;
	display: flex;
	position: relative;
	// transform: translate(-30%, 0);
	// left: 25%;
	.login-header-left {
		position: absolute;
		// top: ;
		// width: 420px;
		height: 78px;
		top: -93px;
		// left: 50%;
		// transform: translate(-50%, 0);
	}
	&-left {
		width: 600px;
		flex-shrink: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: url('~@/assets/images/login/login-form-bg.png');
		background-size: cover;
		border-radius: 8px 0 0 8px;
		overflow: hidden;
		// margin-top: 135px;
		// .code {
		// 	width: 210px;
		// 	height: 236px;
		// 	margin-bottom: 23px;
		// }
		// .text {
		// 	font-size: 16px;
		// 	font-family: Microsoft YaHei;
		// 	font-weight: 400;
		// 	color: #ffffff;
		// 	line-height: 16px;
		// }
	}
	&-right {
		flex: 1;
		// background: rgba(0, 0, 0, 0.45);
		border-radius: 0 8px 8px 0;
		padding: 38px 30px 0;
		&-title {
			font-size: 30px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 30px;
			margin-bottom: 42px;
			text-align: center;
		}
		&-tabs {
			display: flex;
			// align-items: center;
			// justify-content: space-around;
			margin-bottom: 50px;
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			&-tab {
				display: flex;
				flex-direction: column;
				align-items: center;
				cursor: pointer;
				.text {
					font-size: 18px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #ffffff;
					line-height: 18px;
					margin-bottom: 16px;
				}
				.select {
					color: #8fc7ff;
				}
				.selectLine {
					background: #8fc7ff !important;
				}
				.line {
					width: 26px;
					height: 3px;
					background: red;
					border-radius: 2px;
				}
			}
		}
		&-input {
			width: 340px;
			height: 42px;
			background: #f6f7f9;
			border-radius: 20px;
			margin-bottom: 30px;
			padding: 10px 13px 10px 10px;
			display: flex;
			align-items: center;
			.input {
				flex: 1;
				border: none;
				outline: none;
				background: #f6f7f9;
				line-height: 30px;
				&::placeholder {
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #bbbbbb;
					line-height: 30px;
				}
			}
			&-icon {
				width: 16px;
				height: 16px;
				margin-right: 3px;
			}
			.password {
				cursor: pointer;
				width: 30px;
				height: 30px;
			}
		}
		&-code {
			.code-input {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				&-con {
					margin-bottom: 0;
					width: 225px;
					&-i {
						max-width: 160px;
					}
				}
				&-button {
					width: 120px;
					height: 50px;
					background: #f6f7f9;
					border-radius: 25px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #999999;
					line-height: 50px;
					text-align: center;
					margin-left: 15px;
					cursor: pointer;
				}
				&-text {
					font-size: 12px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #ffffff;
					line-height: 12px;
					text-align: center;
					//margin: 24px 0 15px;
					height: 49px;
					display: inline-block;
					width: 100%;
				}
			}
		}
		&-tip {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			//margin-bottom: 35px;
			margin-top: -10px;
			&-left {
				display: flex;
				align-items: center;
			}
			&-right {
				display: flex;
				align-items: center;
				cursor: pointer;
			}
		}
		&-button {
			width: 100%;
			height: 42px;
			// background: var(--brand-6, #0076e8);
			background: linear-gradient(90deg, #00e0ff 0%, #0175e8 100%);
			border-radius: 20px;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: #ffffff;
			line-height: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 20px;
			cursor: pointer;
		}
		&-tp {
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			display: flex;
			.color {
				color: #f4ff7c;
				cursor: pointer;
			}
		}
	}
	.radio {
		margin-right: 10px;
		width: 16px;
		height: 16px;
		cursor: pointer;
		position: relative;
		&-sure {
			position: absolute;
			top: -30px;
			border-radius: 3px;
			left: -4px;
			width: 96px;
			height: 20px;
			font-size: 12px;
			line-height: 20px;
			text-align: center;
			font-family: Microsoft YaHei;
			font-weight: 400;
			// color: #ff0705;
			// background: #fff1f1;
			background: rgba(0, 0, 0, 0.6);
			border: 1px solid #1fb2cf;
			color: #2adcff;
		}
		.tooltip-icon {
			position: absolute;
			top: -12px;
			left: 0;
			width: 16px;
			height: 16px;
			-webkit-clip-path: polygon(100% 0, 0 0, 50% 50%);
			clip-path: polygon(100% 0, 0 0, 50% 50%);
			// background: #fff1f1;
			background: rgba(0, 0, 0, 0.6);
			// border: 1px solid #1FB2CF;
		}
		&-div {
			width: 16px;
			height: 16px;
			background: #ffffff;
			border: 1px solid #dadada;
			border-radius: 50%;
		}
		&-icon {
			width: 16px;
			height: 16px;
		}
	}
}
.dialog-content {
	max-height: 50vh;
	overflow: auto;
}
</style>
