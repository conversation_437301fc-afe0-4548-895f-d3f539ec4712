<template>
	<div class="footer" :style="{ backgroundImage: `url(${bg})` }">
		<div class="footer-content">
			<!-- 友情连接 -->
			<div class="link-box">
				<img src="@/assets/images/layout/link-icon.png" class="link-img link-item" />
				<span
					v-for="(item, index) in aboutLink"
					:key="index"
					class="link-item"
					@click="opoenLink(item)"
				>
					{{ item.title }}
				</span>
				<!-- <span class="link-item">友情链接：中国政府网</span>
				<span class="link-item">中华人民共和国教育部</span>
				<span class="link-item">现代高等职业技术教育网</span>
				<span class="link-item">四川省教育厅</span>
				<span class="link-item">宜宾市人民政府</span>
				<span class="link-item">宜宾职业技术学院</span> -->
			</div>
			<!--  -->
			<div class="contact-box">
				<span class="contact-item" @click="toBottomInformation('about-us')">关于我们</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('user-agreement')">用户协议</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('disclaimer')">免责声明</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('privacy-policy')">隐私政策</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('copyright-notice')">版权声明</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('contact-us')">联系我们</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('help-center')">帮助中心</span>
				<span class="item-space"></span>
				<span class="contact-item" @click="toBottomInformation('website-map')">网站地图</span>
			</div>
			<!-- Service Hotline -->
			<div class="service-box">
				<img src="@/assets/images/layout/service-icon.png" class="service-hotline" />
				<div class="service-right">
					<span class="service-title">Service Hotline</span>
					<span class="service-phone">0831-8275466</span>
				</div>
			</div>
			<!--  -->
			<div class="bottomm-box">
				<p class="bottomm-item">
					<span class="marginRight22">Copyright</span>
					<span>2020-2023 ybzy.cn, All Rights Reserved.</span>
				</p>
				<p class="bottomm-item">
					<span class="marginRight38">工业和信息化部备案登记号：蜀ICP备05003421号</span>
					<span class="marginRight7">
						<img src="@/assets/images/layout/police.png" class="police-img" />
						川公安网备 51150202000048号
					</span>
					<span>技术支持：宜宾三江电子信息产业有限公司</span>
				</p>
				<p><span>建议使用Chrome、Mozilla Firefox,1366*768以上分辨率浏览平台</span></p>
			</div>
			<div class="qrcode-box">
				<div class="qrcode-item">
					<img
						src="@/assets/images/layout/qrcode-l.png"
						class="qrcode-img"
						alt="技状元数字化校园移动端"
					/>
					<!-- <span class="qrcode-text">技状元数字化校园APP</span> -->
				</div>
				<!-- <div class="qrcode-item">
					<img src="@/assets/images/layout/qrcode-r.png" class="qrcode-img" />
					<span class="qrcode-text">技状元数字化校园公众号</span>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			bg: require('@/assets/images/layout/footer-bg.png'),
			aboutLink: [] //友情链接
		};
	},
	mounted() {
		this.getInformation('friendLink');
	},
	methods: {
		opoenLink(i) {
			window.open(i.linkUrl);
		}, //友情链接跳转

		/**
		 * @description 获取列表
		 * */
		getInformation(nodeCode) {
			this.noticeLoading = true;
			this.loading = true;
			let data = {
				nodeCode,
				tenantId: this._userinfo.tenantId || this.$tenantId
			};
			this.$api.information_api.paging(data).then(res => {
				this.aboutLink = res.results.records;
			});
		},

		toBottomInformation(code) {
			this.$router.push(`/bottomInformation?code=${code}`);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
* {
	padding: 0;
	margin: 0;
}
.footer {
	width: 100%;
	height: 370px;
	// border-top: 8px solid #3274e0;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	&-content {
		width: $max-width;
		height: 100%;
		margin: 0 auto;
		padding: 53px 0 65px;
		position: relative;
	}
	.link-box {
		cursor: pointer;
		border-bottom: 1px solid rgba(255, 255, 255, 0.2);
		height: 56px;
		.link-img {
			width: 29px;
			height: 26px;
			vertical-align: bottom;
		}
		.link-item {
			font-size: 14px;
			font-weight: 500;
			color: #ffffff;
			line-height: 22px;
			margin-right: 22px;
		}
	}
	.contact-box {
		margin-top: 26px;
		display: flex;
		align-items: center;
		.contact-item {
			cursor: pointer;
			font-size: 14px;
			font-weight: 500;
			color: #ffffff;
			line-height: 22px;
		}
		.item-space {
			height: 12px;
			width: 0px;
			border: 1px solid rgba(255, 255, 255, 0.2);
			margin: 0 26px;
		}
	}
	.service-box {
		display: flex;
		align-items: center;
		margin-top: 20px;
		.service-hotline {
			width: 41px;
			height: 41px;
			margin-right: 8px;
		}
		.service-title {
			font-size: 14px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: rgba(255, 255, 255, 0.3);
			line-height: 22px;
			display: block;
		}
		.service-phone {
			font-size: 24px;
			font-family: Catamaran-Regular, Catamaran;
			font-weight: 400;
			color: #ffffff;
			line-height: 22px;
		}
	}
	.bottomm-box {
		font-size: 14px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 0.3);
		line-height: 22px;
		margin-top: 14px;
	}
	.bottomm-item {
		margin-bottom: 3px;
	}
	.police-img {
		width: 18px;
		height: 18px;
		vertical-align: text-top;
	}
	.marginRight7 {
		margin-right: 7px;
	}
	.marginRight22 {
		margin-right: 22px;
	}
	.marginRight38 {
		margin-right: 38px;
	}
	.qrcode-box {
		font-size: 14px;
		font-family: MicrosoftYaHei;
		font-weight: 400;
		color: #ffffff;
		display: flex;
		align-items: center;
		position: absolute;
		top: 150px;
		right: 0;
		.qrcode-item {
			text-align: center;
			margin-left: 30px;
		}
		.qrcode-img {
			width: 130px;
			height: 130px;
			margin-bottom: 11px;
		}
		.qrcode-text {
			display: block;
		}
	}
}
</style>
