<template>
	<div v-loading="loading" class="order_table">
		<div class="th">
			<div class="td">商品</div>
			<div class="td">单价</div>
			<div class="td">实付款</div>
			<div class="td">支付渠道</div>
			<div class="td">交易状态</div>
			<div class="td">操作</div>
		</div>
		<div
			v-for="(item, index) of table"
			:key="index"
			class="list"
			:class="index === 0 ? 'last' : ''"
		>
			<div class="data">
				<div class="cell">
					<div class="lf">订单号：</div>
					<div class="rf">{{ item.ORDER_ID }}</div>
				</div>
				<div class="cell">
					<div class="lf">类型：</div>
					<div class="rf">{{ item.regTypeStr }}</div>
				</div>
				<div class="cell">
					<div class="lf">下单时间：</div>
					<div class="rf">{{ item.ORDER_TIME }}</div>
				</div>
				<div class="msg" @click="openMsg(item)">
					<svg
						t="1685929424736"
						class="icon"
						viewBox="0 0 1024 1024"
						version="1.1"
						xmlns="http://www.w3.org/2000/svg"
						p-id="3984"
						width="200"
						height="200"
					>
						<path
							d="M773.592949 798.845831c92.576542-51.429966 153.877695-140.240271 153.877695-241.381967 0-63.965288-24.779932-122.862644-66.199864-170.673898a387.562305 387.562305 0 0 0 4.647051-56.840678c0-8.829831-0.976271-17.442712-1.562034-26.142373 76.643797 62.863186 124.667661 151.430508 124.667661 249.869017 0 110.409763-60.342237 208.453424-153.882034 271.620339v172.053695l-172.509288-104.695322a463.589966 463.589966 0 0 1-73.697628 6.104949c-118.510644 0-193.874441-44.691525-267.138169-115.317152 12.058034 0.685559 24.055322 1.549017 36.321627 1.549017 17.182373 0 34.130441-0.837424 50.904949-2.169492 57.842983 39.233085 100.200136 62.841492 179.911593 62.841492a394.543729 394.543729 0 0 0 79.620339-8.352543l105.033763 69.020204v-107.485288z m-338.536135-74.517695a913.464407 913.464407 0 0 1-103.515119-7.992407L158.073492 815.338305v-172.045017c-80.414373-67.479864-123.105627-161.219254-123.105628-271.620339 0-190.576814 178.818169-345.070644 400.093289-345.070644 183.942508 0 369.308203 154.493831 369.308203 345.070644s-148.349831 352.655186-369.312542 352.655187z m-110.401085-69.024543c25.6 5.258847 82.926644 8.348203 110.405424 8.348204 186.96678 0 307.75539-129.019661 307.755389-288.190916s-154.719458-288.190915-307.755389-288.190915c-192.256 0-338.536136 129.032678-338.536136 288.190915 0 101.128678 48.023864 181.308746 123.101288 241.36895v107.498305z m279.669152-234.335457a45.507254 45.507254 0 1 1 46.16678-45.507255 45.837017 45.837017 0 0 1-46.16678 45.507255z m-184.654101 0a45.507254 45.507254 0 1 1 46.16244-45.507255A45.841356 45.841356 0 0 1 419.67078 420.968136z m-184.658441 0a45.507254 45.507254 0 1 1 46.16678-45.507255 45.837017 45.837017 0 0 1-46.16678 45.507255z"
							fill="#8C8C8C"
							p-id="3985"
						></path>
					</svg>
					<span>联系商家</span>
				</div>
				<div v-if="item.jr" class="right">{{ item.jr.status | statusName }}</div>
			</div>
			<div class="tr">
				<div class="td1">
					<div
						v-for="(item1, i) in item.ORDER_GOODS_DETAILS"
						:key="i"
						class="box"
						:style="{ borderTop: i != 0 ? `1px solid #eee` : 'none' }"
					>
						<img :src="$judgeFile(item1.COVER_URL)" alt="" class="lf" />
						<div class="rf">
							<div class="title nth2">
								{{ item1.NAME }}
							</div>
							<div class="money">商品数量：{{ item1.NUMBER }}</div>
						</div>
					</div>
				</div>
				<div class="td2">
					<div
						v-for="(item1, i) in item.ORDER_GOODS_DETAILS"
						:key="i"
						class="box"
						:style="{ borderTop: i != 0 ? `1px solid #eee` : 'none' }"
					>
						商品单价： ¥{{ Number(item1.PRICE).toFixed(2) }}
					</div>
				</div>
				<div
					class="td3"
					:style="{
						height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px'
					}"
				>
					<div :style="{ lineHeight: getHeight(item.ORDER_GOODS_DETAILS.length) / 1.2 + 'px' }">
						商品总额：
						<span :style="{ 'text-decoration': item.ORDER_POINT ? 'line-through' : 'none' }">
							¥{{ Number(item.ORDER_MONEY).toFixed(2) }}
						</span>
						<span v-if="item.ORDER_POINT">（{{ item.ORDER_POINT }}积分）</span>
					</div>
					<div>
						实付金额：¥{{ (item.ACTUAL_MONEY && Number(item.ACTUAL_MONEY).toFixed(2)) || '0.00' }}
					</div>
				</div>
				<div class="td4" :style="{ height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px' }">
					<div
						class="state"
						:style="{ lineHeight: getHeight(item.ORDER_GOODS_DETAILS.length) / 1.2 + 'px' }"
					>
						{{ item.payModeStr }}
					</div>
					<div v-if="item.rz" class="btn" @click="goDeta(item.rz)">
						{{ item.rz.orderStateName }}>
					</div>
				</div>
				<div class="td5" :style="{ height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px' }">
					<div
						class="state"
						:style="{ lineHeight: getHeight(item.ORDER_GOODS_DETAILS.length) / 1.2 + 'px' }"
					>
						{{ item.orderStateStr }}
					</div>
					<div
						class="btn"
						@click="
							skip(
								`/orderDetails?type=1&shopType=${item.REG_CODE}&id=${item.ORDER_ID}${
									isRefund ? '&isrefund=1' : ''
								}${$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
									$route.query.url ? '&url=' + $route.query.url : ''
								}`
							)
						"
					>
						订单详情>
					</div>
				</div>
				<div class="td6">
					<el-button
						v-if="item.ORDER_STATE == 101 && item.PAY_MODE != 9 && item.PAY_MODE != 8"
						size="mini"
						type="primary"
						plain
						@click="
							$router.push({
								path: `/settleAccounts?orderid=${item.ORDER_ID}${
									$route.query.isLaw == 1 ? '&isLaw=1' : ''
								}${$route.query.url ? '&url=' + $route.query.url : ''}`
							})
						"
					>
						立即付款
					</el-button>
					<el-button
						v-if="item.CAN_REFUND"
						size="mini"
						type="primary"
						plain
						@click="
							$router.push({
								path: `/drawback?id=${item.ORDER_ID}${$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
									$route.query.url ? '&url=' + $route.query.url : ''
								}`
							})
						"
					>
						申请退款
					</el-button>
					<el-button
						v-if="item.ORDER_STATE == 202"
						size="mini"
						type="primary"
						plain
						@click="confirmReceipt(item)"
					>
						{{ item.type == 3 ? '服务结束' : '确认收货' }}
					</el-button>
					<div
						v-if="
							item.addressInfo &&
							item.addressInfo.mode == 1 &&
							item.updateAddress &&
							item.PAY_MODE != 8
						"
						class="btn"
						@click="editAddr(item)"
					>
						修改自提地址
					</div>
					<div
						v-if="
							item.addressInfo &&
							item.addressInfo.mode != 1 &&
							item.updateAddress &&
							item.PAY_MODE != 8
						"
						class="btn"
						@click="editAddr(item)"
					>
						修改地址
					</div>
					<el-button
						v-if="item.ORDER_STATE == 101"
						size="mini"
						type="primary"
						plain
						@click="cancelOrder(item)"
					>
						取消订单
					</el-button>
					<div v-if="item.applyBill" class="btn" @click="saveBillApply(item)">申请开票</div>
					<el-button
						v-if="item.ORDER_STATE == 302 || item.ORDER_STATE == 303"
						size="mini"
						type="primary"
						plain
						@click="
							$router.push({
								path: `/appraise?id=${item.ORDER_ID}${$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
									$route.query.url ? '&url=' + $route.query.url : ''
								}`
							})
						"
					>
						立即评价
					</el-button>
					<el-button v-if="item.jr" size="mini" type="primary" plain @click="handleContract(item)">
						查看/签署合同
					</el-button>
				</div>
			</div>
			<div v-if="item.addressInfo" class="data">
				<div class="cell">
					<div class="lf">收货人：</div>
					<div class="rf">{{ item.addressInfo.recipients }}</div>
				</div>
				<div class="cell">
					<div class="lf">手机号：</div>
					<div class="rf">{{ item.addressInfo.mobilePhone }}</div>
				</div>
				<div class="cell">
					<div class="lf">{{ item.addressInfo.mode == 2 ? '收货' : '自提' }}地址：</div>
					<div class="rf">{{ item.addressInfo.detailAddress }}</div>
				</div>
			</div>
		</div>
		<el-empty v-if="table.length == 0" description="暂无数据"></el-empty>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="info"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import contactMessage from '@/components/public/contactMessage.vue';
import config from '@/config';
import { getCookie } from '@/utils/auth';
export default {
	name: 'OrderTable',
	components: {
		contactMessage
	},
	filters: {
		statusName(i) {
			if (i == 0) {
				return '合同待签订';
			} else if (i == 1) {
				return '合同待生效';
			} else if (i == 2) {
				return '合同已生效';
			} else {
				return '-';
			}
		},
		orderType(value) {
			if (value == 101) {
				return '待付款';
			} else if (value == 102) {
				return '待预约';
			} else if (value == 200) {
				return '待确认';
			} else if (value == 201) {
				return '待发货';
			} else if (value == 202) {
				return '待收货';
			} else if (value == 203) {
				return '已确认';
			} else if (value == 301) {
				return '消费中';
			} else if (value == 302) {
				return '已使用';
			} else if (value == 303) {
				return '已收货';
			} else if (value == 304) {
				return '已评价';
			} else {
				return '订单异常';
			}
		}
	},
	props: {
		table: {
			type: Array,
			default: () => {
				return [];
			}
		},
		total: {
			type: Number,
			default: 0
		},
		isRefund: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			main: window.__POWERED_BY_WUJIE__,
			dialogMessageVisible: false,
			info: {}
		};
	},
	created() {
		this.userId = this.isShopLogin();
	},
	methods: {
		goDeta(i) {
			if (i.orderState == 4) {
				window.open(
					`${config.domainUrl}${
						config.appList.financeService
					}/#/product/release?redirect=${encodeURIComponent(window.location.href)}&orderId=${i.id}`
				);
			} else {
				let url = config.url.replace(/\./g, '/');
				if (this.main) {
					top.window.location.href = `${config.domainUrl}${config.appList.userCenter}?scwl_main_view=%2Fuser_center_financial_services%2F%23%2Forder%2Fdetail%3Fid%3D${i.id}#/${url}/user_center_financial_services/order/index`;
				} else {
					window.open(
						`${config.domainUrl}${config.appList.userCenter}?scwl_main_view=%2Fuser_center_financial_services%2F%23%2Forder%2Fdetail%3Fid%3D${i.id}#/${url}/user_center_financial_services/order/index`
					);
				}
			}
		},
		openMsg(i) {
			this.info = {
				SHOP_LOG: i.SHOP_LOG,
				SHOP_NAME: i.SHOP_NAME,
				SELLER_ID: i.SELL_ID,
				isGoods: true
			};
			this.dialogMessageVisible = true;
		},
		handleContract(i) {
			this.$router.push({
				path: `contract_details?id=${i.jr.instanceId}&enterpriseId=${getCookie('enterpriseId')}`
			});
		},
		// 申请开票
		saveBillApply(i) {
			this.$emit('billApply', i);
		},
		// 确认收货
		confirmReceipt(item) {
			this.$confirm('我已收到商品，确认收货？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					const res = await this.$api.shop_api.confirmReceipt({
						orderId: item.ORDER_ID,
						userId: this.userId
					});
					if (res.state) {
						this.$message.close();
						this.$message({
							message: res.msg || '收货成功',
							type: 'success'
						});
						this.$emit('confirmReceipt');
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
					this.loading = false;
				})
				.catch(() => {});
		},
		// 取消订单
		cancelOrder(item) {
			this.$confirm('确认要取消该订单吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					const res = await this.$api.shop_api.userCancelOrder({
						orderId: item.ORDER_ID,
						userId: this.userId
					});
					if (res.state) {
						this.$message.close();
						this.$message({
							message: res.msg || '取消成功',
							type: 'success'
						});
						this.$emit('cancelOrder');
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
					this.loading = false;
				})
				.catch(() => {});
		},
		getHeight(i) {
			return i * 100;
		},
		// 跳转
		skip(url) {
			this.$router.push({
				path: url
			});
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.$emit('sizeChange', this.size);
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.$emit('pageChange', this.page);
		},
		// 修改地址
		editAddr(i) {
			this.$emit('editAddr', i);
		}
	}
};
</script>

<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.order_table {
	.th {
		display: flex;
		height: 40px;
		align-items: center;
		background: #f4f4f4;
		top: 0;
		.td {
			font-size: 14px;
			font-weight: 500;
			color: #8c8c8c;
			padding-left: 15px;
			box-sizing: border-box;
		}
		.td:nth-child(1) {
			width: 238px;
		}
		.td:nth-child(2) {
			width: 177px;
		}
		.td:nth-child(3) {
			width: 224px;
		}
		.td:nth-child(4) {
			width: 98px;
		}
		.td:nth-child(5) {
			width: 95px;
		}
		.td:nth-child(6) {
			width: 80px;
		}
	}
	.list {
		border: 1px solid #eeeeee;
		margin-top: 16px;
	}
	.last {
		margin-top: 12px;
	}
	.data {
		height: 40px;
		background: #f6f6f6;
		display: flex;
		align-items: center;
		border-top: 1px solid #eeeeee;
		.cell {
			display: flex;
			align-items: center;
			padding-left: 13px;
			margin-right: 25px;
			.lf {
				font-size: 14px;
				font-weight: 400;
				color: #8c8c8c;
			}
			.rf {
				font-size: 14px;
				font-weight: 400;
				color: #404040;
			}
		}
		.msg {
			cursor: pointer;
			margin: 0 20px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			display: flex;
			align-items: center;
			.icon {
				width: 20px;
				height: 21px;
				margin-right: 5px;
			}
		}
		.right {
			margin: 0 12px 0 auto;
			font-size: 14px;
			font-weight: 400;
			color: #f2a665;
			line-height: 22px;
		}
	}
	.tr {
		display: flex;
		align-items: center;
		border-top: 1px solid #eeeeee;
		.td1 {
			width: 294px;
			border-right: 1px solid #eeeeee;
			.box {
				height: 100px;
				display: flex;
				align-items: center;
				box-sizing: border-box;
				padding: 0 13px 0 11px;
			}
			.lf {
				width: 72px;
				height: 70px;
				object-fit: contain;
			}
			.rf {
				margin-left: auto;
				width: calc(100% - 78px);
				.title {
					font-size: 16px;
					font-weight: 400;
					color: #404040;
					line-height: 24px;
				}
				.money {
					margin-top: 6px;
					font-size: 14px;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 22px;
				}
			}
		}
		.td2 {
			width: 220px;
			height: auto;
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			border-right: 1px solid #eeeeee;
			.box {
				padding: 0 13px 0 11px;
				box-sizing: border-box;
				height: 100px;
				line-height: 100px;
			}
		}
		.td3 {
			min-width: 165px;
			padding: 0 13px 0 11px;
			align-items: center;
			border-right: 1px solid #eeeeee;
			box-sizing: border-box;
			height: 100%;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			div {
				width: 100%;
				height: 50%;
				line-height: 100%;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
				margin: 0 auto;
				margin-bottom: 8px;
			}
		}
		.td4 {
			width: 116px;
			padding: 0 13px 0 11px;
			border-right: 1px solid #eeeeee;
			box-sizing: border-box;
			height: 100%;
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			.state {
				height: 50%;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				text-align: center;
			}
			.btn {
				margin-top: 8px;
				font-size: 14px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
				text-align: center;
			}
		}
		.td5 {
			width: 110px;
			padding: 0 13px 0 11px;
			border-right: 1px solid #eeeeee;
			box-sizing: border-box;
			height: 100%;
			.state {
				height: 50%;
				font-size: 14px;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 22px;
				text-align: center;
			}
			.btn {
				margin-top: 8px;
				font-size: 14px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
				text-align: center;
			}
		}
		.td6 {
			width: 104px;
			padding: 0 13px 0 11px;
			text-align: center;
			display: flex;
			align-items: baseline;
			flex-wrap: wrap;
			justify-content: center;
			.btn {
				margin-top: 8px;
				font-size: 12px;
				font-weight: 400;
				color: #404040;
				line-height: 20px;
			}
			::v-deep.el-button {
				margin: 8px 0 0 0;
			}
		}
	}
	.btn {
		cursor: pointer;
	}
	.page {
		margin-top: 41px;
		::v-deep .el-pagination {
			display: flex;
			.btn-prev {
				margin-left: auto;
			}
		}
	}
}
</style>
