<template>
	<div class="login-container" :style="{ width: isMain ? '1200px' : '100%' }">
		<div class="container content">
			<div class="certif-tatus">
				<div class="certif-tatus-left">认证状态</div>
				<div :class="{ 'certif-tatus-right': true, active: epData.status == 1 }">
					{{ epData.status == 1 ? '已认证' : '待认证' }}
				</div>
			</div>
			<div class="alert">
				<el-alert
					title="提示：默认当前申请企业认证的账号为企业管理员账号，若需要更换，请重新注册并提交认证"
					type="warning"
					:closable="false"
					show-icon
				></el-alert>
			</div>
			<component
				:is="compName"
				:ep-data="epData"
				@compChange="compChange"
				@enterpriseInfo="handlerGetEnterpriseInfo"
			></component>
			<div v-if="epData.status == 1" class="buttons">
				<el-button type="primary" icon="el-icon-share" @click="goPage">企业管理中心</el-button>
			</div>
		</div>
	</div>
</template>

<script>
import Form from './form';
import InReview from './inreview';
import ReviewFailed from './review-failed';
import ReviewPass from './review-pass';
import { mapMutations, mapGetters } from 'vuex';

export default {
	name: 'Register',
	components: {
		Form,
		InReview,
		ReviewFailed,
		ReviewPass
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			steps: {
				index: 1,
				data: [
					{
						title: '手机号注册',
						comp: 'RegisterForm'
					},
					{
						title: '注册完成',
						comp: 'RegisterResult'
					}
				]
			},
			active: 2,
			compName: 'Form',
			epData: {}
		};
	},
	computed: {
		...mapGetters(['roles']),
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	mounted() {
		this.handlerGetEnterpriseInfo();
	},
	methods: {
		...mapMutations('user', ['SET_ROLES']),

		goPage() {
			this.$router.push('/independentPersonal/enterprise');
		},

		compChange(compName, failed) {
			this.compName = compName;
			if (compName == 'Form' && !failed) {
				this.handlerGetEnterpriseInfo();
			}
		},
		handlerGetEnterpriseInfo() {
			this.$api.personal_api.getEnterpriseInfo().then(res => {
				this.epData = res.results;
				if (this.epData.status == '9') {
					this.compName = 'Form';
				} else if (this.epData.status == '0') {
					this.compName = 'InReview';
				} else if (this.epData.status == '1') {
					this.compName = 'ReviewPass';
					if (!this.roles.includes(3)) {
						//判断是否已经包含认证过后的商家角色，没有才手动加入，主要是认证成功之后未重新登录时，某些需要商家角色的入口进不去
						this.SET_ROLES([...this.roles, 3]);
					}
				} else if (this.epData.status == '2') {
					this.compName = 'ReviewFailed';
				}
			});
		}
	}
};
</script>

<style lang="scss">
.el-tabs__header {
	margin: 0 0 24px;
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
	overflow: hidden;
	margin: 0 auto;
	.content {
		background-color: #fff;
		margin-top: 16px;
		padding: 29px 40px;
	}
	.certif-tatus {
		display: flex;
		height: 80px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		border: 1px solid #eeeeee;
		margin-bottom: 10px;
		&-left {
			width: 129px;
			height: 100%;
			line-height: 78px;
			text-align: center;
			font-size: 18px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #262626;
		}
		&-right {
			flex: 1;
			line-height: 78px;
			padding-left: 17px;
			font-size: 20px;
			font-family: Source Han Sans SC-Bold, Source Han Sans SC;
			font-weight: bold;
			color: #ff7051;
			background: linear-gradient(91deg, #fff7ea 0%, rgba(252, 233, 233, 0) 100%);
			&.active {
				color: #76bf6a;
				background: linear-gradient(91deg, #f6f9ff 0%, #f9fffb 39%);
			}
		}
	}
	.el-alert {
		height: 42px;
		background: #fff9f9;
		border-radius: 4px 4px 4px 4px;
	}
	.el-alert--warning.is-light {
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #ed7b2f;
	}
}

.buttons {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
