<template>
	<div class="form-content">
		<div class="auth-title">认证资料</div>
		<div class="form-box">
			<div class="form-box-title">其他申请材料：</div>
			<el-form ref="myForm" :model="formData" :rules="rules" class="el-form-area">
				<div v-for="(item, index) in formItemList" :key="index" class="form-item">
					<uploadItem
						:desc="item.remark"
						:code="item.dataCode"
						:own-id="details.id"
						:file-number="1"
						tips
						@fileChange="
							files => {
								fileChange(files, item.dataCode);
							}
						"
					/>
					<el-form-item :prop="item.dataCode" :label="item.dataName"></el-form-item>
				</div>
			</el-form>
		</div>
		<div class="btns">
			<el-button @click="handleCancel">取消</el-button>
			<el-button type="primary" @click="submit">保存并提交</el-button>
		</div>
	</div>
</template>

<script>
import uploadItem from './uploadItem';
import { submitInfo } from '@/api/auth';
export default {
	name: 'LogisticForm',
	components: {
		uploadItem
	},
	props: {
		formItemList: {
			type: Array,
			default() {
				return [];
			}
		},
		details: {
			type: Object,
			default() {
				return {};
			}
		}
	},
	data() {
		return {
			formData: {}
		};
	},
	computed: {
		rules() {
			let ruleObj = {};
			this.formItemList.forEach(item => {
				let ruleItem = [
					{ required: true, message: item.tips || '请上传' + item.dataName, trigger: 'change' }
				];
				ruleObj[item.dataCode] = ruleItem;
			});
			return ruleObj;
		}
	},
	created() {
		let form = {};
		this.formItemList.forEach(item => {
			form[item.dataCode] = '';
		});
		this.formData = form;
	},
	methods: {
		fileChange(files, field) {
			if (files.length) {
				this.formData[field] = '1111';
			} else {
				this.formData[field] = '';
			}
			this.$forceUpdate();
		},
		submit() {
			this.$refs.myForm.validate(res => {
				if (res) {
					submitInfo({
						enterpriseTypeId: this.details.enterpriseTypeId,
						enterpriseId: this.details.enterpriseId,
						id: this.details.id,
						status: 0,
						certifyFrom: 1
					}).then(res => {
						if (res.rCode === 0) {
							this.$message.success('操作成功');
							this.$emit('refresh');
						} else {
							this.$message.error(res.msg || '操作失败');
						}
					});
				}
			});
		},
		// 取消
		handleCancel() {
			// 兼容外部入口跳转
			if (window.history.length <= 1) {
				this.$router.push('/auth-center/index');
			} else {
				window.history.go(-1);
			}
		}
	}
};
</script>

<style lang="scss">
.form-box {
	width: 100%;
	display: flex;
	&-title {
		width: 130px;
		font-size: 14px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.85);
		line-height: 22px;
	}
}
.auth-title {
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 50px;
	margin-bottom: 66px;
	&::after {
		position: absolute;
		content: '';
		display: inline-block;
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.form-content {
	margin-top: 24px;
	overflow: hidden;
	.el-form-area {
		overflow: hidden;
		flex: 1;
	}
	.form-item {
		float: left;
		width: 50%;
		.el-form-item {
			border: none;
			.el-form-item__content {
				// display: none;
			}
		}
	}
	.btns {
		border-top: 1px solid #d9d9d9;
		text-align: center;
		padding: 40px 0;
	}
}
.el-form-item__label {
	font-size: 14px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: rgba(0, 0, 0, 0.85);
	line-height: 22px;
	margin-top: 10px;
}
</style>
