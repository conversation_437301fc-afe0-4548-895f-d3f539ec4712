<template>
	<div class="person">
		<div class="person-left">
			<div class="person-left-con">
				<el-menu
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
				>
					<el-menu-item v-for="menu of menus" :key="menu.index" :index="menu.index">
						<span slot="title">{{ menu.name }}</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb>
			<personHeader :title="activeName"></personHeader>
			<component
				:is="activeComponent"
				:key="params.key || ''"
				:is-main="false"
				:params="params"
			></component>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
import Curriculum from './components/curriculum.vue';
import Deliver from './components/deliver.vue';
import Practice from './components/practice.vue';
import StartUp from './components/start-up.vue';
import Interview from './components/interview.vue';
import Collect from './components/collect.vue';
import Attention from './components/attention.vue';
import Evaluate from './components/evaluate.vue';
import Consult from './components/consult.vue';
import Recruit from './components/recruit.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import personHeader from '@/components/person-header';

export default {
	components: {
		Curriculum,
		subBreadcrumb,
		personHeader,
		Interview,
		Practice,
		Collect,
		Consult,
		Evaluate,
		Recruit,
		Attention,
		StartUp,
		Deliver
	},
	data() {
		return {
			activeMenu: '1-1',
			activeComponent: 'Curriculum',
			activeName: '我的简历',

			params: { key: 3, showActiveCode: 'works' }
		};
	},
	computed: {
		...mapGetters(['roles']),

		menus() {
			let arr = [
				{
					name: '我的简历',
					index: '1-1',
					path: 'Curriculum'
				},
				// {
				// 	name: '我的实习报告',
				// 	index: '2-1',
				// 	path: 'Practice'
				// },
				{
					name: '我的投递',
					index: '3-1',
					path: 'Deliver'
				},
				// {
				// 	name: '我的面试邀请',
				// 	index: '4-1',
				// 	path: 'Interview'
				// },
				{
					name: '我的收藏',
					index: '5-1',
					path: 'Collect'
				},
				{
					name: '我的关注',
					index: '6-1',
					path: 'Attention'
				},
				// {
				// 	name: '我的评价',
				// 	index: '6-1',
				// 	path: 'Evaluate'
				// },
				// {
				// 	name: '我的咨询',
				// 	index: '7-1',
				// 	path: 'Consult'
				// },
				{
					name: '我要招聘',
					index: '8-1',
					role: [1],
					path: 'Recruit'
				},
				{
					name: '创业项目管理',
					index: '9-1',
					path: 'StartUp'
				}
			];
			arr = arr.filter(item => {
				item.role = item.role || [];
				let isRole = true;
				for (let role of this.roles) {
					if (item.role.includes(role)) {
						isRole = false;
					}
				}
				return isRole;
			});
			return arr;
		}
	},

	methods: {
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.menus.find(item => {
				return item.index === index;
			});
			if (currentObj.params) {
				this.params = currentObj.params;
			} else {
				this.params = {};
			}
			this.activeMenu = currentObj.index;
			this.activeName = currentObj.name;
			this.activeComponent = currentObj.path;
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
	}
	&-right {
		width: calc(100% - 236px);
	}
}
::v-deep .el-menu-item {
	padding-left: 40px !important;
}
.is-active {
	position: relative;
	&::before {
		content: '';
		display: inline-block;
		position: absolute;
		left: 20px;
		top: calc(50% - 7px);
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background: #3274e0;
	}
}
</style>
