/**
 * Name:子项目的路由
 * 目录：shop-views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */
export default {
	views: [],
	subViews: [
		{
			path: 'online-study',
			name: 'OnlineStudy',
			component: () => import('@/online-study-views/home'),
			meta: { title: '在线学习', noShowBreadCrumb: true }
		},
		{
			path: 'course-list',
			name: 'CourseList',
			component: () => import('@/online-study-views/course-list'),
			meta: { title: '课程列表', noShowBreadCrumb: true }
		},
		{
			path: 'type-list',
			name: 'TypeList',
			component: () => import('@/online-study-views/type-list'),
			meta: { title: '类型列表', noShowBreadCrumb: true }
		},
		{
			path: 'freecourses',
			name: 'Freecourses',
			component: () => import('@/online-study-views/freecourses'),
			meta: { title: '技术技能培训', noShowBreadCrumb: true }
		},
		{
			path: 'freecourses-order',
			name: 'FreecoursesOrder',
			component: () => import('@/online-study-views/freecourses-order'),
			meta: { title: '提交订单', noShowBreadCrumb: false }
		},
		{
			path: 'payment-center',
			name: 'PaymentCenter',
			component: () => import('@/online-study-views/payment-center'),
			meta: { title: '支付中心', noShowBreadCrumb: true }
		},
		{
			path: 'video-play',
			name: 'VideoPlay',
			component: () => import('@/online-study-views/video-play'),
			meta: { title: '视频播放', noShowBreadCrumb: true, needLogin: true }
		},
		{
			path: 'collet-list',
			name: 'ColletList',
			component: () => import('@/online-study-views/collet-list'),
			meta: { title: '课程收藏列表', alive: true, noShowBreadCrumb: true }
		},
		{
			path: 'study-car',
			name: 'StudyCar',
			component: () => import('@/online-study-views/car'),
			meta: { title: '购物车', alive: true, noShowBreadCrumb: true }
		},
		{
			path: 'study-order-list',
			name: 'StudyOrderList',
			component: () => import('@/online-study-views/order-list'),
			meta: { title: '订单列表', alive: true, noShowBreadCrumb: true }
		},
		{
			path: 'study-personal',
			name: 'StudyPersonal',
			component: () => import('@/online-study-views/personal'),
			meta: { title: '个人中心', alive: true, noShowBreadCrumb: true }
		},
		{
			path: 'examination-result',
			name: 'ExaminationResult',
			component: () => import('@/online-study-views/examination-result'),
			meta: { title: '考试结果', alive: true, noShowBreadCrumb: true }
		},
		{
			path: 'examination',
			name: 'Examination',
			component: () => import('@/online-study-views/examination'),
			meta: { title: '考试', alive: true, noShowBreadCrumb: true, needLogin: true }
		},
		// 在线直播
		{
			path: 'onlineLive',
			name: 'onlineLive',
			component: () => import('@/online-study-views/live'),
			meta: { title: '在线直播', alive: true, noShowBreadCrumb: true }
		}
	]
};
