import request from '@/utils/request';

/**获取全部应用*/
export function allAppList(data) {
	return request({
		url: '/ybzy/platuser/front/allAppList',
		method: 'post',
		data
	});
}
/**获取消息*/
export function findCmsInfo(params) {
	return request({
		url: '/ybzy/cmsinfo/front/paging',
		method: 'get',
		params
	});
}
/**获取企业信息*/
export function getEnterpriseInfo() {
	return request({
		url: '/ybzy/platenterprise/front/getMyEnterprise',
		method: 'get'
	});
}
/**获取商家信息*/
export function shopAddressManagement(params) {
	return request({
		url: '/trip-api/api/product/getShopTransactionData',
		method: 'get',
		params
	});
}

export function changeUserInfo(data) {
	return request({
		url: '/ybzy/platuser/front/updateInfo',
		method: 'post',
		data
	});
}

export function uploadFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/upload',
		method: 'post',
		data
	});
}
export function previewFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=' + data.adjunctId,
		headers: {
			responseType: 'blob'
		},
		responseType: 'blob',
		method: 'get'
	});
}
export function deleteFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/delAdjunct?id=' + data.id,
		method: 'post'
	});
}
export function getFileInfo(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/getAdjunctFileInfos',
		method: 'get',
		params: data
	});
}
/**获取文章详情*/
export function getArticleDetail(data) {
	return request({
		url: '/ybzy/cmsinfo/front/detail',
		method: 'get',
		params: data
	});
}
/**获取重置密码验证码*/
export function sendResetCode({ phone }) {
	return request({
		url: '/ybzy/platuser/front/sendResetPwdVerifyCode',
		method: 'post',
		data: { phone }
	});
}
/**通过验证码重置密码*/
export function codeResetPwd({ phone, password, verifyCode }) {
	return request({
		url: '/ybzy/platuser/front/resetPwdByVerifyCode',
		method: 'post',
		type: 'JSON',
		data: {
			phone,
			// 密码注册base64编码
			password: btoa(password),
			verifyCode
		}
	});
}

export function findCmsDetail(params) {
	return request({
		url: '/ybzy/cmsinfo/front/detail',
		method: 'get',
		params
	});
}
/**增加文字点击量*/
export function addViews(params) {
	return request({
		url: '/ybzy/cmsinteract/front/view',
		method: 'get',
		params
	});
}
export function getRegion() {
	return request({
		url: '/ybzy/platenterprise/front/region',
		method: 'get'
	});
}

export function saveEnterpriseInfo(data) {
	return request({
		url: '/ybzy/platenterprise/front/saveEnterpriseInfo',
		method: 'post',
		data
	});
}

export function queryDict(params) {
	return request({
		url: '/ybzy/mecpSys/front/findSysCodeList.dhtml',
		method: 'get',
		params
	});
}
// 获取学校学院的数据
export function getOrgList(params) {
	return request({
		url: '/ybzy/front/specificOrg/getOrgSelectList',
		method: 'get',
		params
	});
}
// 获取学校学院的数据二级
export function getColSchSelectList(params) {
	return request({
		url: '/ybzy/front/specificOrg/getColSchSelectList',
		method: 'get',
		params
	});
}

// 获取专业数据接口
export function getMajorList(params) {
	return request({
		url: '/ybzy/front/specificMajor/getMajorSelectList',
		method: 'get',
		params
	});
}

/**撤回认证*/
export function cancelEnterpriseInfo() {
	return request({
		url: '/ybzy/platenterprise/front/cancelAttestation',
		method: 'get'
	});
}

/**修改密码*/
export function updatePassword(data) {
	return request({
		url: '/ybzy/platuser/front/resetPwdByOldPwd',
		method: 'post',
		data
	});
}
