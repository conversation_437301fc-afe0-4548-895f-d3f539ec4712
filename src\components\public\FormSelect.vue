<template>
	<el-select
		v-model="valueInput"
		:style="{ width: width }"
		filterable
		clearable
		:placeholder="placeholder"
		:multiple="multiple"
		:disabled="disabled"
		:collapse-tags="collapseTags"
		:size="size"
		@change="change"
	>
		<el-option
			v-for="option in optionsC"
			:key="option[props.key]"
			:label="option[props.label]"
			:value="option[props.key]"
		/>
	</el-select>
</template>

<script>
export default {
	name: 'FormSelect',
	props: {
		value: {
			type: [String, Number, Boolean, Object, Array, Function],
			default: null
		},
		width: {
			type: String,
			default: '400px'
		},
		options: {
			type: [Array, Object],
			default: () => []
		},
		props: {
			type: Object,
			default: () => ({ key: 'key', label: 'label' })
		},
		multiple: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: ''
		},
		disabled: {
			type: Boolean,
			default: false
		},
		change: {
			type: Function,
			default: function () {}
		},
		collapseTags: {
			type: <PERSON><PERSON><PERSON>,
			default: false
		},
		size: {
			type: String,
			default: ''
		}
	},
	computed: {
		valueInput: {
			get() {
				return this.value;
			},
			set(value) {
				this.$emit('input', value);
			}
		},
		optionsC() {
			if (!this.options) return [];
			if (Array.isArray(this.options)) {
				return this.options;
			} else {
				return Object.keys(this.options).map(i => {
					return {
						key: i,
						label: this.options[i]
					};
				});
			}
		}
	}
};
</script>
