<template>
	<div class="detail">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<!-- 中心内容展示部分 -->
		<div v-loading="loading" class="detail-box">
			<div v-if="visiable" class="top-content">
				<div class="pic">
					<!-- <el-carousel arrow="never" :interval="5000" :autoplay="true" height="400px">
						<el-carousel-item>
							<img
								src="https://t7.baidu.com/it/u=2168645659,3174029352&fm=193&f=GIF"
								alt=""
								width="100%"
							/>
						</el-carousel-item>
						<el-carousel-item>
							<img
								src="https://t7.baidu.com/it/u=1819248061,230866778&fm=193&f=GIF"
								alt=""
								width="100%"
							/>
						</el-carousel-item>
					</el-carousel> -->
					<img :src="detailContent.pictureurl" alt="" />
				</div>
				<div class="desc">
					<div class="title">
						<div class="title-text">
							{{ detailContent.activity_title }}
						</div>
					</div>
					<div class="addirse">
						<i class="el-icon-location-outline icon"></i>
						{{ detailContent.activity_address }}
					</div>
					<div class="time">
						<i class="el-icon-time icon"></i>
						{{ handleTimeChange(detailContent.activity_start_time) }}
						{{ cutString(detailContent.activity_time) }}~
						{{ cutStringEnd(detailContent.activity_time) }}
						{{ handleTimeChange(detailContent.activity_end_date) }}
					</div>
					<div class="price">
						<span class="left-text">活动报名费：</span>
						<span class="right-price">
							{{ detailContent.pay == 1 ? `￥${detailContent.payment_amount}` : '免费' }}
						</span>
					</div>
					<div class="btn-box">
						<div
							:class="[1, 4].includes(detailContent.status) ? 'btn color-blue' : 'btn'"
							@click="openQR"
						>
							{{ statuStranslate(detailContent.status) }}
						</div>
					</div>
				</div>
			</div>
			<div v-if="visiable" class="bottom-content">
				<el-tabs v-model="activeName" @tab-click="handleClick">
					<el-tab-pane label="活动介绍" name="first"></el-tab-pane>
				</el-tabs>
				<!-- eslint-disable-next-line vue/no-v-html -->
				<div class="html-content" v-html="detailContent.introduction"></div>
			</div>
		</div>
		<el-dialog title="手机扫描二维码" :visible.sync="dialogVisible" width="500px">
			<div class="QR-box">
				<div id="qrCodeUrl"></div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import QRCode from 'qrcodejs2';
import { alumniUrl } from '@/config';
export default {
	components: {
		subBreadcrumb
	},
	data() {
		return {
			dialogVisible: false,
			loading: false, //加载动画
			activeName: 'first',
			id: '',
			detailContent: {},
			visiable: false,
			qrCode: null,
			originUrl: alumniUrl + '/project-ybzy/ybzy_zjptH5/#/alumni_pages/activeDetail' //二维码地址
		};
	},
	watch: {
		$route: {
			handler: function (route) {
				this.id = this.$route.query.id;
			},
			immediate: true
		}
	},
	mounted() {
		this.getInterfaceData();
	},
	methods: {
		// 二维码
		// refreshQrCode() {
		// 	this.$nextTick(function () {
		// 		document.getElementById('qrCodeUrl').innerHTML = '';
		// 		let qrCodeUrl = new QRCode('qrCodeUrl', {
		// 			width: 250,
		// 			height: 250,
		// 			text: this.originUrl,
		// 			colorDark: 'red',
		// 			colorLight: 'yellow'
		// 		});
		// 	});
		// },
		openQR() {
			this.dialogVisible = true;
			// setTimeout(() => {
			// 	document.getElementById('qrCodeUrl').innerHTML = '';
			// 	let qrCodeUrl = new QRCode('qrCodeUrl', {
			// 		width: 250,
			// 		height: 250,
			// 		text: this.originUrl,
			// 		colorDark: 'red',
			// 		colorLight: 'yellow'
			// 	});
			// }, 1000);
			this.$nextTick(function () {
				document.getElementById('qrCodeUrl').innerHTML = '';
				let qrCodeUrl = new QRCode('qrCodeUrl', {
					width: 250,
					height: 250,
					text: this.originUrl + '?id=' + this.id,
					colorDark: 'black',
					colorLight: '#ffffff'
				});
				this.qrCode = qrCodeUrl;
			});
		},
		handleClick(tab, event) {},
		getInterfaceData() {
			this.loading = true;
			this.$api.alumni_association_api.alumniActiveListDetail({ id: this.id }).then(res => {
				this.loading = false;
				this.visiable = true;
				this.detailContent = res.data.entity;
			});
		},
		//把时间戳转换成时间格式
		handleTimeChange(time) {
			let timestamp = time;
			let date = new Date(timestamp);
			let Year = date.getFullYear();
			let Moth = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
			let Day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
			let GMT = Year + '-' + Moth + '-' + Day;
			return GMT;
		},
		//截取字符串
		cutString(string) {
			let str = string;
			let arr = str.split('-');
			return arr[0];
		},
		//截取字符串
		cutStringEnd(string) {
			let str = string;
			let arr = str.split('-');
			return arr[1];
		},
		//状态
		statuStranslate(status) {
			switch (status) {
				case 0:
					return '待开放';
				case 1:
					//预约中
					return '立即报名';
				case 2:
					// 已结束
					return '报名结束';
				case 3:
					return '已取消';
				case 4:
					return '已预约';
				default:
					break;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.detail-box {
	width: $max-width !important;
	margin: 20px auto;
	min-height: 400px;
	.top-content {
		width: 100%;
		height: 400px;
		display: flex;
		margin: 0 0 20px 0;
		.pic {
			overflow: hidden;
			width: 900px;
			height: 400px;
			background: #ffffff;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
		.desc {
			cursor: default;
			flex: 1;
			height: 400px;
			background: #ffffff;
			.title {
				height: 140px;
				font-size: 18px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #333333;
				padding: 33px 26px;
				.title-text {
					width: 100%;
					height: 100%;
					white-space: normal;
					text-overflow: ellipsis;
					overflow: hidden;
					display: -webkit-box;
					/* 限制在一个块元素显示的文本的行数 */
					/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
					-webkit-line-clamp: 3;
					/* 设置或检索伸缩盒对象的子元素的排列方式 */
					-webkit-box-orient: vertical;
				}
			}
			.addirse {
				padding: 0 27px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #7a8392;
				line-height: 35px;
				white-space: normal;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.time {
				padding: 0 27px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #7a8392;
				line-height: 35px;
			}
			.price {
				display: flex;
				height: 60px;
				padding: 0 27px;
				.left-text {
					display: block;
					height: 60px;
					line-height: 60px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
				}
				.right-price {
					display: block;
					height: 60px;
					line-height: 60px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #ff0000;
				}
			}
			.btn-box {
				height: 50px;
				width: 100%;
				padding: 0 27px;
				.btn {
					cursor: pointer;
					width: 100%;
					height: 100%;
					background: #dcdcdc;
					box-shadow: 0px 4px 10px 0px rgba(103, 103, 103, 0.2);
					border-radius: 25px;
					text-align: center;
					font-size: 18px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #ffffff;
					line-height: 50px;
				}
			}
		}
	}
	.bottom-content {
		background: #ffffff;
		.html-content {
			padding: 33px;
		}
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: #ffffff;
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #ffffff;
	border-radius: 50%;
}
.color-blue {
	background: linear-gradient(98deg, #248ef0 0%, #0076e8 100%) !important;
}
::v-deep .el-tabs__item {
	//tabs
	width: 120px;
	text-align: center;
	height: 50px;
	font-size: 18px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #0076e8;
	line-height: 50px;
}
::v-deep .el-dialog__body {
	height: 340px;
}
// ::v-deep .el-dialog {
// 	margin-top: calc(50vh - 330px) !important;
// }
.QR-box {
	width: 100%;
	height: 250px;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
