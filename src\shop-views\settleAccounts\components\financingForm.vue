<template>
	<div>
		<el-form ref="form" :model="form" label-width="120px">
			<el-form-item label="已选融资产品:">
				<div class="title">
					<div class="logo"></div>
					<div class="title-text">大宗商品买断式金融-中国平安银行股份有限公司</div>
				</div>
			</el-form-item>
			<el-form-item label="申请融资金额:">
				<div class="price">10000元</div>
			</el-form-item>
			<el-form-item label="活动时间">
				<div class="number_input">
					<el-input v-model="form.name"></el-input>
					<div>个月</div>
				</div>
			</el-form-item>
			<el-form-item label="担保方式:">
				<el-radio-group v-model="form.resource">
					<el-radio label="线上品牌商赞助"></el-radio>
					<el-radio label="线下场地免费"></el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="还款方式:">
				<el-radio-group v-model="form.resource">
					<el-radio label="线上品牌商赞助"></el-radio>
					<el-radio label="线下场地免费"></el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
export default {
	name: 'FinancingForm',
	data() {
		return {
			form: {
				name: '',
				region: '',
				date1: '',
				date2: '',
				delivery: false,
				type: [],
				resource: '',
				desc: ''
			}
		};
	},
	methods: {
		onSubmit() {
			console.log('submit!');
		}
	}
};
</script>

<style lang="scss" scoped>
.title {
	display: flex;
	align-items: center;
	.logo {
		width: 118px;
		height: 40px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
	}
	&-text {
		margin-left: 6px;
		height: 24px;
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 24px;
	}
}
.number_input {
	display: flex;
	.el-input {
		width: 240px;
	}
	div {
		margin-left: 5px;
	}
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
	background: var(--brand-6, '#ca3f3b') !important;
}
</style>
