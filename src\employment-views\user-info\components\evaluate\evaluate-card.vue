<!--
 @desc:我的评价卡片
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="card" @click="clickCard">
		<div class="star-box">
			满意度：
			<div class="star">
				<img
					v-for="(item, index) in Math.floor(cardData.starNum)"
					:key="index"
					:src="starSolid"
					alt=""
				/>
				<img v-if="starHalfNum" :src="starHalf" alt="" />
				<img v-for="(item, index) in starEmptyNum" :key="index + 'empty'" :src="starEmpty" alt="" />
			</div>
		</div>
		<div class="conpany-img">
			<img src="@/assets/employment-images/tool-enterprise.png" alt="" />
		</div>
		<header>
			<div class="hr-info">
				<span>{{ cardData.conpany }}</span>
				<p>
					<i>[</i>
					{{ cardData.tagsStr }}
					<i>]</i>
				</p>
			</div>
			<p class="is-time">
				{{ cardData.time }}
			</p>
		</header>
		<article>
			<div class="content-box">
				经营范围：
				<span>{{ cardData.content }}</span>
			</div>
			<ul class="tags-box">
				<li v-for="(item, index) in cardData.tags" :key="index">{{ item }}</li>
			</ul>
		</article>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					imgUrl: '',
					time: '2023-06-30 13:30:00',
					state: '0',
					conpany: '扭博科技有限公司',
					content: '通用航空服务；民用航空器零部件制造；民用航空器（发动机、发动机、发动机、发动机',
					tagsStr: '[航空/航天设备· 500-999人]',
					tags: ['1-3', '五险一金', '大专', '带薪年假'],
					starNum: 3
				};
			}
		}
	},
	data() {
		return {
			starSolid: require('@imgs/home/<USER>'),
			starHalf: require('@imgs/home/<USER>'),
			starEmpty: require('@imgs/home/<USER>')
		};
	},
	computed: {
		starHalfNum() {
			//任何整数都会被1整除
			return this.cardData.starNum % 1 !== 0 ? true : false;
		},
		starEmptyNum() {
			//向下取整
			return Math.floor(5 - this.cardData.starNum);
		}
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	position: relative;
	width: 100%;
	height: 150px;
	margin-bottom: 20px;
	padding: 30px 0 0 88px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	overflow: hidden;
	// border: 1px solid red;

	.star-box {
		@include flexBox(flex-start);
		position: absolute;
		top: 0;
		left: 0;
		font-size: 16px;
		margin: 8px 0 6px 0;
		color: #333;
		.star {
			margin-right: 6px;
			img {
				width: 16px;
				height: 16px;
			}
		}
	}
	.conpany-img {
		@include flexBox();
		position: absolute;
		top: 40px;
		left: 0;
		width: 72px;
		height: 72px;
		border: 1px solid #f0f3f7;
		border-radius: 5px;
		img {
			width: 46px;
			height: 46px;
			border-radius: 6px;
		}
	}
	header {
		@include flexBox(space-between);
		// border: 1px solid red;
		margin-top: 8px;
		.hr-info {
			@include flexBox(flex-start);
			p {
				font-size: 14px;
				color: #666666;
			}

			span {
				display: inline-block;
				font-size: 16px;
				color: #333333;
				margin-right: 10px;
			}
		}
		.is-time {
			font-size: 12px;
			color: #999999;
		}
	}
	article {
		.content-box {
			width: 460px;
			font-size: 14px;
			margin: 14px 0;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			color: #333333;
			span {
				color: #666666;
			}
		}
	}
}
.tags-box {
	@include flexBox(flex-start);
	li {
		margin-right: 10px;
		font-size: 14px;
		padding: 4px 10px;
		background: #f1f3f8;
		color: #999;
		border-radius: 2px;
	}
}
</style>
