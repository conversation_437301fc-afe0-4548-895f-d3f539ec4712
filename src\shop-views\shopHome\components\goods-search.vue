<template>
	<div class="goods-search">
		<div class="search">
			<el-form :inline="true" :model="formInline" class="demo-form-inline">
				<el-form-item label="商品名称">
					<el-input v-model="formInline.keywords" clearable placeholder="请输入商品名称"></el-input>
				</el-form-item>
				<el-form-item label="价格">
					<div class="inputs">
						<el-input v-model="formInline.minPrice" placeholder="请输入价格区间"></el-input>
						<span>-</span>
						<el-input v-model="formInline.maxPrice" placeholder="请输入价格区间"></el-input>
					</div>
				</el-form-item>
				<el-form-item>
					<el-button icon="el-icon-search" @click="onSubmit">查询</el-button>
				</el-form-item>
			</el-form>
		</div>
		<div class="tags">
			<el-checkbox-group v-model="checkedCities">
				<el-checkbox
					v-for="item in cities"
					:key="item.label"
					:label="item.label"
					@change="handleCheckedCitiesChange(item.label)"
				>
					{{ item.name }}
				</el-checkbox>
			</el-checkbox-group>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodsSearch',
	data() {
		return {
			formInline: {
				keywords: '',
				minPrice: null,
				maxPrice: null
			},
			soltActive: '1',
			checkAll: false,
			checkedCities: ['all'],
			cities: [
				{ label: 'all', name: '综合' },
				{ label: 'time', name: '最新' },
				{ label: 'saledNum', name: '销量' }
			],
			isIndeterminate: true
		};
	},
	methods: {
		onSubmit() {
			this.$emit('search', this.formInline);
		},
		handleCheckedCitiesChange(item) {
			this.checkedCities = [];
			this.checkedCities.push(item);
			this.formInline.orderRule = item;
			this.soltActive = '';
			this.formInline.isDesc = true;
			this.$emit('search', this.formInline);
		}
	}
};
</script>

<style lang="scss" scoped>
.goods-search {
	margin-top: 16px;
	width: 100%;
	background-color: #fff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	.search {
		padding: 9px 30px;
		.el-form-item {
			margin-bottom: 0px;
			margin-right: 21px;
			.inputs {
				display: flex;
				::v-deep .el-input__inner {
					width: 134px;
				}
				span {
					padding: 0px 10px;
				}
			}
		}
	}
	.tags {
		width: 1200px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border-top: 1px solid #eeeeee;
		padding: 18px 30px;
	}
}
::v-deep .el-button:focus,
.el-button:hover {
	background: var(--brand-1, '#ca3f3b');
	color: var(--brand-6, '#ca3f3b');
	border: 1px solid var(--brand-6, '#ca3f3b');
}
</style>
<style lang="scss">
.popover-slot {
	width: 130px;
	height: 80px;
	background: #ffffff;
	border-radius: 4px 4px 4px 4px;
	padding: 12px 0px !important;
	opacity: 1;
	.slot {
		&-text {
			width: 100%;
			height: 34px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			background: #ffffff;
			font-size: 14px;
			font-family: Noto Sans SC-Regular, Noto Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 34px;
			padding: 0 20px;
		}
		&-text_active {
			width: 100%;
			height: 34px;
			background: #fff4f3;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: var(--brand-6, '#ca3f3b');
			line-height: 34px;
		}
	}
}
</style>
