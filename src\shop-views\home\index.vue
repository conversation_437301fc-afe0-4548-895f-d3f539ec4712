<template>
	<div class="goodsDeal">
		<goods-deal-recommend />
		<goods-like></goods-like>
	</div>
</template>

<script>
import GoodsDealRecommend from './components/goods-deal-recommend.vue';
import GoodsLike from './components/goods-like.vue';
export default {
	name: 'GoodsDeal',
	components: {
		GoodsDealRecommend,
		GoodsLike
	},
	data() {
		return {};
	}
};
</script>
<style lang="scss" scoped>
.goodsDeal {
	overflow: hidden;
	width: 1200px;
	margin: 0 auto;
}
</style>
