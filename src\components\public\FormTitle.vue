<template>
	<div>
		<div
			class="title-container"
			:style="{
				'margin-top': `${marginTop}px`,
				'margin-bottom': `${marginBottom}px`
			}"
		>
			<div class="title">
				<div class="title-line"></div>
				<div
					class="title-text"
					:style="{
						'margin-left': `${textLeft}px`
					}"
				>
					{{ title }}
				</div>
			</div>
			<div :style="{ 'padding-right': `${paddingRight}px` }">
				<slot name="right" />
			</div>
		</div>
		<slot />
	</div>
</template>
<script>
export default {
	props: {
		// 标题
		title: {
			type: String,
			default: ''
		},
		paddingRight: {
			type: Number,
			default: 25
		},
		// 标题上间距
		marginTop: {
			type: Number,
			default: 5
		},
		// 标题下间距
		marginBottom: {
			type: Number,
			default: 10
		},
		// 标题文字左间距
		textLeft: {
			type: Number,
			default: 15
		}
	}
};
</script>
<style scoped>
.title {
	display: flex;
	align-items: center;
}
.title-container {
	display: flex;
	justify-content: space-between;
}
.title-line {
	border-left: 2px solid #4974f5;
	height: 13px;
}
.title-text {
	color: #2b2b2b;
	font-weight: bold;
	font-size: 14px;
}
</style>
