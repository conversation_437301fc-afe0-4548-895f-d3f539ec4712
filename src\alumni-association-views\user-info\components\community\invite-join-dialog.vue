<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 邀请加入弹窗
 @author: WH
 @date: 2023/9/13
 -->
<template>
	<el-dialog title="邀请提示" :visible.sync="visible" :before-close="beforeClose" width="620px">
		<div class="is-head">
			{{ info.inviterName }}
			&nbsp;&nbsp; 邀请您加入：
			<span>{{ info.communityName }}</span>
		</div>
		<div class="info-card">
			<div class="community-info">
				<p>{{ info.communityName }}</p>
				<p>{{ info.capacityNum }}人</p>
				<p>{{ info.adminUserName }}创建于{{ info.createTime }}</p>
			</div>
			<ul class="tags-box">
				<li v-for="(item, index) in info.labelNames" :key="index">{{ item }}</li>
			</ul>
			<div class="content">社区介绍：{{ info.introduce }}</div>
		</div>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" :loading="submitLoading" @click="resolve">同 意</el-button>
			<el-button @click="reject">拒 绝</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	props: {
		info: {
			type: Object,
			required: true
		},
		visible: {
			type: Boolean,
			required: true
		}
	},
	data() {
		return {
			submitLoading: false
		};
	},

	mounted() {},
	methods: {
		beforeClose() {
			this.$emit('update:visible', false);
		},
		reject() {
			let url = window.location.href;
			let firstParamsIdx = url.indexOf('&');
			url = url.substring(0, firstParamsIdx);
			window.location.replace(url);
			this.$emit('handle');
		},
		//申请创建社区——————————————————————————————
		async resolve() {
			this.submitLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.joinCommunity({
					communityId: this.info.id
				});
				if (rCode == 0) {
					this.$message.success(msg + ',请等待管理员审核');
					this.reject();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.submitLoading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.is-head {
	font-size: 14px;
	font-weight: 400;
	color: #8390a3;
	margin-bottom: 20px;
	span {
		color: #df1529;
	}
}
.info-card {
	@include flexBox(space-between, flex-start);
	flex-direction: column;
	width: 580px;
	min-height: 130px;
	max-height: 200px;
	overflow: auto;
	border-radius: 10px;
	padding: 20px;
	background: #ffffff;
	border: 1px solid #e8eaec;
	.community-info {
		@include flexBox(flex-start);
		p {
			font-size: 14px;
			color: #8390a3;
			&:nth-of-type(1) {
				font-size: 16px;
				color: #0076e8;
			}
			&:nth-of-type(2) {
				margin-left: 20px;
			}
			&:nth-of-type(3) {
				margin-left: 40px;
			}
		}
	}
	.tags-box {
		@include flexBox(flex-start);
		li {
			margin-right: 8px;
			font-size: 12px;
			padding: 8px 10px;
			border-radius: 2px;
			background: #fff8eb;
			color: #b38f48;
		}
	}
	.content {
		font-size: 14px;
		// overflow: hidden;
		// text-overflow: ellipsis;
		// white-space: nowrap;
		color: #8390a3;
	}
}
.dialog-footer {
	text-align: center;
}
</style>
