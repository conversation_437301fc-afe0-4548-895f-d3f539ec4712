/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-09-25 11:20:44
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-10-27 13:50:40
 * @FilePath: /ybzy-zhxy-pc/src/router/modules/personal.js
 * @Description:
 */
import vocationalPerson from '@/layout/vocationalPerson';

/**
 * Name:子项目的路由
 * 目录：views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */

export default {
	views: [
		{
			path: '/independentPersonal',
			component: vocationalPerson,
			redirect: '/independentPersonal/vocational',
			children: [
				{
					path: 'vocational',
					name: 'Vocational',
					component: () => import('@/views/vocational-education/serveHall/index'),
					meta: { title: '职教大厅-个人中心', hideSelfFooter: true }
				},
				{
					path: 'enterprise',
					name: 'Enterprise',
					component: () => import('@/views/enterprise-management/index'), // 暂时先跳
					meta: { title: '企业管理中心', type: 'EnterInfo', needLogin: true }
				}
			]
		}
	],
	subViews: []
};
