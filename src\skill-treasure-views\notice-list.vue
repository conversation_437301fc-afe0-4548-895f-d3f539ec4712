<template>
	<div class="skill-treasure" :style="{ width: isMain ? '1200px' : '100%' }">
		<tabs-nav :type-list="typeList" :active-code="activeCode" @click="typeClick" />
		<!-- 内容区域 -->
		<div v-loading="loading" class="content-box">
			<Empty v-if="list.length == 0" :tips="'暂无数据'" />
			<div
				v-for="(item, index) in list"
				:key="index"
				:class="['content-box-item', activeCode == 'comment' ? 'content-box-item-comment' : '']"
				@click="contentClick(item)"
			>
				<img :src="item.headImg ? $judgeFile(item.headImg) : defaulAvatar" class="item-img" />
				<div class="item-right">
					<div class="item-info">
						<span class="item-name">{{ item.nickName }}</span>
						<div class="item-middle">
							<span class="item-operate">{{ actTypeList[item.actType].title }}</span>
							<span class="item-time">{{ item.createTime }}</span>
						</div>
						<span :class="['item-comment', activeCode == 'comment' ? 'comment-content' : '']">
							{{ item.content }}
						</span>
						<!-- 是否关注，只在评论类下面存在 -->
						<div v-if="activeCode == 'comment'" class="is-interest">
							<img
								v-if="item.isInterest"
								src="@/assets/skill-treasure/heart-avtive-border.png"
								class="interest-icon"
							/>
							<img v-else src="@/assets/skill-treasure/heart-border.png" class="interest-icon" />
						</div>
					</div>
					<img :src="$judgeFile(item.coverImg)" class="content-img" />
				</div>
			</div>
		</div>
		<el-pagination
			v-if="total"
			background
			layout="prev, pager, next"
			class="pagination"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
import tabsNav from './components/tabs.vue';
export default {
	components: { tabsNav },
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			// 类型列表
			typeList: [
				{
					name: '评论',
					id: '',
					code: 'comment'
				},
				{
					name: '赞和收藏',
					id: '',
					code: 'digs'
				}
				// {
				// 	name: '通知',
				// 	id: '',
				// 	code: 'notice'
				// }
			],
			activeCode: '', //选中的类型
			loading: false,
			list: [], // 内容部分数据
			pageNum: 1,
			pageSize: 10,
			total: 0,
			actTypeList: {
				1: {
					title: '赞了你的作品'
				},
				2: {
					title: '收藏了你的作品'
				},
				3: {
					title: '评论了你的作品'
				},
				6: {
					title: '关注了你'
				},
				11: {
					title: '赞了你的评论'
				}
			}
		};
	},
	mounted() {
		this.activeCode = this.$route.query.code || 'comment';
		if (this.activeCode == 'comment') {
			this.getInteractFeedbackList();
		} else {
			this.getLaudCollectList();
		}
	},
	methods: {
		/**
		 * @description 类型点击事件，用于切换内容
		 * @params {item} 点击的类型数据
		 */
		typeClick(item) {
			this.activeCode = item.code;
			this.total = 0;
			this.pageNum = 1;
			this.list = [];
			if (this.activeCode == 'comment') {
				this.getInteractFeedbackList();
			} else {
				this.getLaudCollectList();
			}
		},
		/**
		 * @description 内容点击事件
		 * @params {item} 点击内容数据
		 * */
		contentClick(item) {
			this.jumpPage(`/skill-treasure-details?id=${item.storyId}`);
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url, type) {
			this.$router.push(url);
		},
		/**
		 * @description 关注点击事件
		 * @params {item} 点击内容数据
		 * */
		interestClick(item) {},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			if (this.activeCode == 'comment') {
				this.getInteractFeedbackList();
			} else {
				this.getLaudCollectList();
			}
		},
		/**
		 * @description 游玩攻略-通知-评论列表
		 * */
		getInteractFeedbackList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				actType: 3,
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api
				.getInteractFeedbackList(params)
				.then(res => {
					if (res.code == 200) {
						this.list = res?.results?.records || [];
						this.total = res?.results?.total || 0;
					}
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 游玩攻略-点赞和收藏列表
		 * */
		getLaudCollectList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				types: '1,2',
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.getLaudCollectList(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.skill-treasure {
	margin: 0 auto;
	padding: 0 0 16px;
	font-family: PingFang SC-Regular, PingFang SC;
	margin-bottom: 60px;
	.content-box {
		margin-top: 16px;
		padding: 20px;
		display: flex;
		flex-wrap: wrap;
		background: #fff;
		&-item {
			width: 100%;
			height: 82px;
			display: flex;
			// align-items: ;
			margin-bottom: 16px;
		}
		&-item-comment {
			height: 126px;
		}
		.item-img {
			width: 44px;
			height: 44px;
			border-radius: 50%;
			flex-shrink: 0;
			margin-right: 16px;
			object-fit: cover;
		}
		.item-right {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			height: 100%;
			border-bottom: 1px solid #f0f0f0;
			.item-name {
				font-size: 16px;
				color: #404040;
				line-height: 16px;
				display: block;
			}
			.item-middle {
				font-size: 14px;
				color: #8c8c8c;
				line-height: 16px;
				margin-top: 8px;
				.item-time {
					margin-left: 16px;
				}
			}

			.item-comment {
				font-size: 12px;
				color: #bfbfbf;
				display: inline-block;
				width: 100%;
				margin-top: 12px;
			}
			.is-interest {
				margin-top: 16px;
				height: 24px;
				.interest-icon {
					width: 24px;
					height: 24px;
				}
			}
			.comment-content {
				color: #404040;
				font-size: 14px;
			}
			.content-img {
				width: 56px;
				height: 56px;
				border-radius: 4px;
				flex-shrink: 0;
				object-fit: cover;
			}
		}
	}
	.pagination {
		margin-top: 8px;
		text-align: right;
	}
}
</style>
