<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<!-- 筛选区域 -->
			<filter-box
				:filter-list="filterList"
				:filter-form="filterForm"
				@filterChange="filterChange"
				@search="search"
			></filter-box>
			<!-- 列表数据区域 -->
			<ul v-loading="loading" class="list-box">
				<li v-for="(item, index) in list" :key="index" class="list-item">
					<img class="item-img" :src="getImgUrl(item.cover)" alt="" />
					<div class="item-base" @click="jumpPage(`/services-detail?id=${item.id}&type=${type}`)">
						<p class="name over1">{{ item.title }}</p>
						<span class="desc over4">{{ item.introduction }}</span>
					</div>
					<div class="btns-box">
						<el-button type="info" round class="online-btn" @click="contact(item)">
							在线咨询
						</el-button>
						<el-button type="info" plain round class="contact-btn" @click="showTel(item)">
							联系企业
						</el-button>
					</div>
				</li>
				<!-- 分页 -->
				<el-pagination
					v-if="total"
					class="pagination"
					background
					layout="prev, pager, next,jumper"
					:total="total"
					:page-size="pageSize"
					@current-change="handleCurrentChange"
				></el-pagination>
				<Empty v-else :tips="'暂无数据'" />
			</ul>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="messageInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import filterBox from '../components/filter-box.vue';
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
import contactMessage from '@/components/public/contactMessage.vue';
export default {
	components: {
		subBreadcrumb,
		contactMessage,
		filterBox
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			dialogMessageVisible: false,
			messageInfo: {},
			filterList: {
				typeId: {
					label: '',
					porp: 'typeId',
					typeList: []
				}
			}, //
			filterForm: {
				typeId: ''
			},
			loading: false, //加载动画
			keyword: '', //关键字
			type: '',
			typeNum: '', //当前类型是属于就业还是创业 //0是创业 1是就业
			typeId: '', //类型id
			pageNum: 1, //当前页码
			pageSize: 10, //每页条数
			total: 0, //总条数
			filterTitle: '创业服务：',
			list: [] //列表数据
		};
	},
	created() {},
	mounted() {
		this.type = this.$route.query.type || 'serviceType'; //获取类型
		this.typeNum = this.$route.query.typeNum || '0'; //获取类型

		this.typeId = this.$route.query.typeId || ''; //获取类型
		this.filterForm.typeId = this.typeId;
		const listFun = {
			// 就业服务
			serviceType: {
				typeList: 'getServiceTypeList',
				list: 'getServiceList'
			},
			example: {
				typeList: 'getServiceTypeList',
				list: 'getExampleList'
			}
		};
		if (this.typeNum == '0') {
			this.filterTitle = '创业服务：';
		} else {
			this.filterTitle = '就业服务：';
		}
		this.filterList.typeId.label = this.filterTitle;
		listFun[this.type].typeList && this[listFun[this.type].typeList]('typeId');
		listFun[this.type].list && this[listFun[this.type].list]('typeId');
	},
	methods: {
		/**
		 * @description 获取就业类型分页接口
		 * */
		getServiceTypeList(type) {
			const param = {
				type: this.typeNum
			};
			this.$api.employment_api.serviceTypeList(param).then(res => {
				this.filterList[type].typeList = res?.results?.records || [];
				this.filterList[type].typeList.label = this.filterTitle;
			});
		},
		/**
		 * @description 获取就业分页接口
		 * */
		getServiceList(type) {
			if (this.typeNum == 1) {
				this.getJobServiceListE();
			} else {
				this.getJobServiceListS();
			}
			// this.loading = true;
			// const param = {
			// 	keyword: this.keyword,
			// 	typeId: this.typeId
			// };
			// this.$api.employment_api
			// 	.serviceList(param)
			// 	.then(res => {
			// 		this.list = res?.results?.records || [];
			// 		this.total = res.results?.total || 0;
			// 		this.loading = false;
			// 	})
			// 	.catch(() => {
			// 		this.loading = false;
			// 	});
		},
		// 就业服务分页接口
		getJobServiceListE() {
			this.loading = true;
			let param = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				keyword: this.keyword,
				auditStatus: 1,
				typeId: this.typeId
			};
			this.$api.employment_api
				.jobServiceListE(param)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		// 创业服务分页接口
		getJobServiceListS() {
			this.loading = true;
			let param = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				keyword: this.keyword,
				auditStatus: 1,
				typeId: this.typeId
			};
			this.$api.employment_api
				.jobServiceListS(param)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		setMessageInfo(item) {
			let obj = {
				SHOP_NAME: item.enterpriseName,
				SHOP_LOG: item.cover || '',
				SELLER_ID: item.createUserId,
				isGoods: true
			};
			this.messageInfo = obj;
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
			this.getServiceList();
		},
		filterChange(form) {
			this.typeId = form.typeId.id;
			this.pageNum = 1;
			this.getServiceList();
		},
		/**
		 * @description 关键字搜索事件
		 */
		search(name) {
			this.keyword = name;
			this.pageNum = 1;
			this.getServiceList();
		},
		/**
		 * @description 在线咨询
		 * */
		contact(item) {
			this.setMessageInfo(item);
			this.dialogMessageVisible = true;
		},
		/**
		 * @description 展示电话号码
		 */
		showTel(item) {
			this.$alert(item.telephone, '联系号码');
		},
		/**
		 * @description 关键字重置事件
		 */
		// reset() {
		// 	this.pageNum = 1;
		// 	this.getServiceList();
		// },
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.main {
	width: $max-width;
	margin: 20px auto 60px;
}

.list-box {
	width: 100%;
	background: #ffffff;
	padding: 0 20px;
	padding-bottom: 30px;
	margin-top: 20px;
	.list-item {
		width: 100%;
		border-bottom: 1px solid #e8eaf0;
		padding: 18px 0px;
		display: flex;
		justify-content: space-between;
		&:nth-child(4n) {
			margin-right: 0;
		}
		.item-img {
			width: 240px;
			height: 160px;
			// background: #0076e8;
			border-radius: 6px;
			flex-shrink: 0;
			object-fit: cover;
		}
		.item-base {
			width: 100%;
			// padding: 17px 19px;
			margin-left: 18px;
			cursor: pointer;
			.name {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
			}
			.desc {
				display: inline-block;
				font-size: 14px;
				line-height: 18px;
				color: #999999;
				margin-top: 23px;
			}
		}
		.btns-box {
			width: 100px;
			margin-left: 46px;
			flex-shrink: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.online-btn,
			.contact-btn {
				width: 100px;
				height: 30px;
				border-radius: 15px;
				font-size: 14px;
				margin: 10px 0;
				border: 1px solid #0076e8;
			}
			.online-btn {
				background: #0076e8;
				color: #ffffff;
			}
			.contact-btn {
				background: rgba(255, 255, 255, 0);

				color: #0076e8;
			}
		}
	}
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 30px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
