<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="information">
			<LeftNavigation @activeHandel="activeHandel" />
			<RightContent :active="active" />
		</div>
	</div>
</template>

<script>
import LeftNavigation from './components/LeftNavigation.vue';
import RightContent from './components/RightContent.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import { mapState, mapMutations } from 'vuex';
export default {
	components: {
		LeftNavigation,
		RightContent,
		subBreadcrumb
	},
	data() {
		return {
			active: {
				name: '',
				code: ''
			}
		};
	},
	computed: {
		...mapState({
			current: state => state.app.current
		})
	},
	watch: {
		$route: {
			handler: function (route) {
				this.active = route.query.code;
			},
			immediate: true
		}
	},
	mounted() {
		this.SET_CURRENT(-9);
	},
	methods: {
		...mapMutations('app', ['SET_CURRENT']),
		activeHandel(item) {
			this.active = item;
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1460px;
.information {
	width: $max-width;
	margin: 20px auto 0;
	display: flex;
	overflow: hidden;
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #fff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
</style>
