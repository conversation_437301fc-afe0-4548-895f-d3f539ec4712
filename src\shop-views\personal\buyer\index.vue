<template>
	<div class="buyer">
		<div class="main" :style="{ width: isHead() ? '1200px' : '100%' }">
			<div v-if="isHead()" class="lf">
				<div class="head">
					<i class="el-icon-star-off"></i>
					买家中心
				</div>
				<div class="menu">
					<div
						v-for="(item, index) in menuList"
						:key="index"
						class="item"
						:class="menuAct == index ? 'act' : ''"
						@click="handleMenu(index)"
					>
						{{ item.name }}
					</div>
					<div class="line" :style="{ top: lineTop(menuAct) }"></div>
				</div>
			</div>
			<div class="rf" :style="{ width: '964px', margin: isLaw ? '0 auto' : '0 0 0 auto' }">
				<div v-if="isHead()" class="nav">
					<i class="el-icon-s-home icon" @click="$router.push({ path: '/' })"></i>
					<el-breadcrumb separator-class="el-icon-arrow-right">
						<el-breadcrumb-item :to="{ path: '/personal' }">买家中心</el-breadcrumb-item>
						<el-breadcrumb-item>
							<span class="red">{{ urlName || menuList[menuAct].name }}</span>
						</el-breadcrumb-item>
					</el-breadcrumb>
				</div>
				<div class="box"><router-view /></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Buyer',
	data() {
		return {
			isLaw: this.$route.query.isLaw,
			menuAct: 0,
			menuList: [
				// { name: '首页', url: '/personal/home' },
				{ name: '我的订单', url: '/personal/my_order' },
				{ name: '退款订单', url: '/personal/refund_order' },
				{ name: '我的购物车', url: '/shoppingCart' },
				{ name: '我的需求', url: '/personal/need_list' },
				{ name: '收货地址', url: '/personal/shipping_address' },
				{ name: '我的收藏', url: '/personal/collect_list' },
				{ name: '开票记录', url: '/personal/bill_list' },
				{ name: '我的合同', url: '/personal/contract_list' }
				// { name: '基础信息', url: '' },
				// { name: '安全设置', url: '/personal/security_setting' }
			],
			urlName: ''
		};
	},
	created() {
		this.urlName = '';
		this.getUrl(this.$route.path);
		this.isShopLogin();
	},
	methods: {
		isHead() {
			if (this.isLaw == 1) {
				return false;
			} else {
				return true;
			}
		},
		// 判断当前路由
		getUrl(url) {
			if (url == '/personal/order_details') {
				if (this.$route.query.isrefund == 1) {
					this.menuAct = 1;
				} else {
					this.menuAct = 0;
				}
			} else if (url == '/personal/need_details') {
				this.menuAct = 3;
				this.urlName = '需求详情';
			} else if (url == '/personal/bill_details') {
				this.menuAct = 6;
			}
			for (let i = 0; i < this.menuList.length; i += 1) {
				if (url == this.menuList[i].url) {
					this.menuAct = i;
					return;
				}
			}
		},
		// 点击导航栏
		handleMenu(i) {
			if (i !== this.menuAct) {
				this.menuAct = i;
				this.$router.push({
					path: this.menuList[this.menuAct].url
				});
			}
		},
		// 左侧高度
		lineTop(i) {
			return 56 * i + 'px';
		}
	}
};
</script>

<style lang="scss" scoped>
.buyer {
	width: 100%;
	margin: 0 auto;
	background: #f4f5f8;
	min-height: 100vh;
	.main {
		margin: 0 auto;
		width: 1200px;
		min-height: 100vh;
		display: flex;
	}
	.lf {
		width: 220px;
		background: #fff;
		min-height: 900px;
		.head {
			width: 220px;
			height: 66px;
			background: linear-gradient(91deg, #fff7ea 0%, rgba(252, 233, 233, 0) 100%);
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			text-align: center;
			line-height: 66px;
			font-size: 20px;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
		}
		.menu {
			width: 100%;
			position: relative;
			.item {
				padding-left: 20px;
				height: 56px;
				background: #ffffff;
				line-height: 56px;
				font-size: 14px;
				font-weight: 400;
				color: #262626;
				cursor: pointer;
			}
			.act {
				color: var(--brand-6, '#ca3f3b');
			}
			.line {
				width: 4px;
				position: absolute;
				background: var(--brand-6, '#ca3f3b');
				left: 0;
				height: 56px;
				transition: all 0.3s;
			}
		}
	}
	.rf {
		width: 964px;
		margin-left: auto;
		margin-bottom: 55px;
		.icon {
			color: #9aa3ba;
			font-size: 17px;
			margin-right: 8px;
			cursor: pointer;
		}
		.nav {
			margin: 22px 0;
			display: flex;
			align-items: center;
			.red {
				color: var(--brand-6, '#ca3f3b');
			}
		}
		.box {
			background: #fff;
			padding: 20px 16px 20px 14px;
		}
	}
}
</style>
