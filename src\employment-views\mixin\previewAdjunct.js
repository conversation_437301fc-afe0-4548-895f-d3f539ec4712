// import { alumniUrl } from '@/config';

export default {
	watch: {},
	beforeMount() {},
	beforeDestroy() {},
	mounted() {},
	methods: {
		/**获取应用logo*/
		getImgUrl(ids) {
			// baseUrl
			// http://ybzy.eoss.wisesoft.net.cn/gwapi/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=
			// return `${baseUrl}/gwapi/ybzy/front/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
			if (ids) {
				if (ids.indexOf('http') !== -1) {
					return ids;
				}
				const origin = window.location.origin + '/';
				// 判断是否为本地运行localhost，本地加代理
				const host =
					origin.includes('localhost') || origin.includes('10.40') ? origin + 'dev-api/' : origin;
				if (ids.includes('adjunctId=')) {
					const imageIdArray = ids.split('adjunctId=');

					return `${host}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${
						imageIdArray[imageIdArray.length - 1]
					}`;
				}
				const imageIdArray = ids.split(',');
				return `${host}/gwapi/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${
					imageIdArray[imageIdArray.length - 1]
				}`;
			}
		}
	}
};
