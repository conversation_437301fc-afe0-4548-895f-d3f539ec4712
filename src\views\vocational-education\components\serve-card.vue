<!--
 @desc:职教视野（资讯）卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- <img :src="@imgs/home/<USER>" alt="" /> -->
		<img :src="$judgeImg(cardData.logo)" alt="" />
		<p>{{ cardData.name }}</p>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					logo: '',
					name: '故障报修'
				};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'serve', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(flex-start);
	width: 20%;
	// border: 1px solid red;
	// height: 80px;
	border-radius: 5px;
	padding: 15px 15px 35px;
	// margin-bottom: 20px;
	font-family: Microsoft YaHei;
	// background: #e8eaf0;
	cursor: pointer;
	img {
		width: 60px;
		height: 60px;
		// border-radius: 50%;
		margin-right: 19px;
		object-fit: contain;
	}
	p {
		font-size: 16px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #333333;
	}
}
</style>
