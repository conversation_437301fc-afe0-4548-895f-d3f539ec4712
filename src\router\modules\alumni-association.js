/**
 * Name:子项目的路由
 * 目录：alumni-association-views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */
export default {
	views: [],
	subViews: [
		{
			path: 'alumniAssociation',
			name: 'AlumniAssociation',
			component: () => import('@/alumni-association-views/home'),
			meta: { title: '校友会', noShowBreadCrumb: true }
		},
		{
			path: 'alumni-association-list',
			name: 'AlumniAssociationList',
			component: () => import('@/alumni-association-views/active/list'),
			meta: { title: '校友活动', noShowBreadCrumb: true }
		},
		{
			path: 'alumni-association-detail',
			name: 'AlumniAssociationDetail',
			component: () => import('@/alumni-association-views/active/detail'),
			meta: { title: '校友活动详情页', noShowBreadCrumb: true }
		},
		{
			path: 'alumni-show-list',
			name: 'AlumniShowList',
			component: () => import('@/alumni-association-views/show/list'),
			meta: { title: '校友风采', noShowBreadCrumb: true }
		},
		{
			path: 'alumni-news-list',
			name: 'AlumniNewsList',
			component: () => import('@/alumni-association-views/news/list'),
			meta: { title: '列表', noShowBreadCrumb: true }
		},
		{
			path: 'alumni-news-detail',
			name: 'AlumniNewsDetail',
			component: () => import('@/alumni-association-views/news/detail'),
			meta: { title: '详情页', noShowBreadCrumb: true }
		}
	]
};
