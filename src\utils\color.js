/* eslint-disable no-unused-vars */
/*
 * @Description: 颜色模式
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2022-08-19 16:03:26
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-06-01 13:36:20
 */
// 设置全局新的色彩变量
import { presetPalettes, generate } from '@eoss-design/color';

// theme.scss主题文件字符串
let globalThalk = '';

// 默认切换前的风格模式：光亮模式
let oldMode = 'light';

// 默认就的主题色为：'#409EFF'
let globalOldColor = '#409EFF';

// 默认新的主题色为：'#409EFF'
let globalNewColor = '#409EFF';

/**
 * 获取内置色系
 * @function themeTools#handleGetDefaultColor
 * @example
 * ```js
 * const colorList = handleGetDefaultColor()
 * console.log(colorList)
 * >> [{key: color:'blue',color:'#1890ff'},{key:'gold',color:'#faad14'},...]
 * ```
 * @return {Array} 内置12个主色列表red|volcano|orange|gold|yellow|lime|green|cyan|blue|geekblue|purple|magenta
 */
export function handleGetDefaultColor() {
	let newColor = [];
	newColor = Object.keys(presetPalettes).map(colors => {
		return {
			key: colors,
			color: presetPalettes[colors][5]
		};
	});
	return newColor;
}

/**
 * 设置项目主题全局变量,通过计算新的主题色系，将计算好的主题色系通过 <style theme-color='color'>标记放入全局:root以便使用
 * @function themeTools#handleSetThemeVar
 * @param {String} color - 新的主题色
 * @param {String} mode - 风格模式light ｜ dark
 * @param {String} backgroundColor - mode = dark时的页面背景颜色
 *
 * @example
 * ```js
 * handleSetThemeVar(globalNewColor, mode, backgroundColor);
 * ```
 * @return document 新增<style theme-var="globalNewColor">
 *  //主题色色卡列表的css var变量
 * :root[theme-color='#0E91EF']{
 * 	--brand-1:#e6f9ff;
 * 	--brand-2:#b3e9ff;
 * 	--brand-3:#8adaff;
 * 	--brand-4:#61c8ff;
 * 	--brand-5:#38b1fc;
 * 	--brand-6:#0e91ef;
 * 	--brand-7:#0270c9;
 * 	--brand-8:#0054a3;
 * 	--brand-9:#003c7d;
 * 	--brand-10:#002757
 * }
 * </style>
 */
export function handleSetThemeVar(color = '#409EFF', mode = 'light', backgroundColor = '#000000') {
	// 设置缓存主题
	localStorage.setItem('admin-theme', color);
	// 主题名称
	const root =
		mode == 'dark'
			? `:root[theme-color='${color}'][theme-mode='dark']`
			: `:root[theme-color='${color}']`;
	// 获取色彩色系，暗黑模式需要传入模式和暗黑的背景色值即可
	const colorLine = handlerGetThemeCluster(color, mode, backgroundColor);
	// 生成sass变量
	const primaryLight = colorLine.map((color, index) => {
		return `--brand-${index + 1}:${color}`;
	});
	// 生成sass代码
	const themVarString = `${root}{${primaryLight.join(';')}`;
	const themeVarEle = document.getElementById('theme-var');
	// 设置全局主题类型
	// document.documentElement.setAttribute('theme-color', color);
	if (!themeVarEle) {
		const ele = document.createElement('style');
		ele.setAttribute('id', 'theme-var');
		ele.innerText = themVarString;
		document.head.appendChild(ele);
		return;
	}
	themeVarEle.innerText = themVarString;
}

/**
 * 风格模式切换,切换theme-mode风格模式，然后通过<html theme-mode='dark'>切换全局变量
 * @function themeTools#changeMode
 * @param {String} mode - 风格模式light ｜ dark
 * @param {String} backgroundColor - mode = dark时的页面背景颜色
 *
 * @example
 * ```js
 * changeMode('dark' , '#333333');
 * ```
 * @return {null} document 新增<style theme-var="globalNewColor">
 *  //主题色色卡列表的css var变量
 * <html theme-mode="light"></html>
 * <style theme-var="globalNewColor">
 *  //主题色色卡列表的css var变量
 * :root[theme-color='#0E91EF'][theme-mode='dark']{
 * 	--brand-1:#e6f9ff;
 * 	--brand-2:#b3e9ff;
 * 	--brand-3:#8adaff;
 * 	--brand-4:#61c8ff;
 * 	--brand-5:#38b1fc;
 * 	--brand-6:#0e91ef;
 * 	--brand-7:#0270c9;
 * 	--brand-8:#0054a3;
 * 	--brand-9:#003c7d;
 * 	--brand-10:#002757
 * }
 * </style>
 */
export function changeMode(mode = 'light', backgroundColor = '#000000') {
	// 切换主题模式light、dark
	document.documentElement.setAttribute('theme-mode', mode);
	// 模式切换后从新计算一下暗黑或普通的色系
	handleSetThemeVar(globalNewColor, mode, backgroundColor);
	// 同步组件库色彩,将上一次新的颜色作为后一次旧的颜色进行替换，操作风格时是同一组色系的替换
	handleColorLoad(globalNewColor, globalNewColor, mode, backgroundColor);
}

/**
 * 获取样式文件方法
 * @function themeTools#handlerGetCSSString
 * @param {String} url - element主题文件远端地址
 *
 * @example
 * ```js
 * const url = `http://git.wisesoft.net.cn/fe/element-ui/raw/commit/2d25eee89a2a7d7a8fb68f1014f6cefe01aaa5d3/lib/theme-chalk/index.css`;
 * handlerGetCSSString('url');
 * ```
 * @return {Promise} element主题scss文件字符串
 */
export function handlerGetCSSString(url) {
	// 创建XHR对象
	return new Promise(resolve => {
		const xhr = new XMLHttpRequest();
		xhr.onreadystatechange = () => {
			if (xhr.readyState === 4 && xhr.status === 200) {
				resolve(xhr.responseText);
			}
		};
		xhr.open('GET', url);
		xhr.send();
	});
}

/**
 * 获取色彩列表的方法
 * @function themeTools#handlerGetThemeCluster
 * @param {String} color - 主题色
 * @param {String} mode - 风格模式light ｜ dark
 * @param {String} backgroundColor - mode = dark时的页面背景颜色
 *
 * @example
 * ```js
 * handlerGetThemeCluster('#0e91ef','dark','#000000');
 * ```
 * @return {Array} ['#e6f9ff','#0e91ef',...]
 */
export function handlerGetThemeCluster(color, mode = 'light', backgroundColor = '#000000') {
	return generate(color, mode == 'dark' ? { theme: 'dark', backgroundColor } : {});
}

/**
 * 更新样式的方法
 * @function themeTools#handleUpdateColor
 * @param {String} chalk - element主题scss文件代码字符串
 * @param {String} newCluster - 新的主题色系
 * @param {String} oldCluster - 旧的主题色系
 *
 * @example
 * ```js
 * handleUpdateColor(chalkString,['#1890ff',...],['#bfbfbf',...])
 * ```
 * @return {String} 更新完主题颜色的element主题scss文件字符串
 */
export function handleUpdateColor(chalk, newCluster, oldCluster) {
	let colorString = chalk;
	oldCluster.forEach((color, index) => {
		// 遍历就颜色替换为新颜色
		colorString = colorString.replace(new RegExp(color, 'ig'), newCluster[index]);
	});
	return colorString;
}

/**
 * 色彩同步到页面
 * @function themeTools#handleColorLoad
 * @param {String} newVal - 新主题的
 * @param {String} oldVal - 旧主题色
 * @param {String} mode - 风格模式light ｜ dark
 * @param {String} backgroundColor - mode = dark时的页面背景颜色
 *
 * @example
 * ```js
 * handleColorLoad('#1890ff','#bfbfbf','dark','#000000')
 * ```
 * @return {null} 通过将element-theme.scss文件色彩进行更新，更新完后再将字符串写入到<style id='chalk-style'>中,更新全局element到组件样式
 */
export function handleColorLoad(
	newVal,
	oldVal,
	mode = 'light',
	backgroundColor = '#000000',
	url = `http://git.wisesoft.net.cn/fe/element-ui/raw/commit/2d25eee89a2a7d7a8fb68f1014f6cefe01aaa5d3/lib/theme-chalk/index.css`
) {
	// const $message = this.$message({
	// 	message: '  正在切换主题',
	// 	type: 'success',
	// 	duration: 0,
	// 	iconClass: 'el-icon-loading'
	// });
	const oldCluster = handlerGetThemeCluster(oldVal, oldMode, backgroundColor);
	const newCluster = handlerGetThemeCluster(newVal, mode, backgroundColor);
	// 获取主题文件内容
	// |----获取sass文件远程URl
	// 如果设置过主题的话
	if (!globalThalk) {
		// |----获取sass文件内容
		var myStyle = require('!!raw-loader!element-eoss/lib/theme-chalk/index.css');
		// 去除字体文件引入，因为原有样式文件自带字体了，此次无需引入
		const fontFaceString =
			'@font-face{font-family:element-icons;src:url(fonts/element-icons.woff) format("woff"),url(fonts/element-icons.ttf) format("truetype");font-weight:400;font-display:"auto";font-style:normal}';
		globalThalk = myStyle.default.replace(fontFaceString, '');
		hadleUpdateToProject(newVal, oldVal, mode, backgroundColor, newCluster, oldCluster);
		return;
	}
	hadleUpdateToProject(newVal, oldVal, mode, backgroundColor, newCluster, oldCluster);
}
function hadleUpdateToProject(newVal, oldVal, mode, backgroundColor, newCluster, oldCluster) {
	// 更新到项目中去
	const thalk = document.getElementById('chalk-style');
	oldMode = mode; // 缓存风格模式
	globalOldColor = oldVal; //缓存旧的主题色,在切换风格时使用
	globalNewColor = newVal; //缓存新的主题色,在切换风格时使用
	if (!thalk) {
		// 开始更新色彩文件内容,第一次直接只用获取到的
		const newColorStr = handleUpdateColor(globalThalk, newCluster, oldCluster);
		let ele = document.createElement('style');
		ele.setAttribute('id', 'chalk-style');
		ele.innerText = newColorStr;
		document.head.appendChild(ele);
		// $message.close();
		return;
	}
	// 如果设置过主题的话
	if (thalk.innerText) {
		// 开始更新色彩文件内容
		const newColorStr = handleUpdateColor(thalk.innerText, newCluster, oldCluster);
		// 更新色彩值
		thalk.innerText = newColorStr;
		// $message.close();
	}
}

export const themeDefault = [
	'red',
	'volcano',
	'orange',
	'gold',
	'yellow',
	'lime',
	'green',
	'cyan',
	'blue',
	'geekblue',
	'purple',
	'magenta'
];
