<template>
	<div>
		<div class="steps">
			<Steps :status="0"></Steps>
		</div>
		<div class="ir-content">
			<div class="ir-img"><i class="iconfont icon-01_daishenhe"></i></div>
			<div class="ir-status">审核中</div>
			<div class="ir-info">
				为了认证安全，您于{{ details.applyTime }}提交的资料正在人工审核中，
				审核时间预计1-3个工作日，请耐心等待！
			</div>
			<div class="ir-im">
				若有疑问，可
				<base-wx-kefu :kfid="kfid"><a href="javascript:void(0)">咨询客服</a></base-wx-kefu>
			</div>
		</div>
		<div class="info-save">
			<el-button class="info-save-staging" size="large" @click="$emit('cancelAuth')">
				撤销审核
			</el-button>
			<el-button class="info-save-confirm" type="primary" size="large" @click="$emit('refresh')">
				刷新审核结果
			</el-button>
		</div>
	</div>
</template>

<script>
import Steps from '@/components/auth-center/steps';
import { kfid } from '@/config';

export default {
	name: 'InReview',
	components: {
		Steps
	},
	props: {
		details: {
			type: Object,
			default() {
				return {};
			}
		}
	},
	data() {
		return {
			active: 2,
			kfid
		};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	}
};
</script>
<style lang="scss" scoped>
.steps {
	margin: 44px auto 77px auto;
	width: 438px;
	::v-deep.el-step__head {
		.el-step__icon {
			color: var(--gray-6, #bfbfbf);
			border-color: var(--gray-6, #bfbfbf);
		}
		&.is-finish {
			.el-step__icon {
				background-color: var(--brand-6, #0076e8);
				color: #fff;
				border-color: var(--brand-6, #0076e8);
			}
		}
	}
	::v-deep.el-step__title {
		color: var(--gray-6, #bfbfbf);
		&.is-finish {
			font-weight: 500;
			color: var(--brand-6, #0076e8);
		}
	}
	// ::v-deep.el-step__line-inner {
	// 	border-width: 1px!important;
	// 	width: 50%;
	// }
	::v-deep .el-steps .el-step {
		&:nth-child(2) {
			.el-step__icon {
				display: none;
			}
		}
	}
}
.info-save {
	width: 100%;
	padding-top: 40px;
	border-top: 1px solid #d9d9d9;
	text-align: center;
	&-staging {
		margin-right: 30px;
		padding-left: 56px;
		padding-right: 56px;
	}
	&-confirm {
		padding-left: 56px;
		padding-right: 56px;
	}
}
.ir {
	&-content {
		width: 633px;
		padding-top: 73px;
		padding-bottom: 111px;
		text-align: center;
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
		margin: 0 auto;
	}
	&-img {
		i {
			font-size: 63px;
			color: #ff4936;
			background-image: linear-gradient(90deg, #0872f7 0%, #2db8fd 100%);
			background-clip: text;
			color: transparent;
		}
	}
	&-status {
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 44px;
		margin: 10px 0;
	}
	&-info {
		margin-bottom: 42px;
	}
	&-im a {
		color: var(--brand-6, #0076e8);
	}
}
</style>
