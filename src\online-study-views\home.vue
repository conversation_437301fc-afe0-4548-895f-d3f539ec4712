<template>
	<div class="dashboard-container">
		<!-- <div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div> -->
		<div v-loading="loading_banner" class="banner">
			<div class="inner flex0">
				<div class="bisideLeft" @mouseleave="listShow = false">
					<div class="main">
						<div class="main-title">课程分类</div>
						<el-scrollbar style="height: 100%; overflow-x: hidden">
							<div v-for="(item, index) in lessonList" :key="index" class="ele">
								<!-- @mouseenter="showLesson(index)" -->
								<div
									class="tag"
									:style="{ background: lessonColorList[index % 5] }"
									@click="goLessonListPage(item.id)"
								>
									{{ item.name }}
								</div>
								<div class="cont">
									<span
										v-for="(ele, i) in item.children"
										:key="i"
										class="span-cont"
										@click="goTypeList(item.id, ele.id)"
									>
										{{ ele.name }}
									</span>
								</div>
								<i class="el-icon el-icon-d-arrow-right cont-icon" @click="goTypeList(item.id)"></i>
							</div>
						</el-scrollbar>
					</div>
					<div v-show="listShow" class="childList">
						<div class="typeName">{{ showList.name }}</div>
						<ul class="lessons">
							<li
								v-for="(item, index) in showList.children"
								:key="index"
								class="item"
								@click="goTypeList(item.id)"
							>
								{{ item.name }}
							</li>
						</ul>
					</div>
				</div>
				<!-- 轮播图区域 -->
				<div class="swlper flex0">
					<div class="content-carousel">
						<carousel :carousel-height="'360px'" />
					</div>
				</div>
			</div>
		</div>
		<div class="lesson_education">
			<div v-loading="loading_education" class="inner">
				<div class="stitle flex2">
					<span class="title">职业教育</span>
					<!-- <div
						class="more"
						@click="$router.push({ path: '/course-list', query: { orderByColumn: 4 } })"
					>
						更多
						<span>+</span>
					</div> -->
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in zyjyList"
						:key="index"
						class="temp"
						@click="goDetail(item.id)"
					>
						<div class="imgBox">
							<img class="insureImg" :src="item.coverImg" />
							<span class="time-box">{{ item.time }}</span>
							<div class="mask-box">
								<img class="mask-icon" src="@/assets/study_images/play-icon.png" alt="" />
							</div>
						</div>
						<div class="infoBox">
							<div class="name fs14 c6 blod over2">
								{{ item.name }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="lesson-recently">
			<div v-loading="loading_recently" class="inner">
				<div class="stitle flex2">
					<span class="title">超星直播</span>
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in superStarList"
						:key="index"
						v-loading.fullscreen.lock="liveLoading"
						class="temp"
						@click="openLiveBox(item)"
					>
						<div class="infoBox">
							<div class="name over2">
								{{ item.title }}
							</div>
							<div class="time">{{ item.reserveStartTime + ' 至 ' + item.reserveEndTime }}</div>
							<div class="desc over1">
								{{ item.description }}
							</div>
						</div>
						<span :class="['live-tag', checkInSuperLiveSpan(item) ? 'tag-active' : 'tag-gray']">
							{{ checkInSuperLiveSpan(item) }}
						</span>
					</div>
				</div>
			</div>
		</div> -->
		<!-- <div class="lesson-recently">
			<div v-loading="loading_recently" class="inner">
				<div class="stitle flex2">
					<span class="title">最近直播</span>
					<h3>热销好课</h3>
					<div
						class="more"
						@click="$router.push({ path: '/course-list', query: { orderByColumn: 2 } })"
					>
						更多
						<span>+</span>
					</div>
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in liveList"
						:key="index"
						v-loading.fullscreen.lock="liveLoading"
						class="temp"
						@click="openLiveBox(item)"
					>
						<img class="insureImg" :src="testUrl + item.showPictures" />
						<div class="infoBox">
							<div class="name over2">
								{{ item.title }}
							</div>
							<div class="time">{{ item.liveTimeBegin + ' 至 ' + item.liveTimeEnd }}</div>
							<div class="desc over1">
								{{ item.description }}
							</div>
						</div>
						<span :class="['live-tag', checkInLiveSpan(item) ? 'tag-active' : 'tag-gray']">
							{{ checkInLiveSpan(item) ? '直播中' : '未开始' }}
						</span>
					</div>
				</div>
			</div>
		</div> -->
		<div class="lesson-recommend">
			<div v-loading="loading_recommend" class="inner">
				<div class="stitle flex2">
					<h3 class="title">推荐课程</h3>
					<!-- 可切换的分类 -->
					<!-- <ul class="tabs-recommend">
						<li
							v-for="(item, index) in recommendList"
							:key="index"
							:class="['tabs-item', recommendActiveCode == item.code ? 'is-active' : '']"
							@click="recommendTabsClick(item)"
						>
							<span>{{ item.name }}</span>
						</li>
					</ul> -->
					<!-- <div class="more" @click="$router.push({ path: '/course-list', query: { isFree: 1 } })">
						更多
						<span>+</span>
					</div> -->
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in tjCourseListData"
						:key="index"
						class="temp"
						@click="goDetail(item.id)"
					>
						<div class="imgBox">
							<img class="insureImg" :src="item.coverImg" />
							<span class="time-box">{{ item.time }}</span>
							<div class="mask-box">
								<img class="mask-icon" src="@/assets/study_images/play-icon.png" alt="" />
							</div>
						</div>

						<div class="infoBox">
							<div class="name fs14 c6 blod over2">
								{{ item.name }}
							</div>
							<div class="desc over1">
								{{ item.name }}
							</div>
							<div class="star flex2">
								<div class="flex1">
									<el-rate v-model="item.score" disabled></el-rate>
									<div class="score">{{ Number(item.score).toFixed(2) }}分</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 热门主题 -->
		<div class="hot-box">
			<div class="inner">
				<div class="stitle flex2">
					<h3 class="title">热门主题</h3>
				</div>
				<div class="main flex2">
					<template v-for="(item, index) in topClassList.slice(0, 5)">
						<div :key="index" :class="['temp', index == 0 ? 'temp-twofold' : '']">
							<img class="bg-img" :src="item.coverImg" alt="" />
							<div class="info-block">
								<span class="name">{{ item.name }}</span>
								<div class="btn" @click="goLessonListPage(item.id)">
									<span>了解更多</span>
									<i class="el-icon el-icon-caret-right"></i>
								</div>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
		<div class="lesson_new">
			<div class="inner">
				<div class="main flex2">
					<div class="temp">
						<div class="header">
							新课榜
							<img class="header-icon" src="@/assets/study_images/header-icon.png" alt="" />
						</div>
						<ul class="ul">
							<li
								v-for="(item, index) in xkRankData"
								:key="index"
								class="ele flex1"
								@click="goDetail(item.id)"
							>
								<div class="rank"></div>
								<img class="mImg" :src="item.coverImg" alt="" />
								<div class="text over2">
									<div class="name over2">{{ item.name }}</div>
									<div class="study">{{ item.learnPersonNum }}人学习</div>
								</div>
							</li>
						</ul>
					</div>
					<div class="temp">
						<div class="header">
							畅销榜
							<img class="header-icon" src="@/assets/study_images/header-icon.png" alt="" />
						</div>
						<ul class="ul">
							<li
								v-for="(item, index) in cxRankData"
								:key="index"
								class="ele flex1"
								@click="goDetail(item.id)"
							>
								<div class="rank"></div>
								<img class="mImg" :src="item.coverImg" alt="" />
								<div class="text over2">
									<div class="name over2">{{ item.name }}</div>
									<div class="study">{{ item.learnPersonNum }}人学习</div>
								</div>
							</li>
						</ul>
					</div>
					<div class="temp">
						<div class="header">
							学习榜
							<img class="header-icon" src="@/assets/study_images/header-icon.png" alt="" />
						</div>
						<ul class="ul">
							<li
								v-for="(item, index) in xxRankData"
								:key="index"
								class="ele flex1"
								@click="goDetail(item.id)"
							>
								<div class="rank"></div>
								<img class="mImg" :src="item.coverImg" alt="" />
								<div class="text over2">
									<div class="name over2">{{ item.name }}</div>
									<div class="study">{{ item.learnPersonNum }}人学习</div>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<!-- 精彩评论 -->
		<div class="exciting-comments">
			<img src="@/assets/study_images/comments-bg.png" alt="" class="comments-img" />
			<div class="inner">
				<div class="stitle flex1">
					<span class="title">精彩评价</span>
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in topAssessmentList.slice(0, 3)"
						:key="index"
						class="temp"
						@click="goDetail(item.id)"
					>
						<!-- 头部地域 -->
						<div class="title-box">
							<div class="title-left">
								<img class="head-img" :src="item.avatar" />
								<div class="name u-line-1">
									{{ item.username }}
								</div>
							</div>
							<div class="score-box">
								<el-rate v-model="item.score" disabled></el-rate>
								<div class="score">{{ Number(item.score).toFixed(2) }}分</div>
							</div>
						</div>
						<!-- 中间内容区域 -->
						<div class="comments-content">
							{{ item.content }}
						</div>
						<!-- 引用关系 -->
						<div class="quote-box u-line-1">
							{{ item.courseName }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="hezuo">
			<div class="inner">
				<div class="stitle flex1">
					<span class="title">合作机构</span>
				</div>
				<ul class="list flex2">
					<li v-for="(item, index) in cooperative" :key="index" @click="golinkUrl(item.linkUrl)">
						<img :src="testUrl + item.coverImg" class="hezuo-img" />
					</li>
				</ul>
			</div>
		</div>

		<el-dialog
			:title="liveTitle"
			:visible.sync="liveBoxShow"
			:before-close="closeLiveBox"
			top="5vh"
			width="70%"
		>
			<div v-if="liveBoxShow" style="width: 100%; height: 700px; background-color: #000000">
				<video
					id="video"
					controls
					style="width: 100%; height: 100%; border: 1px #85a5ba solid"
				></video>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import config from '@/config';
import Carousel from './components/Carousel.vue';
import Hls from '@/utils/hls.js@latest';
// import Da from 'element-eoss/src/locale/lang/da';
// import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
export default {
	name: 'Dashboard',
	components: {
		Carousel
		// subBreadcrumb
	},
	data() {
		return {
			testUrl: `${config.alumniUrl}/ybzyfile/`, //合作结构图片域名前缀
			fuId: '', //课程分类的id，用于获取该分类下的所属内容集合
			lessonList: [], //课程分类列表
			lessonColorList: ['#F06219', '#485D8C', '#6F58EB', '#48A8F3', '#FFAE28'], //颜色集合
			showList: { name: '', children: [] },
			listShow: false, //课程分类悬浮是否出现分类列表
			// xsCourseListData: [], //
			tjCourseListData: [], //推荐课程
			// rxCourseListData: [], //最近直播
			liveList: [], //直播列表
			liveTitle: '', //直播内容标题
			liveBoxShow: false, //是否打开直播弹框
			liveLoading: false, //是否在加载直播链接中
			hlsInstance: null, //直播hls实例
			zyjyList: [], //职业教育列表数据
			jpCourseListData: [], //精品免费
			recommendActiveCode: '', //当前选中的推荐课程分类页签code
			cxRankData: [], //新课榜
			xkRankData: [], //畅销榜
			xxRankData: [], // 学习榜
			topClassList: [], //热门主题
			topAssessmentList: [], //精彩评论
			cooperative: [], // 合作企业数据
			loading_education: false, //职业教育加载动画
			loading_recently: false, //最近直播加载动画
			loading_recommend: false, // 推荐课程加载动画
			loading_banner: false, // 分类，轮播部分加载动画
			superStarList: [],
			superPage: { current: 1, size: 10 },
			totalCount: ''
		};
	},
	computed: {
		// ...mapGetters(['name', 'siteId', 'userInfo'])
	},
	mounted() {
		this.getCourseClassTreeFn();
		this.tjCourseListFn();
		// this.xsCourseListFn();
		// this.rxCourseListFn();
		// this.jpCourseListFn();
		this.cxRankFn(); //畅销榜
		this.xkRankFn(); //新课榜
		this.xxRankFn(); //学习榜
		this.cooperativeEnterprise(); //合作机构
		this.onlineLearningLive(); //最近直播
		this.zyjyCourseList(); //职业教育
		this.getTopAssessmentList(); //精彩评论
		this.getTopClassList(); //热门主题
		// this.getSuperStarList();
	},

	methods: {
		//超星视频列表
		// async getSuperStarList() {
		// 	const data = await this.$api.study_api.superStarList(this.superPage);
		// 	this.totalCount = data.results.records || 0;
		// 	this.superStarList = data.results.records || [];
		// },
		/**
		 * @description 获取课程分类
		 */
		async getCourseClassTreeFn() {
			const data = await this.$api.study_api.getCourseClassTree();
			this.lessonList = data.data || [];
		},
		showLesson(index) {
			this.listShow = true;
			this.showList = this.lessonList[index];
			this.fuId = this.lessonList[index].id;
		},
		async tjCourseListFn() {
			const { data } = await this.$api.study_api.tjCourseList();
			// console.log('推荐好课', data);
			this.loading_banner = false;
			this.tjCourseListData = data;
		},
		async xsCourseListFn() {
			const { data } = await this.$api.study_api.xsCourseList();
			// console.log('新上好课', data);
			this.loading_education = false;
			this.xsCourseListData = data;
		},
		// async rxCourseListFn() {
		// 	const { data } = await this.$api.study_api.rxCourseList();
		// 	// console.log('热销好课', data);
		// 	this.loading_recently = false;
		// 	this.rxCourseListData = data || [];
		// },
		async jpCourseListFn() {
			const { data } = await this.$api.study_api.jpCourseList();
			this.loading_recommend = false;
			this.jpCourseListData = data;
		},
		/**
		 * @description 职业教育
		 * */
		async zyjyCourseList() {
			this.$api.study_api
				.zyjyCourseList()
				.then(res => {
					this.zyjyList = res.data || [];
				})
				.catch(() => {});
		},
		/**
		 * @description  精彩评论
		 * */
		async getTopAssessmentList() {
			this.$api.study_api
				.getTopAssessmentList()
				.then(res => {
					this.topAssessmentList = res.data || [];
				})
				.catch(() => {});
		},
		/**
		 * @description 热门主题
		 * */
		async getTopClassList() {
			this.$api.study_api
				.getTopClassList()
				.then(res => {
					this.topClassList = res.data || [];
				})
				.catch(() => {});
		},
		// 畅销榜
		async cxRankFn() {
			const { data } = await this.$api.study_api.cxRank();
			this.cxRankData = data;
		},
		// 新课榜
		async xkRankFn() {
			const { data } = await this.$api.study_api.xkRank();
			this.xkRankData = data;
		},
		// 学习榜
		async xxRankFn() {
			const { data } = await this.$api.study_api.xxRank();
			this.xxRankData = data;
		},
		goLessonListPage(id, childId) {
			this.$router.push({ path: '/course-list', query: { id: id, childId } });
		},
		goTypeList(id, childId) {
			// this.$router.push({ path: '/type-list', query: { id: this.fuId, childId } });
			this.$router.push({ path: '/type-list', query: { id: id, childId } });
		},
		goDetail(id) {
			this.$router.push({ path: '/freecourses', query: { id } });
		},
		hotClick() {
			this.jumpPage(`/course-list`);
		},
		//合作企业
		async cooperativeEnterprise() {
			const { results } = await this.$api.information_api.paging({
				tenantId: this._userinfo.tenantId || this.$tenantId,
				nodeCode: 'logisticsLectureCooperativeEnterprise',
				pageSize: 10
			});
			this.cooperative = results?.records || [];
		},
		//最近直播
		async onlineLearningLive() {
			const { results } = await this.$api.information_api.paging({
				tenantId: this._userinfo.tenantId || this.$tenantId,
				nodeCode: 'technicalAbilityLive',
				pageSize: 20
			});
			this.liveList = results?.records || [];
		},
		//检查是否在时段
		checkInSuperLiveSpan(data) {
			let sTime = data.reserveStartTime;
			let eTime = data.reserveEndTime;
			let curDate = new Date();

			// 检查开始时间是否有效且晚于当前时间
			if (sTime && new Date(sTime) > curDate) {
				return '未开始'; // 直播未开始
			}

			// 检查结束时间是否有效且早于当前时间
			if (eTime && new Date(eTime) < curDate) {
				return '已结束'; // 直播已结束
			}

			// 如果开始时间早于或等于当前时间，并且（如果提供）结束时间晚于或等于当前时间，则直播正在进行
			return '直播中';
		},
		//检查是否在时段
		checkInLiveSpan(data) {
			let sTime = data.liveTimeBegin;
			let eTime = data.liveTimeEnd;
			let curDate = new Date();
			if (sTime) {
				let sDate = new Date(sTime);
				if (sDate > curDate) {
					return false;
				}
			}
			if (eTime) {
				let eDate = new Date(eTime);
				if (eDate < curDate) {
					return false;
				}
			}
			return true;
		},
		//打开直播弹框
		async openLiveBox(data) {
			// 打开新页面
			window.open(data.joinUrl);

			// this.liveTitle = data.title;
			// this.liveLoading = true;
			// const resp = await this.$api.treasure_api.getLivePathAndCheck({
			// 	liveSource: data.liveSource,
			// 	liveCode: data.liveCode,
			// 	livePath: data.livePath
			// });
			// this.liveLoading = false;
			// if (resp.code !== 200) {
			// 	this.$message({ message: resp.msg, type: 'warning' });
			// 	return;
			// }
			// this.liveBoxShow = true;
			// this.$nextTick(() => {
			// 	this.loadLiveInfo(resp.results);
			// });
		},
		//关闭直播弹框
		closeLiveBox() {
			if (this.hlsInstance) {
				this.hlsInstance.stopLoad();
			}
			this.liveBoxShow = false;
		},
		//加载直播流信息
		loadLiveInfo(link) {
			if (!Hls.isSupported()) {
				this.$message({ message: '当前浏览器不支持HLS播放', type: 'warning' });
				this.liveBoxShow = false;
				return;
			}
			this.hlsInstance = new Hls();
			this.hlsInstance.loadSource(link);
			let video = document.getElementById('video');
			this.hlsInstance.attachMedia(video);
			this.hlsInstance.on(Hls.Events.MANIFEST_PARSED, function () {
				video.play();
			});
		},
		golinkUrl(url) {
			window.open(url);
		},
		recommendTabsClick(item) {
			this.recommendActiveCode = item.code;
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>
<style lang="scss" scoped>
.dashboard-container {
	height: 100%;
	background: #f4f5f8;
	.stitle {
		.title {
			font-size: 20px;
			font-weight: bold;
			color: #222222;
			line-height: 30px;
		}
	}
	.inner {
		margin: 0 auto;
		width: 1200px;
	}
	.time-box {
		position: absolute;
		right: 4px;
		bottom: 10px;
		padding: 2px 6px;
		border-radius: 4px;
		color: #fff;
		font-size: 12px;
		background: rgba(0, 0, 0, 0.4);
	}
	.imgBox {
		width: 100%;
		border-radius: 6px;
		position: relative;
		.mask-box {
			display: none;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.1);
			align-items: center;
			justify-content: center;
		}
		.mask-icon {
			width: 50px;
			height: 50px;
		}
	}
	.banner {
		background: #f6f7f9;
		padding: 30px 0;
		.bisideLeft {
			margin-right: 20px;
			position: relative;
			width: 100%;
			.main {
				width: 349px;
				overflow: hidden;
				height: 360px;
				background: #ffffff;
				box-sizing: border-box;
				padding: 24px 13px 14px;
				.main-title {
					font-size: 20px;
					font-family: SourceHanSansCN-Bold, SourceHanSansCN;
					font-weight: bold;
					color: #222222;
				}
				.ele {
					cursor: pointer;
					height: 78px;
					box-sizing: border-box;
					padding: 14px 0;
					display: flex;
					align-items: center;
					.tag {
						min-width: 70px;
						padding: 0 7px;
						height: 24px;
						border-radius: 8px 1px 8px 1px;
						font-size: 14px;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						line-height: 24px;
						color: #ffffff;
						margin-right: 20px;
					}
					.flex2 {
						.flex1 {
							.index {
								width: 20px;
								height: 20px;
								background: var(--brand-6, #0076e8);
								border-radius: 50%;
								font-weight: 400;
								font-style: italic;
								color: #ffffff;
								font-size: 14px;
								text-align: center;
								line-height: 20px;
							}
						}
						.iconfont {
							color: #929293;
						}
					}
					.cont {
						font-size: 12px;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						font-weight: 400;
						color: #747d85;
						width: 100%;
						height: 100%;
						color: #333;
						border-bottom: 1px solid #f1f1f1;
						display: flex;
						align-items: center;
						.span-cont {
							position: relative;
							margin: 0 5px;
							&::after {
								content: '/';
								display: inline-block;
								position: absolute;
								right: -8px;
								bottom: -1px;
								font-size: 14px;
							}
							&:hover {
								color: var(--brand-6, #0076e8);
							}
						}
						.span-cont:last-child {
							&::after {
								opacity: 0;
							}
						}
					}
					.cont-icon {
						width: 12px;
						height: 8px;
						font-size: 12px;
						color: #c3c3c3;
						margin-right: 5px;
						flex-shrink: 0;
						&:hover {
							color: var(--brand-6, #0076e8);
						}
					}
				}
				.ele:last-child {
					border: none;
				}
			}
			.childList {
				padding: 30px;
				border-radius: 10px;
				background: #fff;
				position: absolute;
				left: 349px;
				width: 762px;
				z-index: 20;
				top: 0;

				.typeName {
					font-size: 20px;
					font-weight: bold;
				}
				.lessons {
					padding: 0 20px;
					margin-top: 20px;
					height: 253px;
					display: flex;
					flex-direction: row;
					flex-wrap: wrap;
					.item {
						margin-right: 20px;
						cursor: pointer;
						color: #6d7278;
						font-size: 14px;
					}
				}
			}
		}
		.totalData {
			box-sizing: border-box;
			padding: 0 80px 0 40px;
			margin-top: 30px;
			width: 1200px;
			height: 120px;
			background: linear-gradient(0deg, #f5f6fa 0%, #ffffff 100%);
			border-radius: 10px;
			.temp {
				.iconfont {
					color: var(--brand-6, #0076e8);
					font-size: 40px;
				}
				.data {
					margin-left: 23px;
					.num {
						display: flex;
						flex-direction: row;
						align-items: flex-end;
						.value {
							font-size: 30px;
							font-family: PingFang SC;
							font-weight: 500;
							color: var(--brand-6, #0076e8);
							margin-right: 4px;
						}
						.unit {
							color: #747d85;
							font-size: 14px;
							position: relative;
							.tiebie {
								position: absolute;
								top: -7px;
								right: -7px;
							}
						}
					}
					.text {
						margin-top: 8px;
						font-weight: 400;
						color: #747d85;
						font-size: 14px;
					}
				}
			}
		}
		.goodLesson {
			margin-top: 20px;
			display: flex;
			flex-direction: row;

			flex-wrap: wrap;
			.temp {
				cursor: pointer;
				width: 280px;
				height: 72px;
				background: #ffffff;
				border-radius: 6px;
				box-sizing: border-box;
				padding: 14px;
				margin-bottom: 20px;
				margin-right: 20px;
				img {
					height: 44px;
					width: 44px;
					margin-right: 12px;
				}
				.text {
					.n1 {
						font-size: 14px;
						color: #666;
						font-weight: bold;
						margin-bottom: 10px;
					}
					.n2 {
						color: #979da9;
						font-size: 12px;
						font-weight: 400;
					}
				}
			}
			.temp:nth-child(4n) {
				margin-right: 0;
			}
		}
	}
	.lesson_education {
		background: #fff;
		box-sizing: border-box;
		padding: 60px 0;
		.safeMain {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			.temp {
				margin: 30px 25px 0 0;
				position: relative;
				margin-top: 20px;
				width: 280px;
				height: 206px;
				border-radius: 10px;
				overflow: hidden;
				box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.12);
				transition: 0.3s all;
				cursor: pointer;
				.imgBox {
					height: 160px;
				}
				&:hover {
					box-shadow: 0px 0px 20px 0px rgba(35, 56, 77, 0.4);
					.mask-box {
						display: flex;
					}
				}
				.insureImg {
					width: 100%;
					height: 100%;
				}
				.infoBox {
					box-sizing: border-box;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					padding: 14px 10px 0;
					.name {
						font-size: 14px;
						font-weight: bold;
						color: #222222;
					}
				}
			}
			.temp:nth-child(4n) {
				margin: 20px 0px 0px 0;
			}
		}
	}
	.lesson-recently {
		background: #f6f7f9;
		box-sizing: border-box;
		padding: 40px 0 70px 0;
		.safeMain {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			.temp {
				margin: 28px 24px 0 0;
				padding: 28px 10px 14px 24px;
				position: relative;
				width: 280px;
				// height: 104px;
				background: #ffffff;
				border-radius: 10px;
				overflow: hidden;
				cursor: pointer;
				display: flex;
				align-items: center;
				.insureImg {
					width: 54px;
					height: 54px;
					border-radius: 50%;
					margin-right: 16px;
				}
				.infoBox {
					box-sizing: border-box;
					width: 100%;
					overflow: hidden;
					.name {
						font-size: 14px;
						font-weight: 400;
						color: #222222;
						line-height: 18px;
						margin-bottom: 6px;
					}
					.time {
						font-size: 12px;
						color: #666;
						margin-bottom: 6px;
					}
					.desc {
						font-size: 12px;
						color: #8c8c8c;
						line-height: 18px;
					}
				}
				.live-tag {
					position: absolute;
					top: 0;
					left: 0;
					display: inline-block;
					width: 55px;
					height: 20px;
					font-size: 12px;
					color: #ffffff;
					border-radius: 9px 0px 8px 0px;
					text-align: center;
					line-height: 20px;
				}
				.tag-gray {
					background: #545454;
				}
				.tag-active {
					background: #ea9f12;
				}
			}
			.temp:nth-child(4n) {
				margin-right: 0;
			}
		}
	}
	.lesson-recommend {
		background: #fff;
		box-sizing: border-box;
		padding: 60px 0 60px 0;
		.stitle {
			justify-content: start;
			margin-bottom: 16px;
		}
		.tabs-recommend {
			display: flex;
			margin-left: 70px;
			.tabs-item {
				font-size: 14px;
				color: #222222;
				margin-right: 50px;
				cursor: pointer;
			}
			.is-active {
				color: #4f85ff;
				position: relative;
				&::after {
					content: '';
					position: absolute;
					bottom: -8px;
					left: 0;
					width: 100%;
					height: 3px;
					background: #4f85ff;
					border-radius: 2px;
				}
			}
		}
		.safeMain {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			.temp {
				cursor: pointer;
				position: relative;
				margin-top: 20px;
				width: 224px;
				height: 250px;
				background: #ffffff;
				box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.08);
				margin-right: 20px;
				border-radius: 10px;
				overflow: hidden;
				.imgBox {
					height: 139px;
				}
				.insureImg {
					width: 100%;
					height: 100%;
				}
				.infoBox {
					box-sizing: border-box;
					padding: 6px 10px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					.name {
						font-size: 14px;
						font-weight: bold;
						color: #222222;
					}
					.desc {
						font-size: 12px;
						color: #8c8c8c;
						line-height: 18px;
						margin-top: 12px;
					}
					.star {
						::v-deep .el-rate__icon {
							margin-right: 0;
							transform: scale(0.7);
						}
						.score {
							font-size: 12px;
							font-weight: 400;
							color: #ffa82a;
							margin: 0 7px;
						}
					}
				}
				&:hover {
					box-shadow: 0px 0px 20px 0px rgba(35, 56, 77, 0.4);
					.mask-box {
						display: flex;
					}
				}
			}
			.temp:nth-child(5n) {
				margin-right: 0;
			}
		}
	}
	.hot-box {
		padding: 60px 0;
		background: url('~@/assets/study_images/hot-bg.png') no-repeat center;
		.main {
			display: flex;
			flex-wrap: wrap;
		}
		.temp {
			width: 380px;
			height: 240px;
			margin-top: 30px;
			border-radius: 6px;
			padding: 38px;
			position: relative;
			background: #bfbdbd;
			position: relative;
		}
		.bg-img {
			width: 100%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			object-fit: cover;
		}
		.info-block {
			width: 100%;
			height: 100%;
			position: relative;
			// left: 0;
			// top: 0;
		}
		.name {
			font-size: 26px;
			font-weight: 500;
			color: #ffffff;
		}
		.btn {
			position: absolute;
			width: 134px;
			height: 44px;
			border-radius: 2px;
			border: 1px solid #ffffff;
			font-size: 16px;
			color: #ffffff;
			text-align: center;
			line-height: 44px;
			left: 0;
			bottom: 0;
			cursor: pointer;
			&:hover {
				background: #ffffff;
				color: #413a3a;
			}
		}
		.temp-twofold {
			width: 790px;
		}
	}
	.lesson_new {
		padding: 60px 0;
		background: #ffffff;
		.main {
			.temp {
				width: 380px;
				height: 368px;
				background: #ffffff;
				box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.08);
				border-radius: 10px;
				position: relative;
				overflow: hidden;
				.header {
					width: 100%;
					height: 60px;
					background: linear-gradient(360deg, #ffffff 0%, #ffe2da 100%);
					font-size: 24px;
					font-family: DOUYUFont;
					color: #870e0e;
					line-height: 31px;
					letter-spacing: 4px;
					padding-top: 17px;
					padding-left: 26px;
					position: relative;
				}
				.header-icon {
					position: absolute;
					width: 60px;
					height: 60px;
					right: 12px;
					top: 12px;
				}
				ul {
					width: 100%;
					height: 420px;
					padding-top: 20px;
					box-sizing: border-box;
					li {
						cursor: pointer;
						box-sizing: border-box;
						padding: 0 26px;
						margin-bottom: 20px;
						.rank {
							margin-right: 12px;
							width: 26px;
							height: 39px;
							background-size: contain !important;
						}
						.mImg {
							margin-right: 12px;
							width: 100px;
							height: 74px;
							border-radius: 6px;
						}
						.text {
							width: 168px;
							height: 70px;
							.name {
								height: 36px;
								font-size: 14px;
								font-weight: 400;
								color: #666666;
							}
							.study {
								font-size: 12px;
								font-weight: 400;
								color: #969696;
								margin-top: 14px;
							}
						}
					}
					li:nth-child(1) {
						.rank {
							background: url('~@/assets/study_images/top1.png') no-repeat center;
						}
					}
					li:nth-child(2) {
						.rank {
							background: url('~@/assets/study_images/top2.png') no-repeat center;
						}
					}
					li:nth-child(3) {
						.rank {
							background: url('~@/assets/study_images/top3.png') no-repeat center;
						}
					}
				}
			}
		}
	}
	.exciting-comments {
		padding: 60px 0 60px 0;
		position: relative;
		min-height: 280px;
		.comments-img {
			width: 100%;
			height: 280px;
			position: absolute;
			left: 0;
			top: 0;
		}
		.inner {
			position: relative;
		}
		.stitle {
			.title {
				color: #ffffff;
			}
		}
		.safeMain {
			display: flex;
			justify-content: space-between;
			margin-top: 30px;
			.temp {
				width: 380px;
				height: 256px;
				background: #ffffff;
				box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.06);
				border-radius: 10px;
				padding: 20px;
				.title-box {
					display: flex;
					align-items: center;
				}
				.head-img {
					width: 34px;
					height: 34px;
					border-radius: 50%;
					margin-right: 10px;
					flex-shrink: 0;
				}
				.title-left,
				.score-box {
					display: flex;
					align-items: center;
				}
				.title-left {
					width: calc(100% - 160px);
				}
				.score-box {
					flex-shrink: 0;
					.score {
						font-size: 14px;
						color: #f7762f;
					}
				}
				.comments-content {
					width: 339px;
					height: 140px;
					font-size: 14px;
					color: #333333;
					margin-top: 10px;
				}
				.quote-box {
					font-size: 12px;
					color: #4f85ff;
					margin-top: 20px;
				}
			}
		}
		.el-rate__icon {
			margin: 0;
		}
	}
	.hezuo {
		padding-bottom: 60px;
		.inner {
			.tit {
				padding: 43px 0 32px 0;
				span {
					font-size: 24px;
					font-family: FZZhengHeiS-EB-GB;
					font-weight: 400;
					font-style: italic;
					color: #000000;
					margin-left: 14px;
				}
			}
			.list {
				cursor: pointer;
				margin-top: 30px;
				li {
					width: 230px;
					height: 70px;
					background: #ffffff;
					box-shadow: 0px 0px 20px 0px rgba(55, 62, 69, 0.08);
					border-radius: 10px;
					img {
						width: 100%;
						height: 100%;
						border-radius: 6px;
						object-fit: contain;
					}
				}
			}
		}
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 40px;
	}
}
</style>
