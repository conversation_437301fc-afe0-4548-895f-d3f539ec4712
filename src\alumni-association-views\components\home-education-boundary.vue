<template>
	<div class="education-boundary">
		<div
			v-for="(item, index) in alumnaEducationViewList"
			:key="index"
			:class="alumnaEducationViewList.length == index + 1 ? 'item borderNone' : 'item '"
			@click="toDetail(item.id)"
		>
			<img class="pic" :src="getYbzyImg(item.coverImg)" alt="" />
			<div class="right-content">
				<div class="title">
					{{ item.title }}
				</div>
				<div class="bottom">
					<span class="left">
						<i class="el-icon-timer"></i>
						{{ item.publishTime }}
					</span>
					<span class="right">
						<i class="el-icon-view"></i>
						{{ item.viewNum }}
					</span>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { baseUrl } from '@/config';
export default {
	props: {
		alumnaEducationViewList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		// 跳转详情页面
		toDetail(id) {
			this.jumpPage(
				`alumni-news-detail?updateTitle=职教视界详情页&id=${id}&code=alumnaEducationView`
			);
		},
		jumpPage(url) {
			this.$router.push(url);
		},
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.education-boundary {
	width: 100%;
	height: 100%;
	overflow: hidden;
	.item {
		cursor: pointer;
		display: flex;
		height: 122px;
		border-bottom: 1px solid #dcdcdc;
		align-items: center;
		.pic {
			width: 150px;
			height: 90px;
			border-radius: 8px;
			object-fit: cover;
			flex-shrink: 0;
		}
		.right-content {
			padding: 0 13px;
			height: 76px;
			.title {
				height: 36px;
				white-space: normal;
				text-overflow: ellipsis;
				overflow: hidden;
				display: -webkit-box;
				/* 限制在一个块元素显示的文本的行数 */
				/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
				-webkit-line-clamp: 2;
				/* 设置或检索伸缩盒对象的子元素的排列方式 */
				-webkit-box-orient: vertical;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: bold;
				color: #333333;
			}
			.bottom {
				height: 36px;
				display: flex;
				line-height: 50px;
				justify-content: space-between;
				.left {
					display: block;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
				}
				.right {
					width: 86px;
					display: block;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #7a8392;
				}
			}
		}
	}
}
.borderNone {
	border: none !important;
}
</style>
