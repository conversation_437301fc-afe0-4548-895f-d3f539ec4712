<!--
 @desc:个人中心 校友会 校友社区 创建的详情
 @author: WH
 @date: 2023/9/13
 -->
<template>
	<div v-scroll-bottom="handleScroll" class="detail">
		<set-up-card
			v-show="showName !== 'photoControl'"
			show-btn="inform"
			:card-data="communityData"
			@btnHandle="btnHandle"
			@clickCard="clickCard"
		/>
		<div v-show="showName == 'home'">
			<div class="mini-title">
				<span>社区成员</span>
				<p v-if="communityData.isAudit == 1" @click.stop="InviteFn">
					<i class="el-icon-user"></i>
					邀请成员
				</p>
			</div>
			<div v-loading="memberLoading" class="card-box">
				<!-- isAudit （0:待审核;1:审核通过;2:审核未通过;3:禁用;） -->
				<div
					v-for="(item, index) in memberList"
					:key="item.id"
					:class="{ 'mg-r0': (index + 1) % 4 == 0, 'is-audit': item.isAudit == 0 }"
					class="shade-card"
				>
					<p v-if="item.isAudit == 0" class="state">待审核</p>
					<div v-if="communityData.isAudit == 1" class="shade"></div>
					<div v-if="communityData.isAudit == 1" class="btn-box">
						<p v-if="item.isAudit == 0" @click.stop="memberBtn('audit', item.id)">
							<i class="el-icon-s-check"></i>
							审核
						</p>
						<p @click.stop="memberBtn('del', item.id)">
							<i class="el-icon-delete"></i>
							删除
						</p>
					</div>
					<ab-card :card-data="item" />
				</div>
			</div>
			<div class="mini-title">
				<span>社区相册</span>
				<p v-if="communityData.isAudit == 1" @click.stop="photoBtn('add')">
					<i class="el-icon-picture"></i>
					创建相册
				</p>
			</div>
			<div v-loading="photoLoading" class="card-box">
				<div
					v-for="(item, index) in photoList"
					:key="item.id"
					:class="{ 'mg-r0': (index + 1) % 4 == 0 }"
					class="photo-box"
				>
					<div class="photo">
						<img class="is-img" :src="getImgUrl(item.coverImg)" alt="" />
						<!-- <img class="is-img" src="@/assets/shop-images/default-avatar.png" alt="" /> -->
						<div class="shade"></div>
						<div class="btn-box">
							<div class="is-text" @click.stop="photoBtn('look', item.id)">
								<i class="el-icon-view"></i>
								<p>查看</p>
							</div>
							<div
								v-if="communityData.isAudit == 1"
								class="is-text"
								@click.stop="photoBtn('edit', item.id)"
							>
								<i class="el-icon-edit"></i>
								<p>编辑</p>
							</div>
							<div
								v-if="communityData.isAudit == 1"
								class="is-text"
								@click.stop="photoBtn('del', item.id)"
							>
								<i class="el-icon-delete"></i>
								<p>删除</p>
							</div>
						</div>
					</div>
					<p class="photo-info">{{ item.albumName }}</p>
				</div>
			</div>
		</div>
		<div v-show="showName == 'informControl'" v-loading="inforLoading" class="inform-box">
			<InformCard
				v-for="(item, index) in informList"
				:key="index"
				:card-data="item"
				@crudInform="crudInform"
			/>
			<no-data
				v-if="informList.length == 0"
				:tips="{
					title: '暂无数据'
				}"
			/>
			<!-- @noDataFn="setUpBtn" -->
		</div>
		<photo-card v-if="showName == 'photoControl'" :photo-id="photoId" />

		<photo-dialog
			:visible.sync="photoVisible"
			:community-id="communityId"
			:photo-id="photoId"
			@crudPhoto="crudPhoto"
		/>
		<invite-dialog :visible.sync="InviteVisible" :community-id="communityId" />
		<inform-dialog
			:visible.sync="InformVisible"
			:community-id="communityId"
			@crudInform="crudInform"
		/>
		<member-dialog
			:visible.sync="memberVisible"
			:community-id="communityId"
			:member-id="memberId"
			@crudMember="crudMember"
		/>
	</div>
</template>

<script>
import SetUpCard from './set-up-card.vue';
import AbCard from '../address-book/ab-card.vue';
import InviteDialog from './invite-dialog.vue';
import InformDialog from './inform-dialog.vue';
import InformCard from './inform-card.vue';
import PhotoCard from './photo-card.vue';
import PhotoDialog from './photo-dialog.vue';
import { alumniUrl } from '@/config';
import MemberDialog from './member-dialog.vue';
import NoData from '../no-data.vue';

export default {
	components: {
		SetUpCard,
		AbCard,
		InviteDialog,
		InformDialog,
		PhotoDialog,
		InformCard,
		PhotoCard,
		MemberDialog,
		NoData
	},
	directives: {
		scrollBottom: {
			bind(el, binding) {
				el.addEventListener('scroll', () => {
					const scrollHeight = el.scrollHeight;
					const scrollTop = el.scrollTop;
					const clientHeight = el.clientHeight;

					if (scrollHeight - scrollTop === clientHeight) {
						binding.value(); // 调用绑定的方法
					}
				});
			}
		}
	},
	props: {
		communityId: {
			type: String,
			required: true
		},
		communityData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			photoVisible: false,
			InviteVisible: false,
			InformVisible: false,
			showName: 'home',
			inforLoading: false,
			informList: [], //通知列表
			memberVisible: false,
			memberList: [], //成员列表
			memberLoading: false,
			memberId: '',
			photoList: [], //相册列表
			photoLoading: false,
			photoId: ''
		};
	},

	mounted() {
		this.getMember();
		this.getPhotos();
		// this.setBoxscroll();
	},
	beforeDestroy() {
		// this.removeBoxscroll();
	},
	methods: {
		handleScroll() {
			// 触底逻辑
		},
		//社区成员操作
		memberBtn(btnName, id = null) {
			this.memberId = id;
			switch (btnName) {
				case 'audit':
					this.memberVisible = true;
					break;
				case 'del':
					this.$confirm('是否删除该成员?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.delMember();
						})
						.catch();
					break;
			}
		},
		//分页查询社区成员—————————————————
		async getMember() {
			this.memberLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getMember({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.memberList = results?.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.memberLoading = false;
			}
		},
		//删除成员
		async delMember() {
			this.formLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.delMember({
					memberId: this.memberId,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.getMember();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.formLoading = false;
			}
		},

		//分页查询社区相册—————————————————
		async getPhotos() {
			this.photoLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getPhotos({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.photoList = results?.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.photoLoading = false;
			}
		},
		//相册操作
		photoBtn(btnName, id = null) {
			this.photoId = id;

			switch (btnName) {
				case 'add':
					this.photoVisible = true;
					this.photoId = '';
					break;
				case 'look':
					this.showName = 'photoControl';
					break;
				case 'edit':
					this.photoVisible = true;
					break;
				case 'del':
					this.$confirm('是否删除该相册?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
						.then(() => {
							this.delPhotoFn();
						})
						.catch();
					break;
			}
		},
		async delPhotoFn() {
			this.formLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.delPhotos(this.photoId);
				if (rCode == 0) {
					this.$message.success(msg);
					this.getPhotos();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.formLoading = false;
			}
		},
		//分页查询社区通知公告—————————————————
		async getInform() {
			this.inforLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getInform({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.informList = results.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.inforLoading = false;
			}
		},
		crudPhoto(type) {
			// if (type == 'add' && this.showName !== 'informControl') return;
			this.getPhotos();
		},
		crudMember(type) {
			// if (type == 'add' && this.showName !== 'informControl') return;
			this.getMember();
		},
		crudInform(type) {
			if (type == 'add' && this.showName !== 'informControl') return;
			this.getInform();
		},

		btnHandle(cardData) {
			switch (cardData.btnName) {
				case 'addInform':
					this.InformVisible = true;
					break;
				case 'informControl':
					this.showName = 'informControl';
					this.getInform();
					break;

				default:
					this.$emit('btnHandle', cardData);
					break;
			}
		},
		clickCard() {
			this.$emit('clickCard');
		},
		InviteFn() {
			this.InviteVisible = true;
		},
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
@import '@/styles/alumni-association.scss';
.detail {
	height: 100%;
}
.card-box {
	@include flexBox(flex-start);
	flex-wrap: wrap;
}
.shade-card {
	position: relative;
	width: 228px;
	height: 280px;
	border-radius: 4px;
	margin: 0 20px 20px 0;
	&:hover {
		.shade {
			width: 100%;
			height: calc(100% - 1px);
			background-color: rgba(0, 0, 0, 0.8);
			border: 1px solid #e8eaec;
		}
		.btn-box {
			opacity: 1;
		}
	}
	.state {
		position: absolute;
		top: 14px;
		right: 2px;
		z-index: 3;
		font-size: 12px;
		font-weight: bold;
		color: #ffffff;
		transform: rotateZ(45deg);
	}

	.shade {
		position: absolute;
		z-index: 1;
		top: 0;
		left: 0;
	}
	.btn-box {
		@include flexBox(space-evenly);

		position: absolute;
		z-index: 2;
		bottom: 1px;
		left: 1px;
		width: calc(100% - 1px);
		height: 78px;
		background: #ffffff;
		opacity: 0;

		font-size: 14px;
		p {
			cursor: pointer;
			&:nth-of-type(1) {
				color: #ff0000;
			}
			&:nth-of-type(1) {
				color: #ff0000;
			}
		}
	}
}
.is-audit {
	&::before {
		position: absolute;
		top: 1px;
		right: 1px;
		z-index: 3;
		content: '';
		display: inline-block;
		width: 0;
		height: 0;
		border: 32px solid #0076e8;
		border-left-color: transparent;
		border-bottom-color: transparent;
		border-top-right-radius: 4px;
	}
}
.photo-box {
	width: 226px;
	height: 200px;
	background: #ffffff;
	border: 1px solid #e8eaec;
	border-radius: 4px;
	margin: 0 20px 20px 0;

	.photo {
		position: relative;
		&:hover {
			.shade {
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.8);
			}
			.btn-box {
				opacity: 1;
			}
		}
		.is-img {
			width: 226px;
			height: 150px;
			border-top-left-radius: 4px;
			border-top-right-radius: 4px;
		}

		.shade {
			position: absolute;
			z-index: 1;
			top: 1px;
			left: 1px;
		}
		.btn-box {
			@include flexBox();
			position: absolute;
			z-index: 3;
			top: 50%;
			margin-top: -30px;
			left: 1px;
			width: 228px;
			height: 60px;
			font-size: 14px;
			opacity: 0;
			color: #ffffff;
			.is-text {
				width: 80px;
				text-align: center;
				cursor: pointer;
			}
		}
	}
	.photo-info {
		// @include flexBox();
		width: 230px;
		height: 50px;
		padding: 0 20px;
		font-size: 14px;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #7a8392;
		line-height: 50px;
	}
}
.mg-r0 {
	margin-right: 0;
}
</style>
