<!--
 @desc:个人中心 校友会 校友通讯录 卡片类型列表
 @author: WH
 @date: 2023/9/11
 -->
<template>
	<div class="address-book-card" @click="clickCard">
		<header>
			<!-- <img
				class="is-img"
				:src="getImgUrl(cardData.headImg) || require('@/assets/shop-images/default-avatar.png')"
				alt=""
			/> -->
			<headAvator :own-id="cardData.userId" class="is-img" />
			<p>{{ cardData.memberName }}</p>
		</header>
		<article>
			<p>{{ cardData.phoneNum }}</p>
			<p>{{ cardData.email }}</p>
			<p>{{ cardData.currentCompany }}</p>
			<p>{{ cardData.currentPosition }}</p>
		</article>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';

export default {
	props: {
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			const TEXT = ['待面试', '已面试', '职位已关闭'];
			return TEXT[this.cardData.state - 0];
		}
	},
	methods: {
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.address-book-card {
	@include flexBox();
	flex-direction: column;
	width: 230px;
	height: 280px;
	margin: 0 20px 20px 0;
	border-radius: 4px;
	border: 1px solid #e8eaec;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;

	header {
		text-align: center;
		.is-img {
			width: 90px;
			height: 90px;
			border-radius: 50%;
			object-fit: cover;
		}
		p {
			margin: 20px 0 18px 0;
		}
	}
	article {
		text-align: center;
		p {
			font-size: 14px;
			color: #7a8392;
			line-height: 24px;
		}
	}
}
</style>
