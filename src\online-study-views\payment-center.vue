<!-- eslint-disable vue/no-v-html -->
<template>
	<div v-loading="pageLoading" style="background: #f4f6fb">
		<!-- <div class="top">
			<div class="top-a">
				<i class="iconfont icon-zuobiaodingwei"></i>
				当前所在位置：
				<router-link to="/">增值服务平台</router-link>
				>
				<router-link to="/lawLectureHall">物流大讲堂</router-link>
				>
				<span>确认订单</span>
			</div>
		</div> -->
		<div class="main">
			<div class="mycar inner">
				<div class="mycarTop flex1">
					<div class="ns">我的订单</div>
					<div class="total">
						(当前订单共
						<span>{{ orderInfo.subList.length }}</span>
						件)
					</div>
				</div>
				<div class="step flex4">
					<div class="stepItem">
						<div class="number">1</div>
						<div class="ns c6 fs14">我的购物车</div>
					</div>
					<div class="stepItem">
						<div class="number">2</div>
						<div class="ns c6 fs14">成功提交订单</div>
					</div>
				</div>
			</div>
			<div class="shops inner">
				<div class="allChoose flex2">
					<div class="left flex1">
						<div class="fs14 bold c74">订单：{{ orderInfo.id }}</div>
					</div>
					<div class="right flex1">
						<span class="fs14 bold c74">金额</span>
					</div>
				</div>
				<el-checkbox-group>
					<div class="content">
						<div v-for="(item, index) in orderInfo.subList" :key="index" class="temp flex2">
							<div class="tempLeft flex0">
								<!-- <el-checkbox v-model="checked" class="fs14 bold c74"></el-checkbox> -->
								<img class="keImg" :src="item.coverImg" alt="" />
								<div class="word">
									<div class="ns">{{ item.goodsName }}</div>
									<div class="study"></div>
								</div>
							</div>
							<div class="tempRight flex1">
								<div class="price">￥{{ item.goodsAmount }}</div>
							</div>
						</div>
					</div>
				</el-checkbox-group>
			</div>
			<div class="commonTitle inner">支付方式</div>
			<div class="payMoney flex0 inner">
				<img class="orderImg" src="~@/assets/study_images/order.jpg" alt="" />
				<div class="payInfo">
					<div class="p1">您的订单已提交成功，感谢您的订购！</div>
					<div class="p2 flex1">
						在线支付订单编号：
						<span>{{ orderInfo.id }}</span>
						| 应付款金额：
						<span>¥{{ orderInfo.amount.toFixed(2) }}</span>
						<!-- 订单付款详情 -->
					</div>
					<div class="p3">请您在2天23小时59分钟内完成支付，否则订单会被自动取消。</div>
				</div>
			</div>
			<!-- <div class="commonTitle inner">发票信息</div>
			<div class="bill inner flex1">
				<div class="item flex1 item1">
					<div class="block">企业</div>
					<div class="name">四川川大智胜系统集成有限公司</div>
				</div>
				<div class="item flex1 item2">
					<div class="block">个人</div>
					<div class="name">冯佳</div>
				</div>
			</div> -->
			<div class="btns inner">
				<div class="btn1 flex1 btn" @click="orderCancelFn(orderInfo.id)">
					<span>取消订单</span>
				</div>
				<div class="btn2 btn" @click="handlePay(orderInfo.id)">确认无误,立即支付 >></div>
			</div>
		</div>
		<div class="aliform" v-html="payUrl"></div>
	</div>
</template>
<script>
export default {
	components: {},
	data() {
		return {
			orderInfo: {
				subList: []
			},
			payUrl: null,
			dialogVisible: false,
			pageLoading: false
		};
	},
	mounted() {
		this.orderInfo = JSON.parse(localStorage.getItem('orderInfo')) || {
			subList: []
		};
	},
	methods: {
		orderCancelFn(orderId) {
			this.$confirm('确认取消订单, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.study_apiorderCancel({ orderId }).then(res => {
						this.$message({
							type: 'success',
							message: '取消订单成功!'
						});
						setTimeout(() => {
							this.$router.replace({ path: '/course-list' });
						}, 1000);
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消'
					});
				});
		},
		// 支付订单
		handlePay(orderId) {
			this.$message({
				message: '暂不支持购买',
				type: 'warning'
			});
			// this.pageLoading = true;
			// 	lesson
			// 		.orderPay({
			// 			orderId
			// 		})
			// 		.then(res => {
			// 			if (res.code == 200) {
			// 				this.payUrl = res.url;
			// 				this.pageLoading = false;
			// 				this.$nextTick(() => {
			// 					// 获取订单详情来轮询支付结果
			// 					document.forms[0].submit(); //重点--这个才是跳转页面的核心,获取第一个表单并提交
			// 				});
			// 			} else {
			// 				this.pageLoading = false;
			// 				this.$message({
			// 					type: 'info',
			// 					message: '支付信息错误，请联系管理员'
			// 				});
			// 			}
			// 		});
		}
	}
};
</script>
<style scoped lang="scss">
.top {
	width: 100%;
	height: 40px;
	line-height: 40px;
	background: #ffffff;
	color: #999999;
	font-size: 14px;
	.top-a {
		width: 1200px;
		margin: 0 auto;
	}
	span {
		color: var(--brand-6, #0076e8);
	}
}
.inner {
	width: 1200px;
	margin: 0 auto;
}
.main {
	.mycar {
		margin-top: 20px;
		height: 200px;
		background: #ffffff;

		.mycarTop {
			height: 70px;
			padding-left: 23px;
			border-bottom: 1px solid #f2f2f2;
			.ns {
				font-size: 18px;
				font-weight: 400;
				color: #333333;
			}
			.total {
				margin-left: 13px;
				font-size: 14px;

				font-weight: 400;
				color: #999999;
				span {
					color: #ff0000;
				}
			}
		}
		.step {
			margin-top: 45px;
			justify-content: center;
			.stepItem {
				width: 200px;
				height: 9px;
				background: #e8eaf0;
				position: relative;

				.number {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					text-align: center;
					border-radius: 50%;
					line-height: 36px;
					height: 36px;
					width: 36px;
					font-size: 14px;
					background: #e8eaf0;
					color: #999;
				}
				.ns {
					text-align: center;

					margin-top: 30px;
				}
			}
			.stepItem:nth-child(1) {
				border-radius: 5px 0 0 5px;
			}

			.stepItem:nth-child(2) {
				border-radius: 0 5px 5px 0;
				.number {
					background: var(--brand-6, #0076e8);
					font-size: 18px;
					font-weight: 400;
					height: 36px;
					width: 36px;
					color: #fff;
				}
			}
		}
	}
	.shops {
		margin-top: 10px;
		.allChoose {
			height: 40px;
			background: #e8eaf0;
			padding: 0 20px;
			::v-deep .el-checkbox__input {
				border-radius: 50px !important;
			}
			.left {
				:nth-child(2) {
					margin-right: 70px;
				}
			}
			.right {
				:nth-child(1) {
					margin-right: 50px;
				}
			}
		}
		.content {
			.temp {
				border-bottom: 1px solid #e8eaf0;
				padding: 20px;
				background: #fff;
				.tempLeft {
					.keImg {
						margin-left: 5px;
						width: 166px;
						height: 100px;
						border-radius: 6px;
					}
					.word {
						margin-left: 17px;
						.ns {
							padding-top: 10px;
							font-size: 16px;
							font-weight: 400;
							color: #333333;
							padding-bottom: 15px;
						}
						.study {
							font-size: 14px;
							font-weight: 400;
							color: #747d85;
						}
					}
				}
				.tempRight {
					.oprate {
						text-align: center;
						.item {
							color: #747d85;
							font-size: 14px;
							margin: 10px 0;
							font-weight: 400;
						}
					}
					.price {
						font-size: 14px;
						font-weight: 400;
						color: #747d85;
						width: 140px;
						text-align: center;
						margin-right: 0px;
					}
				}
			}
		}
	}
	.commonTitle {
		height: 40px;
		background: #e8eaf0;
		line-height: 40px;
		padding-left: 20px;
		font-size: 14px;
		font-weight: bold;
	}
	.payMoney {
		height: 160px;
		background: #fff;
		box-sizing: border-box;
		padding: 44px 0 0 83px;
		.orderImg {
			margin-right: 14px;
			height: 42px;
			width: 42px;
		}
		.payInfo {
			.p1 {
				font-size: 18px;

				font-weight: 400;
				color: #00714d;
				padding-bottom: 14px;
			}
			.p2 {
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #525252;
				span {
					color: #ff0000;
					font-size: 14px;
					margin-right: 20px;
				}
			}
			.p3 {
				font-size: 12px;
				padding-top: 13px;
				font-weight: 400;
				color: #525252;
			}
		}
	}
	.bill {
		height: 80px;
		padding: 0 20px;
		background: #fff;
		.item {
			margin-right: 70px;
			.block {
				width: 40px;
				height: 40px;

				border-radius: 50%;
				text-align: center;
				line-height: 40px;
				font-size: 14px;
				font-weight: 400;
			}
			.name {
				font-size: 14px;
				margin-left: 16px;
				font-weight: 400;
				color: #747d85;
			}
		}
		.item1 {
			.block {
				background: var(--brand-6, #0076e8);
				color: #fff;
			}
		}
		.item2 {
			.block {
				background: #22ac38;
				color: #fff;
			}
		}
	}
	.btns {
		padding: 20px 0 50px 0;
		text-align: right;
		.btn {
			cursor: pointer;
			display: inline-block;
		}
		.btn1 {
			padding: 0 20px;
			position: relative;
			bottom: 3px;
			line-height: 40px;
			text-align: center;
			height: 40px;
			border: 1px solid var(--brand-6, #0076e8);
			border-radius: 6px;
			font-size: 14px;
			color: var(--brand-6, #0076e8);
		}
		.btn2 {
			margin-left: 20px;
			padding: 0 20px;
			position: relative;
			bottom: 3px;
			line-height: 40px;
			text-align: center;
			height: 40px;
			background: var(--brand-6, #0076e8);
			border-radius: 6px;
			font-size: 14px;
			color: #fff;
		}
	}
}
</style>
