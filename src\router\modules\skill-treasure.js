/**
 * Name:子项目的路由
 * 目录：shop-views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */
export default {
	views: [],
	subViews: [
		{
			path: 'skill-treasure',
			name: 'SkillTreasure',
			component: () => import('@/skill-treasure-views/home'),
			meta: { title: '技能宝库', noShowBreadCrumb: true }
		},
		{
			path: 'skill-treasure-details',
			name: 'SkillTreasure-details',
			component: () => import('@/skill-treasure-views/details'),
			meta: { title: '详情' }
		},
		{
			path: 'personal-homepage',
			name: 'personalHomepage',
			component: () => import('@/skill-treasure-views/personal-homepage'),
			meta: { title: '个人首页' }
		},
		{
			path: 'interest-list',
			name: 'interest-list',
			component: () => import('@/skill-treasure-views/interest-list'),
			meta: { title: '关注' }
		},
		{
			path: 'notice-list',
			name: 'notice-list',
			component: () => import('@/skill-treasure-views/notice-list'),
			meta: { title: '通知' }
		},
		{
			path: 'publication-works',
			name: 'publication-works',
			component: () => import('@/skill-treasure-views/publication-works'),
			meta: { title: '作品发布', needLogin: true }
		}
	]
};
