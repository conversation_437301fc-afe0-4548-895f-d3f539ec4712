import store from '@/store';
/**
 * @Desc 处理面包屑数组
 * @Params.to 即将打开的路由
 * @Params.to.name 页面名称
 * @Params.to.meta.title 路由名称
 * @Params.to.meta.type 排除相同等级的路由
 * @Params.to.path 点击跳转的路由路径
 * @Params.to.fullPath 路由+参数的路径
 * @Params.to.query 路由参数
 * @Params.from 关闭的路由
 * */
import qs from 'qs';
function breadCrumb(to, from) {
	let toRoute = {
		name: to.name,
		title: to.meta ? to.meta.title : to.title || '',
		fullPath: to.fullPath || `${to.path}?${qs.stringify(to.query || {})}`,
		path: to.path,
		query: to.query || {},
		type: to.meta ? to.meta.type : to.type || ''
	};
	// let fromRoute = {
	// 	name: from.name,
	// 	title: from.meta.title,
	// 	path: from.path
	// };
	// 获取缓存的路由记录   项目缓存(刷新页面会丢失)/本地缓存
	let routePath = store.state.app.routePath.length
		? store.state.app.routePath
		: [{ path: '/home', fullPath: '/home', name: 'Home', title: '首页' }];
	let localStorageRoutePath =
		localStorage.getItem('routePath') && JSON.parse(localStorage.getItem('routePath')).length
			? JSON.parse(localStorage.getItem('routePath'))
			: [{ path: '/home', fullPath: '/home', name: 'Home', title: '首页' }];
	// 如果是原地刷新页面，导致项目缓存清空，就是用本地缓存
	if (toRoute.fullPath === localStorageRoutePath[localStorageRoutePath.length - 1].fullPath) {
		routePath = localStorageRoutePath;
	}
	// 如果路由meta中含有type，则要替换掉面包屑中相同type的路由
	let typeIndex = -1;
	if (toRoute.type) {
		typeIndex = routePath.findIndex(item => {
			return item.type === toRoute.type;
		});
	}
	// 拿到子菜单集合
	let subArr = store.state.app.subList || [];
	// 筛选出非首页的路径集合
	subArr = subArr.map(item => {
		return item.url;
	});
	// 首页的话，删除非首页全部路径
	if (toRoute.path === '/home') {
		routePath.splice(1);
	}
	// 如果是二级菜单，只保留二级之前的路径，其余删除
	else if (subArr.includes(toRoute.path)) {
		routePath.splice(1, routePath.length, toRoute);
	}
	// 如果面包屑中含有相同type的路由则替换掉
	else if (typeIndex > -1) {
		routePath.splice(typeIndex, routePath.length, toRoute);
	}
	// 累加路径
	else {
		// 先从历史路径中查找，如果有，则保留该路径之前的路由历史
		let toPathIndex = routePath.findIndex(item => {
			return item.path === to.path;
		});
		if (toPathIndex > -1) {
			routePath.splice(toPathIndex, routePath.length, toRoute); // 可能刷新名字
		} else {
			routePath.push(toRoute);
		}
	}
	store.commit('app/UPDATE_ROUTE_PATH', routePath);
}
export default breadCrumb;
