<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 通知公告管理
 @author: WH
 @date: 2023/9/14
 -->

<template>
	<div v-loading="loading" class="card" @click="clickCard">
		<div class="is-img">
			<img src="@/assets/alumni-association-images/inform.png" alt="" />
		</div>

		<header>
			<span>
				{{ cardData.title }}
			</span>

			<p v-if="showBtn" @click.stop="delInform">
				<i class="el-icon-delete"></i>
				删除
			</p>
		</header>
		<article>
			<p>日期：{{ cardData.updateTime }}</p>
			<p>{{ cardData.content }}</p>
		</article>
	</div>
</template>

<script>
export default {
	props: {
		showBtn: {
			type: Boolean,
			default: true
		},
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			loading: false
		};
	},
	methods: {
		//删除社区通知公告
		delInform() {
			this.$confirm('是否删除该通告?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.delInformFn();
				})
				.catch();
		},
		async delInformFn() {
			this.loading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.delInform(this.cardData.id);
				if (rCode == 0) {
					this.$message.success(msg);
					this.$emit('crudInform', 'del');

					// this.formData = results;
					// this.formData.labelids = results.labelids.split(',');
					// this.uploadId = id;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		btnHandle(btnName) {
			this.$emit('btnHandle', btnName);
		},
		InviteFn() {
			this.$emit('InviteFn');
		},
		clickCard() {
			this.$emit('clickCard', { ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	position: relative;
	width: 100%;
	height: 142px;
	padding: 30px 0 24px 96px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	overflow: hidden;
	// border: 1px solid red;
	.is-img {
		@include flexBox();
		position: absolute;
		top: 50%;
		left: 0;
		margin-top: -40px;
		width: 80px;
		height: 80px;
		background: #d5e9fc;
		border-radius: 50%;
		img {
			width: 36px;
			height: 34px;
		}
	}
	header {
		@include flexBox(space-between);
		// border: 1px solid red;
		margin-bottom: 10px;
		span {
			font-size: 16px;
			color: #0076e8;
		}
		p {
			font-size: 14px;
			color: #ffb300;
			margin-right: 10px;
		}
	}
	article {
		p {
			font-size: 14px;
			color: #8390a3;
			line-height: 24px;
		}
	}
}
</style>
