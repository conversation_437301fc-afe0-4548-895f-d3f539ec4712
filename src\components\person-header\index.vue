<template>
	<div class="title">
		<div class="line"></div>
		{{ title }}
		<span v-if="total">({{ total }})</span>
	</div>
</template>

<script>
export default {
	name: 'Index',
	props: {
		title: {
			type: String,
			default: () => {
				return '系统公告/消息';
			}
		},
		total: {
			type: [String, Number],
			default: () => {
				return '';
			}
		}
	}
};
</script>

<style scoped lang="scss">
.title {
	padding: 18px 20px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: var(--brand-6, #0076e8);
	border-bottom: 1px solid #e8eaf0;
	display: flex;
	align-items: center;
	background: #ffffff;
	.line {
		width: 4px;
		height: 15px;
		background: var(--brand-6, #0076e8);
		border-radius: 2px;
		margin-right: 8px;
	}
}
</style>
