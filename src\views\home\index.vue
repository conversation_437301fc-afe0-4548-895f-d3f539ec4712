<template>
	<div class="home">
		<el-carousel height="400px" trigger="click">
			<el-carousel-item v-for="(item, index) in banner" :key="index">
				<div class="banner-box">
					<img class="banner-img" :src="$judgeFile(item.url)" alt="" />
				</div>
			</el-carousel-item>
		</el-carousel>
		<title-bar
			v-loading="skill.loading"
			title="推荐技能"
			info="体验沉浸式互动技能"
			code="skill"
			:tab-data="{
				list: skill.tabs,
				active: skill.tabActive
			}"
			@titleBarFn="titleBarFn"
			@moreBtn="jumpPage('/skill-treasure')"
		>
			<div class="skill-box">
				<skill-card
					v-for="(item, index) in skill.list.slice(0, 4)"
					:key="index"
					:class="{ mg: [3].includes(index) }"
					:card-data="item"
					@clickCard="clickCard"
				/>
			</div>
			<!-- <div class="skill-box">
				<skill-card-mini
					v-for="(item, index) in skill.list.slice(4)"
					:key="index + 'mini'"
					:card-data="item"
					@clickCard="clickCard"
				/>
			</div> -->
		</title-bar>
		<title-bar
			title="技能商城"
			info="技状元数字化校园好物"
			code="shop"
			:page="shop.page"
			:show-slot="haveData('shop')"
			@titleBarFn="titleBarFn"
			@moreBtn="jumpPage('/shopHome')"
		>
			<div class="shop-wrapper">
				<div class="type-wrapper">
					<div class="type-title">分类</div>
					<div class="type-item bg1" @click="jumpPage('/shopHome')">特产</div>
					<div class="type-item bg2" @click="jumpPage('/shopHome')">非遗</div>
					<div class="type-item bg3" @click="jumpPage('/shopHome')">文创</div>
					<div class="type-item bg4" @click="jumpPage('/online-study')">技术技能培训</div>
					<div class="type-item bg4" @click="jumpPage('/onlineLive')">在线直播</div>
				</div>
				<div v-loading="shop.loading" class="shop-box">
					<!-- :class="{ mg: [1, 2].includes(index % 4) }" -->
					<shop-card
						v-for="(item, index) in shop.list"
						:key="index"
						:attr="index"
						:card-data="item"
						@clickCard="clickCard"
					/>
				</div>
			</div>
		</title-bar>
		<!-- :tab-data="{
				list: recruit.tabs,
				active: recruit.tabActive
			}" -->
		<title-bar
			title="名企招聘"
			info="高收入，好平台，新机遇"
			code="recruit"
			:page="recruit.page"
			@titleBarFn="titleBarFn"
			@moreBtn="jumpPage('/employment-home')"
		>
			<div class="study-box">
				<recruit-card
					v-for="(item, index) in companyList"
					:key="'recruit' + index"
					:card-data="item"
					@clickCard="clickCard"
				/>
			</div>
			<Empty
				v-if="recruit.list && !recruit.list.length"
				:style-obj="{ padding: '50px 0' }"
				:tips="'暂无数据'"
			/>
			<div class="poster-card-box">
				<recruit-poster-card
					v-for="(item, index) in recruiPoster"
					:key="index + 'poster'"
					:index="item - 1"
					:card-data="item"
					@clickCard="jumpPage(item.url)"
				/>
			</div>
		</title-bar>
		<!-- <title-bar
			title="在线学习"
			info="学习无处不在"
			code="study"
			:page="study.page"
			:tab-data="{
				list: study.tabs,
				active: study.tabActive
			}"
			@titleBarFn="titleBarFn"
			@moreBtn="jumpPage('/online-study')"
		>
			<div class="study-box">
				<study-card
					v-for="(item, index) in study.list"
					:key="index"
					:card-data="item"
					@clickCard="clickCard"
				/>
			</div>
			<Empty
				v-if="study.list && !study.list.length"
				:style-obj="{ padding: '50px 0' }"
				:tips="'暂无数据'"
			/>
		</title-bar> -->

		<!-- <title-bar
			title="积分商城"
			info="积分换好礼"
			code="integral"
			:page="integral.page"
			:show-slot="haveData('integral')"
			@titleBarFn="titleBarFn"
		>
			<div v-loading="integral.loading" class="integral-box">
				<integral-card
					v-for="(item, index) in integral.list"
					:key="index"
					:class="{ mg: [3, 7].includes(index) }"
					:card-data="item"
					@clickCard="clickCard"
				/>
			</div>
		</title-bar> -->
		<!-- 校友活动内容 -->
		<!-- <title-bar title="校友会活动" info="" @moreBtn="jumpPage('/alumniAssociation')">
			<div class="integral-box">
				<div v-if="alumniActiveList && alumniActiveList.length" class="alumni-active-box">
					<alumni-active-card :alumni-active-list="alumniActiveList"></alumni-active-card>
				</div>
				<Empty v-else :tips="'暂无数据'" />
			</div>
		</title-bar> -->

		<footer>
			<div class="footer-left">
				<div class="footer-title">
					<p>最新动态</p>
					<span class="more-btn" @click="jumpPage('/information-list?code=dynamicNews')">
						更多
						<i class="el-icon-d-arrow-right"></i>
					</span>
				</div>
				<div
					v-if="information.list.length > 0"
					class="hot-box"
					@click="
						jumpPage(
							`/information-detail?id=${information.list[0].id}&code=${information.list[0].nodeCode}`
						)
					"
				>
					<div class="mask">
						<span class="day">{{ information.list[0].publishTime | dateShow('mouths', '-') }}</span>
						<span class="year">{{ information.list[0].publishTime | dateShow('year') }}</span>
					</div>
					<el-image
						class="img"
						:src="newsweekImg(information.list[0].coverImg)"
						lazy
						fit="cover"
					></el-image>
					<p>{{ information.list[0].title }}</p>
					<p>
						{{ information.list[0].abstract }}
					</p>
				</div>
				<hr />
				<ul v-if="information.list.length > 1">
					<li
						v-for="(item, index) in information.list.slice(1)"
						:key="index"
						@click="jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`)"
					>
						<div>
							<i class="el-icon-menu"></i>
							{{ item.title }}
						</div>
						<span>{{ item.publishTime }}</span>
					</li>
				</ul>
			</div>
			<div class="footer-right">
				<div class="footer-title">
					<p>职教视界</p>
					<span class="more-btn" @click="jumpPage('/information-list?code=educationView')">
						更多
						<i class="el-icon-d-arrow-right"></i>
					</span>
				</div>
				<template v-for="(item, index) in vocational.list">
					<information-card :key="index" :card-data="item" @clickCard="clickCard" />
					<hr v-if="vocational.list.length - 1 !== index" :key="index" />
				</template>
			</div>
		</footer>
		<!-- 强制修改密码 -->
		<EditProfile ref="refEditProfile" />
	</div>
</template>

<script>
import TitleBar from './components/title-bar.vue';
import SkillCard from './components/skill-card.vue';
// import SkillCardMini from './components/skill-card-mini.vue';
import ShopCard from './components/shop-card.vue';
import RecruitCard from './components/recruit-card.vue';
import RecruitPosterCard from './components/recruit-poster-card.vue';
// import StudyCard from './components/study-card.vue';
// import IntegralCard from './components/integral-card.vue';
import InformationCard from './components/information-card.vue';
import EditProfile from '@/components/EditProfile/index.vue';

// import AlumniActiveCard from '@/alumni-association-views/components/home-alumni-active';
import { baseUrl } from '@/config';
import { getDictionaryByCode } from '@/utils';
import cp1 from '@/assets/images/company/ndsd.png';
import cp2 from '@/assets/images/company/qax.png';
import cp3 from '@/assets/images/company/fcdl.png';
import cp4 from '@/assets/images/company/kyqc.png';
import cp5 from '@/assets/images/company/tlkj.png';
import cp6 from '@/assets/images/company/wly.png';

export default {
	name: 'Home',
	components: {
		TitleBar,
		SkillCard,
		// SkillCardMini,
		ShopCard,
		RecruitCard,
		RecruitPosterCard,
		// StudyCard,
		// IntegralCard,
		InformationCard,
		EditProfile
		// AlumniActiveCard
	},
	directives: {
		focus: {
			bind: function (el) {},
			// 指令的定义
			inserted: function (el) {}
		}
	},
	filters: {
		// 用于处理正常时间格式换成年月与日分开
		dateShow(str, type, space) {
			let strTime = '';
			let dateArray = str.split(' ')[0].split('-');
			if (type == 'mouths') {
				strTime = dateArray[1] + space + dateArray[2];
			} else {
				strTime = dateArray[0];
			}
			return strTime;
		}
	},
	data() {
		return {
			banner: [],
			skill: {
				loading: false,
				list: [],
				tabs: [],
				tabActive: 0,
				page: {
					num: 1, // 页数
					size: 12,
					total: 0
				}
			},
			shop: {
				loading: true,
				list: [],
				page: {
					num: 1, // 页数
					size: 6,
					total: 0
				}
			},
			// import cp1 from "@/assets/images/company/ndsd.png";
			// import cp2 from "@/assets/images/company/qax.png";
			// import cp3 from "@/assets/images/company/fcdl.png";
			// import cp4 from "@/assets/images/company/kyqc.png";
			// import cp5 from "@/assets/images/company/tlkj.png";
			// import cp6 from "@/assets/images/company/wly.png";
			companyList: [
				{ name: '宁德时代', img: cp1 },
				{ name: '奇安信(宜宾)科技', img: cp2 },
				{ name: '宜宾丰川动力', img: cp3 },
				{ name: '宜宾凯翼汽车', img: cp4 },
				{ name: '宜宾天珑科技', img: cp5 },
				{ name: '宜宾五粮液', img: cp6 }
			],
			recruit: {
				loading: false,
				list: [],
				tabs: [{ id: '', name: '推荐' }],
				tabActive: '',
				page: {
					num: 1, // 页数
					size: 12,
					total: 0
				}
			},
			study: {
				loading: false,
				list: [],
				tabs: [{ id: 0, name: '推荐' }],
				tabActive: 0,
				page: {
					num: 1, // 页数
					size: 10,
					total: 0
				}
			},
			integral: {
				loading: false,
				list: [],
				page: {
					num: 1, // 页数
					size: 8,
					total: 0
				}
			},
			//最新动态
			information: {
				loading: false,
				list: [],
				page: {
					num: 1, // 页数
					size: 9,
					total: 0
				}
			},
			//职教视野
			vocational: {
				loading: false,
				list: [],
				page: {
					num: 1, // 页数
					size: 3,
					total: 0
				}
			},
			alumniActiveList: [], //校友活动列表数据
			recruiPoster: [
				{
					name: '校企合作',
					coverImg: require('@/assets/employment-images/xqhz.png'),
					url: '/personal?subMenu=5-1',
					bottomList: [
						{
							name: '企业招聘',
							src: '/project-ybzy/ybzy_zhxy/index.html#/independentPersonal/enterprise?type=position'
						},
						{
							name: '名企入驻',
							src: '/project-ybzy/ybzy_zhxy/index.html#/personal'
						},
						{
							name: '在线面试',
							src: '/project-ybzy/ybzy_zhxy/index.html#/independentPersonal/enterprise?type=interviewRecord'
						}
					]
				},
				{
					name: '名师指导',
					coverImg: require('@/assets/employment-images/mszd.png'),
					url: '/mentor-list?nodeCode=pioneerMentorTeacher',
					bottomList: [
						{
							name: '就业指导',
							src: '/project-ybzy/ybzy_zhxy/index.html#/services-type-list?typeNum=1&typeId='
						},
						{
							name: '创业指导',
							src: '/project-ybzy/ybzy_zhxy/index.html#/services-type-list?typeId=1702531122269360129'
						},
						{
							name: '就业培训',
							src: '/project-ybzy/ybzy_zhxy/index.html#/course-list'
						}
					]
				},
				{
					name: '创业扶持',
					coverImg: require('@/assets/employment-images/cyfc.png'),
					url: '/venture-services?typeNum=0',
					bottomList: [
						{
							name: '政府扶持',
							src: '/project-ybzy/ybzy_zhxy/index.html#/services-type-list?typeId=1702531122269360129'
						},
						{
							name: '资金服务',
							src: '/project-ybzy/ybzy_zhxy/index.html#/services-type-list?typeId=1702531122269360129'
						},
						{
							name: '创业培训',
							src: ''
						}
					]
				},
				{
					name: '数据分析',
					coverImg: require('@/assets/employment-images/sjfx.png'),
					url: '/internship-network',
					bottomList: [
						{
							name: '就业案例',
							src: '/project-ybzy/ybzy_zhxy/index.html#/services-type-list?typeNum=1&typeId='
						},
						{
							name: '创业项目',
							src: '/project-ybzy/ybzy_zhxy/index.html#/venture-services?type=project'
						},
						{
							name: '实习网点',
							src: '/project-ybzy/ybzy_zhxy/index.html#/internship-network'
						}
					]
				}
			], //职位四大海报
			peopleNumCodeList: [], //公司人数字典
			corpIndustryList: [] //行业领域字典
		};
	},
	computed: {
		haveData() {
			return name => {
				let list = this[name].list.length == 0;
				let loading = !this[name].loading;
				return list && loading ? true : false;
			};
		}
	},
	created() {
		this.getAdvert();
		this.getShopList();
		this.getInformation();
		this.getVocational();
		this.getSkillTabs();
		this.getIntegralList();
		this.findSysCode('post_job_type'); //获取岗位分类
		this.findSysCodeList();
		this.getJobCoPostList(); // 获取岗位分页列表
		this.lessonTypeFn(); //获取职业教育分类
		this.lessonListFn(); //获取职业教育分页列表
		this.getInterfaceDataList(); //获取校友活动列表数据
	},
	methods: {
		newsweekImg(url) {
			return `${baseUrl}/ybzyfile${url}`;
			// return ybzyImgUrl + url;
		},
		clickCard(cardData) {
			// console.log('>>>cardData', cardData);
			const { cardName } = cardData;
			let id = cardData?.id;
			let productId = cardData?.productId;
			const stack = {
				skill: () => {
					let url = `/skill-treasure-details?id=${id}&type=user`;
					this.$router.push(url);
				},
				shop: () => {
					this.$router.push({
						path: '/shopDetail',
						query: {
							id: productId || id,
							type: cardData.regCode
						}
					});
				},
				integral: () => {
					this.$router.push({
						path: '/shopDetail',
						query: {
							id: cardData.itemId || '',
							type: cardData.regCode,
							goodsType: 'point'
						}
					});
				},
				recruit: () => {
					this.$router.push({
						path: '/job-detail',
						query: {
							id: cardData.id || ''
						}
					});
				},
				study: () => {
					this.$router.push({
						path: '/freecourses',
						query: {
							id: cardData.id || ''
						}
					});
				},
				information: () => {
					this.$router.push({
						path: '/information-detail',
						query: {
							id: cardData.id || '',
							code: cardData.nodeCode || ''
						}
					});
				}
			};
			stack[cardName]();
		},
		jumpPage(url) {
			this.$router.push(url);
		},
		titleBarFn(data) {
			const { clickType, code } = data;
			function isNumber(value) {
				return typeof value === 'number' && !isNaN(value);
			}
			if (clickType == 'changePage') {
				const { code, type } = data;
				type == 'last' && this[code].page.num--;
				type == 'next' && this[code].page.num++;
				isNumber(type) && (this[code].page.num = type);
			}
			if (clickType == 'changeTab') {
				const { code, id } = data;
				this[code].page.num = 1;
				this[code].tabActive = id;
			}
			const stack = {
				shop: this.getShopList,
				skill: this.getSkillList,
				integral: this.getIntegralList,
				recruit: this.getJobCoPostList,
				study: this.lessonListFn
			};
			stack[code] && stack[code]();
		},
		/**获取广告*/
		async getAdvert() {
			let { result } = await this.$api.shop_api.getAdvertsByCode({
				siteId: this.getSiteId(),
				sysCode: 'pc_index_top_top'
			});
			let arr = result?.adData || [];
			this.banner = arr && arr.length && arr;
		},
		//获取技能商城列表——————————————————————————————
		async getShopList() {
			this.shop.loading = true;
			try {
				const { num, size } = this.shop.page;
				let { state, result, totalNum } = await this.$api.shop_api.getAllProduct({
					offset: (num - 1) * size, //offset是从第几条开始
					psize: size,
					noType: 1,
					type1: 1,
					siteId: this.getSiteId()
					// categoryId:
				});
				if (state) {
					this.shop.list = result;
					this.shop.page.total = totalNum;
				}
			} catch (error) {
				console.error('>>>error', error);
				this.shop.list = [];
			} finally {
				this.shop.loading = false;
			}
		},
		//获取推荐技能tabs分类
		async getSkillTabs() {
			try {
				let { code, results } = await this.$api.treasure_api.memberStoryTypeList({
					rentId: this.getSiteId() // 租户id
				});
				if (code == 200) {
					this.skill.tabs = [...results];
					this.skill.tabActive = results[2]?.id;
				}
				this.getSkillList();
			} catch (error) {
				this.typeList = [];
				console.error('>>>', error);
			}
		},
		//获取推荐技能列表数据
		async getSkillList() {
			this.skill.loading = true;
			try {
				const { page, tabActive } = this.skill;
				let { code, results } = await this.$api.treasure_api.memberStoryList({
					pageNum: page.num, // 页数
					pageSize: page.size, // 页面大小
					typeId: tabActive, //类型id
					rentId: this.getSiteId() // 租户id
				});
				if (code == 200) {
					let list = results.records;
					this.skill.list = list.map(item => {
						return {
							...item,
							name: item.memberName,
							time: item.createTime.substring(0, 10)
						};
					});
				}
			} catch (error) {
				this.skill.list = [];
				console.error('>>>', error);
			} finally {
				this.skill.loading = false;
			}
		},

		//获取积分商城列表数据
		async getIntegralList() {
			this.integral.loading = true;
			try {
				const { num, size } = this.integral.page;
				let { code, data, msg } = await this.$api.shop_api.productSku({
					pageNum: num, // 页数
					pageSize: size, // 页面大小
					rentId: this.getSiteId() // 租户id
				});
				if (code == 200) {
					let { records, total } = data;
					this.integral.list = records;
					this.integral.page.total = total;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				this.integral.list = [];
				console.error('>>>', error);
			} finally {
				this.integral.loading = false;
			}
		},
		/**
		 * @description 获取校友活动列表
		 * */
		getInterfaceDataList() {
			let data = {
				'pager.pageNumber': 1,
				'pager.pageSize': '6',
				'queryParams.status': '0,1,2',
				'queryParams.tenementId': this._userinfo.tenantId || this.$tenantId,
				'queryParams.plateCode': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
				// 'queryParams.activityType': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
			};

			this.$api.alumni_association_api.alumniActiveList(data).then(res => {
				this.alumniActiveList = res.data.list;
			});
		},
		//获取最新动态列表——————————————————————————————
		async getInformation() {
			try {
				const { num, size } = this.information.page;
				let { rCode, results, msg } = await this.$api.information_api.paging({
					pageNum: num,
					pageSize: size,
					nodeCode: 'dynamicNews',
					tenantId: this.$tenantId
				});
				if (rCode == 0) {
					this.information.list = results.records;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				this.information.list = [];
				console.error('>>>error', error);
			}
		},
		//获取职教视野列表——————————————————————————————
		async getVocational() {
			try {
				const { num, size } = this.vocational.page;
				let { rCode, results, msg } = await this.$api.information_api.paging({
					pageNum: num,
					pageSize: size,
					nodeCode: 'educationView',
					tenantId: this.$tenantId
				});
				if (rCode == 0) {
					this.vocational.list = results.records.map(item => {
						return {
							...item,
							coverImg: this.newsweekImg(item.coverImg)
						};
					});
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				this.vocational.list = [];
				console.error('>>>error', error);
			}
		},
		/**
		 * @description 查询数据字典
		 * */
		findSysCode(code) {
			let param = {
				sysAppCode: code
			};
			this.$api.employment_api.findSysCode(param).then(res => {
				let list = res?.results || [];
				list = list.reduce((acc, cur) => {
					const obj = {
						id: cur.cciValue,
						name: cur.shortName
					};
					acc.push(obj);
					return acc;
				}, []);
				list = list.slice(0, 6);
				this.recruit.tabs = this.recruit.tabs.concat(list);
			});
		},
		/**
		 * @description 查询数据字典
		 * */
		async findSysCodeList() {
			const dicts = await getDictionaryByCode(['post_company_scale', 'post_industry_area']);
			this.peopleNumCodeList = dicts.post_company_scale || [];
			this.corpIndustryList = dicts.post_industry_area || [];
		},
		/**
		 * @descrtiption 岗位分页接口
		 * */
		getJobCoPostList() {
			let param = {
				pageNum: 1,
				pageSize: 9,
				postType: this.recruit.tabActive,
				auditStatus: 1 //审核状态(0.待审核、1.审核通过、2.驳回)
			};
			this.$api.employment_api.jobCoPostList(param).then(res => {
				this.recruit.list = res?.results?.records || [];
				this.recruit.page.total = res?.results?.total || 1;
				this.recruit.list.map((item, index) => {
					let industryField = item?.enterprise?.industryField;
					let peopleNumCode = item?.enterprise?.peopleNumCode;
					if (industryField) {
						item.enterprise.industryField = this.enterpriseTypeStr(
							industryField,
							this.corpIndustryList
						);
					}
					if (peopleNumCode) {
						item.enterprise.peopleNumCode = this.enterpriseTypeStr(
							peopleNumCode,
							this.peopleNumCodeList
						);
					}
				});
			});
		},
		// 字典转化为字符串
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		},
		// 获取课程类型数据
		async lessonTypeFn() {
			const { data } = await this.$api.study_api.getCourseClassTree();
			let list = data || [];
			this.study.tabs = this.study.tabs.concat(list.slice(0, 6));
		},
		// 获取课程列表数据
		async lessonListFn() {
			// try {
			const { num, size } = this.study.page;
			let courseClassIds = [];
			if (this.study.tabActive) {
				courseClassIds.push(this.study.tabActive);
			}
			const { data } = await this.$api.study_api.pageList({
				courseClassIds,
				isAsc: 'desc',
				orderByColumn: 1,
				pageNum: num,
				pageSize: size
			});
			this.study.list = data.items || [];
			this.study.page.total = data.total || 1;
			// this.total = data.total;
			// this.totalPage = data.totalPage;
			// } catch (error) {}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.home {
	width: 100%;
	// background: #f5f5f5;
	font-family: Microsoft YaHei;
	.shop-wrapper {
		display: flex;
		.type-wrapper {
			flex: 0 0 220px;
			padding: 20px 15px;
			background-color: #fff;
			height: 450px;
			margin-right: 40px;
			.type-title {
				font-size: 20px;
				text-align: center;
				margin-bottom: 20px;
				background: url(./../../assets/shop-images/title-icon.png) no-repeat left 15px center,
					url(./../../assets/shop-images/title-icon.png) no-repeat right 15px center;
				background-size: 20px;
			}
			.type-item {
				font-size: 18px;
				height: 70px;
				line-height: 70px;
				margin-bottom: 15px;
				padding-left: 25px;
				font-weight: bold;
				cursor: pointer;
				&:hover {
					opacity: 0.9;
				}
			}
			.bg1 {
				// background-color: #fdf1f1;
				background: #fdf1f1 url(./../../assets/shop-images/culture.png) no-repeat right 15px center;
				background-size: 50px auto;
			}
			.bg2 {
				// background-color: #fffcdf;
				background: #fffcdf url(./../../assets/shop-images/fy.png) no-repeat right 15px center;
				background-size: 50px auto;
			}
			.bg3 {
				// background-color: #e0f2f8;
				background: #e0f2f8 url(./../../assets/shop-images/study.png) no-repeat right 15px center;
				background-size: 50px auto;
			}
			.bg4 {
				// background-color: #ffefde;
				background: #ffefde url(./../../assets/shop-images/scenic.png) no-repeat right 15px center;
				background-size: 50px auto;
			}
		}
		.shop-box {
			flex: 1;
		}
	}
	.banner-box {
		width: 100%;
		height: 100%;
		.banner-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.skill-box {
		@include flexBox(flex-start);
		flex-wrap: wrap;
		width: 100%;
		.card {
			margin-right: 20px;
			//v-loading会在第一个位置插入元素
			&:nth-child(4n) {
				margin-right: 0;
			}
		}
		.mg {
			margin-right: 0;
		}
	}
	.shop-box {
		@include flexBox(flex-start);
		flex-wrap: wrap;
		width: 100%;
		flex: 0 0 100% !important;
		min-height: 450px;
		align-items: flex-start;
		.card {
			margin-right: 20px;
			margin-bottom: 20px;
		}
		// .mg {
		// 	margin: 0 30px 20px;
		// 	& + .mg {
		// 		margin-left: 0;
		// 	}
		// }
	}
	.study-box {
		@include flexBox(flex-start);
		// flex-wrap: wrap;
		width: 100%;
		flex-wrap: nowrap;
		overflow-x: auto;
		margin-bottom: 20px;
		justify-content: space-between;
		// min-height: 400px;
	}
	.integral-box {
		@include flexBox(flex-start);
		flex-wrap: wrap;
		width: 100%;
		min-height: 300px;
		.card {
			margin-right: 20px;
			//v-loading会在第一个位置插入元素
		}
		.mg {
			margin-right: 0;
		}
	}
	footer {
		width: 1260px;
		margin: 30px auto 40px auto;
		@include flexBox(space-between, flex-start);
		.footer-title {
			@include flexBox(space-between);
			margin-bottom: 30px;
			p {
				font-size: 24px;
				font-weight: bold;
				color: #000000;
			}
			span {
				font-size: 14px;
				color: #747d85;
				cursor: pointer;
			}
		}
		.footer-right {
			hr {
				margin: 29px 0 35px;
				border: 0.5px solid #dcdcdc;
			}
		}
		.footer-left {
			width: 616px;

			.hot-box {
				position: relative;
				.mask {
					width: 180px;
					height: 150px;
					background: rgba(0, 118, 232, 0.75);
					border-radius: 8px 0 0 8px;
					position: absolute;
					top: 0;
					left: 0;
					padding: 41px 30px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					line-height: 28px;
					color: #ffffff;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					.day {
						font-size: 30px;
					}
					.year {
						font-size: 24px;
					}
				}
				.img {
					width: 100%;
					height: 150px;
					border-radius: 8px;
					object-fit: cover;
				}
				p {
					font-size: 14px;
					font-weight: bold;
					color: #333333;
					&:nth-of-type(1) {
						margin: 22px 0;
					}
					&:nth-of-type(2) {
						font-size: 14px;
						color: #7a8392;
					}
				}
			}
			hr {
				margin: 24px 0;
				border: 0.5px solid #dcdcdc;
			}
			li {
				@include flexBox(space-between);
				font-size: 14px;
				color: #7a8392;
				margin-bottom: 16px;
				cursor: pointer;

				.el-icon-menu {
					margin-right: 6px;
				}
			}
		}
	}
	.poster-card-box {
		width: 100%;
		display: flex;
		justify-content: space-between;
	}
	.more-btn {
		display: inline-block;
		width: 70px;
		height: 36px;
		background: #ffffff;
		box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
		border-radius: 18px;
		font-size: 14px;
		color: #747d85;
		text-align: center;
		line-height: 36px;
		cursor: pointer;
		&:hover {
			color: #0076e8;
		}
	}
}
::v-deep .is-active {
	.el-carousel__button {
		width: 22px;
		height: 10px;
		border-radius: 6px;
		background: var(--brand-6, #0076e8);
	}
}
::v-deep .el-carousel__button {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #fff;
}
</style>
