<template>
	<div class="enterprise-auth">
		<div class="steps">
			<Steps :status="9"></Steps>
		</div>
		<el-form ref="form" :model="form" :rules="rules" label-width="140px">
			<div class="business-info">
				<h3 class="form-title">企业工商信息</h3>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="企业性质" prop="corpType">
							<el-select v-model="form.corpType" placeholder="请选择企业性质">
								<el-option
									v-for="(item, index) in plat_enp_quality"
									:key="index"
									:label="item.shortName"
									:value="item.cciValue"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业规模" prop="corpScale">
							<el-select v-model="form.corpScale" placeholder="请选择企业规模">
								<el-option
									v-for="(item, index) in plat_enp_scale"
									:key="index"
									:label="item.shortName"
									:value="item.cciValue"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="所属行业" prop="corpIndustry">
							<el-select v-model="form.corpIndustry" placeholder="请选择所属行业">
								<el-option
									v-for="(item, index) in plat_enp_industry"
									:key="index"
									:label="item.shortName"
									:value="item.cciValue"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业名称" prop="corpName">
							<el-input
								v-model="form.corpName"
								placeholder="请输入企业名称"
								type="input"
								suffix-icon="el-icon-edit"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="营业执照" prop="licensePath">
							<div class="business-license">
								<div class="business-license-upload">
									<uploadItem
										class="avatar-uploader"
										:file-number="1"
										:code="dataCode"
										:own-id="epData.id"
										@fileChange="fileChange"
									/>
								</div>
								<div class="business-license-demo">
									<el-image
										style="width: 100%; height: 100%"
										:src="BusinessLicense"
										:preview-src-list="[BusinessLicense]"
									></el-image>
									<span>示例</span>
								</div>
								<div class="business-license-desc">
									<p>请上传彩色正本原件或加盖企业公章的副本，系统将自动识别证件信息；</p>
									<p>上传图片需清晰完整，不含水印；</p>
									<p>支持png、jpg、jpeg格式文件，单个大小不超过5M</p>
								</div>
							</div>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="统一社会信用代码" prop="socialCode">
							<el-input
								v-model="form.socialCode"
								placeholder="请输入统一社会信用代码"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业成立时间" prop="establishDate">
							<el-date-picker
								v-model="form.establishDate"
								type="date"
								value-format="yyyy-MM-dd"
								placeholder="选择日期"
							></el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="注册资本" prop="registeredCapital">
							<el-input v-model="form.registeredCapital" placeholder="请输入注册资本" type="text">
								<template slot="append">万</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="法定代表人" prop="lawPerson">
							<el-input
								v-model="form.lawPerson"
								type="input"
								placeholder="请输入法人代表"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="营业执照有效期" prop="validityOfLicense">
							<div class="bl-lifespan">
								<div class="bl-lifespan-picker">
									<el-date-picker
										v-model="form.validityOfLicense"
										prefix-icon="el-icon-date"
										type="daterange"
										value-format="yyyy-MM-dd"
										unlink-panels
										range-separator="至"
										start-placeholder="开始日期"
										end-placeholder="结束日期"
										align="right"
										:disabled="checked"
									></el-date-picker>
								</div>
								<div class="bl-lifespan-checkbox">
									<el-checkbox v-model="checked" size="large" @change="handlerValidity">
										长期
									</el-checkbox>
								</div>
							</div>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业注册地址" prop="registeredAddress">
							<el-input
								v-model="form.registeredAddress"
								placeholder="请输入企业注册地址"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="经营范围" prop="businessScope">
							<el-input
								v-model="form.businessScope"
								class="business-scope"
								type="textarea"
								maxlength="1000"
								show-word-limit
								placeholder="请输入经验范围"
							></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<div class="contact-information">
				<h3 class="form-title">企业联系信息</h3>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="企业所属地区" prop="regionId">
							<el-cascader v-model="form.regionId" :options="regionOptions"></el-cascader>
							<!-- <el-select v-model="form.regionId" placeholder="请选择活动区域">
								<el-option label="区域一" value="shanghai"></el-option>
								<el-option label="区域二" value="beijing"></el-option>
							</el-select> -->
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="通讯地址" prop="contactAddress">
							<el-input
								v-model="form.contactAddress"
								placeholder="请输入通讯地址"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业联系人" prop="linkMan">
							<el-input
								v-model="form.linkMan"
								placeholder="请输入企业联系人"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系电话" prop="linkPhone">
							<el-input
								v-model="form.linkPhone"
								placeholder="请输入联系电话"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业电子邮箱">
							<el-input
								v-model="form.corpEmail"
								placeholder="请输入企业电子邮箱"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人电子邮箱" prop="linkEmail">
							<el-input
								v-model="form.linkEmail"
								placeholder="请输入联系人电子邮箱"
								type="input"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="引荐学院" prop="recommenderCode">
							<el-select v-model="form.recommenderCode" placeholder="请选择引荐学院">
								<el-option
									v-for="(item, index) in getOrgList"
									:key="index"
									:label="item.name"
									:value="item.code"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
		</el-form>
		<div class="info-save">
			<el-button class="info-save-staging" size="large" @click="handlerSubmit(9)">暂存</el-button>
			<el-button class="info-save-confirm" type="primary" size="large" @click="handlerSubmit('0')">
				确认信息，下一步
			</el-button>
		</div>
	</div>
</template>

<script>
var checkNum = (rule, value, callback) => {
	if (!value) {
		return callback(new Error('请输入注册资本'));
	}
	if (isNaN(value)) {
		callback(new Error('请输入数字金额'));
	} else {
		callback();
	}
};
import BusinessLicense from '@/assets/images/person/business_license.jpeg';
import uploadItem from '@/components/auth-center/uploadItem';
import { getDictionaryByCode } from '@/utils';
import Steps from '@/components/auth-center/steps';
export default {
	name: 'Form',
	components: { uploadItem, Steps },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			BusinessLicense,
			active: 2,
			id: null,
			dataCode: 'plat_enterprise_validity',
			form: {
				corpType: '', //企业性质
				corpScale: '', //企业规模
				corpIndustry: '', //所属行业
				licensePath: '', //营业执照
				corpName: '', //企业名称
				socialCode: '', //统一社会信用代码
				establishDate: '', //成立时间
				registeredCapital: '', //注册资本
				lawPerson: '', //法人代表
				validityOfLicense: '', //营业执照有效期
				registeredAddress: '', //企业注册地址
				businessScope: '', //经营范围
				regionId: '', //企业所属地
				contactAddress: '', //通讯地址
				linkMan: '', //企业联系人
				linkPhone: '', //联系电话
				corpEmail: '', //企业邮箱
				linkEmail: '', //联系邮箱
				recommenderCode: '' //引荐学院
			},
			rules: {
				corpType: [{ required: true, message: '请选择企业性质', trigger: 'change' }], //企业性质
				corpScale: [{ required: true, message: '请输选择企业规模', trigger: 'change' }], //企业规模
				corpIndustry: [{ required: true, message: '请选择所属行业', trigger: 'change' }], //所属行业
				licensePath: [{ required: true, message: '请上传营业执照', trigger: 'blur' }], //营业执照
				corpName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }], //企业名称
				socialCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }], //统一社会信用代码
				establishDate: [{ required: true, message: '请输入成立时间', trigger: 'blur' }], //成立时间
				registeredCapital: [{ validator: checkNum, trigger: 'blur' }], //注册资本
				lawPerson: [{ required: true, message: '请输入法人代表', trigger: 'blur' }], //法人代表
				validityOfLicense: [{ required: true, message: '请输入营业执照有效期', trigger: 'blur' }], //营业执照有效期
				registeredAddress: [{ required: true, message: '请输入企业注册地址', trigger: 'blur' }], //企业注册地址
				businessScope: [{ required: true, message: '请输入经营范围', trigger: 'blur' }], //经营范围
				regionId: [{ required: true, message: '请输入企业所属地', trigger: 'blur' }], //企业所属地
				contactAddress: [{ required: true, message: '请输入通讯地址', trigger: 'blur' }], //通讯地址
				linkMan: [{ required: true, message: '请输入企业联系人', trigger: 'blur' }], //企业联系人
				linkPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }], //联系电话
				linkEmail: [{ required: true, message: '请输入联系邮箱', trigger: 'blur' }], //联系邮箱
				recommenderCode: [{ required: true, message: '请选择引荐学院', trigger: 'change' }] //引荐学院
			},
			// 性质区域
			regionOptions: [],
			checked: false,
			plat_enp_quality: [],
			plat_enp_scale: [],
			plat_enp_industry: [],
			regionData: [],
			file: null,
			getOrgList: [] //引荐学院
		};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	watch: {
		epData: {
			handler(newVal, oldVal) {
				const val = JSON.parse(JSON.stringify(newVal));
				const keys = Object.keys(val);
				if (keys.length > 0) {
					keys.forEach(key => {
						const myVal = val[key];
						if (myVal) this.form[key] = myVal;
					});
					// 初始化营业执照地址
					if (this.file) this.form.licensePath = this.file;
					// 企业所属地区
					if (val.regionId) this.form.regionId = val.regionId.split(',');
					// 是否长期
					if (val.validityOfLicense == 'longperiod') {
						this.checked = val.validityOfLicense == 'longperiod' ? true : false;
						this.$nextTick(() => {
							this.handlerValidity(true);
						});
					}
					// 营业执照有效期
					this.form.validityOfLicense =
						val.validityStartDate && val.validityEndDate
							? [val.validityStartDate, val.validityEndDate]
							: null;
					// 注册金额转换成万
					this.form.registeredCapital = val.registeredCapital;
				}
			},
			deep: true, // 深度观察监听 设置为 true
			immediate: true // 第一次初始化渲染就可以监听到
		}
	},
	mounted() {
		this.handlerGetRegion();
		this.handlerQueryDict();
		this.getOrgListF();
	},
	methods: {
		// 获取企业所属地区
		handlerGetRegion() {
			this.$api.personal_api.getRegion().then(res => {
				const { results } = res;
				// 获取格式化数据
				this.regionData = results;
				this.regionOptions = this.handleInitRegionData(results, 100000000000);
			});
		},
		// 格式化行政区域数据格式
		handleInitRegionData(data, parentId) {
			const regTree = [];
			data.forEach(reg => {
				if (reg.parentId == parentId) {
					const regObject = {
						value: reg.id,
						label: reg.name
					};
					const childrenData = this.handleInitRegionData(data, reg.id);
					if (childrenData.length > 0) {
						regObject.children = childrenData;
					}
					regTree.push(regObject);
				}
			});
			return regTree;
		},
		// 获取二级学院数据
		getOrgListF() {
			this.$api.personal_api.getOrgList().then(({ rCode, msg, results }) => {
				if (rCode == 0) {
					let list = results || [];
					this.getOrgList = list.reduce((acc, cur) => {
						acc = acc.concat(cur.children || []);
						return acc;
					}, []);
				}
			});
		},
		// 数据字典查询
		async handlerQueryDict() {
			const dicts = await getDictionaryByCode([
				'plat_enp_quality',
				'plat_enp_scale',
				'plat_enp_industry'
			]);
			this.plat_enp_quality = dicts.plat_enp_quality;
			this.plat_enp_scale = dicts.plat_enp_scale;
			this.plat_enp_industry = dicts.plat_enp_industry;
		},
		// 提交数据
		async handlerSubmit(status) {
			this.$refs.form.validate(async valid => {
				if (valid) {
					// 确认信息提示框
					let contiuneStatus = true;
					if (status != 9) {
						await this.$confirm(
							'我司保证所填信息真实有效，若有虚假宣传，我司承担一切责任。',
							'提示',
							{
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning'
							}
						)
							.then(() => {
								contiuneStatus = true;
							})
							.catch(() => {
								contiuneStatus = false;
							});
					}
					if (!contiuneStatus) {
						return;
					}
					const submitData = {
						id: this.epData.id,
						corpType: this.form.corpType, //企业性质
						corpScale: this.form.corpScale, //企业规模
						corpIndustry: this.form.corpIndustry, //所属行业
						// licensePath: this.form.licensePath, //营业执照
						corpName: this.form.corpName, //企业名称
						socialCode: this.form.socialCode, //统一社会信用代码
						establishDate: this.form.establishDate, //成立时间
						registeredCapital: this.form.registeredCapital, //注册资本转换成元
						registeredAddress: this.form.registeredAddress, //企业注册地址
						businessScope: this.form.businessScope, //经营范围
						contactAddress: this.form.contactAddress, //通讯地址
						linkMan: this.form.linkMan, //企业联系人
						linkPhone: this.form.linkPhone, //联系电话
						corpEmail: this.form.corpEmail, //企业邮箱
						linkEmail: this.form.linkEmail, //联系邮箱
						recommenderCode: this.form.recommenderCode, //引荐学院
						// 法人信息
						lawPerson: this.form.lawPerson, // //法人代表
						status
					};
					// 去最后一个ID
					submitData.regionId = this.form.regionId.join(','); //企业所属地
					// 长期
					if (this.checked) {
						submitData.validityOfLicense = 'longperiod';
						submitData.validityStartDate = null; //营业执照有效期-开始
						submitData.validityEndDate = null; //营业执照有效期-结束
					} else {
						submitData.validityOfLicense = '';
						submitData.validityStartDate = this.form.validityOfLicense[0]; //营业执照有效期-开始
						submitData.validityEndDate = this.form.validityOfLicense[1]; //营业执照有效期-结束
					}
					this.$api.personal_api
						.saveEnterpriseInfo(submitData)
						.then(res => {
							if (res.rCode == 0) {
								// 提交成功
								if (status != 9) {
									this.$message.success('提交成功');
									this.$emit('enterpriseInfo');
									// this.$emit('compChange', 'InReview');
								} else {
									// 暂存成功
									this.$message.success('暂存成功');
								}
							}
						})
						.catch(err => {
							console.log(err);
						});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},
		// 监听长期按钮
		handlerValidity(val) {
			if (val) {
				this.$set(this.rules, 'validityOfLicense', [
					{ required: false, message: '请输入营业执照有效期', trigger: 'blur' }
				]);
			} else {
				this.$set(this.rules, 'validityOfLicense', [
					{ required: true, message: '请输入营业执照有效期', trigger: 'blur' }
				]);
			}
			this.$refs.form.validateField('validityOfLicense');
		},
		fileChange(file) {
			if (file) {
				this.$nextTick(() => {
					this.file = file;
					this.$set(this.form, 'licensePath', file);
					this.$refs.form.validateField('licensePath');
				});
			}
		},
		handleOgInit(id) {
			var nodes = {};
			[...this.regionData].forEach(node => {
				if (id == node.id) {
					nodes = node;
					const ids = this.getOgTree(this.regionData, nodes);
					this.form.regionId = ids.rever.reverse();
					return;
				}
			});
		},
		// 根据一个ID节点查询完整路径
		getOgTree(data, pnode) {
			let ids = [pnode.id];
			if (pnode.id != 100000000000) {
				// 通过当前ID获取上级ID
				data.forEach(node => {
					if (pnode.parentId == node.id) {
						ids.push(node.id);
						// 还存在上级节点
						if (JSON.stringify(data).includes(node.parentId)) {
							ids = [...ids, this.getOgTree(data, node.parentId)];
						} else {
							return;
						}
					}
				});
				return ids;
			} else {
				return [];
			}
		}
	}
};
</script>

<style lang="scss">
.enterprise-auth {
	.el-col-12 {
		.el-select,
		.el-input,
		.el-date-editor,
		.el-textarea,
		.el-date-editor.el-input {
			width: 260px;
		}
	}
	.el-col-24 {
		.el-select,
		.el-input,
		.el-date-editor,
		.el-textarea {
			width: 710px;
		}
	}
	.el-form-item {
		margin-bottom: 24px;
	}
}
</style>

<style lang="scss" scoped>
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.business-info {
	overflow: hidden;
	margin-bottom: 98px;
}
.business-license {
	display: flex;
	&-upload {
		margin-right: 23px;
	}
	&-demo {
		width: 160px;
		height: 106px;
		border: 1px solid #d9d9d9;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		margin-right: 21px;
		span {
			position: absolute;
			display: inline-block;
			width: 33px;
			height: 19px;
			line-height: 20px;
			background: rgba(0, 0, 0, 0.5);
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #ffffff;
			text-align: center;
			right: 0;
			top: 0;
		}
		img {
			width: 100%;
			height: 100%;
		}
	}
	&-desc {
		height: 106px;
		padding-top: 48px;
		p {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
			margin: 0;
		}
	}
}
.business-scope {
	::v-deep textarea {
		height: 105px;
	}
}
.bl-lifespan {
	display: flex;
	::v-deep .el-date-editor {
		width: 260px;
	}
	::v-deep .el-checkbox__inner {
		height: 16px;
		width: 16px;
		border-radius: 0;
		&::after {
			height: 8px;
			left: 5px;
		}
	}
	&-checkbox {
		margin-left: 16px;
	}
}
.contact-information {
	padding-bottom: 73px;
}
.info-save {
	width: 100%;
	padding-top: 40px;
	border-top: 1px solid #d9d9d9;
	text-align: center;
	&-staging {
		margin-right: 30px;
		padding-left: 56px;
		padding-right: 56px;
	}
	&-confirm {
		padding-left: 56px;
		padding-right: 56px;
	}
}
::v-deep .el-range-editor.is-disabled {
	input {
		background-color: transparent !important;
		color: rgba(0, 0, 0, 0.4) !important;
	}
	.el-range-separator {
		color: rgba(0, 0, 0, 0.4) !important;
	}
}
</style>
