<template>
	<div class="skill-treasure">
		<!-- 技能宝库首页 -->
		<div class="type-list-box">
			<tabs-nav :type-list="typeList" :active-code="activeCode" @click="typeClick" />
			<div class="publication-btn" @click="publication()">
				<img src="@/assets/skill-treasure/publication-icon.png" class="publication-icon" />
				发布作品
			</div>
		</div>
		<tabs-nav
			class="sort-list-box"
			:type-list="sortList"
			:active-code="sortCode"
			@click="sortClick"
		/>
		<!-- 内容区域 -->
		<div v-loading="loading" class="content-box">
			<Empty v-if="list.length == 0" :tips="'暂无数据'" />
			<template v-else>
				<div
					v-for="(item, index) in list"
					:key="index"
					class="content-box-item"
					@click="contentClick(item)"
				>
					<div class="item-box">
						<!-- 文章图片 -->
						<img :src="$judgeFile(item.coverImg)" class="item-img" />
						<div class="item-content">
							<!-- 文章详情部分 标题，作者信息 -->
							<span class="content-title u-line-2">{{ item.title }}</span>
							<div class="content-bottom">
								<div class="bottom-left">
									<img
										:src="item.coverImg ? $judgeFile(item.coverImg) : defaulAvatar"
										class="author-img"
									/>
									<span class="author">{{ item.memberName }}</span>
								</div>
								<div class="bottom-right" @click.stop="memberInteract(1, item)">
									<img
										v-if="item.isDiggs"
										src="@/assets/skill-treasure/heart-small-active.png"
										class="item-icon"
									/>
									<img v-else src="@/assets/skill-treasure/heart-small.png" class="item-icon" />
									<span>{{ item.diggs }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<el-pagination
			v-if="total"
			background
			layout="prev, pager, next"
			class="pagination"
			:current-page="pageNum"
			:page-size="pageSize"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
import tabsNav from './components/tabs.vue';
export default {
	components: { tabsNav },
	data() {
		return {
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			// 类型列表
			typeList: [],
			activeCode: '', //选中的类型
			sortList: [
				{ code: 'tb1.create_time', name: '最新' },
				{ code: 'tb1.views', name: '最热' }
			],
			sortCode: '', //选中的排序
			// 内容部分数据
			list: [],
			loading: false,
			pageNum: 1, //页码
			pageSize: 8, //页面请求数
			total: 0 //总数
		};
	},
	mounted() {
		this.sortCode = 'tb1.create_time';
		this.memberStoryTypeList();
	},
	methods: {
		/**
		 * @description 类型点击事件，用于切换内容
		 * @params {item} 点击的类型数据
		 */
		typeClick(item) {
			this.activeCode = item.code;
			this.list = [];
			this.pageNum = 1;
			this.getList();
		},
		sortClick(item) {
			this.sortCode = item.code;
			this.list = [];
			this.pageNum = 1;
			this.getList();
		},
		/**
		 * @description 发布作品
		 * */
		publication() {
			this.$router.push(`/publication-works`);
		},
		/**
		 * @description 内容点击事件
		 * @params {item} 点击内容数据
		 * */
		contentClick(item) {
			let url = `/skill-treasure-details?id=${item.id}&type=user`;
			this.$router.push(url);
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			this.getList();
		},
		/**
		 * @description 获取分类
		 * */
		memberStoryTypeList() {
			let params = {
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.memberStoryTypeList(params).then(res => {
				if (res.code == 200) {
					this.typeList = res.results || [];
					this.typeList = this.typeList.reduce((acc, item) => {
						let obj = {
							...item,
							code: item.id
						};
						acc.push(obj);
						return acc;
					}, []);
					this.activeCode = this.typeList[2].code;
				}
				this.getList();
			});
		},
		/**
		 * @description 获取列表数据
		 * */
		getList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				typeId: this.activeCode, //类型id
				sortValue: this.sortCode, //排序
				rentId: this.getSiteId() // 租户id
				// queryOwn
			};
			this.$api.treasure_api.memberStoryList(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		},
		/**
		 * @description 1点赞攻略    2收藏攻略  6关注用户   11点赞评论 操作
		 * @param {code} 1点赞攻略    2收藏攻略  6关注用户   11点赞评论
		 * */
		memberInteract(code, item, index) {
			// 未登录不继续执行
			if (!this.isShopLogin()) {
				return;
			}
			let objId = item.id;
			let param = {
				rentId: this.getSiteId(), // 租户id
				objId,
				actType: code
			};
			this.$api.treasure_api.memberInteract(param).then(res => {
				if (res.code == 200) {
					item.isDiggs = !item.isDiggs;
					if (item.isDiggs) {
						item.diggs++;
					} else {
						item.diggs--;
					}
					this.$message({
						type: 'success',
						message: `${res.msg}`
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1200px;

.skill-treasure {
	width: $max-width;
	margin: 0 auto;
	padding: 16px 0;
	font-family: PingFang SC-Regular, PingFang SC;
	.type-list-box {
		width: 1200px;
		height: 72px;
		background: #ffffff;
		border-radius: 4px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.publication-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 116px;
			height: 36px;
			background: linear-gradient(180deg, #74a8fc 0%, #3274e0 100%);
			border-radius: 21px;
			font-size: 16px;
			color: #ffffff;
			line-height: 24px;
			cursor: pointer;
			margin-right: 18px;
			flex-shrink: 0;
		}
		.publication-icon {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
	}
	.content-box {
		margin-top: 16px;
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 25%;
			height: 480px;
			margin-bottom: 16px;
			cursor: pointer;
			.item-box {
				width: 288px;
				height: 100%;
				border-radius: 10px;
				overflow: hidden;
				background: #ffffff;
			}
			.item-img {
				width: 100%;
				height: 380px;
				object-fit: cover;
			}
			.item-content {
				padding: 12px 16px;
			}
			.content-title {
				height: 40px;
				font-size: 14px;
				color: #404040;
				line-height: 22px;
			}
			.content-bottom {
				display: flex;
				justify-content: space-between;
				margin-top: 8px;
			}
			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
				flex-shrink: 0;
			}
			.author-img {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				margin-right: 8px;
			}
			.author {
				font-size: 12px;
				color: #404040;
				line-height: 22px;
			}
			.item-icon {
				width: 20px;
				height: 20px;
				margin-right: 8px;
			}
		}
	}
	.pagination {
		margin-top: 8px;
		text-align: right;
	}
	.sort-list-box {
		padding-top: 0px;
		height: 52px;
	}
}
</style>
