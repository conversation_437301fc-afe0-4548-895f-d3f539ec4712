<template>
	<div class="app-wrapper">
		<!--  个人中心的头部  -->
		<div :class="{ 'fixed-header': fixedHeader }">
			<VocationalHeader></VocationalHeader>
		</div>
		<!--  个人中心的路由页面  -->
		<app-main />
		<!--  底部文件  -->
		<SelfFooter v-if="!$route.meta.hideSelfFooter" />
	</div>
</template>

<script>
import { VocationalHeader, AppMain, SelfFooter } from './components';
import ResizeMixin from './mixin/ResizeHandler';

export default {
	name: 'VocationalPerson',
	components: {
		AppMain,
		VocationalHeader,
		SelfFooter
	},
	mixins: [ResizeMixin],
	computed: {
		fixedHeader() {
			return this.$store.state.settings.fixedHeader;
		}
	},
	methods: {}
};
</script>

<style lang="scss" scoped></style>
