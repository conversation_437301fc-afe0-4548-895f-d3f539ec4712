<template>
	<div class="title">
		{{ title }}
		<div class="right"><slot></slot></div>
	</div>
</template>
<script>
export default {
	name: 'Title',
	props: {
		title: {
			type: String,
			default: ''
		}
	}
};
</script>

<style lang="scss" scoped>
.title {
	border-left: 4px solid var(--brand-6, '#ca3f3b');
	font-size: 16px;
	padding-left: 7px;
	font-weight: 500;
	color: #404040;
	display: flex;
	align-items: center;
	.right {
		text-align: right;
		width: 300px;
		margin-left: auto;
	}
}
</style>
