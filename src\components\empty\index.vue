<template>
	<div class="empty" :style="styleObj">
		<img class="img" :src="imgUrl ? imgUrl : require(`@/assets/shop-images/empty.png`)" alt="" />
		<div class="tips">{{ tips }}</div>
	</div>
</template>
<script>
export default {
	name: 'Empty',
	props: {
		imgUrl: {
			type: String,
			default: ''
		},
		tips: {
			type: String,
			default: '暂无数据'
		},
		styleObj: {
			type: Object,
			default: () => {
				return {};
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.empty {
	width: 100%;
	padding: 150px 0;
	.img {
		display: block;
		margin: 0 auto;
		width: 127px;
		height: 122px;
	}
	.tips {
		font-size: 14px;
		font-weight: 400;
		color: #404040;
		line-height: 22px;
		text-align: center;
	}
}
</style>
