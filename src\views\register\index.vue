<template>
	<div class="login-container">
		<!-- <register-header></register-header> -->
		<Header title="欢迎注册" :show-tel="false" />
		<div class="container to-login">
			已有账号？
			<router-link class="to-login-link" :to="{ path: '/login', query: $route.query }">
				请点击登录>>
			</router-link>
		</div>
		<div class="container regiter-box">
			<div class="steps">
				<div
					v-for="(step, index) in steps.data"
					:key="index"
					:class="{
						step: true,
						'step-action': index + 1 <= steps.index
					}"
				>
					<span class="step-num">{{ index + 1 }}</span>
					<span class="step-text">{{ step.title }}</span>
				</div>
				<span
					class="steps_index"
					:style="{
						width: stepIndex
					}"
				></span>
			</div>
			<div class="register_form">
				<component :is="steps.data[steps.index - 1]['comp']" @comp-change="compChange"></component>
			</div>
		</div>
	</div>
</template>

<script>
import registerHeader from './components/header';
import Header from '@/components/header';
import RegisterForm from './components/form.vue';
import RegisterResult from './components/result.vue';
export default {
	name: 'Register',
	components: {
		Header,
		registerHeader,
		RegisterForm,
		RegisterResult
	},
	data() {
		return {
			steps: {
				index: 1,
				data: [
					{
						title: '手机号注册',
						comp: 'RegisterForm'
					},
					{
						title: '注册完成',
						comp: 'RegisterResult'
					}
				]
			},
			loading: true
		};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	methods: {
		compChange(index) {
			this.$set(this.steps, 'index', index);
		}
	}
};
</script>

<style lang="scss" scoped>
$bg: #283443;
$light_gray: #fff;
$cursor: #404040;
/* reset element-ui css */
.login-container {
	width: 1200px;
	margin: 0 auto;
	background: #f4f5f8;
	::v-deep .el-input {
		display: inline-block;
		height: 38px;
		width: 85%;

		input {
			background: transparent;
			border: 0px;
			border-radius: 0px;
			padding: 12px 5px 12px 15px;
			height: 38px;
			caret-color: $cursor;
			&:focus {
				outline: none;
			}
			// &:-webkit-autofill {
			// 	box-shadow: 0 0 0px 1000px $bg inset !important;
			// 	-webkit-text-fill-color: $cursor !important;
			// }
		}
	}

	::v-deep .el-form-item {
		border: 1px solid rgba(255, 255, 255, 0.1);
		background: rgba(0, 0, 0, 0.1);
		border-radius: 5px;
		color: #454545;
	}
}
.login-container ::v-deep .el-input__inner:focus {
	box-shadow: none !important;
}
::v-deep .el-tabs__header {
	margin: 0 0 24px;
}
.login-container ::v-deep .el-form-item {
	height: 40px;
	background: #ffffff;
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	border: 1px solid #d9d9d9;
	&:hover {
		border-color: var(--brand-6, #0076e8);
	}
}
::v-deep .el-form-item__content {
	padding-left: 12px;
	line-height: 36px !important;
}
::v-deep .el-checkbox {
	display: flex;
	align-items: center;
}
::v-deep .el-checkbox__label {
	height: 14px;
	font-size: 14px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #404040;
	line-height: 14px;
}
::v-deep .el-button {
	padding: 7px 16px;
}
::v-deep .el-form-item {
	margin-bottom: 20px;
}
.more {
	display: flex;
	justify-content: space-between;
	font-size: 14px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #404040;
	line-height: 14px;
	.toRegister {
		color: var(--brand-6, #0076e8);
	}
}
::v-deep .el-checkbox__inner {
	width: 16px;
	height: 16px;
	border-radius: 0;
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
	min-height: 100%;
	.to-login {
		height: 77px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #404040;
		&-link {
			color: #0076e8;
		}
	}
	.regiter-box {
		height: 588px;
		background: #ffffff;
		margin-bottom: 79px;
	}
	.steps {
		display: flex;
		position: relative;
		overflow: hidden;
		.step {
			height: 88px;
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #9da5b7;
			border-bottom: 1px solid #cfd8ec;
			&-num {
				width: 24px;
				height: 24px;
				font-size: 14px;
				line-height: 20px;
				text-align: center;
				border-radius: 50%;
				opacity: 1;
				border: 2px solid #9da5b7;
				margin-right: 12px;
			}
			&-action {
				.step-num {
					border: 2px solid #0076e8;
				}
				color: #0076e8;
			}
		}
		&_index {
			content: '';
			display: inline-block;
			position: absolute;
			height: 2px;
			background-color: #0076e8;
			left: 0;
			bottom: 0;
		}
	}
	.register_form {
		width: 350px;
		margin: 85px auto 0;
	}
}
</style>
