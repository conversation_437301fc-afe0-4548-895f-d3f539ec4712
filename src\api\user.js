import request from '@/utils/request';
/**获取用户配置文件*/
export function getTenantId(params) {
	return request({
		url: '/ybzy/platuser/front/getTenantId',
		method: 'get',
		params
	});
}
/**发送验证码*/
export function sendCode(data) {
	return request({
		url: '/ybzy/platuser/front/sendLoginVerifyCode',
		method: 'post',
		data
	});
}
/**验证码登录*/
export function codeLogin(data) {
	return request({
		url: '/ybzy/platuser/front/loginByVerifyCode',
		method: 'post',
		data
	});
}
/**账号密码登录*/
export function passwordLogin(data) {
	return request({
		url: '/ybzy/platuser/front/loginByPassword',
		method: 'post',
		data
	});
}
/**账号密码登录新*/
export function passwordLoginN(data) {
	return request({
		url: '/ybzy/casAuthorize/front/loginByCas',
		method: 'post',
		data
	});
}
/**获取统一登录信息*/
export function checkNotice(data) {
	return request({
		url: '/ybzy/casAuthorize/front/checkNotice',
		method: 'post',
		data
	});
}
/**获取外链*/
export function getOutLink(data) {
	return request({
		url: '/ybzy/platuser/front/appList',
		method: 'post',
		data
	});
}
/**登录电商之前检查登录返回code*/
export function checkLogin(params) {
	return request({
		url: '/ybzy/platauth/front/loginCheck',
		method: 'get',
		params
	});
}
/**获取菜单信息*/
export function getMenuTree(data) {
	return request({
		url: '/ybzy/platuser/front/internalAppMenuTree',
		method: 'post',
		data
	});
}
/**获取注册验证码*/
export function sendRegistCode({ phone }) {
	return request({
		url: '/ybzy/platuser/front/sendRegistVerifyCode',
		method: 'post',
		data: { phone }
	});
}
/**手机号注册*/
export function registByPhone({ phone, password, verifyCode }) {
	return request({
		url: '/ybzy/platuser/front/registByPhone',
		method: 'post',
		data: {
			phone,
			// 密码注册base64编码
			password: btoa(password),
			verifyCode
		}
	});
}
/**手机号注册-管理端接口*/
export function updateLoginUser(data) {
	return request({
		url: '/ybzy/platuser/front/updateLoginUser',
		method: 'post',
		data
	});
}
/**获取验证码-管理端接口*/
export function sendUpdateUserInfoVerifyCode(data) {
	return request({
		url: '/ybzy/platuser/front/sendPcUpdateUserInfoVerifyCode',
		method: 'post',
		data
	});
}
/**获取用户信息-管理端接口*/
export function getLoginUserInfo(params) {
	return request({
		// url: '/ybzy/platuser/front/getPcLoginUserInfo',
		url: '/ybzy/platuser/front/getFrontLoginUserInfo',
		method: 'get',
		params
	});
}
/**单点登录验证-管理端接口*/
export function getEossAuthentication(data) {
	return request({
		url: '/sso2/authCenter/login',
		method: 'post',
		data
	});
}
