<!-- eslint-disable vue/no-v-html -->
<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div v-loading="pageLoading" class="orderList">
			<div class="selectTop flex2">
				<div class="word">筛选查询</div>
				<div class="btns flex1">
					<el-button @click="rest">重置</el-button>
					<el-button type="primary" @click="search">查询</el-button>
				</div>
			</div>
			<div class="selectBot flex2">
				<div class="item flex1">
					<span>订单编号</span>
					<el-input v-model="params.orderId" placeholder="请输入订单编号"></el-input>
				</div>
				<div class="item flex1">
					<span>商品名称</span>
					<el-input v-model="params.keyWord" placeholder="请输入商品名称"></el-input>
				</div>
				<div class="item flex1">
					<span>订单时间</span>
					<el-date-picker
						v-model="times"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="yyyy-MM-dd"
					></el-date-picker>
				</div>
			</div>
			<div style="margin-top: 25px">
				<el-tabs v-model="activeName" @tab-click="handleClick">
					<el-tab-pane label="全部订单" name="a"></el-tab-pane>
					<el-tab-pane label="待付款" name="b"></el-tab-pane>
					<el-tab-pane label="已完成" name="c"></el-tab-pane>
					<el-tab-pane label="已取消" name="d"></el-tab-pane>
				</el-tabs>
			</div>
			<div class="list">
				<div class="listTop">
					<div class="ele">课程名称</div>
					<div class="ele">单价</div>
					<div class="ele">实付款</div>
					<div class="ele">交易状态</div>
					<div class="ele">操作</div>
				</div>
				<div v-for="(item, index) in list" :key="index" class="temp">
					<div class="tempTop">
						<div class="ele flex1">
							<span>订单编号：</span>
							{{ item.id }}
						</div>
						<div class="ele flex1">
							<span>类型：</span>
							课程
						</div>
						<div class="ele flex1">
							<span>下单时间：</span>
							{{ item.createTime }}
						</div>
					</div>
					<div v-for="(ele, i) in item.subList" :key="i" class="lessons flex1">
						<div class="box box1 flex1">
							<img :src="ele.coverImg" alt="" />
							<div class="name">{{ ele.goodsName }}</div>
						</div>
						<div class="box box2">课程单价:{{ ele.goodsPrice }}</div>
						<div class="box box3">实付金额:{{ ele.goodsAmount }}</div>
						<div v-show="item.status == '0'" class="box box4 ac">待支付</div>
						<div v-show="item.status == '1'" class="box box4">已完成</div>
						<div v-show="item.status == '-1'" class="box box4">已取消</div>
						<div class="box box5">
							<div v-show="item.status == '0'" class="btn btn1" @click="handlePay(item.id)">
								立即付款
							</div>
							<div v-show="item.status == '0'" class="btn btn2" @click="orderCancelFn(item.id)">
								取消订单
							</div>
						</div>
					</div>
				</div>
			</div>
			<el-empty v-if="list.length == 0" description="暂无数据"></el-empty>
			<div class="fenye">
				<el-pagination
					:current-page.sync="params.pageNum"
					background
					:page-sizes="[10, 15, 20]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
			<div class="aliform" v-html="payUrl"></div>
		</div>
	</div>
</template>
<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import config from '@/config';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			activeName: 'a',

			times: [],
			params: {
				date1: '',
				date2: '',
				orderId: '',
				isAsc: '',
				keyWord: '',
				orderByColumn: '',
				pageNum: 1,
				pageSize: 10,
				reasonable: true,
				status: ''
			},
			list: [],
			total: 0,
			payUrl: null,
			pageLoading: false
		};
	},
	async mounted() {
		// if (document.getElementsByClassName('commonHeader')[0]) {
		// 	document.getElementsByClassName('commonHeader')[0].style.display = 'none';
		// }
		if (!this._userinfo.id) {
			let queryString = '?';
			queryString += ['redirect', location.href].join('=');
			queryString = encodeURIComponent(queryString);
			localStorage.setItem(
				'scwl_homepage',
				`${config.domainUrl}${config.appList.valueAddedServices}`
			);
			window.location.href = `${config.domainUrl}scwl_user_main/#/login${queryString}`;
			return;
		} else {
			// if (!getLessonToken()) {
			// 	await this.loginFrontFn();
			// }
			this.getOrderPageListFn(this.params);
		}
	},
	// beforeDestroy() {
	// 	document.getElementsByClassName('commonHeader')[0].style.display = 'block';
	// },
	methods: {
		rest() {
			this.params.orderId = '';
			this.params.keyWord = '';
			this.params.date1 = '';
			this.params.date2 = '';
		},
		// async loginFrontFn() {
		// 	const data = await this.$router.push({ path: '/payment-center' });loginFront({
		// 		username: this._userinfo.username,
		// 		userId: this._userinfo.id,
		// 		nickname: this._userinfo.nickname
		// 	});
		// 	setLessonToken(data.data.access_token);
		// },
		search() {
			this.params.pageNum = 1;
			// console.log(this.times);
			if (this.times.length > 0) {
				this.params.date1 = this.times[0];
				this.params.date2 = this.times[1];
			}
			this.getOrderPageListFn(this.params);
		},
		async getOrderPageListFn(params) {
			const data = await this.$api.study_api.getOrderPageList(params);
			this.list = data?.data?.items || [];
			this.total = data?.data?.total || 0;
		},
		handleClick() {
			switch (this.activeName) {
				case 'a':
					this.params.status = '';
					break;
				case 'b':
					this.params.status = '0';
					break;
				case 'c':
					this.params.status = '1';
					break;
				case 'd':
					this.params.status = '-1';
					break;
			}
			this.getOrderPageListFn(this.params);
		},
		orderCancelFn(orderId) {
			this.$confirm('确认取消订单, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.study_api.orderCancel({ orderId }).then(res => {
						this.$message({
							type: 'success',
							message: '取消订单成功!'
						});
						this.getOrderPageListFn(this.params);
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消'
					});
				});
		},
		handleSizeChange(val) {
			this.params.pageSize = val;
			this.getOrderPageListFn(this.params);
		},
		handleCurrentChange(val) {
			this.params.pageNum = val;
			this.getOrderPageListFn(this.params);
		},
		// 支付订单
		handlePay(orderId) {
			this.$message({
				message: '暂不支持购买',
				type: 'warning'
			});
			// this.pageLoading = true;
			// lesson
			// 	.orderPay({
			// 		orderId
			// 	})
			// 	.then(res => {
			// 		if (res.code == 200) {
			// 			this.payUrl = res.url;
			// 			this.pageLoading = false;
			// 			this.$nextTick(() => {
			// 				// 获取订单详情来轮询支付结果
			// 				document.forms[0].submit(); //重点--这个才是跳转页面的核心,获取第一个表单并提交
			// 			});
			// 		} else {
			// 			this.pageLoading = false;
			// 			this.$message({
			// 				type: 'info',
			// 				message: '支付信息错误，请联系管理员'
			// 			});
			// 		}
			// 	});
		}
	}
};
</script>
<style scoped lang="scss">
.orderList {
	width: 1200px;
	margin: 0 auto;
	background: #fff;
	box-sizing: border-box;
	padding: 20px 15px 200px 15px;
	.selectTop {
		height: 48px;
		background: #f4f4f4;
		border: 1px solid #d4d4d4;
		box-sizing: border-box;
		padding: 0 12px;
		font-size: 14px;
		.word {
			color: #404040;
			font-weight: bold;
		}
	}
	.selectBot {
		height: 74px;
		background: #ffffff;
		border: 1px solid #d4d4d4;
		border-top: none;

		padding: 12px;
		.item {
			font-size: 14px;
			span {
				color: #000000;
				margin-right: 12px;
			}
		}
		::v-deep .el-input {
			width: 180px;
		}
		::v-deep .el-date-editor {
			width: 280px;
		}
	}
	.list {
		.listTop {
			width: 100%;
			height: 40px;
			background: #f4f4f4;
			position: relative;
			.ele {
				font-size: 14px;

				color: #8c8c8c;
				position: absolute;
				top: 13px;
			}
			.ele:nth-child(1) {
				left: 20px;
			}
			.ele:nth-child(2) {
				left: 416px;
			}
			.ele:nth-child(3) {
				left: 576px;
			}
			.ele:nth-child(4) {
				left: 697px;
			}
			.ele:nth-child(5) {
				left: 846px;
			}
		}
		.temp {
			.tempTop {
				margin-top: 15px;
				height: 40px;
				background: #f4f4f4;
				border: 1px solid #eeeeee;
				position: relative;
				.ele {
					top: 12px;
					position: absolute;
					color: #404040;
					span {
						font-size: 14px;

						color: #8c8c8c;

						top: 13px;
					}
				}
				.ele:nth-child(1) {
					left: 20px;
				}
				.ele:nth-child(2) {
					left: 276px;
				}
				.ele:nth-child(3) {
					left: 396px;
				}
			}
			.lessons {
				border: 1px solid #eeeeee;
				border-top: none;
				height: 94px;
				.box {
					border-left: 1px solid #eeeeee;
					height: 94px;
					box-sizing: border-box;
				}
				.box:last-child {
					border: none;
				}
				.box1 {
					padding: 0 14px 0 24px;
					width: 332px;
					img {
						width: 72px;
						height: 70px;
					}
					.name {
						margin-left: 12px;
						width: 210px;
						font-size: 16px;
						font-family: MicrosoftYaHei;
						color: #404040;
					}
				}
				.box2 {
					text-align: center;
					width: 198px;
					font-size: 14px;
					line-height: 94px;
					color: #404040;
				}
				.box3 {
					font-size: 14px;
					line-height: 94px;
					color: #404040;
					width: 138px;
					text-align: center;
				}
				.box4 {
					line-height: 94px;
					width: 120px;
					text-align: center;
					font-size: 14px;
					font-family: MicrosoftYaHei;
					color: #404040;
				}
				.ac {
					color: var(--brand-6, #0076e8);
				}
				.box5 {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 151px;
					font-size: 12px;
					font-family: MicrosoftYaHei;
					color: #404040;
					.btn {
						cursor: pointer;
					}
					.btn1 {
						width: 64px;
						height: 24px;
						line-height: 22px;
						background: #ffffff;
						border-radius: 3px;
						color: var(--brand-6, #0076e8);
						font-size: 12px;
						text-align: center;
						border: 1px solid var(--brand-6, #0076e8);
						margin-bottom: 12px;
					}
				}
			}
		}
	}
	.fenye {
		margin-top: 24px;
		text-align: right;
		// ::v-deep .el-pagination {
		// 	.btn-prev {
		// 		margin-left: 240px;
		// 	}
		// }
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 40px;
	}
}
</style>
