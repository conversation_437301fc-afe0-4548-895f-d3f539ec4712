<template>
	<div class="coupon">
		<div class="coupon-top">
			<div class="line"></div>
			<div calss="tile">使用优惠/购物卡</div>
		</div>
		<div class="coupon-con">
			<el-tabs v-model="activeName" class="coupon-con-tabs" @tab-click="handleClick">
				<el-tab-pane
					v-for="(item, index) of list"
					:key="index"
					:label="`${item.name}（${getCount(item.type)}）`"
					:name="item.type"
				></el-tab-pane>
			</el-tabs>
			<div class="line"></div>
			<couponList
				v-show="activeName === 'coupon'"
				:coupons-info="couponsInfo"
				v-on="$listeners"
			></couponList>
			<giftList
				v-show="activeName === 'giftCard'"
				:gift-info="giftInfo"
				v-on="$listeners"
			></giftList>
		</div>
	</div>
</template>

<script>
import couponList from '@/shop-views/settleAccounts/components/couponList';
import giftList from '@/shop-views/settleAccounts/components/giftList';
export default {
	name: 'Coupon',
	components: {
		couponList,
		giftList
	},
	props: {
		/**商品ID*/
		shopId: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**下单商品列表*/
		goodsList: {
			type: [Array, Object],
			default: () => {
				return [];
			}
		},
		/**商品信息*/
		baseInfo: {
			type: Object,
			default: () => {
				return {};
			}
		},
		totalPrices: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			list: [
				{
					name: '商品优惠券',
					type: 'coupon'
				},
				{
					name: '购物卡',
					type: 'giftCard'
				}
			],
			activeName: 'coupon',
			couponsInfo: {
				availableList: [],
				unavailableList: []
			},
			giftInfo: []
		};
	},
	watch: {
		goodsList(newVal) {
			if (newVal.length) {
				this.init();
			}
		}
	},
	methods: {
		/**获取相应类别的数量*/
		getCount(type) {
			return type === 'coupon' ? this.couponsInfo.availableList.length : this.giftInfo.length;
		},
		/**初始化*/
		init() {
			this.getCoupon();
			this.getGift();

			// if (this.activeName === 'coupon') {
			// 	this.getCoupon();
			// } else {
			// 	this.getGift();
			// }
		},
		handleClick() {
			// this.init();
		},
		/**获取购物卡*/
		getGift() {
			let list = [];
			this.goodsList.forEach(item => {
				let obj = {
					num: item.sku_num,
					price: item.sell_price,
					productTypeId: this.baseInfo.CATEGORY_ID,
					skuId: item.sku_id,
					spuId: item.spu_id,
					shopId: this.shopId
				};
				// 比如酒店商品多个晚上就分为多个商品
				if (item.count_night > 1) {
					for (let i = 0; i < item.count_night; i++) {
						list.push(obj);
					}
				} else {
					list.push(obj);
				}
			});
			let giftParams = {
				rentId: this.getSiteId(),
				shopId: this.shopId,
				orderItems: list,
				amount: this.totalPrices
			};
			this.$api.shop_api.getGoodsGift(giftParams).then(res => {
				if (res.code === 200) {
					this.giftInfo = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**获取优惠券*/
		getCoupon() {
			let list = [];
			this.goodsList.forEach(item => {
				let obj = {
					num: item.sku_num,
					price: item.sell_price,
					productTypeId: this.baseInfo.CATEGORY_ID,
					skuId: item.sku_id,
					spuId: item.spu_id
				};
				// 比如酒店商品多个晚上就分为多个商品
				if (item.count_night > 1) {
					for (let i = 0; i < item.count_night; i++) {
						list.push(obj);
					}
				} else {
					list.push(obj);
				}
			});
			let couponParams = {
				orderItems: list,
				rentId: this.getSiteId(),
				shopId: this.baseInfo.ID
			};
			this.$api.shop_api.getGoodsCoupons(couponParams).then(res => {
				if (res.code === 200) {
					this.couponsInfo = res.results;
				} else {
					this.$message.error(res.msg);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.coupon {
	margin: 16px 0;
	padding: 16px 23px 20px 20px;
	background: #ffffff;
	&-top {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		.line {
			width: 6px;
			height: 20px;
			background: var(--brand-6, #0076e8);
			margin-right: 7px;
		}
		.title {
			font-size: 16px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-con {
		background: #ffffff;
		border: 1px solid #d9d9d9;
		padding: 20px;
		&-tabs {
			::v-deep .el-tabs__header {
				margin-bottom: 0;
			}
			::v-deep .el-tabs__nav-wrap::after {
				display: none;
			}
		}
		.line {
			width: 1200px;
			height: 1px;
			margin-left: -20px;
			background: #f0f0f0;
		}
	}
}
</style>
