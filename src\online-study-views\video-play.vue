<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="video-box">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				background="transparent"
				icon="el-icon-location"
				color="#B8BFD7"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="banner">
				<div class="inner">
					<div class="title flex2">
						<div class="video-left">
							<span class="video-name">{{ basicInfo.name }}</span>
							<span class="video-desc">{{ smallName }}</span>
						</div>
						<div class="right" @click="backFreecourses">
							<img class="back-img" src="@/assets/study_images/video-back.png" alt="" />
							<span>返回课程介绍</span>
						</div>
					</div>
					<div class="videoBox flex0">
						<div class="video">
							<div class="isShow"><video-player v-if="showVideo" :options="videoOptions" /></div>
							<!-- <div class="videoName flex2">
								<div class="lname">{{ basicInfo.name }}</div>
								<div class="rbtn flex1">
									<div
										class="temp flex3"
										:class="{ isCollect: basicInfo.collect }"
										@click="collectCourseFn"
									>
										<i class="iconfont icon-shoucang1"></i>
										<div class="word">收藏</div>
									</div>
									<div class="temp flex3" @click="linkUrl">
										<i class="iconfont icon-fenxiang"></i>
										<div class="word">分享</div>
									</div>
								</div>
							</div> -->
						</div>
						<div class="videoChoose">
							<div class="ke flex2">
								<div class="lke flex1">
									<div class="block"></div>
									<div class="ns">课程目录</div>
								</div>
								<div class="rke">
									共
									<span class="rke-num">{{ lessonList.length }}</span>
									节
								</div>
							</div>
							<div class="list">
								<el-scrollbar style="height: 100%; overflow-x: hidden">
									<div
										v-for="(item, i) in lessonList"
										:key="i"
										:class="{ tempActive: item.name == smallName }"
										class="temp"
										@click="changePlay(item.courseId, item.id)"
									>
										<div class="keName flex1">
											<div class="index over1" :title="item.name">{{ item.name }}</div>
											<!-- <div class="cont">1-1现代物流概括</div> -->
										</div>
										<div class="time flex2">
											<div class="time">总时长：{{ item.courseDuration }}</div>
											<!-- <div class="play" @click="changePlay(item.courseId, item.id)">播放</div> -->
										</div>
									</div>
								</el-scrollbar>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="partFour inner flex0">
				<div class="fourLeft">
					<el-tabs v-model="activeName" @tab-click="handleClick">
						<el-tab-pane label="章节" name="first">
							<div class="tab1" v-html="basicInfo.content"></div>
							<div class="course-list">
								<div v-for="(item, index) in CourseDirectory" :key="index" class="course-item">
									<div class="lesson-name">{{ item.name }}</div>
									<div v-for="(ele, i) in item.courseHourList" :key="i" class="lesson flex2">
										<div class="lname flex1" @click="changePlay(item.courseId, ele.id, 'top')">
											<i class="el-icon el-icon-caret-right"></i>
											<div class="name">{{ ele.name }}</div>
											<div class="long">
												<i class="el-icon el-icon-time ele-icon"></i>
												{{ ele.courseDuration }}
											</div>
										</div>
									</div>
								</div>
							</div>
							<!-- </el-tab-pane>
						<el-tab-pane label="课程大纲" name="second"> -->
							<!-- <div class="tab2">
								<el-collapse>
									<el-collapse-item v-for="(item, index) in CourseDirectory" :key="index">
										<template slot="title">
											<div class="flex1 header">
												<div class="blue">
													<i class="iconfont icon-fenlei1"></i>
												</div>
												<div class="lessonName">{{ item.name }}</div>
												<div class="time">
													({{ item.courseDuration }} {{ item.courseHourList.length }}节)
												</div>
											</div>
										</template>
										<div v-for="(ele, i) in item.courseHourList" :key="i" class="lesson flex2">
											<div class="lname flex1">
												<div class="icon">
													<i class="iconfont icon-bofang"></i>
												</div>
												<div class="name">{{ ele.name }}</div>
												<img
													v-show="basicInfo.isFree == 1"
													class="free"
													src="~@/assets/study_images/3.png"
													alt=""
												/>
											</div>
											<div class="right">
												<div class="long">[{{ ele.courseDuration }}]</div>
												<div class="study" @click="changePlay(item.courseId, ele.id)">开始学习</div>
											</div>
										</div>
									</el-collapse-item>
								</el-collapse>
							</div> -->
						</el-tab-pane>

						<el-tab-pane label="评价" name="fourth">
							<div class="tab4">
								<div class="start">我要评论</div>
								<div class="starList">
									<div class="star flex1">
										<div class="key">课程评分：</div>
										<el-rate v-model="value" show-text allow-half></el-rate>
									</div>
									<div class="star flex1">
										<div class="key" style="margin-right: 15px">印象标签</div>
										<el-checkbox-group v-model="checkList">
											<el-checkbox
												v-for="(item, index) in labelValue"
												:key="index"
												:label="item.value"
											>
												{{ item.label }}
											</el-checkbox>
										</el-checkbox-group>
									</div>
								</div>
								<el-input
									v-model="textarea"
									:rows="4"
									type="textarea"
									placeholder="扯淡、吐槽、表扬、鼓励……想说啥就说啥"
									maxlength="500"
									show-word-limit
								></el-input>
								<div class="pl" @click="addAssessmentFn">发表评论</div>
								<div class="end">{{ commentDataList.length }}条评论</div>
								<div class="tab4top flex1">
									<div class="score">
										<div class="number">{{ basicInfo.score }}</div>
										<el-rate v-model="basicInfo.score" disabled></el-rate>
									</div>
									<div class="label">
										<div class="n1">
											{{ basicInfo.courseAssessmentText }}
										</div>
										<div class="n2 flex1">
											<div v-for="(item, index) in pingAll.tagNums" :key="index" class="item">
												{{ item.value }}
												<span>{{ item.nums }}</span>
											</div>
										</div>
									</div>
								</div>
								<!-- <div class="total">108条评论</div> -->
								<ul class="list">
									<li v-for="(item, index) in commentDataList" :key="index">
										<div class="user flex1">
											<img :src="item.createUser.avatar" class="touxiang" />
											<div class="flex2">
												<div class="flex1">
													<div class="name">{{ item.createUser.nickName }}</div>
													<div class="ps"></div>
													<div class="date flex1">
														<i class="iconfont icon-shijian"></i>
														<span>{{ item.createTime }}</span>
													</div>
												</div>
												<div class="star">
													<el-rate v-model="item.score" disabled></el-rate>
												</div>
											</div>
										</div>
										<div class="text">{{ item.content }}</div>
										<div class="tag flex1">
											<div v-for="(ele, i) in item.tagList" :key="i" class="ele">{{ ele }}</div>
										</div>
										<div v-show="item.replayContent" class="word">{{ item.replayContent }}</div>
									</li>
								</ul>
								<div class="page">
									<el-pagination
										background
										:current-page="pageNum"
										:page-size="12"
										layout="prev, pager, next, jumper"
										:total="totalPage"
										@size-change="handleSizeChange"
										@current-change="handleCurrentChange"
									></el-pagination>
								</div>
							</div>
						</el-tab-pane>
						<el-tab-pane label="考试" name="examination1"></el-tab-pane>
						<el-tab-pane label="测验" name="examination2"></el-tab-pane>
						<el-tab-pane label="作业" name="examination3"></el-tab-pane>

						<el-tab-pane label="笔记" name="note">
							<div class="tab4">
								<div class="start">记录一下</div>
								<el-input
									v-model="textarea1"
									:rows="4"
									type="textarea"
									placeholder="请输入要记录笔记内容"
									maxlength="500"
									show-word-limit
								></el-input>
								<div class="pl" @click="addAssessmentFn1">保存笔记</div>
								<div class="end">{{ commentDataList1.length }}条笔记</div>
								<ul class="list">
									<li v-for="(item, index) in commentDataList1" :key="index">
										<div class="user flex1">
											<img
												src="http://yszj.ybzy.cn/project-ybzy/ybzy_zjptH5/static/img/head.png"
												class="touxiang"
											/>
											<div class="flex2">
												<div class="flex1">
													<div class="name"></div>
													<div class="ps"></div>
													<div class="date flex1">
														<i class="iconfont icon-shijian"></i>
														<span>{{ item.createTime }}</span>
													</div>
												</div>
												<!-- <div class="star">
													<el-rate v-model="item.score" disabled></el-rate>
												</div> -->
											</div>
										</div>
										<div class="text">{{ item.content }}</div>
										<!-- <div class="tag flex1">
											<div v-for="(ele, i) in item.tagList" :key="i" class="ele">{{ ele }}</div>
										</div> -->
										<div v-show="item.replayContent" class="word">{{ item.replayContent }}</div>
									</li>
								</ul>
								<!-- <div class="page">
									<el-pagination
										background
										:current-page="pageNum"
										:page-size="12"
										layout="prev, pager, next, jumper"
										:total="totalPage"
										@size-change="handleSizeChange"
										@current-change="handleCurrentChange"
									></el-pagination>
								</div> -->
							</div>
						</el-tab-pane>
					</el-tabs>
				</div>
				<!-- <div class="fourRight">
					<div class="type1">
						<div class="title">推荐课程</div>
						<div class="list">
							<li v-for="(item, index) in tjLesson" :key="index" @click="changLesson(item)">
								<img class="leftImg" :src="item.coverImg" alt="" />
								<div class="info flex6">
									<div class="title over2">
										{{ item.name }}
									</div>
								</div>
							</li>
						</div>
					</div>
				</div> -->
			</div>
		</div>
	</div>
</template>
<script>
import ElementUI from 'element-eoss';
import VideoPlayer from './components/VideoPlayer.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
// import config from '@/config';
export default {
	components: { VideoPlayer, subBreadcrumb },
	data() {
		return {
			showVideo: false,
			value: 5,
			activeName: 'first',
			basicInfo: {},
			CourseDirectory: [],
			commentDataList: [],
			commentDataList1: [],

			pageSize: 10,
			pageNum: 1,
			totalPage: 0,
			total: 0,
			labelValue: [],
			textarea: '',
			textarea1: '',
			pingAll: {},
			checkList: [],
			videoOptions: {
				autoplay: true,
				controls: true,
				playbackRates: [0.5, 1, 1.5, 2, 3],
				sources: [
					{
						src: 'http://**************:8080/file/statics/2023/05/04/index-video_20230504135607A003.mp4',
						type: 'video/mp4'
					}
				]
			},
			smallName: '',
			lessonList: [],
			tjLesson: [],
			xkLesson: []
		};
	},
	computed: {
		// ...mapGetters(['this._userinfo'])
	},
	created() {
		this.getAssessmentTagListFn();
	},
	async mounted() {
		console.log(this._userinfo, 'this._userinfo');
		// if (!this._userinfo.id) {
		// 	let queryString = '?';
		// 	queryString += ['redirect', location.href].join('=');
		// 	queryString = encodeURIComponent(queryString);
		// 	localStorage.setItem(
		// 		'scwl_homepage',
		// 		`${config.domainUrl}${config.appList.valueAddedServices}`
		// 	);
		// 	window.location.href = `${config.domainUrl}${config.appList.userCenter}/#/login${queryString}`;
		// 	return;
		// } else {
		// if (!getLessonToken()) {
		// 	await this.loginFrontFn();
		// }
		// this.xgCourseFn();
		// this.tjCourseFn();
		this.getLessonInfo(this.$route.query.id);
		this.getCourseVideoListFn(this.$route.query.id);
		this.getCourseDirectoryFn(this.$route.query.id);
		this.getAssessmentPageListFn(this.$route.query.id);
		this.getAssessmentPageListFn1(this.$route.query.id);
		this.getAssessmentTagNumsFn(this.$route.query.id);
		this.getCourseHourVideoFn(this.$route.query.id, this.$route.query.lessonId);
		// }
	},
	methods: {
		// async loginFrontFn() {
		// 	const data = await this.$api.study_api.loginFront({
		// 		username: this._userinfo.username,
		// 		userId: this._userinfo.id,
		// 		nickname: this._userinfo.nickname
		// 	});
		// 	console.log('token', data.data.access_token);
		// 	setLessonToken(data.data.access_token);
		// },
		handleSizeChange(val) {
			this.pageSize = val;
			this.getAssessmentPageListFn(this.$route.query.id);
		},
		handleCurrentChange(val) {
			this.pageNum = val;
			this.getAssessmentPageListFn(this.$route.query.id);
		},
		// tab切换时触发的事件
		handleClick() {
			if (
				this.activeName == 'examination1' ||
				this.activeName == 'examination2' ||
				this.activeName == 'examination3'
			) {
				if (!this.basicInfo.examId) {
					return this.$message.error('无相关考试信息');
				}
				let query = {
					examId: this.basicInfo.examId,
					courseId: this.basicInfo.id,
					exam: this.basicInfo.exam ? 'true' : 'false',
					title:
						this.activeName == 'examination1'
							? '考试'
							: this.activeName == 'examination2'
							? '测试'
							: '作业'
				};
				this.$router.push({
					path: '/examination',
					query
				});
			}
		},
		// 获取课程信息
		async getLessonInfo(id) {
			const { data } = await this.$api.study_api.getCourseDetailInfo({ id });
			data.score = Number(data.score.toFixed(2));
			this.basicInfo = data;
			localStorage.setItem('courseName', data.name);
		},
		//课程大纲
		async getCourseDirectoryFn(id) {
			const { data } = await this.$api.study_api.getCourseDirectory({ courseId: id });
			console.log(data);

			this.CourseDirectory = data;
		},
		//课程评价
		async getAssessmentPageListFn(id) {
			const { data } = await this.$api.study_api.getAssessmentPageList({
				courseId: id,
				pageSize: this.pageSize,
				pageNum: this.pageNum
			});
			this.totalPage = data.totalPage;
			this.total = data.total;
			for (let x = 0; x < data.items.length; x++) {
				if (data.items[x].tagList?.length) {
					for (let y = 0; y < data.items[x].tagList.length; y++) {
						data.items[x].tagList[y] = this.getValue(data.items[x].tagList[y]);
					}
				}
			}

			this.commentDataList = data.items;
		},
		//笔记
		async getAssessmentPageListFn1(id) {
			const { data } = await this.$api.study_api.getAssessmentPageList({
				courseId: id + '66',
				pageSize: 11,
				pageNum: 1
			});
			// this.totalPage = data.totalPage;
			// this.total = data.total;
			for (let x = 0; x < data.items.length; x++) {
				if (data.items[x].tagList?.length) {
					for (let y = 0; y < data.items[x].tagList.length; y++) {
						data.items[x].tagList[y] = this.getValue(data.items[x].tagList[y]);
					}
				}
			}

			this.commentDataList1 = data.items;
		},
		//收藏或取消收藏
		async collectCourseFn() {
			const { data } = await this.$api.study_api.collectCourse({
				courseId: this.$route.query.id,
				type: this.basicInfo.collect ? 0 : 1
			});
			console.log('收藏', data);

			this.getLessonInfo(this.$route.query.id);
			ElementUI.Message({
				type: 'success',
				message: '操作成功'
			});
		},
		//获取评价标签
		async getAssessmentTagListFn() {
			const { data } = await this.$api.study_api.getAssessmentTagList();
			// console.log('评价标签', data);
			this.labelValue = data;
		},
		//获取评价标签数量
		async getAssessmentTagNumsFn(courseId) {
			const { data } = await this.$api.study_api.getAssessmentTagNums({ courseId });

			data.tagNums = data.tagNums.map(item => {
				return {
					value: this.getValue(item.value),
					nums: item.nums
				};
			});
			// console.log('评价标签数量', data);
			this.pingAll = data;
		},
		//发表评论
		async addAssessmentFn() {
			if (!this.textarea) {
				ElementUI.Message({
					type: 'warning',
					message: '请输入评论'
				});
				return;
			}
			// console.log(this.checkList);
			let params = {
				score: this.value,
				content: this.textarea,
				courseId: this.$route.query.id,
				tagList: this.checkList
			};

			await this.$api.study_api.addAssessment(params);
			this.getAssessmentPageListFn(this.$route.query.id);
			this.textarea = '';
			ElementUI.Message({
				type: 'success',
				message: '发表成功'
			});
		},
		//保存笔记
		async addAssessmentFn1() {
			if (!this.textarea1) {
				ElementUI.Message({
					type: 'warning',
					message: '请输入笔记'
				});
				return;
			}
			// 获取当前时间年月日时分秒
			let date = new Date();
			date =
				date.getFullYear() +
				'-' +
				(date.getMonth() + 1) +
				'-' +
				date.getDate() +
				' ' +
				date.getHours() +
				':' +
				date.getMinutes() +
				':' +
				date.getSeconds();

			this.commentDataList1.push({
				avatar: '',
				content: this.textarea1,
				createTime: date
			});
			// console.log(this.checkList);
			// let params = {
			// 	score: this.value,
			// 	content: this.textarea,
			// 	courseId: this.$route.query.id + '66',
			// 	tagList: this.checkList
			// };

			// await this.$api.study_api.addAssessment(params);
			this.getAssessmentPageListFn1(this.$route.query.id + '66');
			this.textarea1 = '';
			ElementUI.Message({
				type: 'success',
				message: '发表成功'
			});
		},
		getValue(value) {
			// console.log(1111);
			// console.log(value, this.labelValue);
			for (let item of this.labelValue) {
				if (value == item.value) {
					return item.label;
				}
				// break;
			}
		},
		//获取课程列表
		async getCourseVideoListFn(courseId) {
			const { data } = await this.$api.study_api.getCourseVideoList({ courseId });
			// console.log('获取课程列表', data);
			this.lessonList = data;
		},
		//获取视频播放链接
		async getCourseHourVideoFn(courseId, courseHourId) {
			this.showVideo = false;
			const { data, code, msg } = await this.$api.study_api.getCourseHourVideo({
				courseId,
				courseHourId
			});

			// if (code == 500) {
			// 	ElementUI.Message({
			// 		type: 'warning',
			// 		message: msg
			// 	});
			// 	setTimeout(() => {
			// 		this.$router.push({ path: '/course-list' });
			// 	}, 1500);

			// 	return;
			// }
			if (code == 200) {
				let videoOptions = {
					autoplay: true,
					controls: true,
					playbackRates: [0.5, 1, 1.5, 2, 3],
					sources: [
						{
							src: 'http://ybzy.eoss.wisesoft.net.cn/yb-zxxx/file/statics/2023/05/08/%E5%A4%A7%E9%B1%BC_20230508165716A019.mp4',
							type: 'video/mp4'
						}
					]
				};
				videoOptions.sources[0].src = data?.url || '';
				this.showVideo = true;
				this.videoOptions = videoOptions;
				this.smallName = data?.name;
			} else {
				ElementUI.Message({
					type: 'warning',
					message: msg
				});
			}
		},
		changePlay(courseId, courseHourId, type) {
			if (type == 'top') {
				window.scrollTo(0, 200);
			}
			this.getCourseHourVideoFn(courseId, courseHourId);
		},

		//推荐好课
		async tjCourseFn() {
			const data = await this.$api.study_api.tjCourse({ courseId: this.$route.query.id });

			this.tjLesson = data.data;
		},
		async xgCourseFn() {
			const data = await this.$api.study_api.xgCourse({ courseId: this.$route.query.id });

			this.xkLesson = data.data;
		},
		async changLesson(item) {
			const { data } = await this.$api.study_api.getCourseVideoList({ courseId: item.id });
			this.lessonList = data;
			this.$router.push({ path: this.$route.path, query: { id: item.id, lessonId: data[0].id } });
			this.getLessonInfo(this.$route.query.id);
			this.getCourseVideoListFn(this.$route.query.id);
			this.getCourseDirectoryFn(this.$route.query.id);
			this.getAssessmentPageListFn(this.$route.query.id);
			this.getAssessmentTagNumsFn(this.$route.query.id);
			this.getCourseHourVideoFn(this.$route.query.id, this.$route.query.lessonId);
			// this.tjCourseFn();
			// this.xgCourseFn();
		},
		// 返回课程介绍
		backFreecourses() {
			this.$router.push({
				path: '/freecourses',
				query: { id: this.$route.query.id }
			});
		}
	}
};
</script>
<style scoped lang="scss">
.video-box {
	background: #ffffff;
}
.top {
	width: 100%;
	height: 40px;
	line-height: 40px;
	background: #ffffff;
	color: #999999;
	font-size: 14px;
	.top-a {
		width: 1200px;
		margin: 0 auto;
	}
	span {
		color: var(--brand-6, #0076e8);
	}
}
.inner {
	width: 1200px;
	margin: 0 auto;
}
.main {
	.banner {
		height: 610px;
		background: #232b41;
		.inner {
			position: relative;
			.title {
				padding: 20px 0;
				height: 92px;
			}
			.video-left {
				display: flex;
				flex-direction: column;
			}
			.video-name {
				font-size: 24px;
				line-height: 24px;
				font-family: SourceHanSansCN-Bold, SourceHanSansCN;
				font-weight: bold;
				color: #ffffff;
			}
			.video-desc {
				font-size: 16px;
				line-height: 16px;
				font-family: SourceHanSansCN-Regular, SourceHanSansCN;
				font-weight: 400;
				color: #a9b1c9;
				margin-top: 12px;
			}
			.right {
				font-size: 16px;
				font-weight: 400;
				color: #a1beff;
				display: flex;
				align-items: center;
				cursor: pointer;
			}
			.back-img {
				width: 16px;
				height: 16px;
				margin-right: 6px;
			}
			.videoBox {
				width: 100%;
				height: 518px;
				background: #1c1d30;
				position: relative;
			}
			.isShow {
				height: 100%;
				> div {
					width: 920px;
					height: 100%;
				}
			}
			.videoName {
				height: 60px;
				padding: 0 20px;
				.lname {
					font-size: 18px;
					font-weight: 400;
					color: #ffffff;
				}
				.rbtn {
					color: #fff;
					font-size: 12px;
					.temp {
						cursor: pointer;
						margin-left: 42px;
					}
				}
			}
			.videoChoose {
				position: absolute;
				right: 0;
				top: 0;
				width: 280px;
				padding: 20px;
				box-sizing: border-box;
				background: #171d2f;
				.list {
					height: 445px;
					.temp {
						width: 100%;
						height: 88px;
						// background: #282943;
						// margin-bottom: 10px;
						box-sizing: border-box;
						padding: 20px 11px;
						border-bottom: 1px solid rgba(250, 250, 250, 0.2);
						cursor: pointer;
						.keName {
							font-size: 14px;

							font-weight: 400;
							color: #ffffff;
							.index {
								margin-right: 10px;
							}
							margin-bottom: 15px;
						}
						.time {
							font-size: 14px;

							font-weight: 400;
							color: #a9b1c9;
							// .play {
							// 	width: 44px;
							// 	height: 19px;
							// 	background: var(--brand-6, #0076e8);
							// 	border-radius: 2px;
							// 	text-align: center;
							// 	line-height: 19px;
							// 	color: #fff;
							// 	font-size: 12px;
							// 	cursor: pointer;
							// }
						}
						&:hover {
							background: #2e3f63;
						}
					}
					.tempActive {
						background: #374d7e;
					}
				}
				::v-deep .el-scrollbar__wrap {
					overflow-x: hidden;
				}

				.ke {
					// margin-top: 20px;
					margin-bottom: 10px;
					.lke {
						.block {
							width: 3px;
							height: 16px;
							background: var(--brand-6, #0076e8);
						}
						.ns {
							margin-left: 8px;
							font-size: 16px;
							font-weight: bold;
							color: #ffffff;
						}
					}
					.rke {
						font-size: 14px;
						font-weight: 400;
						color: #a9b1c9;
					}
					.rke-num {
						color: #ffffff;
					}
				}
			}
		}
	}
}
.isCollect {
	color: rgb(247, 186, 42);
}
.partFour {
	margin-top: 40px;
	margin-bottom: 30px;
	.fourLeft {
		// width: 860px;
		width: 100%;
		background: #fff;
		::v-deep .el-tabs__item {
			font-size: 16px;
			font-family: SourceHanSansCN-Regular, SourceHanSansCN;
			font-weight: 400;
			color: #666666;
		}
		::v-deep .el-tabs__item.is-active {
			font-size: 20px;
			font-family: SourceHanSansCN-Bold, SourceHanSansCN;
			font-weight: bold;
			color: #222222;
		}
		::v-deep .el-tabs__active-bar {
			height: 4px;
		}
		// ::v-deep .el-tabs__item.is-top:nth-child(2) {
		// 	// padding: 0 20px;
		// }
		::v-deep .el-tabs_header {
			margin: 0;
		}
		::v-deep .el-tabs__item {
			font-weight: 500;
			line-height: 50px;
			height: 50px;
			font-weight: bolder;
		}
		.tab1 {
			background: #ffffff;
			box-sizing: border-box;
			font-size: 14px;
			padding: 20px 0 40px;
			font-weight: 400;
			color: #666666;
			line-height: 24px;
			::v-deep p {
				img {
					width: 100% !important;
				}
			}
		}
		.course-list {
			padding: 0 0 20px;
			.course-item {
				background: #f7f9fb;
				border-radius: 10px;
				padding: 30px;
				margin-top: 10px;
			}
			.lesson-name {
				font-size: 16px;
				font-weight: bold;
				color: #222222;
			}
			.header {
				.blue {
					width: 26px;
					height: 26px;
					text-align: center;
					background: var(--brand-6, #0076e8);
					line-height: 26px;
					color: #fff;
					border-radius: 50%;
				}
				.lessonName {
					color: #313d54;
					font-weight: bold;
					font-size: 18px;
					margin-left: 17px;
					margin-right: 8px;
				}
				.time {
					font-size: 16px;
					font-weight: 400;
					color: #9399a5;
				}
			}
			.lesson {
				// height: 48px;
				color: #313d54;
				margin-left: 60px;
				margin-top: 16px;
				.icon {
					font-size: 18px;
					margin: 0 6px 0 28px;
					color: var(--brand-6, #0076e8);
				}
				.name {
					font-size: 14px;
					font-weight: 400;
					margin-right: 8px;
				}
				.long {
					font-size: 14px;
					flex-shrink: 0;
					font-weight: 400;
					color: #9399a5;
				}
				.right {
					padding-right: 15px;

					.study {
						width: 80px;
						height: 28px;
						background: var(--brand-6, #0076e8);
						border-radius: 14px;
						text-align: center;

						line-height: 28px;
						font-size: 14px;
						color: #fff;
						font-weight: 400;
						display: none;
						cursor: pointer;
					}
				}
			}
			.lesson:hover {
				color: #4f85ff;
				cursor: pointer;
			}
		}
		.tab4 {
			padding: 0 20px;
			::v-deep .el-textarea__inner {
				background-color: #f4f6fb;
			}
			.start {
				color: var(--brand-6, #0076e8);
				font-weight: bold;
				padding: 15px 0;
			}
			.end {
				font-weight: bold;
				color: var(--brand-6, #0076e8);
				padding: 15px 0;
			}
			.starList {
				margin-bottom: 10px;
				.star {
					padding: 8px 0;
					.key {
						color: #666666;
						font-weight: bold;
						font-size: 14px;
						width: 80px;
					}
				}
			}
			.pl {
				cursor: pointer;
				margin: 10px 0 0 670px;
				width: 150px;
				height: 47px;
				text-align: center;
				line-height: 47px;
				color: #fff;
				background: var(--brand-6, #0076e8);
				// position: absolute;
				// right: 0;
				// bottom: -5px;
				box-shadow: 0px 4px 10px 0px rgba(0, 118, 232, 0.2);
				border-radius: 24px;
			}
			.star {
				padding: 5px 0;
				::v-deep .el-rate__icon {
					margin-right: 0;
				}
			}
			.tab4top {
				border-top: 1px solid #e8eaf0;
				border-bottom: 1px solid #e8eaf0;
				padding-bottom: 12px;
				padding-top: 12px;
				.score {
					text-align: center;
					.number {
						font-weight: bold;
						font-size: 50px;
						color: var(--brand-6, #0076e8);
					}
					::v-deep .el-rate__icon {
						margin-right: 0;
					}
				}
				.label {
					margin-left: 22px;
					.n1 {
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #9399a5;
						margin-bottom: 12px;
						span {
							color: #666666;
						}
					}
					.n2 {
						.item {
							margin-right: 10px;
							height: 26px;
							background: #e5f1fd;
							border-radius: 13px;
							text-align: center;
							line-height: 26px;
							font-size: 12px;
							color: var(--brand-6, #0076e8);
							font-weight: 400;
							padding: 0 10px;
							span {
								margin-right: 2px;
							}
						}
					}
				}
			}
			.list {
				li {
					border-bottom: 1px solid #e8eaf0;
					.user {
						padding: 10px 20px;
						.touxiang {
							width: 60px;
							height: 60px;
							border-radius: 50%;
							margin-right: 10px;
						}
						.flex2 {
							width: 710px;
							margin-top: -30px;
						}
						.name {
							color: #333333;
							font-size: 14px;
							font-weight: 500;
						}
						.ps {
							margin: 0 15px;
							color: #999999;
							font-size: 14px;
						}
						.date {
							font-size: 14px;
							color: #999999;
							.iconfont {
								color: #999999;
							}
							span {
								color: #999999;
								margin-left: 5px;
							}
						}
					}
					.text {
						padding: 5px 5px 10px 90px;
						margin-top: -40px;
						margin-bottom: 15px;
					}
					.tag {
						margin-bottom: 20px;
						padding-left: 90px;
						.ele {
							background: #e8eaf0;
							border: 1px solid #dddddd;
							border-radius: 11px;
							height: 22px;
							text-align: center;
							line-height: 22px;
							font-weight: 400;
							color: #959ba7;
							font-size: 12px;
							padding: 0 7px;
							margin-right: 10px;
						}
					}
					.word {
						background: #f4f5f7;
						padding: 20px;
						margin-left: 90px;
						margin-bottom: 20px;
						font-weight: 400;
						color: #6b7484;
						line-height: 24px;
						border-radius: 6px;
					}
				}
			}

			.page {
				text-align: center;
				padding: 30px 0;
			}
		}
	}
	.fourRight {
		margin-top: 0;
		width: 280px;
		margin-left: 60px;
		.type1 {
			padding-bottom: 20px;
			width: 100%;
			background: #ffffff;
			.title {
				font-size: 20px;
				font-weight: bold;
				color: #222222;
				position: relative;
				&::after {
					content: '';
					position: absolute;
					width: 40px;
					height: 5px;
					background: #4f85ff;
					border-radius: 3px;
					left: 0;
					bottom: -10px;
				}
			}
			.list {
				// padding: 0 20px;
				margin-top: 30px;
				li {
					// height: 80px;
					margin-bottom: 20px;
					cursor: pointer;
					.leftImg {
						width: 100%;
						height: 160px;
						border-radius: 10px;
					}
					.info {
						width: 100%;
						padding: 16px 0 0;
						.title {
							font-size: 16px;
							font-weight: 400;
							color: #222222;
						}
					}
				}
			}
		}
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #2f3750;
	.sub-breadcrumb {
		width: 1240px !important;
		height: 40px;
		color: #b8bfd7;
	}
}
</style>
