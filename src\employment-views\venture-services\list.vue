<template>
	<div v-loading="loading">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<!-- 列表数据区域 -->
		<ul v-if="list.length" class="list-box">
			<li v-for="(item, index) in list" :key="index" class="list-item" @click="itemJump(item)">
				<!-- <img class="item-img" :src="getImgUrl(item.cover)" alt="" /> -->
				<el-image class="item-img" fit="contain" :src="getImgUrl(item.cover)" lazy></el-image>
				<div class="item-base">
					<p class="name over1">{{ item.name }}</p>
					<span class="desc over2">{{ item.profiles }}</span>
				</div>
			</li>
			<!-- 分页 -->
			<el-pagination
				class="pagination"
				background
				layout="prev, pager, next,jumper"
				:total="total"
				:page-size="pageSize"
				@current-change="handleCurrentChange"
			></el-pagination>
		</ul>
		<Empty v-else :tips="'暂无数据'" />
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
export default {
	components: {
		subBreadcrumb
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			loading: false, //加载动画
			pageNum: 1, //当前页码
			pageSize: 8, //每页展示条数
			total: 0, //总共数量
			typeNum: '', //0是创业 1是就业
			type: 'serve',
			typeList: {
				serve: 'getServiceTypeList',
				project: 'getProjectList'
			},
			list: [] //列表数据
		};
	},
	mounted() {
		this.typeNum = this.$route.query.typeNum || '0';
		this.type = this.$route.query.type || 'serve';
		this.typeList[this.type] && this[this.typeList[this.type]]();
	},
	methods: {
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
			this.getServiceTypeList();
		},
		itemJump(item) {
			if (this.type == 'project') {
				return this.jumpPage(`/services-detail?type=project&id=${item.id}`);
			}
			this.jumpPage(`/services-type-list?typeId=${item.id}&typeNum=${this.typeNum}`);
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		/**
		 * @description 获取就业类型分页接口
		 * */
		getServiceTypeList() {
			this.loading = true;
			let param = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				type: this.typeNum
			};
			this.$api.employment_api
				.serviceTypeList(param)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/*
		 * @description 创业项目表分页接口
		 * */
		getProjectList() {
			this.loading = true;
			let param = {
				pageNum: 1,
				pageSize: 8,
				auditStatus: 1
			};
			this.$api.employment_api
				.jobStudentProject(param)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.list-box {
	width: $max-width;
	margin: 30px auto 60px;
	// margin-top: 30px;
	display: flex;
	flex-wrap: wrap;
	.list-item {
		width: 300px;
		height: 280px;
		background: #ffffff;
		border-radius: 5px;
		margin-right: 20px;
		margin-bottom: 20px;
		&:nth-child(4n) {
			margin-right: 0;
		}
		.item-img {
			width: 100%;
			height: 178px;
			background: #ffffff;
			object-fit: contain;
		}
		.item-base {
			padding: 17px 19px;
			.name {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
			}
			.desc {
				font-size: 14px;
				line-height: 18px;
				color: #999999;
				margin-top: 12px;
			}
		}
	}
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
