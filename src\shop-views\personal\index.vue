<template>
	<div class="person">
		<div class="person-left">
			<!--			<div class="person-left-top">-->
			<!--				<img class="img" src="@/assets/shop-images/user_center_frame.png" alt="" />-->
			<!--				<div class="title">个人中心</div>-->
			<!--			</div>-->
			<div class="person-left-con">
				<el-menu
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
				>
					<!--					<el-submenu index="1">-->
					<!--						<template slot="title">-->
					<!--							<i class="el-icon-location"></i>-->
					<!--							<span>导航一</span>-->
					<!--						</template>-->
					<!--						<el-menu-item-group>-->
					<!--							<el-menu-item index="1-1">选项1</el-menu-item>-->
					<!--							<el-menu-item index="1-2">选项2</el-menu-item>-->
					<!--						</el-menu-item-group>-->
					<!--					</el-submenu>-->
					<el-menu-item v-for="menu of menus" :key="menu.index" :index="menu.index">
						<span slot="title">{{ menu.name }}</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb>
			<personHeader :title="activeName"></personHeader>
			<component :is="activeComponent" :is-main="false"></component>
		</div>
	</div>
</template>

<script>
import myOrder from '@/shop-views/personal/buyer/my_order';
import myCourse from '@/shop-views/personal/buyer/my_course';
import billList from '@/shop-views/personal/buyer/bill_list';
import shoppingCart from '@/shop-views/shoppingCart';
import shoppingAddress from '@/shop-views/personal/buyer/shipping_address';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import collectList from '@/shop-views/personal/buyer/collect_list';
import pointsMall from '@/shop-views/pointsMall';
import personHeader from '@/components/person-header';

export default {
	components: {
		myOrder,
		myCourse,
		billList,
		shoppingCart,
		shoppingAddress,
		subBreadcrumb,
		collectList,
		pointsMall,
		personHeader
	},
	data() {
		return {
			activeMenu: '1-1',
			activeComponent: 'myOrder',
			activeName: '我的订单',
			menus: [
				{
					name: '我的订单',
					index: '1-1',
					path: 'myOrder'
				},
				{
					name: '我的课程',
					index: '7-1',
					path: 'myCourse'
				},
				{
					name: '开票记录',
					index: '2-1',
					path: 'billList'
				},
				{
					name: '购物车',
					index: '3-1',
					path: 'shoppingCart'
				},
				{
					name: '收货地址',
					index: '4-1',
					path: 'shoppingAddress'
				},

				{
					name: '我的收藏',
					index: '5-1',
					path: 'collectList'
				},
				{
					name: '积分商城',
					index: '6-1',
					path: 'pointsMall'
				}
			]
		};
	},
	created() {},
	methods: {
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.menus.find(item => {
				return item.index === index;
			});
			this.activeName = currentObj.name;
			this.activeMenu = currentObj.index;
			this.activeComponent = currentObj.path;
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
		&-top {
			background: linear-gradient(136deg, #dee9ff 0%, #ffffff 100%);
			width: 100%;
			height: 66px;
			font-size: 20px;
			font-family: Source Han Sans SC-Bold, Source Han Sans SC;
			font-weight: bold;
			color: #2b99fe;
			line-height: 28px;
			display: flex;
			align-items: center;
			justify-content: center;
			.img {
				width: 32px;
				height: 32px;
			}
		}
		::v-deep .el-menu-item {
			padding-left: 40px !important;
		}
		::v-deep .is-active {
			position: relative;
			&::before {
				content: '';
				display: inline-block;
				position: absolute;
				left: 20px;
				top: calc(50% - 7px);
				width: 16px;
				height: 16px;
				border-radius: 50%;
				background: #3274e0;
			}
		}
	}
	&-right {
		width: calc(100% - 236px);
	}
}
</style>
