<template>
	<div class="steps">
		<div :class="{ step: true, active: true, examine: true }">
			<div class="step_num">1</div>
			<div class="step_title">填写{{ $router.match('authinfo') ? '认证' : '企业' }}信息</div>
		</div>
		<div
			:class="{
				step: true,
				active: status == 1,
				examine: status == 1 || status == 0,
				reject: status == 2
			}"
		>
			<div class="step_num">2</div>
			<div class="step_title">认证完成</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'Steps',
	components: {},
	props: {
		// status 9草稿，0待审核，1审核通过，2审核不通过
		status: {
			type: Number,
			default: 9
		}
	}
};
</script>
<style lang="scss" scoped>
.steps {
	display: flex;
	margin: 0 auto;
	margin-top: 44px;
	margin-bottom: 88px;
	justify-content: center;
	.step {
		width: 316px;
		position: relative;
		&_num {
			width: 24px;
			height: 24px;
			background: #fff;
			border-radius: 50%;
			opacity: 1;
			border: 2px solid #bfbfbf;
			font-size: 14px;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: #bfbfbf;
			line-height: 20px;
			text-align: center;
			margin: 0 auto;
			position: relative;
			z-index: 10;
		}
		&_title {
			height: 24px;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #bfbfbf;
			line-height: 24px;
			text-align: center;
			margin-top: 4px;
		}
		&::after {
			content: '';
			display: inline-block;
			width: 50%;
			height: 2px;
			background: #d9d9d9;
			position: absolute;
			top: 11px;
			z-index: 2;
		}
		&:first-child {
			&::after {
				content: '';
				display: inline-block;
				width: 50%;
				height: 2px;
				background: #d9d9d9;
				position: absolute;
				top: 11px;
				right: 0;
				z-index: 2;
			}
		}
		&.active {
			.step_num {
				background: var(--brand-6, #0076e8);
				border: 2px solid var(--brand-6, #0076e8);
				color: #fff;
			}
			.step_title {
				color: var(--brand-6, #0076e8);
			}
		}
		// 审核中
		&.examine {
			&::after {
				background: var(--brand-6, #0076e8);
			}
		}
		// 驳回
		&.reject {
			.step_num {
				background: #ff4936;
				border: 2px solid #ff4936;
				color: #fff;
			}
			.step_title {
				color: #ff4936;
			}
			&::after {
				background: #ff4936;
			}
		}
	}
}
</style>
