<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="main">
		<!-- 服务列表 -->
		<h3 class="form-title">
			服务列表
			<el-button class="new-btn" type="primary" @click="handlerNew">新增</el-button>
		</h3>
		<el-table
			v-loading="tableLoading"
			:data="serviceList"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<!-- <el-table-column type="index" label="序号" width="150"></el-table-column> -->
			<el-table-column prop="title" label="标题"></el-table-column>
			<el-table-column prop="typeId" label="分类">
				<template #default="{ row }">
					{{ getServiceType(row.typeId) }}
				</template>
			</el-table-column>
			<el-table-column prop="cover" label="封面">
				<template #default="{ row }">
					<el-image class="item-img" :src="getImgUrl(row.cover || '')" alt="" fit="contain" />
				</template>
			</el-table-column>
			<!-- <el-table-column prop="introduction" label="简介"></el-table-column> -->
			<el-table-column prop="updateUserName" label="更新人">
				<template #default="{ row }">
					{{ row.updateUserName || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="updateTime" width="170" label="更新时间"></el-table-column>
			<el-table-column prop="auditStatus" width="80" label="审核状态">
				<template #default="{ row }">
					<el-tag :type="{ 0: '', 1: 'success', 9: 'info', 2: 'warning' }[row.auditStatus]">
						{{ row.auditStatusName }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="status" width="80" label="发布状态">
				<template #default="{ row }">
					<el-tag :type="{ 0: '', 1: 'success', 9: 'info', 2: 'warning' }[row.status]">
						{{ row.statusName }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="200">
				<template #default="{ row }">
					<el-button type="text" size="small" class="del-btn" @click="delClick(row)">
						删除
					</el-button>
					<el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
					<el-button type="text" size="small" @click="handleView(row)">查看</el-button>
					<el-button
						v-if="row.auditStatus === 1"
						type="text"
						size="small"
						@click="handleClose(row)"
					>
						{{ row.status ? '停用' : '启用' }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="handleCurrentChange"
		/>
		<el-dialog
			width="1000px"
			:title="updateServiceDialog.title"
			:visible.sync="updateServiceDialog.visible"
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<div v-loading="updateServiceDialog.submitLoading" class="content">
				<el-form
					ref="form"
					:model="updateServiceDialog.form"
					:rules="updateServiceDialog.rules"
					label-width="80px"
				>
					<div class="business-info">
						<el-row :gutter="20">
							<el-col :span="12">
								<el-form-item label="项目名称" prop="title">
									<el-input
										v-model="updateServiceDialog.form.title"
										placeholder="请填写项目名称"
										type="input"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="简介" prop="introduction">
									<el-input
										v-model="updateServiceDialog.form.introduction"
										placeholder="请填写项目简介"
										type="textarea"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="内容" prop="description">
									<Tinymce
										ref="tinymce"
										:value.sync="updateServiceDialog.form.description"
										:form-data="formData"
										:image-upload-url="updUrl"
										@imageUploadSuccess="handleImageUploadSuccess"
									></Tinymce>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="分类" prop="typeId">
									<el-cascader
										v-model="updateServiceDialog.form.typeId"
										:options="jobServiceTypeList"
										placeholder="请选择分类"
									></el-cascader>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="封面" prop="cover">
									<el-upload
										v-loading="uploadLoading"
										class="avatar-uploader"
										:action="updUrl"
										:show-file-list="false"
										:accept="acceptImg"
										:data="{
											ownId: updateServiceDialog.form.id || uuidv4(),
											code: 'job_co_startup_cover'
										}"
										:on-success="handleAvatarSuccess"
										:before-upload="beforeAvatarUpload"
										:on-remove="uploadOnRemove"
									>
										<el-image
											v-if="updateServiceDialog.form.cover"
											:src="getImgUrl(updateServiceDialog.form.cover)"
											alt=""
											class="avatar"
										/>
										<i v-else class="el-icon-plus avatar-uploader-icon"></i>
									</el-upload>
								</el-form-item>
							</el-col>
						</el-row>
					</div>
				</el-form>
				<div class="info-save">
					<el-button class="info-save-confirm" type="primary" size="large" @click="handlerSubmit">
						提交
					</el-button>
					<el-button class="info-save-staging" size="large" @click="resetForm">重置</el-button>
				</div>
			</div>
		</el-dialog>
		<!--查看服务信息-->
		<el-dialog
			title="查看服务信息"
			width="800px"
			:visible.sync="viewJobServiceDialog.visible"
			:close-on-click-modal="false"
		>
			<el-descriptions class="margin-top" :column="2">
				<el-descriptions-item label="服务标题">
					{{ viewJobServiceDialog.row.title }}
				</el-descriptions-item>
				<el-descriptions-item label="分类">
					{{ getServiceType(viewJobServiceDialog.row.typeId) }}
				</el-descriptions-item>
				<el-descriptions-item label="封面图">
					<el-image :src="getImgUrl(viewJobServiceDialog.row.cover)"></el-image>
				</el-descriptions-item>
				<el-descriptions-item label="简介">
					<div v-html="viewJobServiceDialog.row.introduction"></div>
				</el-descriptions-item>
				<el-descriptions-item label="内容">
					<div class="content-box" v-html="viewJobServiceDialog.row.description"></div>
				</el-descriptions-item>
			</el-descriptions>
		</el-dialog>
	</div>
</template>

<script>
const INIT_JOB_DETAIL = {
	title: '', //标题
	typeId: '', //分类
	cover: '', //封面
	description: '', //内容
	introduction: '' // 简介
};
import { baseUrl } from '@/config';
import Tinymce from '@/components/tinymce';
import { v4 as uuidv4 } from 'uuid';
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	name: 'ServeList',
	components: { Tinymce },
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			baseUrl,
			uuidv4,
			updateServiceDialog: {
				visible: false,
				title: '新增服务',
				submitLoading: false,
				form: { ...INIT_JOB_DETAIL },
				rules: {
					title: [{ required: true, message: '请输入标题', trigger: 'blur' }], //
					typeId: [{ required: true, message: '请选择类型', trigger: 'blur' }], //
					cover: [{ required: true, message: '请选择封面', trigger: 'blur' }], //
					description: [{ required: true, message: '请填写内容', trigger: 'blur' }],
					introduction: [{ required: true, message: '请填写简介', trigger: 'blur' }]
				}
			},
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			formData: {
				ownId: uuidv4(),
				code: 'job_service_richtext'
			},
			imgSize: 10, // 图片上传大小限制在10M
			acceptImg: '.jpg,.gif,.jpeg,.png,.JPG,.GIF,.JPEG,.PNG', //上传图片格式
			updUrl: baseUrl + '/ybzy/mecpfileManagement/front/upload', // 文件上传地址
			jobServiceTypeList: [],
			serviceList: [], //职位列表
			viewJobServiceDialog: {
				visible: false,
				row: []
			},
			uploadLoading: false
		};
	},
	created() {
		this.getJobServiceType();
		this.getJobServiceList();
	},
	methods: {
		async getJobServiceType() {
			const { results } = await this.$api.enterprise_center.getJobServiceType({
				pageNum: 1,
				pageSize: 999
			});

			this.jobServiceTypeList = results?.records;
		},
		async getJobServiceList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getJobServiceList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					console.log('列表数据', res.results);
					this.serviceList = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		/**
		 * @description 删除操作
		 * */
		delClick(row) {
			this.$confirm('确认删除该服务？')
				.then(_ => {
					this.$api.enterprise_center.delJobService({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getJobServiceList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		handleAvatarSuccess(res, file, fileList) {
			this.imageList = fileList;
			this.updateServiceDialog.form.cover = fileList
				.map(file => {
					if (file.response) {
						return file.response.results[0].adjunctId;
					}
					return file.name;
				})
				.join(',');
			this.uploadLoading = false;
		},
		// 监听文件删除
		uploadOnRemove(file, fileList) {
			this.imageList = fileList;
			this.updateServiceDialog.form.cover = fileList
				.map(file => {
					if (file.response) {
						return file.response.results[0].adjunctId;
					}
					return file.name;
				})
				.join(',');
		},
		/**
		 * @description 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
		 * */
		beforeAvatarUpload(file) {
			this.uploadLoading = true;
			const isImgSize = file.size / 1024 / 1024 < this.imgSize;
			if (!isImgSize) {
				this.uploadLoading = false;
				this.$message.error(`上传头像图片大小不能超过 ${this.imgSize}MB!`);
			}
			return isImgSize;
		},
		handlerNew() {
			this.updateServiceDialog.visible = true;
			this.updateServiceDialog.title = '新增服务';
			this.updateServiceDialog.form = { ...INIT_JOB_DETAIL };
		},
		/**
		 * @description 编辑操作
		 * */
		handleEdit(row) {
			this.updateServiceDialog.visible = true;
			this.updateServiceDialog.title = '编辑服务';
			this.updateServiceDialog.form = {
				...row
			};
		},
		/**
		 * @description 查看操作
		 * */
		handleView(row) {
			this.viewJobServiceDialog.visible = true;
			this.viewJobServiceDialog.row = { ...row };
		},
		closeDialog() {
			this.updateServiceDialog.visible = false;
			this.resetForm();
		},
		handlerSubmit() {
			this.updateServiceDialog.form.description = this.$refs.tinymce.getContent() || '';
			this.$refs.form.validate(valid => {
				if (valid) {
					this.updateServiceDialog.submitLoading = true;
					const apiMap = {
						add: this.$api.enterprise_center.saveJobService,
						edit: this.$api.enterprise_center.updateJobService
					};

					apiMap[this.updateServiceDialog.form.id ? 'edit' : 'add']({
						...this.updateServiceDialog.form
					})
						.then(res => {
							if (res.success) {
								this.$message.success('操作成功');
								this.updateServiceDialog.visible = false;
								this.getJobServiceList();
							} else {
								this.$message.error(res.msg);
							}
						})
						.finally(() => {
							this.updateServiceDialog.submitLoading = false;
						});
				}
			});
		},
		resetForm() {
			this.$refs.form.resetFields();
		},
		handleCurrentChange() {
			this.getJobList();
		},
		// 获取创业服务分类
		getServiceType(value) {
			return this.jobServiceTypeList?.find(option => option.value === value)?.label;
		},
		/**
		 * @description 停用服务
		 * */
		handleClose(row) {
			this.$confirm('确认停用该服务？')
				.then(_ => {
					this.$api.enterprise_center.changeJobServiceStatus({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getJobServiceList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		// 富文本框上传成功
		handleImageUploadSuccess(res, success, failure) {
			if (res.success && res.rCode === 0) {
				success(
					`${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${res.results[0].adjunctId}`
				);
			} else {
				failure(res.msg);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.new-btn {
	margin-left: 10px;
}
.item-img {
	width: 60px;
	height: 60px;
}

.avatar-uploader {
	width: 100px;
	height: 100px;
	background: #f9f9f9;
	display: inline-block;
	&::v-deep {
		.el-upload {
			width: 100%;
			height: 100%;
			border: 1px dashed #d9d9d9;
			border-radius: 6px;
			cursor: pointer;
			position: relative;
			overflow: hidden;
			&:hover {
				border-color: #1890ff;
			}
		}
	}
}
.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 100px;
	height: 100px;
	line-height: 100px;
	text-align: center;
}
.avatar {
	width: 100%;
	height: 100%;
	display: inline-block;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
.content-box {
	width: 714px;
	overflow: hidden;
}
// 详情内富文本图片展示裁剪问题处理
::v-deep .content-box img {
	max-width: 100%;
}
</style>
