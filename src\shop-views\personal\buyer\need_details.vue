<template>
	<div v-loading="loading" class="need_details">
		<div class="btn-box">
			<el-button @click="$router.go(-1)">返回</el-button>

			<div class="rf">
				<el-button v-if="details.canManualTraded" type="primary" @click="manualTraded">
					已成交
				</el-button>
				<el-button v-if="details.canCancel" type="primary" @click="requirementCancel">
					取消
				</el-button>
				<el-button v-if="details.canUpdate" type="primary" @click="goEdit">编辑</el-button>
			</div>
		</div>
		<div class="data">
			<div class="title">
				<div class="lf">采购品种：{{ details.goodsTypeDesc || '-' }}</div>
				<div class="rf">
					发布于 {{ details.publishTime || '-' }}
					<span v-if="details.state !== 4">{{ details.stateDesc || '-' }}</span>
					<b v-else>
						<AlIcon name="icon-time" color="#ca3f3b" size="18px" />
						{{ details.validityDay }}
					</b>
				</div>
			</div>
			<div class="box">
				<div class="cell">
					<div class="name">采购数量：</div>
					<div class="val nth1">
						{{ details.quantity ? details.quantity + details.units : '-' }}
					</div>
				</div>
				<!-- <div class="cell">
					<div class="name">规格：</div>
					<div class="val nth1">{{ details.goodsVarietyDesc || '-' }}</div>
				</div> -->
				<div class="cell">
					<div class="name">提货方式：</div>
					<div class="val nth1">{{ details.pickUpTypeDesc || '-' }}</div>
				</div>
				<div class="cell">
					<div class="name">浏览次数：</div>
					<div class="val nth1">{{ details.view || '0' }}</div>
				</div>
				<div class="cell">
					<div class="name">期望货源地：</div>
					<div class="val nth1">{{ details.expectedAddrDesc || '-' }}</div>
				</div>
				<div class="cell">
					<div class="name">收货地：</div>
					<el-tooltip class="item" effect="dark" :content="details.receiveAddrDesc" placement="top">
						<div class="val nth1">{{ details.receiveAddrDesc || '-' }}</div>
					</el-tooltip>
				</div>
				<div class="cell">
					<div class="name">补充说明：</div>
					<el-tooltip class="item" effect="dark" :content="details.illustrate" placement="top">
						<div class="val nth1">{{ details.illustrate || '-' }}</div>
					</el-tooltip>
				</div>
				<div class="cell">
					<div class="name">报价情况：</div>
					<div class="val nth1">{{ details.quotedNum || '-' }}</div>
				</div>
				<div class="cell">
					<div class="name">浏览次数：</div>
					<div class="val nth1">{{ details.view || '0' }}</div>
				</div>
			</div>
		</div>
		<div class="table">
			<Title title="报价情况"></Title>
			<el-table :data="tableData" style="width: 100%" align="center">
				<el-table-column show-overflow-tooltip prop="quantity" label="商品名称" width="100px">
					<template slot-scope="scope">
						<div
							@click="
								$router.push({
									path: `/goodsList/details?id=${scope.row.spuId}`
								})
							"
						>
							{{ scope.row.goodsName }}
						</div>
					</template>
				</el-table-column>
				<el-table-column show-overflow-tooltip prop="quantity" label="供货数量" width="100px">
					<template slot-scope="scope">
						{{ scope.row.quantity + scope.row.units }}
					</template>
				</el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="price"
					label="商品单价"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="stockUpDays"
					label="备货天数"
					width="134px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="shipAddrDesc"
					label="发货地"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="quotedPriceTypeDesc"
					label="报价方式"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="supplier"
					label="供应商"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="contactPhone"
					label="联系电话"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="illustrate"
					label="补充说明"
					width="100px"
				></el-table-column>
				<el-table-column label="状态" width="100px">
					<template slot-scope="scope">
						<span :style="{ color: scope.row.state === 5 ? '#76BF6A' : '#8C8C8C' }">
							{{ scope.row.stateDesc }}
						</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="170px" fixed="right">
					<template slot-scope="scope">
						<div v-if="scope.row.state == 0" class="btn">
							<el-link
								type="danger"
								@click="
									purchase(
										scope.row.id,
										scope.row.skuId,
										scope.row.quantity,
										scope.row.type,
										scope.row.reg_code
									)
								"
							>
								下单购买
							</el-link>
							<el-link type="info" @click="closeOrder(scope.row)">放弃此单</el-link>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				hide-on-single-page
				:current-page="tablePage"
				layout="total, prev, pager, next"
				:total="tableTotal"
				@current-change="handlePage1"
			></el-pagination>
		</div>
		<div class="table">
			<Title title="成交信息"></Title>
			<el-table :data="tableData1" style="width: 100%" align="center">
				<el-table-column show-overflow-tooltip prop="reqId" label="需求编码"></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="orderId"
					label="订单编码"
					width="100px"
				></el-table-column>
				<el-table-column show-overflow-tooltip prop="quantity" label="供货数量" width="100px">
					<template slot-scope="scope">
						{{ scope.row.quantity + scope.row.units }}
					</template>
				</el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="price"
					label="商品单价"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="supplier"
					label="供应商"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="contactPerson"
					label="联系人"
					width="100px"
				></el-table-column>
				<el-table-column
					show-overflow-tooltip
					prop="contactPhone"
					label="联系电话"
					width="100px"
				></el-table-column>
				<el-table-column show-overflow-tooltip prop="orderTime" label="下单时间"></el-table-column>
				<el-table-column show-overflow-tooltip prop="date" label="操作" width="140px">
					<template slot-scope="scope">
						<el-button
							@click="
								$router.push({
									path: `/personal/order_details?id=${scope.row.orderId}`
								})
							"
						>
							查看订单
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				hide-on-single-page
				:current-page="tablePage2"
				layout="total, prev, pager, next"
				:total="tableTotal2"
				@current-change="handlePage2"
			></el-pagination>
		</div>
		<div class="recommend">
			<HeadTitle title="平台推荐"></HeadTitle>
			<div class="list-box">
				<GoodList :list="goodList"></GoodList>
			</div>
		</div>
	</div>
</template>

<script>
import GoodList from './../components/good_list.vue';
import Title from './../components/title.vue';
import HeadTitle from '@/views/index/components/Title.vue';
import { personal } from '@/api';
export default {
	name: 'NeedDetails',
	components: {
		Title,
		HeadTitle,
		GoodList
	},
	data() {
		return {
			tableData: [],
			tableData1: [],
			details: {},
			loading: false,
			id: '',
			tablePage: 1,
			tablePage2: 1,
			tableTotal: 0,
			tableTotal2: 0,
			goodList: [],
			main: window.__POWERED_BY_WUJIE__
		};
	},
	created() {
		if (!this.$route.query.id) {
			this.$message.close();
			this.$message.error('参数错误！');
			this.$router.go(-1);
		} else {
			this.id = this.$route.query.id;
			this.needDetail();
			this.needList('all', this.tablePage);
			this.needList('order', this.tablePage2);
		}
	},
	methods: {
		goEdit() {
			this.$router.push({
				path: `/add_need?id=${this.id}`
			});
		},
		requirementCancel() {
			this.$confirm('确认需要将当前需求取消?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.loading = true;
					personal.requirementCancel({ id: this.details.id }).then(res => {
						if (res.code == 200) {
							this.$message.close();
							this.$message({
								message: '操作成功',
								type: 'success'
							});
							this.needDetail();
							this.needList('all', this.tablePage);
							this.needList('order', this.tablePage2);
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				})
				.catch(() => {});
		},
		manualTraded() {
			this.$confirm('确认需要将当前需求设置为已成交?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.loading = true;
					personal.manualTraded({ id: this.details.id }).then(res => {
						if (res.code == 200) {
							this.$message.close();
							this.$message({
								message: '操作成功',
								type: 'success'
							});
							this.needDetail();
							this.needList('all', this.tablePage);
							this.needList('order', this.tablePage2);
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				})
				.catch(() => {});
		},
		// 购买
		purchase(id, skuId, quantity, type, type2) {
			this.$router.push({
				path: `/settleAccounts/settleAccountsIndex?channel=market&ids=${id}&skuid=${skuId}&skunum=${quantity}&value=${type}&shopType=${type2}`
			});
		},
		// 推荐商品
		getNewProduct() {
			const data = {
				siteId: this.getSiteId(),
				offset: 0,
				psize: 10,
				categoryId: this.details.goodsClassify
			};
			personal.getNewProduct(data).then(res => {
				if (res.state) {
					this.goodList = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
			});
		},
		handlePage1(i) {
			this.tablePage = i;
			this.needList(this.tablePage, 'all');
		},
		handlePage2(i) {
			this.tablePage2 = i;
			this.needList(this.tablePage, 'order');
		},
		// 获取报价列表
		needList(queryType, pageNum) {
			const data = {
				queryType,
				pageNum,
				pageSize: 10,
				reqId: this.id
			};
			personal.needList(data).then(res => {
				if (res.code === 200) {
					if (queryType == 'all') {
						this.tableData = res.results.records;
						this.tableTotal = res.results.total;
					} else {
						this.tableData1 = res.results.records;
						this.tableTotal2 = res.results.total;
					}
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
			});
		},
		// 获取详情
		needDetail() {
			this.loading = true;
			personal.needDetail({ id: this.id }).then(res => {
				if (res.code == 200) {
					this.details = res.results;
					this.getNewProduct();
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		closeOrder(i) {
			this.$confirm('确定要放弃此单吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					personal.abandon({ quotedId: i.id }).then(res => {
						if (res.code == 200) {
							this.$message.close();
							this.$message({
								message: res.msg,
								type: 'success'
							});
							this.needList('all', this.tablePage);
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				})
				.catch(() => {});
			console.log(i);
		}
	}
};
</script>

<style lang="scss" scoped>
.nth1 {
	overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
	text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
	white-space: nowrap; /*文本不换行*/
}
.need_details {
	width: 100%;
	.btn-box {
		margin-bottom: 14px;
		display: flex;
		.rf {
			margin-left: auto;
		}
	}
	.data {
		padding: 22px 30px 21px 16px;
		background: #f7f8fa;
		.title {
			display: flex;
			align-items: center;
			.lf {
				font-size: 20px;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
			}
			.rf {
				margin-left: auto;
				font-size: 16px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 24px;
				span {
					margin-left: 29px;
					font-size: 18px;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
					line-height: 32px;
				}
				b {
					margin-left: 33px;
					font-size: 18px;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
					line-height: 32px;
				}
			}
		}
		.box {
			margin-top: 17px;
			display: flex;
			align-items: center;
			width: 100%;
			padding: 0 10px;
			box-sizing: border-box;
			flex-wrap: wrap;
			.cell {
				margin-top: 18px;
				width: 33.3%;
				display: flex;
				align-items: center;
				.name {
					width: 96px;
					height: 24px;
					font-size: 16px;
					font-weight: 400;
					color: #9da5b7;
					line-height: 24px;
					text-align: right;
				}
				.val {
					width: calc(100% - 100px);
					height: 24px;
					font-size: 16px;
					font-weight: 400;
					color: #404040;
					line-height: 24px;
				}
			}
		}
	}
	.table {
		margin-top: 41px;
		::v-deep .el-table {
			margin-top: 17px;
			th {
				background: #f6f6f6;
			}
		}
		::v-deep.el-pagination {
			margin-top: 20px;
			display: flex;
			.btn-prev {
				margin-left: auto;
			}
		}
		.btn {
			padding: 0 10px;
			display: flex;
			justify-content: space-between;
		}
	}
	.recommend {
		margin-top: 30px;
		padding: 0 42px;
	}
	::v-deep.el-dialog__body {
		padding: 10px;
		text-align: center;
	}
}
</style>
