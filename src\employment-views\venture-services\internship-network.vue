<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div id="map" class="map">
				<img class="map-img" :src="require('@/assets/employment-images/map-img.png')" alt="" />
			</div>
			<div class="right-box">
				<div class="filter-box">
					<div class="select-box">
						<!-- {{this.cityOptions}} -->
						<el-select
							v-model="value"
							placeholder="选择省份"
							class="select-item"
							@change="regionChange"
						>
							<el-option
								v-for="item in regionOptions"
								:key="item.value"
								:label="item.label"
								:value="item"
							></el-option>
						</el-select>
						<el-select
							v-model="cityValue"
							placeholder="选择城市"
							class="select-item"
							@change="cityChange"
						>
							<el-option
								v-for="item in cityOptions"
								:key="item.value"
								:label="item.label"
								:value="item"
							></el-option>
						</el-select>
						<el-select
							v-model="ereaValue"
							placeholder="选择区/县"
							class="select-item"
							@change="ereaChange"
						>
							<el-option
								v-for="item in ereaOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</div>
					<div class="select-box">
						<el-input
							v-model.trim="keyword"
							placeholder="请输入实习网点名称关键字"
							class="input-icon"
						>
							<i slot="prepend" class="el-icon el-icon-search"></i>
						</el-input>
						<el-button type="primary" class="btn searrch-btn" @click="search">查询</el-button>
						<el-button class="btn reset-btn" @click="reset">重置</el-button>
					</div>
				</div>
				<div
					v-loading="loading"
					v-infinite-scroll="load"
					class="list-box"
					style="overflow: auto"
					infinite-scroll-distance="5"
				>
					<ul v-if="list.length" class="list">
						<!-- class="content-right-middle" -->
						<li
							v-for="(item, index) in list"
							:key="index"
							class="item-card"
							@click="jumpPage(`/company-detail?id=${item.id}`)"
						>
							<div class="item-index">{{ index + 1 }}</div>
							<div class="item-info">
								<p class="name">{{ item.corpName }}</p>
								<p class="address">{{ item.contactAddress }}</p>
								<p class="item-contact">
									<span>{{ item.linkMan }}</span>
									<span class="tel">{{ item.linkPhone }}</span>
								</p>
							</div>
							<!-- <img class="item-img" :src="item.logo" alt="" /> -->
							<img v-if="item.logo" class="item-img" :src="getImgUrl(item.logo)" alt="" />
							<img
								v-else
								class="item-img"
								:src="require('@/assets/employment-images/com-defalut-img.jpg')"
								alt=""
							/>
						</li>
					</ul>
					<div v-else class="empty">
						<img class="img" :src="require('@/assets/employment-images/no-data.png')" alt="" />
						<span class="text">暂无实习网点数据</span>
						<span class="desc">您搜索的区域暂无实习网点数</span>
						<span class="desc">请查询其它区域网点</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
// import initMap from '../map';
export default {
	components: {
		subBreadcrumb
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			keyword: '',
			pageNum: 1,
			value: '',
			options: [],
			loading: false,
			list: [],
			regionOptions: [],
			cityOptions: [],
			cityValue: '',
			ereaOptions: [],
			ereaValue: '',
			regionId: '',
			loadingFlag: true //是否可以滚动
			// https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json
			// jsonUrl: 'https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000_full'
		};
	},
	mounted() {
		this.getRegion();
		this.handlerGetRegion();
		this.getQueryPractice();
		// initMap();
	},
	methods: {
		getRegion() {
			let param = {
				level: 1
			};
			this.$api.employment_api
				.getRegion(param)
				.then(res => {
					const { results } = res;
					// 获取格式化数据
					this.regionOptions = this.handleInitRegionData(results, 100000000000);
					// this.list = res?.results?.records || [];
					// this.total = res?.results?.total || 0;
					// this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 获取地区信息
		 */
		async handlerGetRegion(code) {
			await this.$api.personal_api.getRegion().then(res => {
				const { results } = res;
				// 获取格式化数据
				this.regionOptions = this.handleInitRegionData(results, 100000000000);
				// this.filterList[code].typeList = this.filterList[code].typeList.concat(this.regionOptions || [])
			});
		},
		// 格式化行政区域数据格式
		handleInitRegionData(data, parentId) {
			const regTree = [];
			data.forEach(reg => {
				if (reg.parentId == parentId) {
					const regObject = {
						value: reg.id,
						label: reg.name
					};
					const childrenData = this.handleInitRegionData(data, reg.id);
					if (childrenData.length > 0) {
						regObject.children = childrenData;
					}
					regTree.push(regObject);
				}
			});
			return regTree;
		},
		regionChange(item) {
			this.regionId = item.value;
			this.cityOptions = item.children || [];
			this.cityValue = '';
		},
		cityChange(item) {
			this.regionId = item.value;
			this.ereaOptions = item.children || [];
			this.ereaValue = '';
		},
		ereaChange(item) {
			this.ereaValue = item;
			this.regionId = this.ereaValue;
		},
		/**
		 * @descrtiption 查询实习网点查询
		 * */
		getQueryPractice() {
			this.loading = true;
			let param = {
				pageNum: this.pageNum,
				pageSize: 10,
				keyword: this.keyword, //关键字
				regionId: this.regionId //企业所属地区
			};
			this.$api.employment_api
				.queryPractice(param)
				.then(res => {
					this.list = this.list.concat(res?.results?.records || []);
					this.total = res?.results?.total || 0;
					if (this.pageNum >= res.results.pages) {
						this.loadingFlag = false;
					}
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 关键字搜索事件
		 */
		search() {
			this.pageNum = 1;
			this.list = [];
			this.getQueryPractice();
		},
		/**
		 * @description 关键字重置事件
		 */
		reset() {
			this.keyword = '';
			this.value = '';
			this.cityValue = '';
			this.ereaValue = '';
			this.regionId = '';
			this.cityOptions = [];
			this.ereaOptions = [];
			this.search();
		},
		load() {
			if (!this.loadingFlag) {
				return;
			}
			this.pageNum++;
			this.getQueryPractice();
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
		},
		findValue1(ele, group) {
			if (group.length == 0) return;
			let result = group.indexOf(ele) == '-1' ? false : true;
			return result;
		},
		/**
		 * @description 类型点击事件
		 */
		typeClick(item, group) {
			if (group.indexOf(item) != '-1') return;
			group.push(item);
		},
		/**
		 * @description 类型删除事件
		 */
		deleteValue(item, group) {
			// this.form.courseClassId = '';
			let index;
			for (let i = 0; i < group.length; i++) {
				if (item == group[i]) {
					index = i;
					break;
				}
			}
			group.splice(index, 1);
			// this.lessonListFn();
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
		// async initMap1() {
		// 	let jsonData = await (await fetch(jsonUrl)).json();
		// 	let map = new dt.Group();
		// 	if (type && type === 'world') {
		// 		jsonData.features = jsonData.features.filter(ele => ele.properties.name === 'China');
		// 	}
		// 	jsonData.features.forEach((elem, index) => {
		// 		if (filter && filter(elem) == false) {
		// 			return;
		// 		}
		// 		if (!elem.properties.name) {
		// 			return;
		// 		}
		// 		// 定一个省份3D对象
		// 		const province = new dt.Group();
		// 		// 每个的 坐标 数组
		// 		const coordinates = elem.geometry.coordinates;
		// 		const color = COLOR_ARR[index % COLOR_ARR.length];
		// 		// 循环坐标数组
		// 		coordinates.forEach((multiPolygon, index) => {
		// 			if (elem.properties.name == '海南省' && index > 0) {
		// 				return;
		// 			}
		// 			if (elem.properties.name == '台湾省' && index > 0) {
		// 				return;
		// 			}
		// 			if (elem.properties.name == '广东省' && index > 0) {
		// 				return;
		// 			}
		// 			multiPolygon.forEach(polygon => {
		// 				const shape = new dt.Shape();

		// 				let positions = [];
		// 				for (let i = 0; i < polygon.length; i++) {
		// 					let [x, y] = projection(polygon[i]);

		// 					if (i === 0) {
		// 						shape.moveTo(x, -y);
		// 					}
		// 					shape.lineTo(x, -y);

		// 					positions.push(x, -y, 4);
		// 				}

		// 				const lineMaterial = new dt.LineBasicMaterial({
		// 					color: 'white'
		// 				});
		// 				const lineGeometry = new dt.LineXGeometry();
		// 				// let attribute = new dt.BufferAttribute(new Float32Array(positions), 3);
		// 				// lineGeometry.setAttribute("position", attribute);
		// 				lineGeometry.setPositions(positions);

		// 				const extrudeSettings = {
		// 					depth: 4,
		// 					bevelEnabled: false,
		// 					bevelSegments: 5,
		// 					bevelThickness: 0.1
		// 				};

		// 				const geometry = new dt.ExtrudeGeometry(shape, extrudeSettings);
		// 				// console.log("geometyr", geometry);
		// 				const material = new dt.StandardMaterial({
		// 					metalness: 1,
		// 					// color: color,
		// 					map: texture,
		// 					transparent: true
		// 				});

		// 				let material1 = new dt.StandardMaterial({
		// 					// polygonOffset: true,
		// 					// polygonOffsetFactor: 1,
		// 					// polygonOffsetUnits: 1,
		// 					metalness: 1,
		// 					roughness: 1,
		// 					color: color //"#3abcbd",
		// 				});

		// 				material1 = createSideShaderMaterial(material1);

		// 				const mesh = new dt.Mesh(geometry, [material, material1]);
		// 				if (index % 2 === 0) {
		// 					// mesh.scale.set(1, 1, 1.2);
		// 				}

		// 				mesh.castShadow = true;
		// 				mesh.receiveShadow = true;
		// 				mesh._color = color;
		// 				mesh.properties = elem.properties;
		// 				if (!type) {
		// 					province.add(mesh);
		// 				}

		// 				const matLine = new dt.LineXMaterial({
		// 					polygonOffset: true,
		// 					polygonOffsetFactor: -1,
		// 					polygonOffsetUnits: -1,
		// 					color: type === 'world' ? '#00BBF4' : 0xffffff,
		// 					linewidth: type === 'world' ? 3.0 : 0.25, // in pixels
		// 					vertexColors: false,
		// 					dashed: false
		// 				});
		// 				matLine.resolution.set(graph.width, graph.height);
		// 				line = new dt.LineX(lineGeometry, matLine);
		// 				line.computeLineDistances();
		// 				province.add(line);
		// 			});
		// 		});
		// 		province.properties = elem.properties;
		// 		if (elem.properties.centorid) {
		// 			const [x, y] = projection(elem.properties.centorid);
		// 			province.properties._centroid = [x, y];
		// 		}
		// 		map.add(province);
		// 	});
		// }
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.main {
	width: $max-width;
	margin: 30px auto 60px;
	display: flex;
	justify-content: space-between;
}
.map {
	// width: 100%;
	width: 784px;
	.map-img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}
.right-box {
	width: 440px;
	height: 780px;
	background: #ffffff;
	border-radius: 4px;
	margin-left: 20px;
	flex-shrink: 0;
	.filter-box {
		height: 96px;
		padding: 0 20px;
		border-bottom: 1px solid #e8eaf0;
		.select-box {
			display: flex;
			justify-content: space-between;
			margin-top: 20px;
		}
		.select-item {
			width: 120px;
			height: 28px;
			background: #ffffff;
			border: 1px solid #dcdfe6;
			border-radius: 4px;
		}
		.input-icon {
			width: 238px;
			height: 28px;
			background: #ffffff;
			border: 1px solid #dcdfe6;
			border-radius: 4px;
		}
		::v-deep.el-input-group__prepend {
			border: none;
			background: transparent;
			padding: 0 8px;
		}
		::v-deep.el-input__inner {
			border: none;
			padding-left: 0;
			height: 28px;
			line-height: 28px;
		}
		.btn {
			width: 70px;
			height: 28px;
			border-radius: 4px;
			border: none;
			margin-left: 10px;
		}
		.searrch-btn {
			background: #409eff;
		}
		.reset-btn {
			border: 1px solid #dcdfe6;
		}
	}
	.list-box {
		width: 100%;
		height: calc(100% - 166px);
		padding: 16px 20px;
		overflow: auto;
		.item-card {
			width: 399px;
			padding: 20px;
			border-radius: 4px;
			display: flex;
			cursor: pointer;
			&:hover {
				background: #ebecf0;
			}
			.item-index {
				flex-shrink: 0;
				background: #0076e8;
				width: 20px;
				height: 20px;
				font-size: 12px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #ffffff;
				border-radius: 50%;
				text-align: center;
				line-height: 20px;
			}
			.item-info {
				width: 230px;
				margin-left: 10px;
				.name {
					font-size: 16px;
					line-height: 16px;
					color: #0076e8;
				}
				.address {
					font-size: 14px;
					color: #666666;
					margin-top: 10px;
					word-break: break-all;
					// display: inline-block;
				}
				.item-contact {
					display: inline-block;
					font-size: 14px;
					color: #666666;
					margin-top: 10px;
				}
				.tel {
					margin-left: 10px;
				}
			}
			.item-img {
				width: 76px;
				height: 76px;
				background: #ffffff;
				border-radius: 5px;
				padding: 14px;
				margin-left: 24px;
				flex-shrink: 0;
				object-fit: contain;
			}
		}
	}
	.empty {
		width: 100%;
		height: calc(100% - 166px);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		.img {
			width: 82px;
			height: 86px;
		}
		.text {
			font-size: 18px;
			color: #0076e8;
			margin-top: 30px;
		}
		.desc {
			font-size: 14px;
			color: #b0bbd4;
			margin-top: 16px;
		}
	}
}
</style>
