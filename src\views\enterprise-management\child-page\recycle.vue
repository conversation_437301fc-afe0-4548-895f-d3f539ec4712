<template>
	<div class="main">
		<!-- 简历回收站 -->
		<h3 class="form-title">简历回收站</h3>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="post.name" label="职位名称"></el-table-column>
			<el-table-column prop="user.username" label="姓名"></el-table-column>
			<el-table-column prop="createTime" label="投递时间"></el-table-column>
			<el-table-column fixed="right" label="操作" width="160">
				<template slot-scope="scope">
					<el-button type="text" size="small" class="del-btn" @click="handleDelete(scope.row)">
						删除
					</el-button>
					<el-button type="text" size="small" @click="handleRecover(scope.row)">恢复</el-button>
					<el-button type="text" size="small" @click="biographicalView(scope.row)">
						查看简历
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList"
		/>
		<!-- 查看简历弹窗 -->
		<biographical-dialog ref="biographicalDialog" />
	</div>
</template>

<script>
import biographicalDialog from './components/biographical.vue';
export default {
	name: 'Recycle',
	components: {
		biographicalDialog // 简历弹窗内容
	},
	data() {
		return {
			list: [],
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getPostResumeList({
					isView: 2,
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},

		// 恢复
		handleRecover(row) {
			this.$confirm('确认恢复简历？恢复之后简历将回到收到的简历列表')
				.then(_ => {
					this.$api.enterprise_center.recoverPostResume({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		// 彻底删除
		handleDelete(row) {
			this.$confirm('确认删除简历？此操作将会彻底删除该简历')
				.then(_ => {
					this.$api.enterprise_center.delPostResume({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},

		// 查看简历
		biographicalView(row) {
			this.$refs.biographicalDialog.show(row);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
</style>
