<template>
	<div class="tool">
		<div v-for="(item, index) of showLost" :key="index" class="tool-item">
			<div class="tool-item-title">
				<div class="line"></div>
				<div class="title">{{ item.name }}</div>
			</div>
			<!-- eslint-disable-next-line vue/no-v-html -->
			<div v-if="item.contentType === 'html'" class="tool-item-con" v-html="item.value"></div>
			<div v-else-if="item.contentType === 'textarea'" class="tool-item-con">
				<div v-for="(tag, i) of item.value.split(',')" :key="i * 999" class="tag">
					{{ tag }}
				</div>
			</div>
			<div v-else class="tool-item-con">{{ item.value }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Tool',
	props: {
		info: {
			type: Object,
			default: () => {
				return { attrs: [] };
			}
		}
	},
	data() {
		return {
			list: {
				policy: { contentType: 'html', sort: 1 },
				phone: { contentType: 'text', sort: 2 },
				jiudiansheshi: { contentType: 'text', sort: 3 },
				tesefuwu: { contentType: 'textarea', sort: 4 }
			},
			showLost: []
		};
	},
	created() {
		let list = Object.keys(this.list);
		this.showLost = this.info.attrs.filter(item => {
			if (list.includes(item.code)) {
				Object.assign(item, this.list[item.code]);
				return true;
			} else {
				return false;
			}
		});
		this.showLost.sort((a, b) => {
			return a.sort - b.sort;
		});
	}
};
</script>

<style scoped lang="scss">
.tool {
	padding: 39px;
	&-item {
		margin-bottom: 30px;
		&-title {
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			.line {
				width: 6px;
				height: 20px;
				background: var(--brand-6, #0076e8);
				border-radius: 4px;
				margin-right: 7px;
			}
			.title {
				font-size: 16px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
			}
		}
		&-con {
			font-size: 16px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #404040;
			line-height: 28px;
			display: flex;
			align-items: center;
			.tag {
				font-size: 16px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #262626;
				line-height: 28px;
				margin-right: 20px;
			}
		}
	}
}
</style>
