<template>
	<div class="slider">
		<div
			v-for="(item, index) of showList"
			:key="index"
			class="slider-item"
			@click="handleEvent(item.type)"
		>
			<el-badge v-if="item.type === 'shop'" :value="totalNum" class="item">
				<img
					:src="item.icon"
					alt=""
					class="slider-item-img"
					:style="{ width: item.width, height: item.height }"
				/>
			</el-badge>
			<img
				v-else
				:src="item.icon"
				alt=""
				class="slider-item-img"
				:style="{ width: item.width, height: item.height }"
			/>
			<div class="slider-item-text">{{ item.name }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Index',
	props: {
		noType: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			list: [
				{
					icon: require('@/assets/images/layout/sendMsg.png'),
					name: '消息',
					width: '30px',
					height: '30px',
					type: 'msg'
				},
				// {
				// 	icon: require('@/assets/images/layout/toShopping.png'),
				// 	name: '购物车',
				// 	width: '20px',
				// 	height: '20px',
				// 	type: 'shop'
				// },
				{
					icon: require('@/assets/images/layout/toTop.png'),
					name: '回顶部',
					width: '20px',
					height: '20px',
					type: 'top'
				}
			],
			userId: '',
			totalNum: 0
		};
	},
	computed: {
		showList() {
			return this.list.filter(item => {
				return !this.noType.includes(item.type);
			});
		}
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getShopData();
		}
	},
	methods: {
		/**获取购物车数量*/
		getShopData() {
			let data = {
				rentId: this.getSiteId(),
				memberId: this.userId,
				offset: 0,
				psize: 10
			};
			this.$api.shop_api.getShoppingCar(data).then(res => {
				this.totalNum = res.totalNum || 0;
			});
		},
		/**处理按钮事件*/
		handleEvent(type) {
			if (type === 'top') {
				window.scrollTo(0, 0);
			} else if (type === 'shop') {
				this.$router.push('/shoppingCart');
			} else {
				this.$emit('openKeFu');
			}
		}
	}
};
</script>

<style scoped lang="scss">
.slider {
	background: #ffffff;
	border-radius: 24px 0px 0px 24px;
	padding: 30px 20px 0;
	position: fixed;
	right: 0;
	top: 60%;
	&-item {
		margin-bottom: 30px;
		display: flex;
		flex-direction: column;
		align-items: center;
		cursor: pointer;
		&-img {
			margin-bottom: 8px;
		}
		&-text {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			text-align: center;
		}
	}
}
</style>
