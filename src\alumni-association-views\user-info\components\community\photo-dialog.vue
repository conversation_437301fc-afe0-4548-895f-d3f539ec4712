<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 创建/编辑相册弹窗
 @author: WH
 @date: 2023/9/13
 -->
<template>
	<el-dialog title="创建/编辑相册" :visible.sync="visible" :before-close="beforeClose" @open="init">
		<el-form ref="form" v-loading="loading" :rules="rules" :model="form" label-width="140px">
			<el-form-item label="相册名称" prop="albumName">
				<el-input v-model="form.albumName"></el-input>
			</el-form-item>
			<el-form-item label="相册用途" prop="introduce">
				<el-input v-model="form.introduce"></el-input>
			</el-form-item>
			<el-form-item label="相册封面" prop="coverImg">
				<uploadItem
					class="avatar-uploader"
					:file-number="1"
					:code="dataCode"
					:own-id="uploadId"
					@fileChange="fileChange"
				/>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" @click="submit">{{ photoId ? '修 改' : '保 存' }}</el-button>
			<el-button @click="beforeClose">取 消</el-button>
		</div>
	</el-dialog>
</template>

<script>
import uploadItem from '../uploadItem.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
	components: { uploadItem },

	props: {
		photoId: {
			type: [String, null],
			required: true
		},
		communityId: {
			type: String,
			required: true
		},
		visible: {
			type: Boolean,
			required: true
		}
	},
	data() {
		return {
			loading: false,
			dataCode: 'alumna_community_album_cover',
			uploadId: null,
			form: {},
			rules: {
				albumName: [{ required: true, message: '请输入相册名称', trigger: 'blur' }],
				introduce: [{ required: true, message: '请输入相册用途', trigger: 'blur' }]
			}
		};
	},

	methods: {
		init() {
			if (this.photoId) {
				this.getPhotoInfo();
				this.uploadId = this.photoId;
				return;
			}
			this.uploadId = uuidv4();
		},
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					this.file = file;
					this.form.coverImg = file[0]?.adjunctId;
					// this.$set(this.formData, 'communityLogo', file.adjunctId);
					// this.$refs.form.validateField('communityLogo');
				});
			}
		},
		submit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.photoId ? this.updatePhoto() : this.addPhotos();
				}
			});
		},
		beforeClose() {
			this.$emit('update:visible', false);
		},
		//新增社区相册——————————————————————————————
		async addPhotos() {
			this.loading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.addPhotos({
					...this.form,
					id: this.uploadId,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.$message.success(msg);
					this.formData = {};
					this.beforeClose();
					this.$emit('crudPhoto', 'add');
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//修改社区相册——————————————————————————————
		async updatePhoto(data) {
			this.loading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.updatePhoto(this.form);
				if (rCode == 0) {
					this.$message.success(msg);
					this.formData = {};
					this.beforeClose();
					this.$emit('crudPhoto', 'update');
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//查询详情社区相册——————————————————————————————
		async getPhotoInfo() {
			this.loading = true;
			try {
				let { rCode, results } = await this.$api.alumni_api.getPhotoInfo(this.photoId);
				if (rCode == 0) {
					this.form = results;
					this.uploadId = results.id;
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
ul {
	@include flexBox(space-evenly);
	width: 100%;
}
li {
	text-align: center;
	.img {
		width: 48px;
		height: 48px;
		padding: 6px;
		margin-bottom: 10px;
		background: #f3f5f7;
		img {
			width: 100%;
			height: 100%;
		}
	}
	p {
		font-size: 14px;
		color: #7a8392;
	}
}
</style>
