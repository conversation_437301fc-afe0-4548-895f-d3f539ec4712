<template>
	<div class="good-stuff">
		<div class="top">
			<img src="@/assets/shop-images/Frame-1.png" alt="" />
			<span>精选好物</span>
		</div>
		<div class="list">
			<div v-for="(item, index) of shopList" :key="index" class="item">
				<a href="javascript:void(0)" @click="goLink(item)">
					<img class="item-img" :src="$judgeFile(item.coverUrl)" alt="" />
					<!-- <div>{{ item.productName }}</div> -->
					<div class="item-nav">
						<div class="left">
							<div class="index">{{ index + 1 }}</div>
							<div class="num">热销{{ item.selledNum }}件</div>
						</div>
						<div class="right">¥{{ item.sellPrice.toFixed(2) }}</div>
					</div>
				</a>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodStuff',
	props: {
		shopList: {
			type: Array,
			default: () => []
		}
	},
	methods: {
		goLink(t) {
			this.$router.push({
				path: '/goodsList/details',
				query: {
					id: t.productId
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.good-stuff {
	background: #ffffff;
	height: 100%;
	.top {
		width: 290px;
		height: 56px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		padding: 8px 19px;
		background: url('../../../assets/shop-images/Rectangle-29.png') no-repeat;
		display: flex;
		align-items: center;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #404040;
		line-height: 24px;
		img {
			margin-right: 5px;
			object-fit: cover;
		}
	}
	.list {
		padding: 20px 14px;
		margin-bottom: 60px;
		.item {
			margin-bottom: 20px;
			&-img {
				width: 261px;
				height: 261px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				object-fit: cover;
			}
			.item-nav {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 14px;
				.left {
					display: flex;
					.index {
						width: 20px;
						height: 20px;
						background: #cf5336;
						opacity: 1;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #ffffff;
						line-height: 22px;
						margin-right: 7px;
					}
					.num {
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #404040;
						line-height: 22px;
					}
				}
				.right {
					font-size: 24px;
					font-family: Rany-Medium, Rany;
					font-weight: 500;
					color: var(--brand-6, '#ca3f3b');
					line-height: 32px;
				}
			}
		}
	}
}
</style>
