<template>
	<el-checkbox-group v-model="valueInput" :disabled="disabled" @change="change">
		<el-checkbox v-for="(option, index) in optionsC" :key="index" :label="option[props.key]">
			{{ option[props.label] }}
		</el-checkbox>
	</el-checkbox-group>
</template>

<script>
export default {
	name: 'FormSelect',
	props: {
		value: {
			type: [String, Number, Boolean, Object, Array, Function],
			default: null
		},
		options: {
			type: [Array, Object],
			default: () => []
		},
		props: {
			type: Object,
			default: () => ({ key: 'key', label: 'label' })
		},
		change: {
			type: Function,
			default: function () {}
		},
		// 复选框禁用
		disabled: {
			type: Boolean,
			default: false
		},
		// 键名类型
		keyType: {
			type: Function,
			default: key => Number(key)
		}
	},
	computed: {
		valueInput: {
			get() {
				return this.value;
			},
			set(value) {
				this.$emit('input', value);
			}
		},
		// 键值对类型枚举转换为数组
		optionsC() {
			if (!this.options) return [];
			if (Array.isArray(this.options)) {
				return this.options;
			} else {
				return Object.keys(this.options).map(key => {
					return {
						key: this.keyType(key),
						label: this.options[key]
					};
				});
			}
		}
	}
};
</script>
