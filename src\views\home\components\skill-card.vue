<!--
 @desc:推荐技能大卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<el-image class="img" :src="$judgeFile(cardData.coverImg)" lazy fit="cover"></el-image>
		<div class="content-box">
			<span class="over1">{{ cardData.title }}</span>
			<p>{{ cardData.content }}</p>
			<footer>
				<span>{{ cardData.name }}</span>
				<span>{{ cardData.time }}</span>
			</footer>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					imgUrl: '',
					title: '标题',
					content: '内容区域内容区域内容区域内容区域内容区域内容区域',
					name: '导师姓名',
					time: '09月01日 08:00'
				};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'skill', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 300px;
	height: 300px;
	border-radius: 8px;
	margin-bottom: 20px;
	font-family: Microsoft YaHei;
	cursor: pointer;
	background: #ffffff;
	.img {
		width: 300px;
		height: 200px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		object-fit: cover;
	}
	.content-box {
		@include flexBox(space-between, flex-start);
		flex-direction: column;
		padding: 20px;
		height: calc(100% - 200px);
		& > span {
			font-size: 14px;
			font-weight: 400;
			color: #333333;
			display: inline-block;
			width: 100%;
		}
		& > p {
			font-size: 12px;
			font-weight: 400;
			color: #7a8392;
			width: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		footer {
			@include flexBox(space-between);
			width: 100%;
			span {
				font-size: 12px;
				font-weight: 400;
				color: #999999;
			}
		}
	}
}
</style>
