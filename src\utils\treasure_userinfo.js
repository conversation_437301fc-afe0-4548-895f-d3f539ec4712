import { getCookie } from '@/utils/auth';
import { getUserInfo } from '@/api/model/skill-treasure';
import { loginFrontByCode } from '@/api/model/online-study';
import { checkLogin, getEossAuthentication } from '@/api/user';

// 大预订登录信息
export async function getTreasureUserInfo() {
	let params = {
		userId: getCookie('user_id')
	};
	let res = await getUserInfo(params);
	console.log('getTreasureUserInfo', res);
	if (res.state) {
		localStorage.setItem('dydUserInfo', JSON.stringify(res.result || {}));
	}
}
export async function getLoginFrontByCode(code) {
	let checkRes = await checkLogin();
	if (checkRes.rCode === 0) {
		let params = {
			code: checkRes?.results?.code || ''
		};
		// results.authCode

		// 在线学校登陆
		let res = await loginFrontByCode(params);
		console.log('getLoginFrontByCode', res);
		if (res.code == 200) {
			localStorage.setItem('lessonToken', res.data.access_token || '');
		}

		// await getTreasureUserInfo();
	} else {
		// this.$message.error(checkRes.msg);
	}
}

// 管理端权限登录
export async function getLoginAdmini(code) {
	let checkRes = await checkLogin();
	if (checkRes.rCode === 0) {
		// 管理端权限登录
		let code = checkRes?.results?.code || '';
		localStorage.setItem('ssoCode', code);
		let data = {
			serverId: 'ybzyDtcSso',
			authType: '6',
			code
		};
		console.log('getLoginAdmini', data);
		getEossAuthentication(data).then(res => {
			localStorage.setItem('mobileToken', res.results.mobileToken);
			localStorage.setItem('mobileUserId', res.results.userId);
			console.log('getEossAuthentication', res);
		});
		// .catch(err => {
		// 	console.log(err, '认证失败err');
		// 	return '认证失败';
		// });
	} else {
		// this.$message.error(checkRes.msg);
	}
}
