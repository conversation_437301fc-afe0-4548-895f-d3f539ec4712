<template>
	<div class="logistics-info">
		<div class="logistics-info-header">
			<div class="icon"></div>
			<div class="title">支付方式</div>
		</div>
		<div class="logistics-info-offline">
			<el-form label-width="100px" :model="formLabelAlign">
				<el-form-item label="支付方式：">
					<div class="distribution">
						<!-- v-if="type == 1" -->
						<!-- <div
							:class="[
								'distribution-mode',
								formLabelAlign.distributionType == 1 ? 'distribution-active' : ''
							]"
							@click="distributionClick(1)"
						>
							<span>在线支付</span>
							<div v-show="formLabelAlign.distributionType == 1" class="img">
								<img src="@/assets/shop-images/check.png" alt="" />
							</div>
						</div> -->
						<!-- v-if="type == 2" -->
						<div
							:class="[
								'distribution-mode',
								formLabelAlign.distributionType == 2 ? 'distribution-active' : ''
							]"
							@click="distributionClick(2)"
						>
							<span>线下支付</span>
							<div v-show="formLabelAlign.distributionType == 2" class="img"></div>
							<img class="check-img" src="@/assets/shop-images/check.png" alt="" />
						</div>
						<div
							v-if="value == 2"
							:class="[
								'distribution-mode',
								formLabelAlign.distributionType == 3 ? 'distribution-active' : ''
							]"
							@click="distributionClick(3)"
						>
							<span>申请融资</span>
							<div v-show="formLabelAlign.distributionType == 3" class="img"></div>
							<img class="check-img" src="@/assets/shop-images/check.png" alt="" />
						</div>
					</div>
				</el-form-item>
				<div v-if="formLabelAlign.distributionType == 3">
					<div class="details">
						<div class="conten">
							<div class="conten-list">
								<div
									v-for="item of financingList"
									:key="item.id"
									:class="[
										'conten-list-item',
										financingActive.id == item.id ? 'financing_actove' : ''
									]"
									@click="financingClick(item)"
								>
									<div v-show="financingActive.id == item.id" class="bg-img">
										<img class="bg-img_img" src="@/assets/shop-images/check.png" alt="" />
									</div>
									<div class="conten-list-item-header">
										<div class="logo">
											<img
												:src="`${config.baseUrl}/scswl/common/docking/front/loadFile?path=/plat/scswl/mecpfileManagement/front/previewAdjunct.json&adjunctId=${item.lenderLogo.adjunctId}`"
												class="img"
												alt=""
											/>
										</div>
										<div class="header-title">{{ item.productName }}</div>
										<div class="header-flex">
											<span class="lable">参考年利率:</span>
											<span class="interest">
												{{ parseInt(item.interestRateMin * 100) }}%-{{
													parseInt(item.interestRateMax * 100)
												}}%
											</span>
										</div>
										<div class="header-flex">
											<span class="lable">融资额度:</span>
											<span class="text">
												{{ getLoadLimitMin(item.loadLimitMin) }}-{{
													getLoadLimitMax(item.loadLimitMax)
												}}万
											</span>
										</div>
										<div class="header-flex">
											<span class="lable">融资期限:</span>
											<span class="text">
												{{ item.loanPeriodMin }}-{{ item.loanPeriodMax }}个月
											</span>
										</div>
									</div>
									<div class="conten-list-item-button">
										<a href="javascript:void(0)">
											<div class="button" @click="financingDetails(item)">查看详情 ></div>
										</a>
									</div>
								</div>
								<Empty v-show="financingList.length == 0" :tips="'暂无融资信息'" />
							</div>
							<div>
								<Pagination
									:current-page="pageData.page"
									:total="pageData.total"
									:layout="' prev,  pager, next'"
									:page-size="pageData.limit"
									@paginationChange="handlePageChange"
								></Pagination>
							</div>
						</div>
						<!-- <el-button type="primary" @click="activeClick">确定</el-button> -->
						<div class="flow-img">
							<img src="@/assets/shop-images/Group-1313.png" alt="" />
						</div>
					</div>

					<!-- 已选融资 -->
					<!-- <div v-if="active.id" class="financing—active">
						<div class="financing—active-item">
							<div class="financing—active-item-lable">已选：</div>
							<div class="financing—active-item-logo">
								<img
									:src="`${config.baseUrl}/scswl/common/docking/front/loadFile?path=/plat/scswl/mecpfileManagement/front/previewAdjunct.json&adjunctId=${active.lenderLogo.adjunctId}`"
									class="img"
									alt=""
								/>
							</div>
						</div>
						<el-button @click="setFinancingActive">点击更换</el-button>
					</div> -->
				</div>
			</el-form>
		</div>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';
import config from '@/config';
import Pagination from '@/components/public/Pagination';

export default {
	name: 'LogisticsInfo',
	components: { Pagination },
	// components: { financingList, financingForm, FinancingDetails },
	data() {
		return {
			config,
			formLabelAlign: {
				distributionType: 2
			},
			form: {},
			checked: false,
			type: 1,
			value: 0,
			dialogVisible: false,
			financingList: [],
			financingActive: {},
			active: {},
			// 分页数据
			pageData: {
				page: 1,
				totalPage: 0,
				total: 0,
				limit: 12
			},
			enterpriseId: getCookie('enterpriseId')
		};
	},
	created() {
		this.type = this.$route.query.type;
		this.value = this.$route.query.value;

		// this.formLabelAlign.distributionType = this.type || 1;
	},
	methods: {
		financingDetails(t) {
			window.open(
				`${config.domainUrl}${config.appList.financeService}/#/financial/detail?id=${t.id}&subject=1`
			);
		},
		// 分页事件
		handlePageChange(e) {
			if (e.type === 1) {
				this.pageData.limit = e.value;
				this.pageData.page = 1;
			} else {
				this.pageData.page = e.value;
			}
			this.getList();
		},
		getList() {
			// enterpriseId企业ID、productType商品分类、productName商品名称、loadMoney订单金额
			let data = {
				path: '/plat/scswl/order/openapi/productList',
				labelCode: 'e-commerce-recommend',
				pageNum: this.pageData.page,
				pageSize: this.pageData.limit,
				enterpriseId: this.enterpriseId
				// productType:
				// 	this.$parent.$refs?.settleAccountsGoodsInfo?.info?.baseInfo?.CATEGORY_ID || '',
				// productName:
				// 	this.$parent.$refs?.settleAccountsGoodsInfo?.info?.baseInfo?.PRODUCT_NAME || '',
			};
			let loanPeriod =
				this.$parent.distributionModeType == 2
					? this.$parent.preferentialPrice
					: this.$parent.totalPrices;
			if (loanPeriod && !isNaN(loanPeriod)) {
				data.loanPeriod = loanPeriod;
			}
			this.$api.shop_api
				.doPostForm(data)
				.then(res => {
					this.financingList = res?.results?.records || [];
					this.pageData.total = res?.results?.total || 0;
					if (this.financingList.length) {
						this.financingList = this.financingList.map(item => {
							return {
								...item,
								lenderLogo: JSON.parse(item.lenderLogo) || {}
							};
						});
						this.financingActive = this.financingList[0];
					}
					this.$parent.loading = false;
				})
				.catch(err => {
					this.$parent.loading = false;
				});
		},
		distributionClick(item) {
			this.formLabelAlign.distributionType = item;
			if (item == 3) {
				this.$parent.loading = true;
				this.getList();
			}
		},
		financingClick(item) {
			this.financingActive = item;
		},
		activeClick() {
			this.active = this.financingActive;
		},
		setFinancingActive() {
			this.financingActive = this.active;
			this.active = {};
		},
		getLoadLimitMin(item) {
			return item == 0 ? item : item / 10000;
		},
		getLoadLimitMax(item) {
			return item == 0 ? item : item / 10000;
		},
		handleClose(done) {
			this.$confirm('确认关闭？')
				.then(_ => {
					done();
				})
				.catch(_ => {});
		}
	}
};
</script>
<style lang="scss" scoped>
.logistics-info {
	background-color: #fff;
	margin-top: 16px;
	padding: 16px 20px;
	&-header {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-offline {
		margin-top: 15px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		padding: 16px 0px;
		.distribution {
			display: flex;
		}
		.distribution-mode {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid #d9d9d9;
			padding: 8px 18px;
			margin-right: 10px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 22px;
			overflow: hidden;
			position: relative;
			.check-img {
				position: absolute;
				bottom: 0px;
				right: 0px;
			}
			.img {
				position: absolute;
				height: 27px;
				width: 15px;
				bottom: -10px;
				right: -3px;
				background: var(--brand-6, '#ca3f3b');
				-moz-transform: rotate(45deg);
				-webkit-transform: rotate(45deg);
				-o-transform: rotate(45deg);
				transform: rotate(45deg);
			}
		}
		.distribution-active {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			color: var(--brand-6, '#ca3f3b');
		}
		.site {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #262626;
			line-height: 20px;
		}
		.location {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
		}
		.add-location {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 20px;
		}
		.site-list {
			display: flex;

			&-item {
				width: 238px;
				height: 72px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #d9d9d9;
				padding: 0px 20px;
				margin-right: 8px;
				&-nav {
					display: flex;
					align-items: center;
					i {
						color: #8c8c8c;
					}
					&-location {
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
			.site-list-item-active {
				border: 1px solid var(--brand-6, '#ca3f3b');
				::v-deep .el-checkbox__label {
					color: var(--brand-6, '#ca3f3b');
				}
				.site-list-item-nav {
					display: flex;
					align-items: center;
					i {
						color: var(--brand-6, '#ca3f3b');
					}
					.site-list-item-nav-location {
						color: var(--brand-6, '#ca3f3b');
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
		}
	}
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
	background-color: var(--brand-6, '#ca3f3b');
	border-color: var(--brand-6, '#ca3f3b');
}
.details {
	margin-top: 15px;
	width: 100%;
	padding: 0 23px;
	.conten {
		width: 100%;
		background: #f7f8fa;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		margin-bottom: 12px;
		&-list {
			display: flex;
			padding: 16px 0 16px 18px;
			align-items: center;
			min-height: 206px;
			flex-wrap: wrap;
			.financing_actove {
				border: 1px solid var(--brand-6, '#ca3f3b');
				position: relative;
				overflow: hidden;
				.bg-img {
					position: absolute;
					height: 25px;
					width: 16px;
					bottom: -9px;
					right: -4px;
					background: var(--brand-6, '#ca3f3b');
					-moz-transform: rotate(45deg);
					-webkit-transform: rotate(45deg);
					-o-transform: rotate(45deg);
					transform: rotate(45deg);
					.bg-img_img {
						position: absolute;
						top: 6px;
						left: 1px;
						-moz-transform: rotate(-40deg);
						-webkit-transform: rotate(-40deg);
						-o-transform: rotate(-40deg);
						transform: rotate(-40deg);
					}
				}
			}
			&-item {
				text-align: center;
				width: 164px;
				height: 195px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				overflow: hidden;
				margin-right: 18px;
				cursor: pointer;
				position: relative;
				margin-bottom: 16px;
				&:hover {
					a {
						color: var(--brand-6, '#ca3f3b');
					}
				}
				&-button {
					margin-top: 7px;
					border-top: 1px dotted #eeeeee;
					display: flex;
					justify-content: center;
					height: 38px;
					align-items: center;
					.button {
						width: 90px;
						height: 26px;
						background: #ffffff;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
				.header-title {
					width: 139px;
					height: 22px;
					font-size: 14px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #262626;
					line-height: 22px;
					margin: 0 auto 10px;
					overflow: hidden; //超出的文本隐藏
					text-overflow: ellipsis; //溢出用省略号显示
					display: -webkit-box;
					-webkit-line-clamp: 1; // 超出多少行
					-webkit-box-orient: vertical;
				}
				.header-flex {
					display: flex;
					align-items: center;
					justify-content: center;
				}
				.logo {
					width: 100%;
					margin: 9px 0;
					display: flex;
					justify-content: center;
					.img {
						width: 70px;
						height: 30px;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
					}
				}
				.lable {
					height: 18px;
					font-size: 12px;
					font-family: Noto Sans SC-Regular, Noto Sans SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 18px;
					margin-right: 5px;
				}
				.text {
					height: 22px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #262626;
					line-height: 22px;
					margin-top: 2px;
				}
				.interest {
					height: 22px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #f2a665;
					line-height: 22px;
				}
			}
		}
	}
	.flow-img {
		margin-top: 25px;
	}
}
.financing—active {
	margin-top: 15px;
	width: 100%;
	padding: 0 23px;
	&-item {
		width: 100%;
		height: 63px;
		background: #f7f8fa;
		border-radius: 0px 0px 0px 0px;
		display: flex;
		align-items: center;
		opacity: 1;
		border: 1px solid #d9d9d9;
		padding: 0 23px;
		margin-bottom: 12px;
		.img {
			width: 148px;
			height: 44px;
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
		}
		&-lable {
			height: 22px;
			font-size: 14px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #000000;
			line-height: 22px;
		}
	}
}
::v-deep .empty {
	padding: 0px;
}
::v-deep .my-pagination {
	padding: 0px 0px 25px;
	.btn-prev,
	.el-pager,
	.btn-next {
		background-color: transparent;
		li {
			background-color: transparent;
		}
	}
}
</style>
