<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="company-info">
				<!-- <img class="company-img" :src="companyDetail.coverImg" alt="" /> -->
				<img
					class="company-bg"
					:src="getImgUrl(enterprise.coverImg && enterprise.coverImg.split(',')[0])"
					alt=""
				/>
				<div class="company-base">
					<img
						v-if="enterprise.logo"
						class="company-img"
						:src="getImgUrl(enterprise.logo)"
						alt=""
					/>
					<img
						v-else
						class="company-img"
						:src="require('@/assets/employment-images/com-defalut-img.jpg')"
						alt=""
					/>
					<div>
						<span class="name">{{ enterprise.corpName || '-' }}</span>
						<p class="desc">
							<span class="desc-item">
								{{ enterprise.industryField | enterpriseTypeStr(industryArea) }}
							</span>
							<span class="desc-item">
								{{ enterprise.peopleNumCode | enterpriseTypeStr(peopleNumCodeList) }}
							</span>
						</p>
						<p class="labels-box">
							<span v-for="(item, index) in labels" :key="index" class="label">
								{{ item }}
							</span>
						</p>
					</div>
				</div>
				<div class="btns-box">
					<span :class="['btn-item', isFollowedC && 'actvie-btn']" @click="followObjects">
						<!-- <i class="iconfont icon-heart btn-icon"></i> -->
						<img
							v-if="isFollowedC"
							class="btn-icon"
							:src="require('@/assets/employment-images/followed.png')"
							alt=""
						/>
						<img
							v-else
							class="btn-icon"
							:src="require('@/assets/employment-images/follow.png')"
							alt=""
						/>
						关注
					</span>
					<span :class="['btn-item', isCollect && 'actvie-btn']" @click="collect">
						<!-- <i class="el-icon el-icon-star-off btn-icon"></i> -->
						<img
							v-if="isCollect"
							class="btn-icon"
							:src="require('@/assets/employment-images/collected.png')"
							alt=""
						/>
						<img
							v-else
							class="btn-icon"
							:src="require('@/assets/employment-images/collect-icon.png')"
							alt=""
						/>
						收藏
					</span>
					<span class="btn-item" @click="urlCopy">
						<!-- <i class="el-icon el-icon-share btn-icon"></i> -->
						<img src="@/assets/employment-images/share-icon.png" class="btn-icon btn-img" alt="" />
						分享
					</span>
				</div>
			</div>
			<!-- 公司简介 -->
			<div class="company-profile">
				<div class="title">公司简介</div>
				<div class="info-box">
					<!-- eslint-disable-next-line vue/no-v-html -->
					<div class="profile-content" v-html="enterprise.introduction"></div>
					<!-- 图集展示轮播 -->
					<el-carousel
						v-if="enterprise.coverImg"
						:interval="5000"
						arrow="always"
						height="350px"
						class="carousel-box"
					>
						<el-carousel-item v-for="item in enterprise.coverImg.split(',')" :key="item">
							<img :src="getImgUrl(item)" class="profile-img" />
						</el-carousel-item>
					</el-carousel>
					<!-- <img
						class="profile-img"
						:src="getImgUrl(enterprise.coverImg && enterprise.coverImg.split(',')[0])"
						alt=""
					/> -->
				</div>
			</div>
			<!-- 公司简介 -->
			<div v-if="video" class="company-profile">
				<div class="title">宣传视频</div>
				<div class="info-box">
					<video ref="video" class="video-box" controls="controls">
						<!-- @pause="pause" -->
						<!-- @play="play" -->
						<source :src="getImgUrl(video.adjunctId)" type="video/ogg" />
						<source :src="getImgUrl(video.adjunctId)" type="video/mp4" />
						<source :src="getImgUrl(video.adjunctId)" type="video/webm" />
					</video>
				</div>
			</div>
			<!-- 招聘职位 -->
			<div class="posision-box">
				<div class="title">招聘职位</div>
				<ul class="posision-list">
					<li
						v-for="(item, index) in positionList"
						:key="index"
						class="list-item"
						@click="jumpPage(`/job-detail?id=${item.id}`)"
					>
						<div class="title">
							<span class="title-text over1">
								{{ item.name }}
								<img
									v-if="item.isDualSelect"
									class="tag-img"
									:src="require('@/assets/employment-images/tag-samll.png')"
									alt=""
								/>
							</span>
							<span class="price">
								<!-- {{ item.salaryStructure }} -->
								{{ item.minMoney || 0 }}-{{ item.maxMoney || 0 }}k
								<!-- <span v-if="item.payment">/{{ item.payment }}</span> -->
							</span>
						</div>
						<p class="position">{{ item.areaName }}</p>
						<p class="labels-box over1">
							<span v-for="(labelItem, labelIndex) in item.tags" :key="labelIndex" class="label">
								{{ labelItem }}
							</span>
						</p>
					</li>
					<Empty
						v-if="positionList && !positionList.length"
						:tips="'暂无数据'"
						:style-obj="{ padding: '50px 0' }"
					/>
				</ul>
			</div>
			<!-- 工商信息 -->
			<div class="business-box">
				<div class="title">工商信息</div>
				<ul class="business-info">
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/qyqc.png')" alt="" />
							<span>企业全称</span>
						</div>
						<p class="info-text">{{ enterprise.corpName || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/clsj.png')" alt="" />
							<span>成立时间</span>
						</div>
						<p class="info-text">{{ enterprise.establishDate || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/zczb.png')" alt="" />
							<span>注册资本</span>
						</div>
						<p class="info-text">{{ enterprise.registeredCapital + '万元' || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/frdb.png')" alt="" />
							<span>法人代表</span>
						</div>
						<p class="info-text">{{ enterprise.lawPerson || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/zcdz.png')" alt="" />
							<span>注册地址</span>
						</div>
						<p class="info-text">{{ enterprise.registeredAddress || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/xydm.png')" alt="" />
							<span>统一信用代码</span>
						</div>
						<p class="info-text">{{ enterprise.socialCode || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/sshy.png')" alt="" />
							<span>所属行业</span>
						</div>
						<p class="info-text">
							{{ enterprise.corpIndustry | enterpriseTypeStr(corpIndustryList) }}
						</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/szd.png')" alt="" />
							<span>所在地</span>
						</div>
						<p class="info-text">{{ enterprise.regionName || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/djjg.png')" alt="" />
							<span>登记机关</span>
						</div>
						<p class="info-text">{{ enterprise.registerOffice || '-' }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/yyzz.png')" alt="" />
							<span>营业执照</span>
						</div>
						<p v-if="enterprise.validityOfLicense" class="info-text">
							{{ enterprise.validityOfLicense }}
						</p>
						<p v-else class="info-text">
							<span>{{ enterprise.validityStartDate }}</span>
							<span v-if="enterprise.validityEndDate">-{{ enterprise.validityEndDate }}</span>
						</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/qylx.png')" alt="" />
							<span>企业类型</span>
						</div>
						<p class="info-text">{{ enterprise.corpType | enterpriseTypeStr(corpTypeList) }}</p>
					</li>
					<li class="info-item">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/jyzt.png')" alt="" />
							<span>经营状态</span>
						</div>
						<p class="info-text">
							{{ enterprise.managementFormsCode | enterpriseTypeStr(businessStatus) }}
						</p>
					</li>
					<!-- 范围 -->
					<li class="info-item item-block">
						<div class="info-title">
							<img class="item-img" :src="require('@/assets/employment-images/jyfw.png')" alt="" />
							<span>经营范围</span>
						</div>
						<p class="info-text">{{ enterprise.businessScope || '-' }}</p>
					</li>
				</ul>
			</div>
			<!-- 用户评价 -->
			<!-- <div class="commentList-box">
				<div class="title">用户评价</div>
				<ul class="commentList-list">
					<li v-for="(item, index) in commentList" :key="index" class="item-commentList">
						<div class="person-info">
							<img class="commentList-img" :src="item.img" alt="" />
							<span class="commentList-name">{{ item.name }}</span>
						</div>
						<div class="commentList-info">
							<p class="commentList-rate">
								<span>满意度：</span>
								<el-rate v-model="item.score" disabled></el-rate>
							</p>
							<p>
								<span v-for="(labelItem, labelIndex) in item.label" :key="labelIndex" class="label">
									{{ labelItem }}
								</span>
							</p>
							<p class="time">{{ item.time }}</p>
							<p class="content">{{ item.content }}</p>
							<p>
								<span
									v-for="(labelItem, labelIndex) in item.statusLabel"
									:key="labelIndex"
									class="status-label"
								>
									{{ labelItem }}
								</span>
							</p>
						</div>
					</li>
				</ul>
			</div>
			-->
			<!-- 分页 -->
			<!-- <el-pagination
				class="pagination"
				background
				layout="prev, pager, next,jumper"
				:total="1000"
				@current-change="handleCurrentChange"
			></el-pagination> -->
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import { baseUrl } from '@/config';
import { getDictionaryByCode } from '@/utils';
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
import urlCopy from '@/utils/url_copy';

export default {
	components: {
		subBreadcrumb
	},
	filters: {
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value && value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		}
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			baseUrl,
			companyId: '', // 公司id
			isCollect: false, //是否收藏
			isFollowedC: false, //是否关注
			labels: [], //岗位标签，处理之后用于页面渲染
			companyDetail: {},
			enterprise: {}, //
			positionList: [], // 招聘岗位
			peopleNumCodeList: [], //人数规模
			corpIndustryList: [], //所属行业
			industryArea: [], //行业领域
			corpTypeList: [], //企业类型
			businessStatus: [], //经营状态
			video: '', //视频
			commentList: [] //评价列表
		};
	},
	mounted() {
		this.companyId = this.$route.query.id || '';
		this.getDetail();
		this.isCollected();
		this.isFollowed();
		this.findSysCode(); //人数规模 //行业
		this.getFiles('job_co_enterprise_video'); //视频数据
	},
	methods: {
		/**
		 * @description 获取公司详情
		 * */
		getDetail() {
			let param = {
				id: this.companyId
			};
			this.$api.employment_api.getCompanyInfo(param).then(res => {
				this.companyDetail = res.results;
				this.enterprise = this.companyDetail.enterprise;
				this.positionList = this.companyDetail.postList;
				this.commentList = this.companyDetail.commentList;
				this.labels = this.enterprise.tags && this.enterprise.tags.split(',');
			});
		},
		// 获取企业video视频
		getFiles(code) {
			this.$api.personal_api.getFileInfo({ code: code, ownId: this.companyId }).then(async res => {
				this.video = res.results[res.results.length - 1] || '';
			});
		},
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode(code, list) {
			const dicts = await getDictionaryByCode([
				'plat_enp_quality',
				'plat_enp_industry',
				'post_company_scale',
				'post_industry_area',
				'plat_enterprise_management_forms'
			]);
			this.corpTypeList = dicts.plat_enp_quality || [];
			this.peopleNumCodeList = dicts.post_company_scale || [];
			this.corpIndustryList = dicts.plat_enp_industry || [];
			this.industryArea = dicts.post_industry_area || [];
			this.businessStatus = dicts.plat_enterprise_management_forms || [];
		},
		// 判断是否收藏
		isCollected() {
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				id: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.isCollected(param)
				.then(res => {
					if (res.success) {
						this.isCollect = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 收藏
		collect() {
			if (this.isCollect) {
				return this.cancelCollects();
			}
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				collects: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.collect(param)
				.then(res => {
					if (res.success) {
						this.isCollect = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消收藏
		cancelCollects() {
			const param = {
				objectIds: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.cancelCollects(param)
				.then(res => {
					if (res.success) {
						this.isCollect = false;
						this.$message.success(`取消收藏`);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 是否关注公司
		isFollowed() {
			// 收藏内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				id: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.isFollowed(param)
				.then(res => {
					if (res.success) {
						this.isFollowedC = res.results;
					} else {
						// this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 关注公司
		followObjects() {
			if (this.isFollowedC) {
				return this.cancelFollows();
			}
			// 关注内容的类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				follows: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.followObjects(param)
				.then(res => {
					if (res.success) {
						this.isFollowedC = true;
						this.$message.success(res.msg);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		// 取消关注
		cancelFollows() {
			// 类型（0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目）
			const param = {
				objectIds: this.companyId,
				type: '1'
			};
			this.$api.employment_api
				.cancelFollows(param)
				.then(res => {
					if (res.success) {
						this.isFollowedC = false;
						this.$message.success(`取消关注`);
					} else {
						this.$message.error(res.msg);
					}
				})
				.catch(res => {});
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
		},
		/**
		 * @description 类型点击事件
		 */
		typeClick(item, group) {
			if (group.indexOf(item) != '-1') return;
			group.push(item);
		},
		/**
		 * @description 类型删除事件
		 */
		deleteValue(item, group) {
			// this.form.courseClassId = '';
			let index;
			for (let i = 0; i < group.length; i++) {
				if (item == group[i]) {
					index = i;
					break;
				}
			}
			group.splice(index, 1);
			// this.lessonListFn();
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		/**
		 * @description 复制连接
		 * */
		urlCopy() {
			urlCopy();
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.main {
	width: $max-width;
	margin: 20px auto 60px;
	.title {
		font-size: 18px;
		color: #333333;
		line-height: 18px;
	}
}
.company-info {
	width: 100%;
	height: 130px;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 5px;
	padding: 20px;

	position: relative;
	.company-bg {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		object-fit: cover;
	}
	.company-base {
		display: flex;
		position: relative;
	}
	.company-img {
		width: 56px;
		height: 56px;
		background: #ffffff;
		border-radius: 5px;
		padding: 10px;
		margin-right: 10px;
		object-fit: contain;
	}
	.name {
		font-size: 24px;
		font-weight: bold;
		color: #ffffff;
		line-height: 24px;
	}
	.desc {
		font-size: 12px;
		line-height: 12px;
		color: #ffffff;
		margin-top: 10px;
		.desc-item {
			margin-right: 4px;
		}
	}
	.labels-box {
		margin-top: 15px;
	}
	.label {
		display: inline-block;
		padding: 6px 13px;
		background: #ffffff;
		border-radius: 2px;
		font-size: 14px;
		color: #999999;
		margin-right: 10px;
	}
	.btns-box {
		position: absolute;
		right: 20px;
		top: 20px;
		font-size: 14px;
		color: #ffffff;
		.btn-item {
			display: inline-flex;
			margin-left: 35px;
			cursor: pointer;
			align-items: center;
			.btn-icon {
				width: 16px;
				height: 16px;
				margin-right: 4px;
			}
		}
		.actvie-btn {
			color: #ff6b0c;
		}
	}
}
.company-profile {
	width: 100%;
	height: 448px;
	background: #ffffff;
	box-shadow: 0px 0px 10px 0px rgba(200, 200, 200, 0.05);
	border-radius: 5px;
	margin-top: 20px;
	padding: 20px;

	.info-box {
		margin-top: 20px;
		padding-top: 18px;
		border-top: 1px solid #f0f3f7;
		display: flex;
	}
	.profile-content {
		width: 651px;
		height: 350px;
		margin-right: 48px;
		overflow: auto;
	}
	.carousel-box {
		width: 520px;
		height: 350px;
	}
	.video-box {
		width: 100%;
		height: 350px;
	}
	.profile-img {
		width: 520px;
		height: 350px;
		object-fit: cover;
	}
}
.posision-box {
	margin-top: 37px;
	.posision-list {
		margin-top: 20px;
		display: flex;
		flex-wrap: wrap;
		.list-item {
			width: 406px;
			height: 124px;
			background: #ffffff;
			border-radius: 8px;
			padding: 20px;
			margin-right: 20px;
			cursor: pointer;
			&:nth-child(3n) {
				margin-right: 0;
				margin-bottom: 20px;
			}
			.title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title-text {
					position: relative;
					padding: 2px 52px 2px 0;
					margin-right: 6px;
				}
				.tag-img {
					height: 24px;
					position: absolute;
					right: 0;
					top: 0;
				}
				.price {
					flex-shrink: 0;
					font-size: 16px;
					color: #fe574a;
				}
			}
			.position {
				font-size: 12px;
				color: #999999;
				margin-top: 14px;
				line-height: 12px;
			}
			.labels-box {
				margin-top: 16px;
				.label {
					display: inline-block;
					background: #f5f5f5;
					border-radius: 4px;
					font-size: 12px;
					color: #666666;
					padding: 6px 15px;
					margin-right: 10px;
				}
			}
		}
	}
}
.business-box {
	margin-top: 43px;
	.business-info {
		margin-top: 20px;
		width: 100%;
		// min-height: 398px;
		padding: 20px;
		background: #ffffff;
		border-radius: 8px;
		display: flex;
		flex-wrap: wrap;
		flex-shrink: 0;
		.info-item {
			width: 25%;
			min-height: 45px;
			flex-shrink: 0;
			margin-bottom: 45px;
			padding-right: 8px;
		}
		.item-block {
			width: 100%;
			margin-bottom: 0;
			margin-right: 0;
		}
		.info-title {
			font-size: 14px;
			color: #999999;
			display: flex;
		}
		.item-img {
			width: 19px;
			height: 19px;
			margin-right: 4px;
		}
		.info-text {
			margin-top: 10px;
			font-size: 16px;
			color: #333333;
		}
	}
}
.commentList-box {
	margin-top: 40px;
	.commentList-list {
		margin-top: 20px;
		padding: 20px;
		width: 100%;
		// height: 865px;
		background: #ffffff;
		border-radius: 8px;
		.item-commentList {
			width: 100%;
			display: flex;
			margin-bottom: 40px;
			&:last-child {
				margin: 0;
			}
			.person-info {
				width: 64px;
				margin-right: 20px;
				flex-shrink: 0;
			}
			.commentList-img {
				width: 64px;
				height: 64px;
				border-radius: 50%;
			}
			.commentList-name {
				display: inline-block;
				width: 100%;
				font-size: 16px;
				color: #333333;
				margin-top: 10px;
				text-align: center;
			}
			.commentList-rate {
				display: flex;
				font-size: 16px;
				color: #333333;
			}
			.time {
				font-size: 12px;
				color: #999999;
				margin-top: 10px;
			}
			.content {
				font-size: 14px;
				color: #333333;
				margin-top: 15px;
			}
			.label {
				font-size: 12px;
				color: #999999;
				padding-right: 5px;
				padding-left: 5px;
				margin-top: 15px;
				line-height: 12px;
				border-left: 1px solid #999999;
				&:nth-child(1) {
					margin-left: 0;
					border-left: none;
				}
			}
			.status-label {
				display: inline-block;
				padding: 5px 14px;
				font-size: 14px;
				margin-right: 10px;
				margin-top: 15px;
				color: #999999;
				background: #f1f3f8;
				border-radius: 2px;
			}
		}
	}
}
.pagination {
	width: 100%;
	text-align: right;
	margin-top: 40px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
