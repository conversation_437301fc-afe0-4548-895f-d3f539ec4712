// 就业创业
import request from '@/utils/request';
// let baseUrl = '/ybzy/front/'; // 本地连接时
let baseUrl = '/gwapi/ybzy/front/'; //远程连接时
// /   gwapi/ybzy/
// 就业创业首页统计数据接口
export function getHomeStatistics(params) {
	return request({
		url: `/ybzy/front/jobCoPostFront/getHomeStatistics`,
		method: 'get',
		params
	});
}
// 创业服务类型分页接口
export function serviceTypeList(params) {
	return request({
		url: `${baseUrl}jobServiceType/listJson`,
		method: 'get',
		params
	});
}
// 获取创业服务类型信息
export function serviceTypeInfo(params) {
	return request({
		url: `${baseUrl}jobCoStartupServiceTypeFront/info`,
		method: 'get',
		params
	});
}
// 创业服务分页接口
export function serviceList(params) {
	return request({
		url: `${baseUrl}jobCoStartupServiceFront/listJson`,
		method: 'get',
		params
	});
}
// 获取创业服务信息
export function serviceInfo(params) {
	return request({
		url: `${baseUrl}jobService/info`,
		method: 'get',
		params
	});
}
// 就业服务分页接口
export function jobServiceListE(params) {
	return request({
		url: `${baseUrl}jobService/listJsonE`,
		method: 'get',
		params
	});
}
// 创业服务分页接口
export function jobServiceListS(params) {
	return request({
		url: `${baseUrl}jobService/listJsonS`,
		method: 'get',
		params
	});
}
// 获取就业创业服务信息
export function jobServiceInfo(params) {
	return request({
		url: `${baseUrl}jobService/info`,
		method: 'get',
		params
	});
}
// 创业导师分页接口
export function jobCoPioneerMentor(params) {
	return request({
		url: `${baseUrl}jobCoPioneerMentor/listJson`,
		method: 'get',
		params
	});
}
// 创业案例分页接口
export function jobCoExampleList(params) {
	return request({
		url: `${baseUrl}jobCoExampleFront/listJson`,
		method: 'get',
		params
	});
}
// 获取创业案例信息
export function jobCoExampleInfo(params) {
	return request({
		url: `${baseUrl}jobCoExampleFront/info`,
		method: 'get',
		params
	});
}
// 创业项目表分页接口
export function jobStudentProjectList(params) {
	return request({
		url: `${baseUrl}jobStudentProjectFront/listJson`,
		method: 'get',
		params
	});
}
// 获取创业项目表信息
export function jobStudentProjecInfo(params) {
	return request({
		url: `${baseUrl}jobStudentProjectFront/info`,
		method: 'get',
		params
	});
}
// 我的实习报告列表
export function getReportList(params) {
	return request({
		url: `/ybzy/front/jobStudentPerson/getReportList`,
		method: 'get',
		params
	});
}
// 我的实习报告列表
export function saveReport(data) {
	return request({
		url: `/ybzy/front/jobStudentPerson/saveReport`,
		method: 'post',
		data,
		type: 'JSON'
	});
}
// 删除我的实习报告
export function delReport(data) {
	return request({
		url: `/ybzy/front/jobStudentPerson/deleteById`,
		method: 'post',
		data
	});
}

//个人中心————————————————————————————>>>>>>
// 学生端获取学生简历信息
export function getCurriculumInfo(params) {
	return request({
		url: `/ybzy/front/jobStudentPerson/getStudentResume`,
		method: 'get',
		params
	});
}
// 岗位分页接口
export function jobCoPostList(params) {
	return request({
		url: `${baseUrl}jobCoPostFront/list`,
		method: 'get',
		params
	});
}
// 保存或更新学生简历
export function saveCurriculum(data) {
	return request({
		url: `/ybzy/front/jobStudentPerson/saveOrUpdate`,
		method: 'post',
		data,
		type: 'JSON'
	});
}
// 修改学生简历
export function updataCurriculum(data) {
	return request({
		url: `/ybzy/front/jobStudentPerson/update`,
		method: 'post',
		data
	});
}
// 我的投递
export function getPostResumeList(params) {
	return request({
		url: `/ybzy/front/jobStudentPerson/getPostResumeList`,
		method: 'get',
		params
	});
}
// 面试邀请
export function getInterviewList(params) {
	return request({
		url: `/ybzy/front/jobStudentPerson/interviewList`,
		method: 'get',
		params
	});
}
// 我的收藏
export function getCollectList(params) {
	return request({
		url: `/ybzy/front/jobCollect/getCollects`,
		method: 'get',
		params
	});
}
//取消收藏
export function cancelCollect(data) {
	return request({
		url: `/ybzy/front/jobCollect/cancelCollects`,
		method: 'post',
		data
		// type: 'from'
	});
}
// 我的关注
export function getAttentionList(params) {
	return request({
		url: `/ybzy/front/jobFollow/getFollows`,
		method: 'get',
		params
	});
}
//取消关注
export function cancelAttention(data) {
	return request({
		url: `/ybzy/front/jobFollow/cancelFollows`,
		method: 'post',
		data
		// type: 'from'
	});
}
//获取创业项目表
export function getStartUpList(params) {
	return request({
		url: `/ybzy/front/jobStudentProject/cUserListJson`,
		method: 'get',
		params
	});
}
//获取创业项目表详情
export function getStartUpById(params) {
	return request({
		url: `/ybzy/front/jobStudentProject/info`,
		method: 'get',
		params
	});
}
//新增创业项目表
export function addStartUp(data) {
	return request({
		url: `/ybzy/front/jobStudentProject/save`,
		method: 'post',
		data
	});
}
//修改创业项目表
export function updateStartUp(data) {
	return request({
		url: `/ybzy/front/jobStudentProject/update`,
		method: 'post',
		data
	});
}
//批量删除创业项目表
export function delStartUp(data) {
	return request({
		url: `/ybzy/front/jobStudentProject/delete`,
		method: 'post',
		data
	});
}
//baocun
export function saveCollect(data) {
	return request({
		url: `/ybzy/front/jobStudentProject/save`,
		method: 'post',
		data
		// type: 'from'
	});
}
//我的资讯
export function getConsult(params) {
	return request({
		// url: `/ybzy/front/job/chatMsg/chatList`,
		url: `/gwapi/ybzy/front/job/chatMsg/userMsgList`,
		method: 'get',
		params
	});
}
// 岗位详情信息
export function jobCoPostInfo(params) {
	return request({
		url: `${baseUrl}jobCoPostFront/info`,
		method: 'get',
		params
	});
}
// 公司详情信息
export function getCompanyInfo(params) {
	return request({
		url: `${baseUrl}jobCoPostFront/getCompanyInfo`,
		method: 'get',
		params
	});
}
// 实习网点查询
export function queryPractice(params) {
	return request({
		url: `${baseUrl}jobCoPostFront/queryPractice`,
		method: 'get',
		params
	});
}
// 投递简历
export function jobSend(params) {
	return request({
		url: `${baseUrl}jobCoPostFront/send`,
		method: 'get',
		params
	});
}
// 查数据字典的接口
export function findSysCode(params) {
	return request({
		// url: `/gwapi/ybzy/mecpSys/front/findSysCode.dhtml`,
		url: `/ybzy/mecpSys/front/findSysCode.dhtml`,
		method: 'get',
		params
	});
}
// 收藏
export function collect(data) {
	return request({
		url: `${baseUrl}jobCollect/collectObjects`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 取消收藏
export function cancelCollects(data) {
	return request({
		url: `${baseUrl}jobCollect/cancelCollects`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 分页获取收藏
export function getCollects(params) {
	return request({
		url: `${baseUrl}jobCollect/getCollects`,
		method: 'get',
		params
	});
}
//个人中心————————————————————————————<<<<<<
//  确认内容是否被当前用户收藏
export function isCollected(params) {
	return request({
		url: `${baseUrl}jobCollect/isCollected`,
		method: 'get',
		params
	});
}
//  创业项目表分页接口
export function jobStudentProject(params) {
	return request({
		url: `${baseUrl}jobStudentProject/listJson`,
		method: 'get',
		params
	});
}
//  创业项目表详情
export function jobProjectInfo(params) {
	return request({
		url: `${baseUrl}jobStudentProject/info`,
		method: 'get',
		params
	});
}
// 关注
export function followObjects(data) {
	return request({
		url: `${baseUrl}jobFollow/followObjects`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 取消关注
export function cancelFollows(data) {
	return request({
		url: `${baseUrl}jobFollow/cancelFollows`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 是否被关注
export function isFollowed(params) {
	return request({
		url: `${baseUrl}jobFollow/isFollowed`,
		method: 'get',
		// type: 'JSON',
		params
	});
}
// 确认内容是否被当前用户点赞
export function isLiked(params) {
	return request({
		url: `${baseUrl}jobLike/isLiked`,
		method: 'get',
		// type: 'JSON',
		params
	});
}
// 点赞
export function likeObjects(data) {
	return request({
		url: `${baseUrl}jobLike/likeObjects`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 取消点赞
export function cancelLikes(data) {
	return request({
		url: `${baseUrl}jobLike/cancelLikes`,
		method: 'post',
		// type: 'JSON',
		data
	});
}
// 获取行政区划级别
export function getRegion() {
	return request({
		url: `/gwapi/ybzy/cmregion/front/region`,
		method: 'get'
	});
}
// 获取行政区划级别
export function getSubRegion() {
	return request({
		url: `/gwapi/ybzy/cmregion/front/getSubRegion`,
		method: 'get'
	});
}
