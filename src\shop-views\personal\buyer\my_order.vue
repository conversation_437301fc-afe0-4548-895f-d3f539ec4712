<template>
	<div
		v-loading="loading"
		class="my_order"
		:style="isMain ? 'width:1200px;margin-bottom: 60rpx;' : 'width:100%'"
	>
		<div class="head">
			<div class="lf">筛选查询</div>
			<div class="rf">
				<el-button icon="el-icon-search" @click="handleSearch">搜索</el-button>
				<el-button type="primary" @click="exportUserOrderList">导出订单</el-button>
			</div>
		</div>
		<el-form :inline="true" :model="search" class="form">
			<el-form-item label="订单号">
				<el-input v-model="search.code" placeholder="请输入订单编号"></el-input>
			</el-form-item>
			<el-form-item label="店铺名称">
				<el-input v-model="search.name" placeholder="请输入店铺名称"></el-input>
			</el-form-item>
			<el-form-item label="下单时间">
				<el-date-picker
					v-model="search.time"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					value-format="yyyy-MM-dd"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<div class="tab-box">
			<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">全部订单</div>
			<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">待付款</div>
			<div class="tab" :class="tabIndex == 2 ? 'act' : ''" @click="handleTab(2)">进行中</div>
			<div class="tab" :class="tabIndex == 3 ? 'act' : ''" @click="handleTab(3)">已完成</div>
			<div class="tab" :class="tabIndex == 4 ? 'act' : ''" @click="handleTab(4)">已取消</div>
			<div class="line" :style="{ left: lineLeft(tabIndex) }"></div>
		</div>
		<div v-show="tabIndex === 2 || tabIndex === 3" class="option_box">
			<div class="option" :class="option == 0 ? 'act' : ''" @click="handleOption(0)">全部</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 1 ? 'act' : ''"
				@click="handleOption(1)"
			>
				待预约
			</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 2 ? 'act' : ''"
				@click="handleOption(2)"
			>
				待确认
			</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 3 ? 'act' : ''"
				@click="handleOption(3)"
			>
				待发货
			</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 4 ? 'act' : ''"
				@click="handleOption(4)"
			>
				待收货
			</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 5 ? 'act' : ''"
				@click="handleOption(5)"
			>
				已确认
			</div>
			<div
				v-show="tabIndex === 2"
				class="option"
				:class="option == 6 ? 'act' : ''"
				@click="handleOption(6)"
			>
				消费中
			</div>
			<div
				v-show="tabIndex === 3"
				class="option"
				:class="option == 1 ? 'act' : ''"
				@click="handleOption(1)"
			>
				已使用
			</div>
			<div
				v-show="tabIndex === 3"
				class="option"
				:class="option == 2 ? 'act' : ''"
				@click="handleOption(2)"
			>
				已收货
			</div>
			<div
				v-show="tabIndex === 3"
				class="option"
				:class="option == 3 ? 'act' : ''"
				@click="handleOption(3)"
			>
				已评价
			</div>
		</div>
		<div class="table">
			<orderTable
				ref="table"
				:table="orderList"
				:total="total"
				@editAddr="editAddr"
				@sizeChange="handleSizeChange"
				@pageChange="handleCurrentChange"
				@cancelOrder="getUserOrderList"
				@confirmReceipt="getUserOrderList"
				@billApply="billApply"
			></orderTable>
			<ModifyAddress ref="dia" :item="item" @success="getUserOrderList()"></ModifyAddress>
			<ApplyBill ref="bill" :item="item" @success="getUserOrderList()"></ApplyBill>
		</div>
	</div>
</template>

<script>
import orderTable from './../components/order_table.vue';
import ModifyAddress from './../components/modify_address.vue';
import ApplyBill from './../components/apply_bill.vue';
export default {
	name: 'MyOrder',
	components: {
		orderTable,
		ModifyAddress,
		ApplyBill
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			search: {},
			tabIndex: 0,
			option: 0,
			total: 0,
			offset: 0,
			psize: 10,
			pageSize: 10,
			orderList: [],
			loading: false,
			showAddress: true,
			item: {},
			userId: ''
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getUserOrderList();
		}
	},
	methods: {
		// 导出订单
		exportUserOrderList() {
			this.loading = true;
			let type = this.tabIndex;
			if (this.tabIndex == 4) {
				type = 9;
			}
			if (this.tabIndex == 2) {
				if (this.option == 1) {
					type = 20;
				} else if (this.option == 2) {
					type = 21;
				} else if (this.option == 3) {
					type = 22;
				} else if (this.option == 4) {
					type = 23;
				} else if (this.option == 5) {
					type = 24;
				} else if (this.option == 6) {
					type = 25;
				}
			}
			if (this.tabIndex == 3) {
				if (this.option == 1) {
					type = 31;
				} else if (this.option == 2) {
					type = 32;
				} else if (this.option == 3) {
					type = 33;
				}
			}
			let startTime = this.search.time && this.search.time.length == 2 ? this.search.time[0] : '';
			let endTime = this.search.time && this.search.time.length == 2 ? this.search.time[1] : '';
			let data = {
				userId: this.userId,
				offset: this.offset,
				psize: this.psize,
				type,
				siteId: this.getSiteId(),
				keyword: this.search.code || '',
				shopName: this.search.name || '',
				startTime,
				endTime
			};
			this.$api.shop_api.exportUserOrderList(data).then(res => {
				if (!res) {
					this.loading = false;
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
					return;
				}
				this.loading = false;
				const content = res;
				const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8' });
				const fileName = '订单列表';
				if ('download' in document.createElement('a')) {
					// 非IE下载
					const elink = document.createElement('a');
					elink.download = fileName;
					elink.style.display = 'none';
					elink.href = URL.createObjectURL(blob);
					document.body.appendChild(elink);
					elink.click();
					URL.revokeObjectURL(elink.href); // 释放URL 对象
					document.body.removeChild(elink);
				} else {
					// IE10+下载
					navigator.msSaveBlob(blob, fileName);
				}
			});
		},
		// 申请开票
		billApply(i) {
			this.item = i;
			this.$refs.bill.visible = true;
		},
		// 修改收货地址
		editAddr(i) {
			this.item = i;
			this.$refs.dia.visible = true;
		},
		// 搜索
		handleSearch() {
			this.restPage();
			this.offset = 0;
			this.psize = this.pageSize;
			this.getUserOrderList();
		},
		// 重制页数
		restPage() {
			this.$refs['table'].page = 1;
		},
		// 页数
		handleCurrentChange(i) {
			this.offset = this.psize * i - this.psize;
			if (i == 1) {
				this.offset = 0;
			}
			// this.psize = this.offset + this.pageSize - 1;
			this.getUserOrderList();
		},
		// 条数
		handleSizeChange(i) {
			this.offset = 0;
			this.pageSize = i;
			this.psize = i;
			this.restPage();
			this.getUserOrderList();
		},
		// 获取订单列表
		getUserOrderList() {
			this.loading = true;
			let type = this.tabIndex;
			if (this.tabIndex == 4) {
				type = 9;
			}
			if (this.tabIndex == 2) {
				if (this.option == 1) {
					type = 20;
				} else if (this.option == 2) {
					type = 21;
				} else if (this.option == 3) {
					type = 22;
				} else if (this.option == 4) {
					type = 23;
				} else if (this.option == 5) {
					type = 24;
				} else if (this.option == 6) {
					type = 25;
				}
			}
			if (this.tabIndex == 3) {
				if (this.option == 1) {
					type = 31;
				} else if (this.option == 2) {
					type = 32;
				} else if (this.option == 3) {
					type = 33;
				}
			}
			let startTime =
				this.search.time && this.search.time.length == 2 ? this.search.time[0] + ' 00:00:00' : '';
			let endTime =
				this.search.time && this.search.time.length == 2 ? this.search.time[1] + ' 23:59:59' : '';
			let data = {
				userId: this.userId,
				offset: this.offset,
				psize: this.psize,
				type,
				siteId: this.getSiteId(),
				keyword: this.search.code || '',
				shopName: this.search.name || '',
				startTime,
				endTime
			};
			this.$api.shop_api.getUserOrderList(data).then(async res => {
				if (res.state) {
					this.total = res.totalNum;
					if (res.result) {
						let extOrderNos = [];
						let instanceIds = [];
						let targeId = '';
						for (let i = 0; i < res.result.length; i += 1) {
							if (res.result[i].PAY_MODE == 8) {
								extOrderNos.push(res.result[i].ORDER_ID);
							}
							if (res.result[i].OTHER_PARAMS && res.result[i].OTHER_PARAMS.contractData) {
								instanceIds.push(res.result[i].OTHER_PARAMS.contractData.instanceId);
								if (!targeId) {
									targeId = res.result[i].OTHER_PARAMS.contractData.buyer_targetId;
								}
							}
						}
						if (extOrderNos.length > 0) {
							let ext = {
								path: '/plat/scswl/order/openapi/getOrderStates',
								extOrderNos: extOrderNos
							};
							const results = await this.$api.shop_api.doPostJson(ext);
							if (results.rCode == 0) {
								for (let i = 0; i < results.results.length; i += 1) {
									for (let a = 0; a < res.result.length; a += 1) {
										if (results.results[i].extOrderNo == res.result[a].ORDER_ID) {
											res.result[a].rz = results.results[i];
										}
									}
								}
							}
						}
						if (instanceIds.length > 0) {
							let item = {
								instanceIds,
								targeId,
								path: '/plat/scswl/platContract/openapi/sign/mySignStatus'
							};
							const results = await this.$api.shop_api.doPostJson(item);
							if (results.rCode == 0) {
								for (let i = 0; i < results.results.length; i += 1) {
									for (let a = 0; a < res.result.length; a += 1) {
										if (
											res.result[a].OTHER_PARAMS &&
											res.result[a].OTHER_PARAMS.contractData &&
											results.results[i].instanceId ==
												res.result[a].OTHER_PARAMS.contractData.instanceId
										) {
											res.result[a].jr = results.results[i].singInfo;
										}
									}
								}
							}
						}
						this.orderList = res.result;
					} else {
						this.orderList = [];
					}
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 点击option
		handleOption(i) {
			if (i !== this.option) {
				this.option = i;
				this.restPage();
				this.offset = 0;
				this.orderList = [];
				this.getUserOrderList();
			}
		},
		// 点击导航栏
		handleTab(i) {
			if (i !== this.tabIndex) {
				this.option = 0;
				this.tabIndex = i;
				this.restPage();
				this.offset = 0;
				this.orderList = [];
				this.getUserOrderList();
			}
		},
		// 左侧高度
		lineLeft(i) {
			return 100 * i + 'px';
		}
	}
};
</script>

<style lang="scss" scoped>
.my_order {
	margin: 0 auto 0;
	padding: 20px;
	background: #ffffff;
	.head {
		height: 48px;
		background: #f4f4f4;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		padding: 16px;
		.lf {
			font-size: 14px;
			font-weight: 500;
			color: #404040;
		}
		.rf {
			margin-left: auto;
		}
	}
	.form {
		display: flex;
		padding: 19px 16px 0;
		box-sizing: border-box;
		height: 79px;
		border: 1px solid #d9d9d9;
		border-top: none;
		::v-deep .el-date-editor {
			width: 255px;
		}
	}
	.tab-box {
		margin-top: 20px;
		height: 40px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		position: relative;
		.tab {
			width: 60px;
			font-size: 14px;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: #404040;
			margin-right: 40px;
			cursor: pointer;
			text-align: center;
		}
		.act {
			color: var(--brand-6, '#ca3f3b');
		}
		.line {
			width: 60px;
			height: 1px;
			background: var(--brand-6, '#ca3f3b');
			position: absolute;
			left: 0;
			bottom: -1px;
			transition: all 0.3s;
		}
	}
	.option_box {
		margin-top: 18px;
		display: flex;
		align-items: center;
		.option {
			padding: 6px 20px;
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			border: 1px solid #d9d9d9;
			margin-right: 12px;
			cursor: pointer;
		}
		.act {
			color: #ffffff;
			background: var(--brand-6, '#ca3f3b');
			border-style: var(--brand-6, '#ca3f3b');
		}
	}
	.table {
		margin-top: 20px;
	}
}
</style>
