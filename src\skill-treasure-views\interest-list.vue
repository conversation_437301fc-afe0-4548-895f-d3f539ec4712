<template>
	<div class="skill-treasure" :style="{ width: isMain ? '1200px' : '100%' }">
		<tabs-nav v-if="isMain" :type-list="typeList" :active-code="activeCode" @click="typeClick" />
		<!-- 内容区域 -->
		<div class="content-box">
			<Empty v-if="list.length == 0" :tips="'暂无数据'" />
			<div v-for="(item, index) in list" :key="index" class="content-box-item">
				<img :src="item.headImg ? $judgeFile(item.headImg) : defaulAvatar" class="item-img" />
				<div class="item-right">
					<div class="item-info">
						<span class="item-name">{{ item.nickName }}</span>
						<span v-if="activeCode == 'interest'" class="item-desc">
							{{ item.profile || '还没有简介' }}
						</span>
						<span v-if="activeCode == 'fans'" class="item-desc">
							<span>作品·{{ item.storyNum || 0 }}</span>
							<span class="item-space"></span>
							<span>粉丝·{{ item.followerNum || 0 }}</span>
						</span>
					</div>
					<span v-if="item.isInterest" class="interest-active" @click="memberInteract(6, item)">
						互相关注
					</span>
					<span v-else class="interest-btn" @click="memberInteract(6, item)">回关</span>
				</div>
			</div>
		</div>
		<el-pagination
			v-if="total"
			background
			layout="prev, pager, next"
			class="pagination"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
import tabsNav from './components/tabs.vue';
export default {
	components: { tabsNav },
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		params: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			// 类型列表
			typeList: [
				{
					name: '关注',
					id: '',
					code: 'interest'
				},
				{
					name: '粉丝',
					id: '',
					code: 'fans'
				}
			],
			activeCode: '', //选中的类型
			// 内容部分数据
			list: [],
			pageNum: 1,
			pageSize: 10,
			total: 0,
			memberId: '' //用户id
		};
	},
	mounted() {
		this.activeCode = this.$route.query.code || this.params.code || 'interest';
		this.memberId = this.$route.query.memberId || '';
		if (this.activeCode == 'interest') {
			this.getInteractList();
		} else {
			this.getFollowerList();
		}
	},
	methods: {
		/**
		 * @description 类型点击事件，用于切换内容
		 * @params {item} 点击的类型数据
		 */
		typeClick(item) {
			this.activeCode = item.code;
			this.total = 0;
			this.list = [];
			this.pageNum = 1;
			if (this.activeCode == 'interest') {
				this.getInteractList();
			} else {
				this.getFollowerList();
			}
		},
		/**
		 * @description 关注点击事件
		 * @params {item} 点击内容数据
		 * */
		/**
		 * @description 1点赞攻略    2收藏攻略  6关注用户   11点赞评论 操作
		 * @param {code} 1点赞攻略    2收藏攻略  6关注用户   11点赞评论
		 * */
		memberInteract(code, item) {
			let param = {
				rentId: this.getSiteId(), // 租户id
				objId: item.memberId,
				actType: code
			};
			this.$api.treasure_api.memberInteract(param).then(res => {
				if (res.code == 200) {
					//重新获取列表数据
					this.list = [];
					if (this.activeCode == 'interest') {
						this.getInteractList();
					} else {
						this.getFollowerList();
					}
				}
			});
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			if (this.activeCode == 'interest') {
				this.getInteractList();
			} else {
				this.getFollowerList();
			}
		},
		/**
		 * @description 游玩攻略-关注列表
		 * */
		getInteractList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				memberId: this.memberId,
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.getInteractList(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		},
		/**
		 * @description 游玩攻略-粉丝列表
		 * */
		getFollowerList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				memberId: this.memberId,
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.getFollowerList(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1200px;

.skill-treasure {
	margin: 0 auto;
	padding-bottom: 60px;
	font-family: PingFang SC-Regular, PingFang SC;
	.content-box {
		min-height: 480px;
		margin-top: 16px;
		padding: 20px;
		display: flex;
		flex-wrap: wrap;
		flex-direction: column;
		background: #fff;
		&-item {
			width: 100%;
			height: 60px;
			display: flex;
			align-items: center;
			margin-bottom: 16px;
		}
		.item-img {
			width: 44px;
			height: 44px;
			border-radius: 50%;
			flex-shrink: 0;
			margin-right: 16px;
			object-fit: cover;
		}
		.item-right {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			height: 100%;
			border-bottom: 1px solid #f0f0f0;
			.item-name {
				font-size: 16px;
				color: #404040;
				line-height: 16px;
				display: block;
			}
			.item-desc {
				font-size: 14px;
				color: #8c8c8c;
				line-height: 16px;
				margin-top: 8px;
				display: inline-block;
				display: flex;
				align-items: center;
			}
			.item-space {
				height: 16px;
				width: 0px;
				border: 1px solid #f0f0f0;
				margin: 0 8px;
			}
			.interest-btn {
				width: 96px;
				padding: 4px 0;
				background: var(--brand-6, #0076e8);
				border-radius: 21px;
				font-size: 16px;
				color: #ffffff;
				line-height: 24px;
				cursor: pointer;
				text-align: center;
			}
			.interest-active {
				width: 96px;
				padding: 4px 0;
				border-radius: 21px;
				border: 1px solid #d9d9d9;
				font-size: 16px;
				color: #8c8c8c;
				line-height: 24px;
				text-align: center;
				cursor: pointer;
			}
		}
	}
	.pagination {
		margin-top: 8px;
		text-align: right;
	}
}
</style>
