<template>
	<div v-loading="loading" class="GoodsDealRecommend">
		<div class="left">
			<div class="left-title">
				<img class="left-title-img" src="@/assets/shop-images/title-icon.png" alt="" />
				<div class="left-title-text">分类</div>
				<img class="left-title-img" src="@/assets/shop-images/title-icon.png" alt="" />
			</div>
			<div class="left-list">
				<a
					v-for="(item, index) of shopTypeList"
					:key="index"
					href="javascript:void(0)"
					@click="onLinkGoodsList(item.id)"
				>
					<div
						class="top"
						:style="{
							background: bgList[item.code].bg,
							width: shopTypeList.length > 5 ? (index === 0 ? '170px' : '80px') : '172px'
						}"
					>
						<img class="icon" :src="bgList[item.code].icon" alt="" />
						<span class="title">{{ item.name }}</span>
					</div>
				</a>
			</div>
		</div>
		<div class="center">
			<el-skeleton :loading="centerLoading" animated>
				<template slot="template">
					<el-skeleton-item variant="image" style="width: 666px; height: 390px" />
				</template>
				<template>
					<div>
						<a href="javascript:void(0)">
							<img
								v-if="info.url"
								class="center-recommend-img"
								:src="$judgeFile(info.url)"
								alt=""
							/>
						</a>
					</div>
				</template>
			</el-skeleton>
		</div>
		<div class="right">
			<div class="right-top">
				<headAvator :own-id="_userinfo.id" class="right-top-avatar" />
				<div v-if="_userinfo.nickname" class="right-top-name">
					{{ _userinfo.nickname }}
				</div>
				<div v-else class="noLogin">
					<div class="text">您好，请登录</div>
					<div class="button">
						<div class="left" @click="toRegister">注册</div>
						<div class="right" @click="toLogin">登录</div>
					</div>
				</div>
			</div>
			<div class="right-bottom">
				<div
					v-for="(item, index) of list"
					:key="index"
					style="display: flex; align-items: center"
					@click="toLink(item)"
				>
					<div
						class="right-bottom-item"
						:style="{ borderBottom: index < list.length - 2 ? '1px solid #eeeeee' : 'none' }"
					>
						<img class="right-bottom-item-img" :src="item.img" alt="" />
						<div class="right-bottom-item-name">{{ item.name }}</div>
					</div>
					<div v-if="index % 2 === 0" class="line"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodsDealRecommend',
	data() {
		return {
			defaultAvatar: require('@/assets/shop-images/default-avatar.png'),
			loading: false,
			bgList: {
				scene: {
					icon: require('@/assets/shop-images/scenic.png'),
					bg: '#FFEFEE'
				},
				knowledge: {
					icon: require('@/assets/shop-images/scenic.png'),
					bg: '#FFEFDE'
				},
				hotel: {
					icon: require('@/assets/shop-images/hotel.png'),
					bg: '#D4E0F9'
				},
				food: {
					icon: require('@/assets/shop-images/foods.png'),
					bg: '#ECFBE7'
				},
				native: {
					icon: require('@/assets/shop-images/culture.png'),
					bg: '#FDF1F1'
				},
				heritage: {
					icon: require('@/assets/shop-images/fy.png'),
					bg: '#FFFCDF'
				},
				culture: {
					icon: require('@/assets/shop-images/study.png'),
					bg: '#E0F2F8'
				},
				package: {
					icon: require('@/assets/shop-images/group.png'),
					bg: '#FFEFDE'
				}
			}, //每个分类对应的图标和颜色
			shopTypeList: [], // 接口返回的商品类型
			list: [
				{ name: '购物车', img: require('@/assets/shop-images/shop.png'), url: '/shoppingCart' },
				{ name: '我的订单', img: require('@/assets/shop-images/order.png'), url: '/myOrder' },
				{ name: '积分商城', img: require('@/assets/shop-images/cope.png'), url: '/pointsMall' },
				{ name: '我的收藏', img: require('@/assets/shop-images/love.png'), url: '/collectList' }
			],
			info: {},
			centerLoading: false,
			needType: {
				// scene: 1,
				// hotel: 2,
				// food: 3,
				native: 4,
				heritage: 5,
				culture: 6,
				knowledge: 8
				// package: 7
			} // 宜宾项目需要的分类，以及排序
		};
	},
	created() {
		this.centerLoading = true;
		this.getAdvert(); // 获取广告
		this.getShopType(); // 获取分类
	},
	methods: {
		/**注册*/
		toRegister() {
			this.$router.push(`/register?redirect=${this.$route.path}`);
		},
		/**去登录*/
		toLogin() {
			this.$router.push(`/login?redirect=${this.$route.path}`);
		},
		/**跳转购物车等模块*/
		toLink(item) {
			if (item.url) {
				this.$router.push(item.url);
			}
		},
		/**获取广告*/
		getAdvert() {
			this.loading = true;
			this.$api.shop_api
				.getAdvertsByCode({
					siteId: this.getSiteId(),
					sysCode: 'pc_chuan_top'
				})
				.then(res => {
					this.loading = false;
					let arr = res.result?.adData || [];
					this.info = arr && arr.length && arr[0];
					this.centerLoading = false;
				});
		},
		/**获取商品类别*/
		getShopType() {
			let data = {
				type: 1,
				level: 2,
				siteId: this.getSiteId()
			};
			this.$api.shop_api.getProductTypeTree(data).then(res => {
				if (res.state) {
					let arr = res.result.filter(item => {
						if (Object.keys(this.needType).includes(item.code)) {
							item.selfSort = this.needType[item.code];
							return true;
						} else {
							return false;
						}
					});
					arr.sort((a, b) => {
						return a.selfSort - b.selfSort;
					});
					localStorage.setItem('shopType', JSON.stringify(arr));
					this.shopTypeList = arr || [];
				}
			});
			// 获取技术技能培训二级分类，从原技能宝库-视频中迁移过来
			this.$api.study_api.getCourseClassTree().then(res => {
				localStorage.setItem('shopType-knowledge', JSON.stringify(res.data));
			});
		},
		/**跳转商品列表页*/
		onLinkGoodsList(recommendId) {
			this.$router.push({
				path: '/shopGoodsList',
				query: {
					recommendId
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.GoodsDealRecommend {
	display: flex;
	margin-top: 30px;
	.left {
		position: relative;
		width: 200px;
		height: 390px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		&-title {
			display: flex;
			margin: 8px 0;
			align-items: center;
			justify-content: center;
			&-img {
				height: 12px;
				width: 16px;
			}
			&-text {
				margin: 0 16px;
				font-size: 20px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #262626;
				line-height: 44px;
			}
		}
		&-list {
			width: 180px;
			margin: 0 auto;
			display: flex;
			flex-wrap: wrap;
			.dec {
				padding-left: 30px;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
		}
		.top {
			display: flex;
			align-items: center;
			position: relative;
			height: 72px;
			margin: 0 5px 8px;
			background-position: 40% 40%;
			background-size: 120% 120%;
			padding: 20px;
		}
		.icon {
			position: absolute;
			bottom: 12px;
			right: 16px;
			width: 48px;
			height: 48px;
		}
		.title {
			z-index: 11;
			text-align: center;
			font-size: 18px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
		}
	}
	.center {
		margin: 0 10px;
		div {
			width: 666px;
			height: 390px;
		}
		&-recommend-img {
			width: 666px;
			height: 390px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
	}
	.right {
		width: 314px;
		height: 390px;
		background: #ffffff;
		padding: 0 16px;
		&-top {
			height: 182px;
			border-bottom: 1px solid #eeeeee;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			&-avatar {
				width: 80px !important;
				height: 80px !important;
				margin-bottom: 10px;
			}
			&-name {
				font-size: 16px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #404040;
				line-height: 32px;
			}
			.noLogin {
				display: flex;
				flex-direction: column;
				align-items: center;
				.text {
					font-size: 16px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #404040;
					line-height: 32px;
				}
				.button {
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 12px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					line-height: 26px;
					.left {
						width: 64px;
						height: 26px;
						background: #ffffff;
						border-radius: 3px;
						border: 1px solid var(--brand-6, #0076e8);
						color: var(--brand-6, #0076e8);
						margin-right: 16px;
						cursor: pointer;
						text-align: center;
					}
					.right {
						width: 64px;
						height: 26px;
						background: #3274e0;
						border-radius: 3px;
						border: 1px solid var(--brand-6, #0076e8);
						color: #ffffff;
						cursor: pointer;
						text-align: center;
					}
				}
			}
		}
		&-bottom {
			display: flex;
			flex-wrap: wrap;
			&-item {
				height: 104px;
				width: 124px;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				cursor: pointer;
				&-img {
					margin-bottom: 8px;
					width: 30px;
					height: 30px;
				}
				&-name {
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #404040;
					line-height: 20px;
				}
			}
			.line {
				margin: 0 16px;
				width: 1px;
				height: 72px;
				background: #eeeeee;
			}
		}
	}
}
</style>
