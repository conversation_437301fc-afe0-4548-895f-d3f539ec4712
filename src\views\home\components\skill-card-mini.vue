<!--
 @desc:推荐技能小卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- <div :class="{ 'tag-box': true, 'tag-active': cardData.status == 1 }">
			{{ cardData.status == 0 ? '未开始' : '已开始' }}
		</div> -->
		<el-image class="img" lazy :src="$judgeFile(cardData.coverImg)"></el-image>
		<div class="content-box">
			<span class="over2">{{ cardData.title }}</span>
			<footer>
				<span class="over1">{{ cardData.name }}</span>
				<span>{{ cardData.time }}</span>
			</footer>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			required: true
			// default: () => {
			// 	return {
			// 		imgUrl: '',
			// 		status: 1,
			// 		title: '学会这招高效串记法，彻底高度四级词汇！',
			// 		name: '导师姓名',
			// 		time: '09月01日 08:00'
			// 	};
			// }
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'skill', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(space-between);
	position: relative;
	width: 300px;
	height: 130px;
	border-radius: 8px;
	margin-bottom: 20px;
	padding: 30px 40px 30px 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	.tag-box {
		position: absolute;
		top: 0;
		left: 0;
		width: 70px;
		height: 22px;
		border-top-left-radius: 8px;
		border-bottom-right-radius: 30px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: center;
		background: #afc0cc;
		color: #ffff;
	}
	.tag-active {
		background: var(--brand-6, #0076e8);
	}
	.img {
		width: 70px;
		height: 70px;
		border-radius: 50%;
		margin-right: 18px;
		flex-shrink: 0;
		object-fit: cover;
	}
	.content-box {
		@include flexBox(space-between, flex-start);
		width: calc(100% - 88px);
		height: 80px;
		flex-direction: column;
		& > span {
			font-size: 14px;
			font-weight: 400;
			color: #333333;
			line-height: 20px;
		}
		footer {
			// @include flexBox(space-between);
			width: 100%;
			height: 40px;
			span {
				display: inline-block;
				font-size: 12px;
				font-weight: 400;
				color: #999999;
				&:nth-of-type(1) {
					width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}
}
</style>
