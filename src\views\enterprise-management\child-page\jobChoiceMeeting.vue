<template>
	<div class="main">
		<!-- 对我感兴趣 -->
		<h3 class="form-title">双选会邀请</h3>
		<el-input v-model.trim="keywordValue" placeholder="请输入关键字" class="input-with-select">
			<!-- <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button> -->
			<div slot="append" class="search-btn" @click="handleSearch">搜索</div>
		</el-input>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="title" label="标题"></el-table-column>
			<el-table-column prop="meetingStartEndDes" label="双选会开展时间"></el-table-column>
			<el-table-column prop="replyStartEndDes" label="企业回复时间"></el-table-column>
			<el-table-column prop="createOrgName" label="创建机构"></el-table-column>
			<el-table-column prop="statesDes" label="状态"></el-table-column>
			<el-table-column fixed="right" label="操作" width="150">
				<template slot-scope="scope">
					<!-- states 状态（-1、待回复；0、未提交；1、待审核；2、审核中；3、未通过；4、已通过；5、已过期；9、已拒绝） -->
					<el-button type="text" size="small" @click="handleView(scope.row, 'view')">
						查看
					</el-button>
					<!-- 已过期状态不不能进行操作 -->
					<template v-if="scope.row.states != 5">
						<!-- 待回复,未通过状态才能回复 -->
						<el-button
							v-if="[0, -1, 3].includes(scope.row.states)"
							type="text"
							size="small"
							@click="handleView(scope.row, 'reply')"
						>
							回复
						</el-button>
						<!--未提交状态，未通过状态，未通过,已通过，已拒绝 可以重新进行编辑 -->
						<!-- <el-button  v-if="[0,-1].includes(scope.row.states)" type="text" size="small" @click="handleView(scope.row)">编辑</el-button> -->
						<!-- 未进入审核流时才能撤回 1 -->
						<el-button
							v-if="[1, 9].includes(scope.row.states)"
							type="text"
							size="small"
							@click="handleRecall(scope.row)"
						>
							撤回
						</el-button>
					</template>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList"
		/>
		<!-- 查看双选会 -->
		<meetingDetail-dialog ref="meetingDetailDialog" @reply="reply" />
	</div>
</template>

<script>
import meetingDetailDialog from './components/jobChoiceMeetingDetail.vue';
export default {
	name: 'Interest',
	components: {
		meetingDetailDialog // 详情弹窗内容
	},
	data() {
		return {
			tableLoading: true,
			keywordValue: '', //关键字暂存
			keyword: '', //关键字搜索
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [] //选中的职位列表
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.jobDualSelectList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize,
					keyword: this.keyword //关键字
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		// 关键字搜索
		handleSearch() {
			this.keyword = this.keywordValue;
			this.paginationConfig.pageNum = 1;
			this.paginationConfig.pageSize = 10;
			this.getList();
		},
		getTime(row) {
			// 选择时间从当前日期开始，结束时间以标准时间为准
			let startTime = new Date(row.replyStartDate).getTime();
			let maxTime = new Date(row.replyEndDate + ' 23:59:59').getTime();
			const start = new Date().getTime();
			return start > startTime && start < maxTime;
		},
		// 回复 + 查看
		handleView(row, type) {
			if (type == 'reply' && !this.getTime(row)) {
				return this.$message.info('不在企业回复时间内！');
			}
			this.$refs.meetingDetailDialog.show(row, type);
		},
		/**
		 * @description 撤回操作
		 * */
		handleRecall(row) {
			this.$confirm('确认撤回？')
				.then(_ => {
					this.$api.enterprise_center.jobDualSelectRecallReply({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							// this.getJobList();
							this.getList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		// 双选会回复之后的操作，刷新列表页面
		reply() {
			this.getList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.input-with-select {
	width: 300px;
	.search-btn {
		width: 100%;
		height: 100%;
		padding: 0 20px;
	}
}
::v-deep .el-input-group__append {
	background: var(--brand-6, #0076e8);
	color: #fff;
	cursor: pointer;
	padding: 0;
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
.info-save {
	text-align: center;
}
</style>
