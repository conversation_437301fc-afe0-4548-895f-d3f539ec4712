<!--
 @desc:创业项目发布
 @author: WH
 @date: 2023/9/25
 -->
富文本 经纬度 地址手动输入 传codeid
<template>
	<div class="main">
		<div class="mini-title">
			<el-button type="primary" @click="openDetail('add')">新增</el-button>
		</div>
		<div v-loading="loading" class="list-box">
			<el-table
				v-loading="loading"
				:data="list"
				style="width: 100%; height: 100%"
				align="center"
				header-align="center"
				header-row-class-name="history-table"
			>
				<!-- <el-table-column type="index" label="序号" width="150"></el-table-column> -->
				<el-table-column prop="name" label="项目名称" show-overflow-tooltip />
				<el-table-column prop="updateUserName" label="封面">
					<template #default="{ row }">
						<img class="is-img" :src="getImgUrl(row.cover)" alt="" />
					</template>
				</el-table-column>
				<el-table-column prop="address" label="项目地址" />
				<el-table-column prop="telephone" label="电话号码" show-overflow-tooltip />
				<el-table-column prop="email" label="电子邮箱" show-overflow-tooltip />
				<el-table-column prop="updateUserName" label="更新人">
					<template #default="{ row }">
						{{ row.updateUserName || '-' }}
					</template>
				</el-table-column>
				<!-- <el-table-column prop="updateTime" width="170" label="更新时间"></el-table-column> -->
				<el-table-column prop="auditStatus" width="80" label="审核状态">
					<template #default="{ row }">
						<el-tag :type="{ 0: '', 1: 'success', 9: 'info', 2: 'warning' }[row.auditStatus]">
							{{ row.auditStatusVO }}
						</el-tag>
					</template>
				</el-table-column>

				<el-table-column fixed="right" label="操作" width="120">
					<template #default="{ row }">
						<el-button type="text" size="small" class="del-btn" @click="delClick(row.id)">
							删除
						</el-button>
						<el-button type="text" size="small" @click="openDetail('edit', row.id)">编辑</el-button>
						<el-button type="text" size="small" @click="openDetail('look', row.id)">查看</el-button>
						<!-- <el-button
						v-if="row.auditStatus === 1"
						type="text"
						size="small"
						@click="handleClose(row)"
					>
						{{ row.status ? '停用' : '启用' }}
					</el-button> -->
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>

		<detail-dialog :id="handleId" :visible.sync="visible" :type="handleType" @callback="callback" />
	</div>
</template>

<script>
import DetailDialog from './start-up/detail-dialog.vue';
// import { alumniUrl } from '@/config';

export default {
	components: { DetailDialog },
	data() {
		return {
			list: [],
			page: 1, // 页数
			size: 10, // 条数
			total: 0,
			loading: false,
			visible: false,
			handleType: '',
			handleId: ''
		};
	},
	mounted() {
		this.getStartUpList();
	},
	methods: {
		openDetail(type, id = null) {
			this.visible = true;
			this.handleType = type;
			this.handleId = id;
		},
		callback() {
			this.getStartUpList();
		},
		async getStartUpList() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.getStartUpList({
					'page.current': this.page,
					'page.size': this.size
				});
				if (rCode == 0) {
					this.list = results.records;
					this.total = results.total;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		delClick(id) {
			this.$confirm('确定删除此分类?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				type: 'warning'
			})
				.then(() => this.delClickFn(id))
				.catch(() => {});
		},
		async delClickFn(id) {
			try {
				this.loading = true;
				let { rCode, msg } = await this.$api.employment_api.delStartUp({
					ids: id
				});
				if (rCode == 0) {
					this.page = 1;
					this.getStartUpList();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.getStartUpList();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getStartUpList();
		},
		/**获取logo*/
		getImgUrl(id) {
			const origin = window.location.origin + '/';
			// 判断是否为本地运行localhost，本地加代理
			const host = origin.includes('localhost') ? origin + 'dev-api/' : origin;
			return `${host}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
		.is-img {
			width: 60px;
			height: 60px;
			// object-fit: contain;
		}
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
