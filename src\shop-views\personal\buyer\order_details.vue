<template>
	<div v-loading="loading" class="order_details">
		<div class="head">
			<div class="icon">
				<AlIcon :name="orderType(details.ORDER_STATE)" color="#fff" size="40" />
			</div>
			<div class="state">订单状态：{{ details.orderStateStr }}</div>
			<img class="rf" src="@/assets/shop-images/order-details-head-bg.png" alt="" />
			<div v-if="details.PAY_MODE == 8" class="statu">
				<div class="tit">融资进度：{{ details.jr ? details.jr.orderStateName : '' }}</div>
				<el-button plain size="mini" @click="goDeta(details.jr)">前往查看></el-button>
			</div>
		</div>
		<div class="main">
			<Title title="商品信息"></Title>
			<div class="table">
				<el-table :data="tableData" style="width: 100%">
					<el-table-column prop="date" label="商品" width="330">
						<template slot-scope="scope">
							<div v-if="scope" class="box">
								<img class="lf" :src="$judgeFile(scope.row.COVER_URL)" alt="" />
								<div class="rf nth2">{{ scope.row.NAME }}</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="NUMBER" label="数量"></el-table-column>
					<el-table-column prop="date" label="商品单价（元）">
						<template slot-scope="scope">
							<span>{{ Number(scope.row.PRICE).toFixed(2) }}</span>
							<span v-if="scope.row.POINT">+{{ scope.row.POINT }}积分</span>
						</template>
					</el-table-column>
					<el-table-column
						v-if="['scene', 'hotel'].includes(shopType)"
						prop="CHECK_DATE"
						:label="shopType === 'scene' ? '游玩时间' : '入住时间'"
					></el-table-column>
					<el-table-column
						v-if="shopType === 'hotel'"
						prop="LEAVE_DATE"
						label="离店时间"
					></el-table-column>
					LEAVE_DATE
					<el-table-column prop="PRICE_REAL" label="小计（元）">
						<template slot-scope="scope">
							<span>{{ Number(scope.row.PRICE_REAL).toFixed(2) }}</span>
							<span v-if="scope.row.POINT_REAL">+{{ scope.row.POINT_REAL }}积分</span>
						</template>
					</el-table-column>
				</el-table>
				<div class="data">
					<div class="cell">
						<div class="lf">下单数量合计：</div>
						<div class="rf">{{ num }}</div>
					</div>
					<!-- <div class="cell">
						<div class="lf">出库数量合计：</div>
						<div class="rf">SP38475733302093847</div>
					</div> -->
					<div class="cell">
						<div class="lf">订单金额：</div>
						<div class="rf">
							{{ Number(details.ORDER_MONEY).toFixed(2) }}
							<span v-if="details.ORDER_POINT">+{{ details.ORDER_POINT }}积分</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="main">
			<Title title="订单信息"></Title>
			<div class="form">
				<div class="box">
					<div class="label">订单编号：</div>
					<div class="val">{{ details.ORDER_ID || '-' }}</div>
				</div>
				<div v-if="details.mode" class="box">
					<div class="label">收货方式：</div>
					<div class="val">{{ details.mode == 1 ? '买家自提' : '卖家配送' }}</div>
				</div>
				<div class="box">
					<div class="label">创建时间：</div>
					<div class="val">{{ details.ORDER_TIME || '-' }}</div>
				</div>
				<div class="box">
					<div class="label">成交时间：</div>
					<div class="val">{{ details.PAY_TIME || '-' }}</div>
				</div>
			</div>
			<Title
				v-if="details.distribution"
				:title="details.distribution.mode == 1 ? '自提点信息' : '收货信息'"
			></Title>
			<div v-if="details.distribution" class="form">
				<div v-if="details.distribution.mode != 1" class="box box1">
					<div class="label">收货地址：</div>
					<div class="val">
						{{ details.distribution ? details.distribution.detailAddress : '-' }}
					</div>
				</div>
				<div v-if="details.distribution.mode == 1" class="box box1">
					<div class="label">自提点：</div>
					<div class="val">
						{{ details.offlineStore ? details.offlineStore.name : '-' }}
						<span>{{ details.offlineStore ? details.offlineStore.addrDetail : '-' }}</span>
					</div>
				</div>
				<div v-if="details.distribution.mode == 1" class="box" c>
					<div class="label">联系人：</div>
					<div class="val">{{ details.offlineStore ? details.offlineStore.contact : '-' }}</div>
				</div>
				<div v-if="details.distribution.mode == 1" class="box" c>
					<div class="label">联系电话：</div>
					<div class="val">{{ details.offlineStore ? details.offlineStore.telephone : '-' }}</div>
				</div>
				<div v-if="details.distribution.mode == 1" class="box" c>
					<div class="label">营业时间：</div>
					<div class="val">
						{{ details.offlineStore ? details.offlineStore.openTimeStr : '' }} -
						{{ details.offlineStore ? details.offlineStore.closeTimeStr : '' }}
					</div>
				</div>
			</div>
			<Title title="卖家信息"></Title>
			<div class="form">
				<div class="box">
					<div class="label">店铺名称：</div>
					<div class="val">{{ details.SHOP_NAME || '-' }}</div>
				</div>
				<div class="box">
					<div class="label">店铺地址：</div>
					<el-tooltip
						class="item"
						effect="dark"
						:content="details.SHOP_DETAIL ? details.SHOP_DETAIL : ''"
						placement="top"
					>
						<div class="val nth1">{{ details.SHOP_DETAIL || '-' }}</div>
					</el-tooltip>
				</div>
				<div class="box">
					<div class="label">联系电话：</div>
					<div class="val">{{ details.TELPHONE || '-' }}</div>
				</div>
			</div>
		</div>
		<div v-if="details.bankNo" class="main">
			<Title title="支付信息"></Title>
			<div class="form">
				<div class="box box2">
					<div class="label">收款账号：</div>
					<div class="val">{{ details.bankNo || '-' }}</div>
				</div>
				<div class="box box2">
					<div class="label label1">收款公司名：</div>
					<div class="val">{{ details.bankCompany }}</div>
				</div>
				<div class="box box1">
					<div class="label">开户银行：</div>
					<div class="val">{{ details.bankName || '-' }}</div>
				</div>
				<!-- <div class="box box1">
					<div class="label">备注信息：</div>
					<div class="val">-</div>
				</div> -->
			</div>
		</div>
		<div v-if="activities && activities.length > 0" class="main">
			<Title title="物流信息"></Title>
			<div v-if="activities && activities.length > 0" class="list">
				<el-timeline>
					<el-timeline-item
						v-for="(item, index) in activities"
						:key="index"
						:timestamp="item.time"
						:color="index == 0 ? 'rgba(106,193,238,1)' : '#9DA5B7'"
					>
						<div class="rict" :style="{ color: index == 0 ? '#262626' : '#8c8c8c' }">
							{{ item.content }}
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>
			<Empty v-else></Empty>
		</div>
		<div v-if="details.haveConsumeCode" class="main">
			<Title title="核销单"></Title>
			<div v-for="(item, index) in details.codes" :key="index" class="form">
				<div class="box">
					<div class="label">核销状态：</div>
					<div class="val">{{ item.isUsed | uesd }}</div>
				</div>
				<div class="box">
					<div class="label">核销码：</div>
					<div class="val">{{ item.value }}</div>
				</div>
				<div class="box">
					<div class="label">核销方式：</div>
					<div class="val">{{ item.opType | opType }}</div>
				</div>
				<div v-if="item.opType != 2" class="box">
					<div class="label">需核销次数：</div>
					<div class="val">{{ item.opNum }}</div>
				</div>
				<div class="box">
					<div class="label">待核销次数：</div>
					<div class="val">{{ item.opUnNum }}</div>
				</div>
				<div class="box">
					<div class="label">生效时间：</div>
					<div class="val">{{ item.beginDate }}</div>
				</div>
				<!-- <div class="box">
					<div class="label">结束时间：</div>
					<div class="val">{{ item.endDate }}</div>
				</div> -->
				<!-- <div class="box box1">
					<div class="label">备注信息：</div>
					<div class="val">-</div>
				</div> -->
			</div>
		</div>
		<div v-if="contractData" class="main">
			<Title title="在线签约电子合同"></Title>
			<div class="b-box">
				<div class="lf">
					<img src="@/assets/shop-images/ht-bg.jpg" />
					<div class="tit">
						<div class="left">{{ contractData.partyType }}合同</div>
						<div class="right">
							合同状态：
							<span :style="{ color: contractData.signStatus == 0 ? '#CA3F3B' : '#76BF6A' }">
								{{ contractData.signStatusName }}
							</span>
						</div>
					</div>
					<div class="btn">
						<el-button v-if="contractData.signStatus == 0" type="primary" @click="handleContract">
							查看并签署
						</el-button>
						<el-button v-else @click="handleContract">查看详情</el-button>
					</div>
				</div>
				<div v-for="(d, i) of otherContract" :key="i" class="lf">
					<img src="@/assets/shop-images/ht-bg.jpg" />
					<div class="tit">
						<div class="left">{{ d.partyType }}合同</div>
						<div class="right">
							合同状态：
							<span :style="{ color: d.signStatus == 0 ? '#CA3F3B' : '#76BF6A' }">
								{{ d.signStatusName }}
							</span>
						</div>
					</div>
					<div class="btn">
						<el-button @click="handleContract">查看详情</el-button>
					</div>
				</div>
			</div>
		</div>
		<div v-if="imgList && imgList.length > 0" class="content">
			<div class="title">支付凭据</div>
			<div class="img-box">
				<img v-for="(item, index) in imgList" :key="index" :src="$judgeFile(item)" alt="" />
			</div>
		</div>
		<div
			v-if="
				details.ORDER_STATE == 101 && details.PAY_MODE == 9 && details.PAY_MODE != 8 && payState()
			"
			class="content"
		>
			<div class="title">上传支付凭据</div>
			<el-upload
				:action="updUrl"
				list-type="picture-card"
				:on-remove="handleRemove"
				:file-list="fileList"
				:on-success="success"
			>
				<i class="el-icon-plus"></i>
			</el-upload>
		</div>
		<div v-if="details.estimateInfo" class="main mt-10">
			<Title title="评价内容"></Title>
			<div class="box">
				<div class="list">
					<div class="connection">
						<div class="user">您的评价：</div>
						<div class="desc">
							{{ details.estimateInfo ? details.estimateInfo.content : '-' }}
						</div>
						<div class="time">
							{{ details.estimateInfo ? details.estimateInfo.createTime : '-' }}
						</div>
					</div>
					<!-- <div class="business">
						<div class="user">商家评价：</div>
						<div class="desc">感谢您对平台的支持与肯定，我们会做的更好。</div>
						<div class="time">2021-07-22 00:00:00</div>
					</div> -->
				</div>
			</div>
		</div>
		<div class="foot">
			<div class="lf">
				<el-button @click="$router.go(-1)">返回</el-button>
			</div>
			<div class="rf">
				<el-button
					v-if="
						details.ORDER_STATE == 101 &&
						details.PAY_MODE == 9 &&
						details.PAY_MODE != 8 &&
						payState()
					"
					type="info"
				>
					<!-- @click="saveOfflinePayedVoucher" -->
					确认付款
				</el-button>
				<el-button
					v-if="details.ORDER_STATE == 101 && details.PAY_MODE != 9 && details.PAY_MODE != 8"
					type="info"
				>
					<!-- @click="
						router.push({
							path: `/settleAccounts?orderid=${details.ORDER_ID}${
								$route.query.isLaw == 1 ? '&isLaw=1' : ''
							}${$route.query.url ? '&url=' + $route.query.url : ''}`
						})
					" -->
					立即付款
				</el-button>
				<el-button
					v-if="details.CAN_REFUND"
					@click="
						$router.push({
							path: `/drawback?id=${details.ORDER_ID}${$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
								$route.query.url ? '&url=' + $route.query.url : ''
							}`
						})
					"
				>
					申请退款
				</el-button>
				<el-button
					v-if="details.ORDER_STATE == 202"
					type="primary"
					@click="confirmReceipt(details)"
				>
					{{ details.type == 3 ? '服务结束' : '确认收货' }}
				</el-button>
				<el-button v-if="details.applyBill" class="btn" @click="saveBillApply()">
					申请开票
				</el-button>
				<el-button
					v-if="
						details.distribution &&
						details.distribution.mode != 1 &&
						details.updateAddress &&
						details.PAY_MODE != 8
					"
					@click="editAddr()"
				>
					修改地址
				</el-button>
				<el-button
					v-if="details.distribution && details.distribution.mode == 1 && details.updateAddress"
					@click="editAddr()"
				>
					修改自提地址
				</el-button>
				<el-button
					v-if="details.ORDER_STATE == 302 || details.ORDER_STATE == 303"
					size="mini"
					type="primary"
					@click="
						$router.push({
							path: `/appraise?id=${details.ORDER_ID}${$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
								$route.query.url ? '&url=' + $route.query.url : ''
							}`
						})
					"
				>
					立即评价
				</el-button>
				<el-button v-if="details.ORDER_STATE == 101" @click="cancelOrder">取消订单</el-button>
			</div>
		</div>
		<ModifyAddress ref="dia" :item="details" @success="getOrderDetail()"></ModifyAddress>
		<ApplyBill ref="bill" :item="details" @success="getOrderDetail()"></ApplyBill>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
import Title from './../components/title.vue';
import config from '@/config';
import ModifyAddress from './../components/modify_address.vue';
import ApplyBill from './../components/apply_bill.vue';
import { getCookie } from '@/utils/auth';
import isEmpty from '@/utils/isEmpty';

export default {
	name: 'OrderDetails',
	components: {
		Title,
		ModifyAddress,
		ApplyBill
	},
	filters: {
		uesd(i) {
			if (i == 1) {
				return '待使用';
			} else if (i == 2) {
				return '已核销';
			} else if (i == 3) {
				return '使用中';
			} else if (i == 4) {
				return '已激活';
			} else {
				return '已取消';
			}
		},
		opType(i) {
			if (i == 1) {
				return '按次数扣减';
			} else if (i == 2) {
				return '仅记录';
			} else {
				return '计时核销';
			}
		}
	},
	data() {
		return {
			updUrl: baseUrl + '/api/supply-web/fileUpload/singleFile',
			delDia: false,
			delDiaTitle: '',
			type: '',
			tableData: [],
			activities: [],
			id: '',
			details: {},
			num: 0,
			loading: false,
			fileList: [],
			imgList: [],
			userId: '',
			contractData: '',
			otherContract: [],
			main: window.__POWERED_BY_WUJIE__,
			shopType: ''
		};
	},
	created() {
		this.shopType = this.$route.query.shopType || '';
		this.userId = this.isShopLogin();
		this.type = this.$route.query.type;
		if (!this.$route.query.id) {
			this.$message.close();
			this.$message.error('参数错误！');
			this.$router.go(-1);
		} else {
			this.id = this.$route.query.id;
			this.getOrderDetail();
			this.getExpressInfo();
		}
	},
	methods: {
		handleContract(i) {
			this.$router.push({
				path: `contract_details?id=${this.contractData.instanceId}&enterpriseId=${getCookie(
					'enterpriseId'
				)}`
			});
		},
		// 判断金融产品
		payState() {
			if (!this.contractData) {
				return true;
			}
			for (let i = 0; i < this.otherContract.length; i += 1) {
				if (this.otherContract[i].signStatus == 0) {
					return false;
				}
			}
			if (this.contractData.signStatus == 0) {
				return false;
			}
			return true;
		},
		// 打开融资详情
		async goDeta(i) {
			if (i.orderState == 4) {
				if (this.main) {
					top.window.location.href = `${config.domainUrl}${
						config.appList.financeService
					}/#/product/release?redirect=${encodeURIComponent(window.location.href)}&orderId=${i.id}`;
				} else {
					window.open(
						`${config.domainUrl}${
							config.appList.financeService
						}/#/product/release?redirect=${encodeURIComponent(window.location.href)}&orderId=${
							i.id
						}`
					);
				}
			} else {
				let url = config.url.replace(/\./g, '/');
				if (this.main) {
					top.window.location.href = `${config.domainUrl}${config.appList.userCenter}?scwl_main_view=%2Fuser_center_financial_services%2F%23%2Forder%2Fdetail%3Fid%3D${this.details.jr.id}#/${url}/user_center_financial_services/order/index`;
				} else {
					window.open(
						`${config.domainUrl}${config.appList.userCenter}?scwl_main_view=%2Fuser_center_financial_services%2F%23%2Forder%2Fdetail%3Fid%3D${this.details.jr.id}#/${url}/user_center_financial_services/order/index`
					);
				}
			}

			// window.location.href = `${config.domainUrl}${config.appList.userCenter}?scwl_main_view=%2Fuser_center_financial_services%2F%23%2Forder%2Fdetail#/scswl/eimm/wisesoft/net/cn/user_center_financial_services/order/detail?id=${res.results.productId}`;
		},
		// 保存支付凭据
		saveOfflinePayedVoucher() {
			if (this.fileList && this.fileList.length > 0) {
				this.loading = true;
				let voucherFileUrls = this.fileList.map(i => {
					return i.response.results.fileId;
				});
				voucherFileUrls = voucherFileUrls.toString(',');
				const data = {
					orderId: this.id,
					voucherFileUrls
				};
				this.$api.shop_api.saveOfflinePayedVoucher(data).then(res => {
					if (res.state) {
						this.loading = false;
						this.$message.close();
						this.$message({
							message: '支付凭据上传成功，请等待商家确认',
							type: 'success'
						});
						this.getOrderDetail();
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
				});
			} else {
				this.$message.close();
				this.$message.error('请上传支付凭据');
			}
		},
		// 移除图片
		handleRemove(file, fileList) {
			this.fileList = fileList;
		},
		// 上传成功回调
		success(response, file, fileList) {
			this.fileList = fileList;
		},
		// 开票
		saveBillApply() {
			this.$refs.bill.visible = true;
		},
		// 取消订单
		cancelOrder() {
			this.$confirm('确认要取消该订单吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					const res = await this.$api.shop_api.userCancelOrder({
						orderId: this.details.ORDER_ID,
						userId: this.userId
					});
					if (res.state) {
						this.$message.close();
						this.$message({
							message: res.msg || '取消成功',
							type: 'success'
						});
						this.$emit('cancelOrder');
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
					this.loading = false;
				})
				.catch(() => {});
		},
		// 修改地址
		editAddr() {
			this.$refs.dia.visible = true;
		},
		// 确认收货
		confirmReceipt() {
			this.$confirm('我已收到商品，确认收货？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					const res = await this.$api.shop_api.confirmReceipt({
						orderId: this.details.ORDER_ID,
						userId: this.userId
					});
					if (res.state) {
						this.$message.close();
						this.$message({
							message: res.msg || '收货成功',
							type: 'success'
						});
						this.getOrderDetail();
						this.getExpressInfo();
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
					this.loading = false;
				})
				.catch(() => {});
		},
		// 判断icon
		orderType(value) {
			if (value == 101 || value == 102 || value == 200) {
				return 'icon-daifukuanb';
			} else if (value == 201) {
				return 'icon-daifahuo';
			} else if (value == 202) {
				return 'icon-daishouhuo';
			} else if (value == 203 || value == 301 || value == 302 || value == 303) {
				return 'icon-check-circle';
			} else {
				return 'icon-time';
			}
		},
		// 获取物流信息
		getExpressInfo() {
			this.$api.shop_api.getExpressInfo({ orderId: this.id }).then(res => {
				if (res.state) {
					this.activities = res.result.detail || [];
				}
			});
		},
		// 获取订单详情
		getOrderDetail() {
			this.loading = true;
			this.num = 0;
			this.$api.shop_api.getOrderDetail({ orderId: this.id }).then(async res => {
				if (res.state) {
					this.details = res.result;
					this.tableData = this.details.ORDER_GOODS_DETAILS;
					this.details.addressInfo = this.details.distribution;
					/**主要针对酒店商品，多个同类商品，商品类型在对应的单项数据中*/
					if (isEmpty(this.shopType)) {
						this.shopType = this.tableData[0].regCode || '';
						console.log('this.shopType', this.shopType);
					}
					for (let i = 0; i < this.tableData.length; i += 1) {
						this.num += this.tableData[i].NUMBER;
					}
					this.imgList =
						this.details.otherParams && this.details.otherParams.offlinePayedVoucherFileUrls
							? this.details.otherParams.offlinePayedVoucherFileUrls.split(',')
							: '';
					// let item = {
					// 	path: '/plat/scswl/order/openapi/getOrderState',
					// 	extOrderNo: this.details.ORDER_ID
					// };
					// const results = await this.$api.shop_api.doPostForm(item);
					// if (results.rCode == 0) {
					// 	this.details.jr = results.results;
					// }
					// if (this.details.otherParams && this.details.otherParams.contractData) {
					// 	this.otherContract = [];
					// 	let data = {
					// 		path: '/plat/scswl/platContract/openapi/sign/signStatus',
					// 		instanceId: this.details.otherParams.contractData.instanceId,
					// 		targeIds: [
					// 			this.details.otherParams.contractData.buyer_targetId,
					// 			this.details.otherParams.contractData.seller_targetId
					// 		]
					// 	};
					// 	const res1 = await this.$api.shop_api.doPostJson(data);
					// 	if (res1.rCode == 0) {
					// 		for (let a = 0; a < res1.results.length; a += 1) {
					// 			if (
					// 				this.details.otherParams.contractData.buyer_targetId == res1.results[a].targetId
					// 			) {
					// 				this.contractData = res1.results[a];
					// 			} else {
					// 				this.otherContract.push(res1.results[a]);
					// 			}
					// 		}
					// 	}
					// }
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 打开弹窗
		openDelDia(t) {
			this.delDiaTitle = t;
			this.delDia = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.nth1 {
	overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
	text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
	white-space: nowrap; /*文本不换行*/
}
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.order_details {
	width: 1200px;
	margin: 16px auto;
	.head {
		background: linear-gradient(91deg, #eafbff 0%, #f0e9fc 100%);
		height: 60px;
		display: flex;
		align-items: center;
		padding: 0 15px;
		margin-bottom: 20px;
		position: relative;
		.statu {
			display: flex;
			position: absolute;
			right: 0;
			height: 30px;
			align-items: center;
			right: 19px;
			.tit {
				font-size: 14px;
				font-weight: 400;
				color: #cf5336;
				line-height: 22px;
				margin-right: 12px;
			}
		}
		.icon {
			width: 46px;
			height: 46px;
			background: linear-gradient(180deg, #74a8fc 0%, #3274e0 100%);
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
		}
		.state {
			font-size: 20px;
			font-weight: 500;
			color: #3274e0;
			line-height: 24px;
			margin-left: 11px;
		}
		.rf {
			width: 81px;
			height: 59px;
			margin-left: auto;
		}
	}
	.mt-10 {
		margin-top: 10px;
		.box {
			margin-top: 14px;
			.list {
				padding: 0 0 25px 0;
				.connection {
					padding: 0 5px;
				}
				.user {
					font-size: 16px;
					font-weight: 500;
					color: #262626;
					line-height: 24px;
				}
				.desc {
					margin-top: 9px;
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
				}
				.time {
					font-size: 12px;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 20px;
				}
				.business {
					margin-top: 15px;
					background: #f7f8fa;
					border-radius: 8px;
					padding: 12px 12px;
				}
			}
		}
	}
	.main {
		padding: 16px 15px;
		border: 1px solid #d9d9d9;
		margin-bottom: 12px;
		.b-box {
			margin-top: 21px;
			display: flex;
			justify-content: space-between;
			.lf {
				width: 428px;
				height: 120px;
				background: #ffffff;
				border-radius: 8px 8px 8px 8px;
				opacity: 1;
				border: 1px solid #f6a09d;
				position: relative;
				border-radius: 8px;
				padding: 16px 19px 16px 17px;
				box-sizing: border-box;
				> img {
					width: 100%;
					height: 68px;
					top: 0;
					left: 0;
					position: absolute;
				}
				.tit {
					display: flex;
					width: 100%;
					.left {
						font-size: 16px;
						font-family: Source Han Sans SC-Medium, Source Han Sans SC;
						font-weight: 500;
						color: #262626;
						line-height: 24px;
					}
					.right {
						margin-left: auto;
						font-size: 14px;
						font-weight: 400;
						color: #404040;
						line-height: 22px;
						span {
							color: #ca3f3b;
						}
					}
				}
				.btn {
					margin-top: 19px;
				}
			}
		}

		.box {
			margin-top: 14px;
		}
		.table {
			margin-top: 22px;
			::v-deep.el-table th {
				background: #f6f6f6;
			}
			.box {
				display: flex;
				align-items: center;
				.lf {
					width: 60px;
					height: 60px;
					object-fit: contain;
				}
				.rf {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					width: calc(100% - 70px);
					margin-left: auto;
				}
			}
			.data {
				margin-top: 12px;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				.cell {
					display: flex;
					align-items: center;
					margin-left: 25px;
					.lf {
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
					}
					.rf {
						font-size: 16px;
						font-weight: 500;
						color: #404040;
					}
				}
			}
		}
		.form {
			margin: 17px 0;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			.box {
				width: 32%;
				display: flex;
				.label {
					width: 80px;
					height: 36px;
					font-size: 14px;
					font-weight: 400;
					color: #9da5b7;
					line-height: 36px;
				}
				.label {
					width: 90px;
				}
				.val {
					width: calc(100% - 80px);
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					height: 36px;
					line-height: 36px;
					span {
						font-size: 12px;
						margin-left: 15px;
						font-weight: 400;
						color: #8c8c8c;
					}
				}
			}
			.box1 {
				width: 100%;
			}
			.box2 {
				width: 50%;
			}
		}
		.form1 {
			.box {
				.label {
					width: 110px;
				}
				.val {
					width: calc(100% - 110px);
				}
			}
		}
		.list {
			margin-top: 16px;
			padding: 0 12px;
		}
	}
	.content {
		margin-top: 24px;
		.title {
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			margin-bottom: 8px;
		}
		::v-deep.el-upload--picture-card {
			width: 80px;
			height: 80px;
			background: #f5f5f5;
			line-height: 90px;
		}
		.img-box {
			display: flex;
			flex-wrap: wrap;
			img {
				width: 140px;
				height: 140px;
				margin-right: 10px;
				border-radius: 6px;
			}
		}
	}
	.foot {
		margin-top: 129px;
		display: flex;
		align-items: center;
		.rf {
			margin-left: auto;
		}
	}
	::v-deep.el-upload-list--picture-card .el-upload-list__item {
		width: 80px;
		height: 80px;
	}
}
</style>
