<template>
	<div class="person">
		<div class="person-left">
			<div class="person-left-con">
				<el-menu
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
				>
					<!--					<el-submenu index="1">-->
					<!--						<template slot="title">-->
					<!--							<i class="el-icon-location"></i>-->
					<!--							<span>导航一</span>-->
					<!--						</template>-->
					<!--						<el-menu-item-group>-->
					<!--							<el-menu-item index="1-1">选项1</el-menu-item>-->
					<!--							<el-menu-item index="1-2">选项2</el-menu-item>-->
					<!--						</el-menu-item-group>-->
					<!--					</el-submenu>-->
					<el-menu-item v-for="menu of menus" :key="menu.index" :index="menu.index">
						<span slot="title">{{ menu.name }}</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb>
			<personHeader :title="activeName"></personHeader>
			<component
				:is="activeComponent"
				:key="params.key || ''"
				:is-main="false"
				:params="params"
			></component>
		</div>
	</div>
</template>

<script>
import personalHomePage from '@/skill-treasure-views/personal-homepage';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import interestList from '@/skill-treasure-views/interest-list';
import noticeList from '@/skill-treasure-views/notice-list';
import personHeader from '@/components/person-header';

export default {
	components: {
		personalHomePage,
		subBreadcrumb,
		interestList,
		noticeList,
		personHeader
	},
	data() {
		return {
			activeMenu: '2-1',
			activeComponent: 'personalHomePage',
			activeName: '我的作品',
			menus: [
				// {
				// 	name: '我的主页',
				// 	index: '1-1',
				// 	path: 'personalHomePage',
				// 	params: { key: 1 }
				// },
				{
					name: '我的作品',
					index: '2-1',
					path: 'personalHomePage',
					params: { key: 3, showActiveCode: 'works' }
				},
				{
					name: '我的通知',
					index: '3-1',
					path: 'noticeList'
				},
				{
					name: '我的关注',
					index: '4-1',
					path: 'interestList',
					params: { key: 1 }
				},
				{
					name: '我的粉丝',
					index: '5-1',
					path: 'interestList',
					params: { code: 'fans', key: 2 }
				},
				{
					name: '我的喜欢',
					index: '6-1',
					path: 'personalHomePage',
					params: { key: 4, showActiveCode: 'like', type: 1 }
				},
				{
					name: '我的收藏',
					index: '7-1',
					path: 'personalHomePage',
					params: { key: 2, showActiveCode: 'collect', type: 2 }
				}
			],
			params: { key: 3, showActiveCode: 'works' }
		};
	},
	created() {},
	methods: {
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.menus.find(item => {
				return item.index === index;
			});
			if (currentObj.params) {
				this.params = currentObj.params;
			} else {
				this.params = {};
			}
			this.activeMenu = currentObj.index;
			this.activeName = currentObj.name;
			this.activeComponent = currentObj.path;
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
	}
	&-right {
		width: calc(100% - 236px);
	}
}
::v-deep .el-menu-item {
	padding-left: 40px !important;
}
::v-deep .is-active {
	position: relative;
	&::before {
		content: '';
		display: inline-block;
		position: absolute;
		left: 20px;
		top: calc(50% - 7px);
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background: #3274e0;
	}
}
</style>
