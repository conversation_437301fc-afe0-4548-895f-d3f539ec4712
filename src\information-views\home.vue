<template>
	<div class="information-box">
		<!-- 教育资讯页面 -->
		<!-- 顶部大图部分 -->
		<div class="top-box" :style="{ backgroundImage: `url(${adUrl})` }">
			<div class="top-title-box">
				<span class="title">教育资讯</span>
				<span class="title-desc">Educational Information</span>
			</div>
		</div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<!-- 中间内容区域 -->
		<!-- 最新动态区域 -->
		<div v-loading="newsListLoading" class="content-box latest-box">
			<div class="title-box">
				<span class="title">最新动态</span>
				<span class="title-desc">Latest development of</span>
			</div>
			<!-- 内容区域 -->
			<div v-if="newsList && newsList.length" class="latest-content">
				<!-- 左边区域 -->
				<div class="content-left">
					<div class="carousel-box">
						<el-carousel
							ref="carousel"
							trigger="click"
							height="440px"
							arrow="never"
							:autoplay="false"
							indicator-position="none"
							@change="carouselChange"
						>
							<el-carousel-item v-for="(item, index) in newsList.slice(0, 3)" :key="index">
								<img class="item-carousel-img" :src="getYbzyImg(item.coverImg)" alt="" />
							</el-carousel-item>
						</el-carousel>
						<!-- 展示的底部内容 -->
						<div class="carousel-bottom">
							<div class="carousel-left">
								<span class="day">{{ newsList[carouselIndex].publishTime | dateShow('day') }}</span>
								<span class="time">
									{{ newsList[carouselIndex].publishTime | dateShow('mouths', '.') }}
								</span>
							</div>
							<div class="carousel-poits">
								<template v-for="(item, index) in newsList.slice(0, 3)">
									<span
										:key="index"
										:class="[index == carouselIndex ? 'poit-active' : '', 'poit']"
										@click="poitClick(item)"
									></span>
								</template>
							</div>
							<div class="carousel-right">
								<span class="prev" title="上一个" @click="carouselPrevNext('prev')">
									<!-- <i class="el-icon-back carousel-icon"></i> -->
								</span>
								<span class="next" title="下一个" @click="carouselPrevNext('next')">
									<!-- <i class="el-icon-right carousel-icon"></i> -->
								</span>
							</div>
						</div>
					</div>
					<div class="text-content" @click="toDetail(newsList[carouselIndex])">
						<div class="text-title u-line-1">{{ newsList[carouselIndex].title }}</div>
						<div class="text-desc u-line-2">{{ newsList[carouselIndex].abstract }}</div>
					</div>
				</div>
				<!-- 右边区域 -->
				<div class="content-right">
					<div
						v-for="(item, index) in newsList.slice(3, 7)"
						:key="index"
						class="item-card"
						@click="toDetail(item)"
					>
						<div class="time-box">
							<span class="time-day">{{ item.publishTime | dateShow('day') }}</span>
							<span class="time-mouth">{{ item.publishTime | dateShow }}</span>
						</div>
						<div class="text-content">
							<div class="text-title u-line-1">{{ item.title }}</div>
							<div class="text-desc u-line-2">{{ item.abstract }}</div>
						</div>
					</div>
				</div>
			</div>
			<Empty v-else :tips="'暂无数据'" />
			<!--  -->
			<div class="latest-more">
				<span class="more-btn" @click="toList('dynamicNews')">查看更多>></span>
			</div>
		</div>
		<!-- 政策解读 -->
		<div v-loading="policyListLoading" class="content-box policy-box">
			<div class="title-box">
				<span class="title">政策解读</span>
				<span class="title-desc">Policy interpretation</span>
			</div>
			<!-- 内容区域 -->
			<div v-if="policyList && policyList.length" class="policy-content">
				<!--  -->
				<div class="policy-left" @click="toDetail(policyList[0])">
					<img class="policy-left-img" :src="getYbzyImg(policyList[0].coverImg)" alt="" />
					<span class="policy-left-title u-line-2">
						{{ policyList[0].title }}
					</span>
					<span class="policy-left-time">
						<span>{{ policyList[0].publishTime }}</span>
						<span>
							<i class="el-icon-view view-icon"></i>
							{{ policyList[0].viewNum || 0 }}
						</span>
					</span>
					<span class="policy-left-desc u-line-2">
						{{ policyList[0].abstract }}
					</span>
					<div class="policy-left-more">
						<span class="left-more-btn">查看详情 >></span>
					</div>
				</div>
				<div class="policy-right">
					<div
						v-for="(item, index) in policyList.slice(1, 5)"
						:key="index"
						class="policy-right-card"
						@click="toDetail(item)"
					>
						<span class="policy-right-title u-line-2">
							{{ item.title }}
						</span>
						<span class="policy-right-desc u-line-2">
							{{ item.abstract }}
						</span>
						<span class="policy-right-time">
							<span>{{ item.publishTime }}</span>
							<span>
								<i class="el-icon-view view-icon"></i>
								{{ item.viewNum || 0 }}
							</span>
						</span>
					</div>
				</div>
			</div>
			<Empty v-else :tips="'暂无数据'" />
			<div class="policy-more">
				<span class="more-btn" @click="toList('policyReading')">查看更多>></span>
			</div>
		</div>
		<!-- 职教视界 -->
		<div v-loading="educationListLoading" class="content-box education-box">
			<div class="title-box">
				<span class="title">职教视界</span>
				<span class="title-desc">Vocational education horizon</span>
			</div>
			<div v-if="educationList && educationList.length" class="education-content">
				<template v-for="(item, index) in educationList">
					<div v-if="index < 3" :key="index" class="card-list">
						<div class="item-card" @click="toDetail(item)">
							<img class="item-img" :src="getYbzyImg(item.coverImg)" alt="" />
							<div class="text-content">
								<span class="item-title u-line-2">
									{{ item.title }}
								</span>
								<span class="item-desc u-line-2">
									{{ item.abstract }}
								</span>
								<span class="item-time">
									<span>{{ item.publishTime }}</span>
									<span>
										<i class="el-icon-view view-icon"></i>
										{{ item.viewNum || 0 }}
									</span>
								</span>
							</div>
						</div>
					</div>
				</template>
			</div>
			<Empty v-else :tips="'暂无数据'" />
			<div class="education-more">
				<span class="more-btn" @click="toList('educationView')">查看更多>></span>
			</div>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import { baseUrl } from '@/config';
export default {
	components: {
		subBreadcrumb
	},
	filters: {
		// 用于处理正常时间格式换成年月与日分开
		dateShow(str, type, space) {
			let strTime = '';
			let dateArray = str.split(' ')[0].split('-');
			if (type == 'day') {
				strTime = dateArray[2];
			} else {
				if (space == '.') {
					strTime = dateArray[0] + '.' + dateArray[1];
				} else {
					strTime = dateArray[0] + '年' + dateArray[1] + '月';
				}
			}
			return strTime;
		}
	},
	data() {
		return {
			baseUrl,
			adUrl: '',
			carouselIndex: 0, // 轮播下标
			newsList: [], //最新动态数据
			policyList: [], // 政策解读数据
			educationList: [], //职教视界数据
			newsListLoading: false, //加载动画--最新动态
			policyListLoading: false, //加载动画--政策解读
			educationListLoading: false //加载动画--职教视界
		};
	},
	mounted() {
		this.getAdvertsByCode();
		// dynamicNews	最新动态 policyReading	政策解读 educationView	职教视界
		this.getInformation('dynamicNews', 'newsList', 7);
		this.getInformation('policyReading', 'policyList', 5);
		this.getInformation('educationView', 'educationList', 3);
	},
	methods: {
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		async getAdvertsByCode() {
			const { result } = await this.$api.shop_api.getAdvertsByCode({
				sysCode: 'pc_index_middle',
				siteId: this.getSiteId() // 租户id
			});
			let url = result.adData[0].url || '';
			this.adUrl = this.$judgeFile(url);
		},

		/**
		 * @description 幻灯片轮播触发事件
		 * */
		carouselChange(index) {
			this.carouselIndex = index;
		},
		// 自定义轮播点点击事件，主动触发轮播的切换
		poitClick(index) {
			this.$refs.carousel.setActiveItem(index);
		},
		// 自定义轮播前后点击事件，主动触发轮播前一个和后一个的切换
		carouselPrevNext(type) {
			if (type == 'prev') {
				this.$refs.carousel.prev();
			} else {
				this.$refs.carousel.next();
			}
		},
		/**
		 * @description 获取列表
		 * */
		getInformation(code, list, listSize) {
			this[list + `loading`] = true;
			let data = {
				nodeCode: code,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				pageNum: 1,
				pageSize: listSize || 10
			};
			this.$api.information_api.paging(data).then(res => {
				this[list] = res?.results?.records || [];
				this[list + `loading`] = false;
			});
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		// 跳转详情页面
		toDetail(item) {
			// 显示类型(2:普通、1:外链)
			if (item.infoType == 2) {
				this.jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`);
			} else {
				window.open(item.linkUrl);
			}
		},
		// 跳转详情页面
		toList(code) {
			this.jumpPage(`/information-list?code=${code}`);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
@text-desc {
	font-size: 14px;
	font-weight: 400;
	color: #333333;
}
.information-box {
	background: #ffffff;
}
.top-box {
	width: 100%;
	height: 400px;
	background: gray;
	display: flex;
	align-items: center;
	font-family: Microsoft YaHei;

	background-size: cover;
	.top-title-box {
		width: $max-width;
		margin: 0 auto;
	}
	.title {
		font-size: 72px;
		font-weight: bold;
		color: #ffffff;
		line-height: 46px;
		display: block;
		margin-bottom: 28px;
	}
	.title-desc {
		font-size: 24px;
		color: #ffffff;
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
// 内容区域标题统一样式
.title-box {
	width: $max-width;
	margin: 48px auto 0;
	text-align: center;
	.title {
		font-size: 40px;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 30px;
	}
	.title-desc {
		font-size: 18px;
		color: #999999;
	}
}
.content-box {
	width: $max-width;
	margin: 0 auto;
	border-top: 0.1px solid transparent;
}
// 查看更多按钮
.more-btn {
	width: 140px;
	height: 48px;
	background: var(--brand-6, #0076e8);
	border-radius: 24px;
	font-size: 16px;
	color: #ffffff;
	line-height: 48px;
	text-align: center;
	display: inline-block;
	cursor: pointer;
}
.latest-box {
	position: relative;
	.latest-more {
		position: absolute;
		bottom: 50px;
		right: 0;
	}
}
.latest-content {
	display: flex;
	justify-content: space-between;
	margin-top: 40px;
	padding-bottom: 70px;

	.content-left {
		width: 660px;
		.carousel-box {
			position: relative;
		}
		.item-carousel-img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		.carousel-bottom {
			width: 100%;
			height: 68px;
			background: rgba(0, 0, 0, 0.3);
			position: absolute;
			bottom: 0;
			left: 0;
			z-index: 2;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.carousel-left {
				width: 113px;
				height: 68px;
				// background: #2386ee;
				background: url('~@/assets/info-images/carousel-left.png') center;
				background-size: cover;
				color: #ffffff;
				display: flex;
				flex-direction: column;
				justify-content: center;
				// align-items: center;
				padding-left: 20px;
				.day {
					display: block;
					font-size: 26px;
				}
				.time {
					font-size: 14px;
				}
			}
			.carousel-poits {
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.poit {
				width: 12px;
				height: 12px;
				background: #ffffff;
				border-radius: 50%;
				margin: 0 4px;
				cursor: pointer;
			}
			.poit-active {
				background: var(--brand-6, #0076e8);
			}
			.carousel-right {
				width: 122px;
				height: 45px;
				display: flex;
				align-items: center;
				background: url('~@/assets/info-images/carousel-right.png') center;
				background-size: cover;
				.prev,
				.next {
					display: inline-block;
					width: 100%;
					height: 100%;
					text-align: center;
					color: #ffffff;
					line-height: 45px;
					cursor: pointer;
				}
				.prev {
					// background: #ff9f34;
				}
				.next {
					// background: #0076e8;
				}
			}
		}
		.text-content {
			margin-top: 27px;
			cursor: pointer;
			.text-title {
				font-size: 18px;
				color: #5b5b5b;
			}
			.text-desc {
				font-size: 14px;
				color: #7a8392;
				margin-top: 27px;
			}
		}
	}
	.content-right {
		width: 560px;
		.item-card {
			display: flex;
			height: 70px;
			margin-bottom: 68px;
			align-items: center;
			cursor: pointer;
		}
		.time-box {
			display: flex;
			width: 100px;
			height: 66px;
			padding-right: 12px;
			border-right: 2px solid #e5e5e5;
			text-align: center;
			flex-direction: column;
			flex-shrink: 0;
			.time-day {
				height: 49px;
				font-size: 60px;
				font-family: FZLanTingHeiS-UL-GB;
				font-weight: 400;
				color: #e40001;
				line-height: 44px;
			}
			.time-mouth {
				font-size: 14px;
				color: #999999;
				display: inline-block;
				margin-top: 9px;
			}
		}
		.text-content {
			margin-left: 22px;
			width: calc(100% - 100px - 22px);
			.text-title {
				font-size: 16px;
				color: #5b5b5b;
			}
			.text-desc {
				font-size: 14px;
				color: #7a8392;
				margin-top: 19px;
			}
		}
	}
}
.policy-box {
	padding-bottom: 50px;
	width: 100%;
	background: url('~@/assets/info-images/policy-bg.png') center;
	background-size: cover;
	.policy-more {
		text-align: center;
		margin-top: 32px;
	}
}
.policy-content {
	display: flex;
	width: $max-width;
	margin: 0 auto;
	margin-top: 44px;
	.policy-left {
		width: 440px;
		padding-right: 20px;
		border-right: 1px solid #dddddd;
		cursor: pointer;
		&-img {
			width: 420px;
			height: 290px;
			object-fit: cover;
		}
		&-title {
			height: 49px;
			font-size: 18px;
			font-weight: bold;
			color: #333333;
			margin-top: 27px;
		}
		&-time {
			font-size: 14px;
			color: #333333;
			display: flex;
			justify-content: space-between;
			margin-top: 35px;
		}
		&-desc {
			font-size: 14px;
			color: #333333;
			margin-top: 35px;
		}
		&-more {
			margin-top: 18px;
			border-top: 1px solid #dddddd;
		}
		.left-more-btn {
			display: inline-block;
			font-size: 16px;
			line-height: 16px;
			color: #333333;
			margin-top: 20px;
			cursor: pointer;
		}
	}
	.policy-right {
		display: flex;
		flex-wrap: wrap;
		&-card {
			width: 50%;
			height: 270px;
			padding: 39px 46px 47px 38px;
			flex-shrink: 0;
			cursor: pointer;
			&:nth-child(2n) {
				border-left: 1px solid #dddddd;
			}
			&:nth-child(1),
			&:nth-child(2) {
				border-bottom: 1px solid #dddddd;
			}
		}
		&-title {
			height: 49px;
			font-size: 18px;
			font-weight: bold;
			color: #333333;
			margin-top: 22px;
		}
		&-time {
			font-size: 14px;
			color: #333333;
			display: flex;
			justify-content: space-between;
			margin-top: 52px;
		}
		&-desc {
			font-size: 14px;
			color: #333333;
			margin-top: 35px;
		}
	}
}
.education-box {
	padding-bottom: 50px;
	.education-more {
		text-align: center;
		margin-top: 32px;
	}
}
.education-content {
	margin-top: 48px;
	display: flex;
	justify-content: space-between;
	.card-list {
		width: 33.3%;
	}
	.item-card {
		width: 400px;
		height: 540px;
		background: #ffffff;
		box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
		cursor: pointer;
		.item-img {
			width: 400px;
			height: 266px;
			object-fit: cover;
		}
		.text-content {
			padding: 28px 30px 0;
		}
		.item-title {
			font-size: 18px;
			font-weight: bold;
			color: #333333;
		}
		.item-desc {
			font-size: 14px;
			font-weight: 400;
			color: #333333;
			margin-top: 35px;
		}
		.item-time {
			font-size: 14px;
			font-weight: 400;
			color: #333333;
			display: inline-block;
			margin-top: 84px;
			display: flex;
			justify-content: space-between;
		}
	}
}
.view-icon {
	margin-right: 9px;
}
</style>
