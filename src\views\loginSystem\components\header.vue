<template>
	<div class="login-header">
		<div></div>
		<!-- <img src="@/assets/images/login/login-logo.png" alt="" class="login-header-left" /> -->
		<div class="login-header-right">
			<img src="@/assets/images/login/download.png" class="login-header-right-img" alt="" />
			<div class="login-header-right-text">移动应用</div>
			<img src="@/assets/images/login/phone.png" class="login-header-right-img" alt="" />
			<div class="login-header-right-phone">0831-8275466</div>
		</div>
		<img src="@/assets/images/login/login-logo.png" alt="" class="login-header-left" />
	</div>
</template>

<script>
export default {
	name: 'Header'
};
</script>

<style scoped lang="scss">
.login-header {
	position: absolute;
	top: 19px;
	left: 40px;
	width: calc(100% - 80px);
	display: flex;
	align-items: flex-start;
	justify-content: space-between;

	&-left {
		position: absolute;
		height: 78px;
		top: -20px;
		left: 50%;
		transform: translate(-50%, 0);
	}

	&-right {
		display: flex;
		align-items: center;

		&-img {
			margin-right: 11px;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			border: 1px solid #fff;
			padding: 3px;
			cursor: pointer;
		}

		&-text {
			font-size: 14px;
			font-family: MicrosoftYaHei;
			color: #ffffff;
			line-height: 22px;
			margin-right: 38px;
		}

		&-phone {
			font-size: 14px;
			font-family: MicrosoftYaHei;
			color: #ffffff;
			line-height: 22px;
		}
	}
}
</style>
