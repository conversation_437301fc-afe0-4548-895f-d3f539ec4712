<template>
	<div class="main">
		<!-- 对我感兴趣 -->
		<h3 class="form-title">对我感兴趣</h3>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="postName" label="职位名称"></el-table-column>
			<el-table-column prop="userName" label="姓名"></el-table-column>
			<el-table-column prop="createTiem" label="关注时间"></el-table-column>
		</el-table>
		<!-- 分页 -->
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList"
		/>
	</div>
</template>

<script>
export default {
	name: 'Interest',
	data() {
		return {
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [] //职位列表
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getFollowPostList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
</style>
