<template>
	<div class="tab-search-content">
		<div class="tab-content">
			<el-tabs v-model="tabData.activeName" @tab-click="handleClick">
				<el-tab-pane
					v-for="(item, index) in tabData.tabPane"
					:key="index"
					:label="item.label"
					:name="item.name"
				></el-tab-pane>
			</el-tabs>
		</div>
		<div v-if="searchData && searchData.length > 0" class="search-content">
			<el-form :inline="true" :model="formData" class="demo-form-inline" @submit.native.prevent>
				<div v-for="(item, index) in searchData" :key="index" class="form-item">
					<!-- S 插槽 -->
					<el-form-item v-show="!item.hide" v-if="item.type === 'slot'" :label="item.label">
						<slot :name="item.slotName"></slot>
					</el-form-item>
					<!-- E 插槽 -->
					<!-- S input/textarea-->
					<el-form-item
						v-show="!item.hide"
						v-if="item.type === 'text' || item.type === 'textarea'"
						:label="item.label"
					>
						<el-input
							:ref="item.model"
							v-model.trim="formDataSet[item.model]"
							:type="item.type"
							:placeholder="item.placeholder"
							resize="none"
							:rows="1"
							:style="{ width: item.width + 'px' }"
							:maxlength="item.maxlength"
							clearable
						></el-input>
					</el-form-item>
					<!-- E input/textarea -->
					<!-- S select -->
					<el-form-item v-show="!item.hide" v-if="item.type === 'select'" :label="item.label">
						<el-select
							v-model="formDataSet[item.model]"
							:popper-append-to-body="false"
							:placeholder="item.placeholder"
							:style="{ width: item.width + 'px' }"
							clearable
							@change="
								newVal => {
									item.handle && item.handle(newVal);
								}
							"
						>
							<el-option
								v-for="(optionItem, optionIndex) in item.options"
								:key="optionIndex"
								:label="optionItem[item.optionLabel]"
								:value="optionItem[item.optionValue]"
							></el-option>
						</el-select>
					</el-form-item>
					<!-- E select -->
					<!-- S 时间日期选择 -->
					<el-form-item
						v-show="!item.hide"
						v-if="item.type === 'daterange' || item.type === 'datetimerange'"
						v-model="formDataSet[item.model]"
						:label="item.label"
					>
						<el-date-picker
							v-model="formDataSet[item.model]"
							:type="item.type"
							:start-placeholder="item.sPlaceholder"
							:end-placeholder="item.ePlaceholder"
							:default-time="item.defaultTime"
							:value-format="item.valueFormat"
						></el-date-picker>
					</el-form-item>
					<!-- E 时间日期选择 -->
					<!-- S 级联选择器 -->
					<el-form-item v-show="!item.hide" v-if="item.type === 'cascader'" :label="item.label">
						<el-cascader
							:key="item.treeData.length"
							v-model="formDataSet[item.model]"
							:append-to-body="false"
							:options="item.treeData"
							:props="item.cascaderProps || { checkStrictly: true }"
							clearable
							:style="{ width: item.width + 'px' }"
							filterable
							:disabled="item.disabled === undefined ? false : item.disabled"
						></el-cascader>
					</el-form-item>
					<!-- E 级联选择器 -->
				</div>
				<div class="form-item">
					<span v-for="(item, index) in formBottom" :key="index">
						<!-- S 按钮 -->
						<el-form-item
							v-show="!item.hide"
							v-if="item.type === 'button'"
							style="margin-right: 12px"
						>
							<el-button
								:type="item.btnType"
								:icon="item.icon"
								@click="btnHandle(item.handle, index)"
							>
								{{ item.text }}
							</el-button>
						</el-form-item>
						<!-- E 按钮 -->
					</span>
				</div>
			</el-form>
			<div v-if="isHaveHide" class="open-centent">
				<el-button type="text" @click="openMoreHandle">
					{{ openBtn.text }}
					<i
						class="el-icon-d-arrow-left el-icon--right"
						:class="[openBtn.open ? 'open-icon' : 'close-icon']"
					></i>
				</el-button>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Search',
	model: {
		prop: 'formData'
	},
	props: {
		tabClearInput: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		// tab数据
		tabData: {
			type: Object,
			default: () => {
				return {
					tabPane: [], // tab选项数据
					activeName: '' // 默认选中项name
				};
			}
		},
		// 按钮数据
		formBottom: {
			type: Array,
			default: () => {
				return [];
			}
		},
		formData: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 搜索表单数据
		searchData: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			formDataSet: {},
			isHaveHide: false,
			openBtn: {
				text: '展开',
				open: false
			}
		};
	},
	created() {
		for (let i = 0; i < this.searchData.length; i++) {
			if (this.searchData[i].hide) {
				this.isHaveHide = true;
				return;
			}
		}
	},
	methods: {
		// 搜索框文本校验
		validateCharacter(val, event) {
			const REG =
				/^[\u4e00-\u9fa5A-Za-z0-9`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、\s*]+$/;
			let str = val;
			if (!REG.test(str)) {
				// Message.error(`只支持汉字、数字、字母、非特殊符号`)
				str = str.substring(0, str.length - 1);
				setTimeout(() => {
					this.$set(this.formData, event, str);
				}, 300);
			}
			return str;
		},
		// tab 点击事件
		handleClick(tab, event) {
			this.tabClearInputHandle();
			this.$emit('tabClickHandle', tab, event);
		},
		// 按钮点击事件
		btnHandle(handle, index) {
			// debugger
			if (handle && index == 0) {
				// 点击搜索的时候搜索
				Object.assign(this.formData, this.formDataSet);
				this.$route.meta.keepAlive = true;
				handle();
			} else if (handle.name.toLowerCase().indexOf('reset') > -1) {
				// 按钮是重置按钮时触发该逻辑
				this.clearInput();
				handle();
			} else if (handle && index != 0) {
				this.$route.meta.keepAlive = false;
				handle();
			}
		},
		// 展开、收起更多
		openMoreHandle() {
			this.openBtn.open = !this.openBtn.open;
			this.openBtn.text = this.openBtn.open ? '收起' : '展开';
			this.searchData.map(item => {
				if (item.hide !== undefined) {
					item.hide = !item.hide;
				}
			});
		},
		tabClearInputHandle() {
			// 切换tab清空输入框
			if (this.tabClearInput) {
				this.clearInput();
			}
		},
		clearInput() {
			// 清空输入框
			for (const key in this.formDataSet) {
				if (Array.isArray(this.formDataSet[key])) {
					this.formDataSet[key] = [];
				} else if (typeof this.formDataSet[key] === 'string') {
					this.formDataSet[key] = '';
				} else if (typeof this.formDataSet[key] === 'object') {
					this.formDataSet[key] = {};
				} else {
					this.formDataSet[key] = undefined;
				}
			}
			Object.assign(this.formData, this.formDataSet);
		}
	}
};
</script>

<style lang="scss">
.tab-search-content {
	.el-popper.el-cascader__dropdown {
		.el-cascader-panel {
			.el-cascader-node__label {
				max-width: 250px;
			}
		}
	}
	.el-select-dropdown.el-popper {
		.el-select-dropdown__item {
			max-width: 250px;
		}
	}
}
</style>
<style lang="scss" scoped>
.tab-search-content {
	width: 100%;
	background: #ffffff;
	box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.1);
}
.tab-content {
	box-sizing: border-box;
	::v-deep .el-tabs__header {
		margin: 0;
	}
	::v-deep .el-tabs__nav-wrap::after {
		content: '';
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 1px;
		background-color: #e5e5e5;
		z-index: 1;
	}
	::v-deep .el-tabs__item {
		height: 48px;
		line-height: 48px;
		font-size: 14px;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		padding: 0 24px !important;
		box-sizing: border-box;
	}
}
.search-content {
	padding: 16px 20px 0 20px;
	box-sizing: border-box;
	// overflow: hidden;
	.form-item {
		display: inline-block;
		::v-deep .el-form-item__content {
			height: 32px;
			line-height: 32px;
		}
		::v-deep .el-input__inner {
			height: 32px;
			line-height: 32px;
		}
		::v-deep .el-textarea__inner {
			min-height: 32px !important;
		}
		::v-deep .el-input {
			font-size: 13px;
			height: 32px;
			line-height: 32px;
		}
		::v-deep .el-cascader {
			height: 32px;
			line-height: 32px;
		}
		::v-deep .el-form-item__label {
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #222222;
			padding: 0;
			line-height: 32px;
		}
		::v-deep .el-form-item {
			margin: 0 16px 16px 0;
		}
		::v-deep .el-range-editor .el-input__icon {
			line-height: 26px;
		}
		::v-deep .el-range-editor .el-range-separator {
			line-height: 26px;
		}
		::v-deep .el-button {
			padding: 8px 12px;
			box-sizing: border-box;
			border-radius: 2px;
			font-size: 12px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
		}
	}
}
.open-centent {
	width: 100%;
	position: relative;
	text-align: center;
	margin: 0px 0 16px 0;
	padding-bottom: 12px;
	&::before {
		content: '';
		width: 45%;
		height: 1px;
		background-color: #e5e5e5;
		position: absolute;
		left: 0;
		top: 50%;
	}
	&::after {
		content: '';
		width: 45%;
		height: 1px;
		background-color: #e5e5e5;
		position: absolute;
		right: 0;
		top: 50%;
	}
	.close-icon {
		transition: 0.5s;
		transform: rotate(-90deg);
	}
	.open-icon {
		transition: 0.5s;
		transform: rotate(90deg);
	}
}
</style>
