<template>
	<div class="login-form">
		<img :src="logoBj.pcLogo || loginLogo" alt="" class="login-header-left" />
		<!--  表单左边  -->
		<div
			v-if="logoBj.pcLoginLeft"
			class="login-form-left"
			:style="{ backgroundImage: `url(${logoBj.pcLoginLeft || formLeftBj})` }"
		></div>
		<!--  表单右边  -->
		<div class="login-form-right">
			<!-- <div class="login-form-right-title">统一身份认证系统</div> -->
			<div class="login-form-right-tabs">
				<!-- <div class="login-form-right-tabs-tab" @click="type = 'phone'">
					<div class="text" :class="type === 'phone' ? 'select' : ''">手机号登录</div>
					<div :class="type === 'phone' ? 'selectLine' : ''" class="line"></div>
				</div> -->
				<div class="login-form-right-tabs-tab" @click="type = 'user'">
					<div class="text" :class="type === 'user' ? 'select' : ''">账号登录</div>
					<div :class="type === 'user' ? 'selectLine' : ''" class="line"></div>
				</div>
			</div>
			<div class="login-form-right-input">
				<img
					class="login-form-right-input-icon"
					src="@/assets/images/login/phone-icon.png"
					alt=""
				/>
				<input
					v-model="user"
					:maxlength="type === 'phone' ? '11' : ''"
					class="input"
					placeholder="职工号/学号/手机号/身份证号"
					type="text"
				/>
			</div>
			<div v-if="type === 'user'">
				<div class="login-form-right-input">
					<img
						class="login-form-right-input-icon"
						src="@/assets/images/login/password-icon.png"
						alt=""
					/>
					<input
						v-model="password"
						class="input"
						:maxlength="16"
						placeholder="请输入密码"
						:type="passwordType"
					/>
					<img
						class="login-form-right-input-icon password"
						src="@/assets/images/login/kown-password.png"
						alt=""
						@click="changeType"
					/>
					<span class="message">温馨提示：首次登录密码默认为身份证后六位!</span>
				</div>
				<div class="login-form-right-input">
					<img class="login-form-right-input-icon" src="@/assets/images/login/code.png" alt="" />
					<input
						v-model="code"
						class="input"
						autocomplete="off"
						:maxlength="4"
						placeholder="验证码不区分大小写"
					/>
					<VerificationImage
						ref="verifyRef"
						class="img-code"
						:height="42"
						:img-code.sync="imgCode"
					/>
				</div>
				<div class="login-form-right-tip">
					<div class="login-form-right-tip-left">
						<div class="radio">
							<div v-if="!surePassword" class="radio-div" @click="surePassword = true"></div>
							<img
								v-else
								class="radio-icon"
								src="@/assets/images/login/sure-icon.png"
								alt=""
								@click="surePassword = false"
							/>
						</div>
						记住账号
					</div>
					<div class="login-form-right-tip-right">
						<!-- <div>激活账号</div>
						<div style="margin: 0 8px">|</div> -->
						<div @click="resetPassword">忘记密码</div>
					</div>
				</div>
			</div>
			<div v-else class="login-form-right-code">
				<div class="code-input">
					<div class="login-form-right-input code-input-con">
						<img
							class="login-form-right-input-icon"
							src="@/assets/images/login/password-icon.png"
							alt=""
						/>
						<input
							v-model="code"
							maxlength="6"
							class="input code-input-con-i"
							placeholder="请输入短信验证码"
							type="text"
						/>
					</div>
					<div
						class="code-input-button"
						:style="
							userIsOk && time === 60
								? 'background: #0076e8;color: #ffffff'
								: 'background: #f6f7f9;color: #999999'
						"
						@click="sendCodeMethods"
					>
						{{ time === 60 ? '获取验证码' : time }}
					</div>
				</div>
				<div class="code-input-text"></div>
			</div>
			<div class="login-form-right-button" @click="login">登录</div>
			<!-- <div class="login-form-right-tp">
				<div class="radio">
					<div v-if="!sureTp" class="radio-div" @click="changeSureTip"></div>
					<img
						v-else
						class="radio-icon"
						src="@/assets/images/login/sure-icon.png"
						alt=""
						@click="changeSureTip"
					/>
					<div v-if="showTip" class="radio-sure">请阅读并勾选</div>
					<div v-if="showTip" class="tooltip-icon"></div>
				</div>
				我已阅读并同意
				<div class="color" @click="openDialog('隐私政策', '7128298962619994112')">《隐私政策》</div>
				和
				<div class="color" @click="openDialog('用户服务协议', '7128299537076064256')">
					《用户服务协议》
				</div>
			</div> -->
		</div>
		<el-dialog v-loading="dialogLoading" :title="tipTitle" :visible.sync="dialogVisible">
			<!-- eslint-disable-next-line vue/no-v-html -->
			<div v-if="tipContent" class="dialog-content" v-html="tipContent"></div>
			<Empty v-else></Empty>
		</el-dialog>
	</div>
</template>

<script>
import CryptoJS from 'crypto-js';
import { debounce } from 'lodash';
import { Loading } from 'element-eoss';
import { loginShop } from '@/utils/shop_login';
import {
	getTreasureUserInfo,
	getLoginFrontByCode,
	getLoginAdmini
} from '@/utils/treasure_userinfo';
import { mapMutations, mapState } from 'vuex';
import { baseUrl } from '@/config';

import VerificationImage from '@/components/EditProfile/VerificationImage.vue';
export default {
	name: 'Form',
	components: {
		VerificationImage
	},
	data() {
		return {
			tipTitle: '', // 弹窗标题
			dialogVisible: false, // 协议弹窗
			tipContent: '', // 协议内容
			dialogLoading: false,
			loading: null,
			type: 'user', // 登录方式
			passwordType: 'password', // 密码显隐状态
			surePassword: false, // 记住密码
			sureTp: false, // 勾选协议
			showTip: true, //显示勾选提示
			user: '', //账号
			password: '', // 密码
			code: '', // 验证码
			imgCode: null, //验证码-图片
			T: null, // 验证码定时器
			time: 60, // 默认60s
			REG: /^1[3456789]\d{9}$/,
			userIsOk: false,
			redirect: '', // 登录后重定向的路径
			isServer: false,
			defalutreDirect: '/home', //默认的重定向路径，学生路径为/home，教师路径为/independentPersonal/vocational
			loginLogo: require('@/assets/images/login/login-logo.png'),
			formLeftBj: require('@/assets/images/login/login-form-bg.png')
		};
	},
	computed: {
		...mapState('app', ['logoBj'])
	},
	watch: {
		user(newVal) {
			if (this.REG.test(newVal)) {
				this.userIsOk = true;
			} else {
				this.userIsOk = false;
			}
		}
	},
	created() {
		// this.redirect = this.$route?.query?.redirect || this.$route?.query?.service||'';
		const { query: { redirect, service } = {} } = this.$route ?? {};
		this.redirect = redirect ?? service ?? '';

		// vpn回来的跳转逻辑
		const isJump = window.location.href.includes('isJump');
		if (isJump) {
			const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
			this.handleUserInfo(userInfo);
			this.toolJump(userInfo, isJump);
		}

		if (localStorage.getItem('userLoginInfo')) {
			let obj = JSON.parse(localStorage.getItem('userLoginInfo'));
			this.user = obj.user || '';
			this.password = obj.password || '';
		}
		this.isServer = window.location.href.includes('loginServer');
	},
	mounted() {
		// enter 键登录
		document.addEventListener('keydown', this.handleKeyDown);
	},
	beforeDestroy() {
		// 移除事件监听器
		document.removeEventListener('keydown', this.handleKeyDown);
	},
	methods: {
		...mapMutations('user', ['SET_USER_INFO', 'SET_ROLES', 'SET_IDENTITY']),
		handleKeyDown(event) {
			if (event.keyCode === 13) {
				this.login();
			}
		},
		/**打开弹窗*/
		openDialog(title, code) {
			this.tipTitle = title;
			this.tipContent = '';
			this.dialogVisible = true;

			this.handleGetMyMessageInfo(code);
		},
		/**获取详情*/
		handleGetMyMessageInfo(code) {
			this.dialogLoading = true;
			let params = { id: code };
			this.$api.personal_api
				.findCmsDetail(params)
				.then(res => {
					this.dialogLoading = false;
					this.tipContent = res?.results?.detail || '';
				})
				.catch(() => {
					this.dialogLoading = false;
				});
		},
		/**用户登录后，根据登录信息存储用户的身份和权限信息*/
		handleUserInfo(info) {
			let identityInfo = info.identityInfo || [];
			let roles = identityInfo.map(item => {
				return this.$getEnumeration('authority', item.code);
			});
			this.SET_IDENTITY(identityInfo);
			this.SET_ROLES(roles);
			this.isRole(roles);
		},
		// 判断当前用户是否有教师角色，改变展示页面路径
		isRole(roles) {
			let roleL = [1, 2]; // 老师的角色
			for (let role of roles) {
				if (roleL.includes(role)) {
					// this.defalutreDirect = `/independentPersonal/vocational`;
					this.defalutreDirect = `${baseUrl}/project-ybzy/ybzy/index.html#/home?serverId=ybzyDtcSso&authType=6`;
				}
			}
		},
		/**改变密码框的显隐状态*/
		changeType() {
			if (this.passwordType === 'text') {
				this.passwordType = 'password';
			} else {
				this.passwordType = 'text';
			}
		},
		/**改变是否阅读协议状态*/
		changeSureTip() {
			if (this.sureTp) {
				this.sureTp = false;
				this.showTip = true;
			} else {
				this.sureTp = true;
				this.showTip = false;
			}
		},
		/**登录*/
		login: debounce(function () {
			this.$loginOut(true); // 先清除旧的信息

			// if (!this.sureTp) {
			// 	this.$message.warning('请阅读并勾选');
			// 	return;
			// }
			// 密码登录
			if (this.type === 'user') {
				if (!this.user) {
					this.$message.warning('请输入账号');
					return;
				}
				if (!this.password) {
					this.$message.error('请输入密码');
					return;
				}
				const LowerImgCode = this.imgCode.toLowerCase();
				const LowerValue = this.code.toLowerCase();
				if (!LowerValue) {
					this.$message.warning('请输入验证码');
					return;
				} else if (LowerValue !== LowerImgCode) {
					this.$message.warning('验证码错误');
					this.$refs.verifyRef.handleDraw();
					return;
				}
				// 记住密码
				if (this.surePassword) {
					let obj = { user: this.user, password: this.password };
					localStorage.setItem('userLoginInfo', JSON.stringify(obj));
				}

				let data = {
					loginname: this.user,
					password: CryptoJS.MD5(this.password).toString(),
					service: this.$route.query.service || ''
				};
				this.loading = Loading.service({ text: '登录中...', background: 'transparent' });
				// const isServer = window.location.href.includes('loginServer');
				const isServer = this.isServer;
				// loginServer和login不同登录调用不同接口
				const apiFn = isServer ? this.$api.passwordLoginN : this.$api.passwordLogin;
				apiFn(data).then(async res => {
					console.log(res);

					if (res.rCode === 0) {
						try {
							// await Promise.allSettled([
							// 	loginShop(res.results), // 登录技能商城
							// 	// getTreasureUserInfo(), // 大预订登录信息
							// 	getLoginFrontByCode(res.results), // 在线学习登录信息
							// 	getLoginAdmini() // 管理端权限登录
							// ]);
							this.SET_USER_INFO(res.results);
							this.handleUserInfo(res.results);
							// 只有宜职院和本地才登录电商
							if (this.$hasEcomPermission()) {
								await loginShop(res.results); // 登录技能商城（电商）
								await getLoginFrontByCode(res.results); // 在线学习登录信息
								await getTreasureUserInfo(); // 大预订登录信息
							}
							await getLoginAdmini(); // 管理端权限登录
						} catch (error) {
							console.log('单点激活错误', error.msg);
						}

						this.loading.close();
						this.$message.success('登录成功!');
						this.toolJump(res.results, !isServer);
					} else {
						this.loading.close();
						this.$message.error(res.msg);
					}
				});
				// .catch(res => {
				// 	this.loading.close();
				// 	this.$message.error(res.msg);
				// });
			}
			// 验证码登录登录
			else {
				if (!this.user) {
					this.$message.error('请输入账号');
					return;
				}
				if (!this.code) {
					this.$message.error('请输入验证码');
					return;
				}
				let obj = { user: this.user };
				localStorage.setItem('userLoginInfo', JSON.stringify(obj));
				let data = {
					phone: this.user,
					verifyCode: this.code
				};
				this.loading = Loading.service({ text: '登录中...', background: 'transparent' });
				this.$api
					.codeLogin(data)
					.then(async res => {
						if (res.rCode === 0) {
							this.SET_USER_INFO(res.results);
							this.handleUserInfo(res.results); // 处理用户信息，将用户身份存储起来
							await loginShop(res.results); // 登录技能商城
							await getTreasureUserInfo(); // 大预订的登录信息
							await getLoginFrontByCode(res.results); // 在线学习登录信息
							this.loading.close();
							this.$message.success('登录成功!');
							// this.$router.push(this.redirect ? this.redirect : this.defalutreDirect);
							this.toolJump(res.results, true);
						} else {
							this.loading.close();
							this.$message.error(res.msg);
						}
					})
					.catch(res => {
						this.loading.close();
						this.$message.error(res.msg);
					});
			}
		}, 500),
		/**发送验证码*/
		sendCodeMethods: debounce(function () {
			if (this.userIsOk) {
				this.loading = Loading.service({ text: '发送中...', background: 'transparent' });
				this.$api.sendCode({ phone: this.user }).then(res => {
					this.loading.close();
					if (res.rCode === 0) {
						this.$message.success('发送成功!');
						this.time = 59;
						this.T = setInterval(() => {
							this.time -= 1;
							if (this.time === 0) {
								clearInterval(this.T);
								this.T = null;
								this.time = 60;
							}
						}, 500);
					} else {
						this.$message.error(res.msg);
					}
				});
			}
		}, 500),
		// loginServer和login不同跳转  isJump-是否走正常跳转逻辑
		toolJump(userInfo, isJump = false) {
			if (isJump) {
				if (this.redirect) {
					if (this.redirect.includes('http')) {
						window.location.href = this.redirect;
					} else {
						this.$router.push(this.redirect);
					}
					return;
				}
				// 正常跳转
				if (this.defalutreDirect == '/home') {
					this.$router.push(this.redirect ? this.redirect : this.defalutreDirect);
				} else {
					window.open(this.defalutreDirect, '_self');
				}
			} else {
				// 走vpn跳转
				if (userInfo.service) {
					window.location.href = userInfo.service;
				}
			}
		},
		// 重置密码
		resetPassword() {
			this.$router.push('/loginReset');
		}
	}
};
</script>

<style scoped lang="scss">
.login-form {
	// width: 400px;
	//height: 500px;
	background: rgba(0, 0, 0, 0.4);
	border-radius: 16px;
	display: flex;
	position: relative;
	// overflow: hidden;

	// transform: translate(-30%, 0);
	// left: 25%;
	.login-header-left {
		position: absolute;
		// top: ;
		// width: 420px;
		height: 78px;
		top: -93px;
		right: calc(50% - 210px);
		// left: 50%;
		// transform: translate(50%, 0);
	}
	&-left {
		width: 600px;
		flex-shrink: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		// background: url('~@/assets/images/login/login-form-bg.png');
		background-size: cover;
		border-radius: 16px 0 0 16px;
		overflow: hidden;
		// margin-top: 135px;
		// .code {
		// 	width: 210px;
		// 	height: 236px;
		// 	margin-bottom: 23px;
		// }
		// .text {
		// 	font-size: 16px;
		// 	font-family: Microsoft YaHei;
		// 	font-weight: 400;
		// 	color: #ffffff;
		// 	line-height: 16px;
		// }
	}
	&-right {
		flex: 1;
		// background: rgba(0, 0, 0, 0.45);
		border-radius: 0 8px 8px 0;
		padding: 38px 30px 0;
		&-title {
			font-size: 30px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 30px;
			margin-bottom: 42px;
			text-align: center;
		}
		&-tabs {
			display: flex;
			// align-items: center;
			// justify-content: space-around;
			margin-bottom: 50px;
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			&-tab {
				display: flex;
				flex-direction: column;
				align-items: center;
				cursor: pointer;
				.text {
					font-size: 18px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #ffffff;
					line-height: 18px;
					margin-bottom: 16px;
				}
				.select {
					color: #8fc7ff;
				}
				.selectLine {
					background: #8fc7ff !important;
				}
				.line {
					width: 26px;
					height: 3px;
					background: red;
					border-radius: 2px;
				}
			}
		}
		&-input {
			width: 340px;
			height: 42px;
			background: #f6f7f9;
			border-radius: 20px;
			margin-bottom: 30px;
			padding: 10px 13px 10px 20px;
			display: flex;
			align-items: center;
			position: relative;
			.input {
				flex: 1;
				border: none;
				outline: none;
				background: #f6f7f9;
				line-height: 30px;
				width: 100%;
				&::placeholder {
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #bbbbbb;
					line-height: 30px;
				}
			}
			.img-code {
				position: relative;
				right: -13px;
				border-radius: 0 21px 21px 0;
				overflow: hidden;
			}
			&-icon {
				width: 16px;
				height: 16px;
				margin-right: 8px;
			}
			.password {
				cursor: pointer;
				width: 30px;
				height: 30px;
			}
			.message {
				position: absolute;
				bottom: -24px;
				left: 44px;
				color: #ff0000;
				font-size: 13px;
			}
		}
		&-code {
			.code-input {
				display: flex;
				align-items: center;
				margin-bottom: 24px;
				&-con {
					margin-bottom: 0;
					width: 225px;
					&-i {
						max-width: 160px;
					}
				}
				&-button {
					width: 120px;
					height: 50px;
					background: #f6f7f9;
					border-radius: 25px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #999999;
					line-height: 50px;
					text-align: center;
					margin-left: 15px;
					cursor: pointer;
				}
				&-text {
					font-size: 12px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: #ffffff;
					line-height: 12px;
					text-align: center;
					//margin: 24px 0 15px;
					height: 49px;
					display: inline-block;
					width: 100%;
				}
			}
		}
		&-tip {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			//margin-bottom: 35px;
			margin-top: 15px;
			&-left {
				display: flex;
				align-items: center;
			}
			&-right {
				display: flex;
				align-items: center;
				cursor: pointer;
			}
		}
		&-button {
			width: 100%;
			height: 42px;
			// background: var(--brand-6, #0076e8);
			background: linear-gradient(90deg, #00e0ff 0%, #0175e8 100%);
			border-radius: 20px;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: #ffffff;
			line-height: 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 20px;
			margin-top: 110px;
			cursor: pointer;
		}
		&-tp {
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			display: flex;
			.color {
				color: #f4ff7c;
				cursor: pointer;
			}
		}
	}
	.radio {
		margin-right: 10px;
		width: 16px;
		height: 16px;
		cursor: pointer;
		position: relative;
		&-sure {
			position: absolute;
			top: -30px;
			border-radius: 3px;
			left: -4px;
			width: 96px;
			height: 20px;
			font-size: 12px;
			line-height: 20px;
			text-align: center;
			font-family: Microsoft YaHei;
			font-weight: 400;
			// color: #ff0705;
			// background: #fff1f1;
			background: rgba(0, 0, 0, 0.6);
			border: 1px solid #1fb2cf;
			color: #2adcff;
		}
		.tooltip-icon {
			position: absolute;
			top: -12px;
			left: 0;
			width: 16px;
			height: 16px;
			-webkit-clip-path: polygon(100% 0, 0 0, 50% 50%);
			clip-path: polygon(100% 0, 0 0, 50% 50%);
			// background: #fff1f1;
			background: rgba(0, 0, 0, 0.6);
			// border: 1px solid #1FB2CF;
		}
		&-div {
			width: 16px;
			height: 16px;
			background: #ffffff;
			border: 1px solid #dadada;
			border-radius: 50%;
		}
		&-icon {
			width: 16px;
			height: 16px;
		}
	}
}
.dialog-content {
	max-height: 50vh;
	overflow: auto;
}
</style>
