<template>
	<div v-loading="loading" class="points-swiper">
		<headAvator :own-id="_userinfo.id" class="avatar" />
		<div class="con">
			<div class="con-name">{{ info.nickName }}</div>
			<div class="con-point">
				<div class="con-point-icon">
					<AlIcon name="el-icon-star-on" color="#ffffff" size="14"></AlIcon>
				</div>
				<div class="con-point-label">积分：</div>
				<div class="con-point-num">{{ info.point }}</div>
			</div>
			<div class="con-more" @click="openAllPoint">
				<div style="margin-right: 8px">收支/兑换记录</div>
				<AlIcon name="el-icon-arrow-right"></AlIcon>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Header',
	data() {
		return {
			loading: false,
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			info: {}
		};
	},
	created() {
		if (!this.isShopLogin()) {
			return;
		}
		this.getInfo();
	},
	methods: {
		/**打开收支记录*/
		openAllPoint() {
			console.log(111);
			this.$emit('openAllPoint');
		},
		/**获取用户信息*/
		getInfo() {
			let data = {
				rentId: this.getSiteId()
			};
			this.loading = true;
			this.$api.shop_api.getPointInfo(data).then(res => {
				this.loading = false;
				let { code, data, msg } = res;
				if (code != 200) return this.$message.error(msg);
				this.info = data;
			});
		}
	}
};
</script>

<style scoped lang="scss">
.points-swiper {
	height: 221px;
	background: linear-gradient(270deg, #ffa858 0%, #fff5ea 69%);
	border-radius: 4px;
	padding: 60px 0 0 172px;
	display: flex;
	.avatar {
		width: 100px;
		height: 100px;
		border-radius: 50%;
		margin-right: 20px;
	}
	.con {
		&-name {
			font-size: 20px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
		}
		&-point {
			display: flex;
			align-items: center;
			margin: 4px 0 8px;
			&-icon {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #f95f55;
				padding: 5px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			&-label {
				font-size: 16px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 24px;
				margin: 0 4px 0 8px;
			}
			&-num {
				font-size: 20px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #f95f55;
				line-height: 24px;
			}
		}
		&-more {
			font-size: 16px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 32px;
			display: flex;
			align-items: center;
			cursor: pointer;
		}
	}
}
</style>
