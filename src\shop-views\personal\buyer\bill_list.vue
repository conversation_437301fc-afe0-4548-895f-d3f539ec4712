<template>
	<div v-loading="loading" class="bill_list" :style="{ width: isMain ? '1200px' : '100%' }">
		<div class="head">
			<div class="lf">筛选查询</div>
			<div class="rf">
				<el-button
					icon="el-icon-search"
					@click="
						() => {
							offset = 0;
							page = 1;
							getBillApplyList();
						}
					"
				>
					查询
				</el-button>
			</div>
		</div>
		<el-form :inline="true" :model="search" class="form">
			<el-form-item label="发票类型：">
				<el-select v-model="search.billType" placeholder="请选择类型" clearable>
					<el-option label="单位增值税发票" :value="1"></el-option>
					<el-option label="单位普通发票" :value="2"></el-option>
					<el-option label="个人发票" :value="3"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="状态：">
				<el-select v-model="search.billStatus" placeholder="请选择状态">
					<el-option label="全部" value=""></el-option>
					<el-option label="未开票" :value="-9"></el-option>
					<el-option label="申请中" :value="0"></el-option>
					<el-option label="已处理" :value="1"></el-option>
					<el-option label="已拒绝" :value="2"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="订单号">
				<el-input v-model="search.keyword" placeholder="请输入订单编号"></el-input>
			</el-form-item>
		</el-form>
		<!-- <div class="btn">
			<el-button type="primary">发票抬头管理</el-button>
		</div> -->
		<div class="table">
			<div class="th">
				<div class="td">订单详情</div>
				<div class="td">发票类型</div>
				<div class="td">状态</div>
				<div class="td">操作</div>
			</div>
			<div
				v-for="(item, index) of tableData"
				:key="index"
				class="list"
				:class="index === 0 ? 'last' : ''"
			>
				<div class="data">
					<div class="cell">
						<div class="cell">
							<div class="rf">{{ item.ORDER_TIME }}</div>
						</div>
						<div class="lf">订单号：</div>
						<div class="rf">{{ item.ORDER_ID }}</div>
					</div>
					<div class="cell">
						<div class="lf">
							<AlIcon name="icon-4" color="#8C8C8C"></AlIcon>
						</div>
						<div class="rf">{{ item.SHOP_NAME }}</div>
					</div>
				</div>
				<div class="tr">
					<div class="td1">
						<div
							v-for="(item1, i) in item.ORDER_GOODS_DETAILS"
							:key="i"
							class="box"
							:style="{ borderTop: i != 0 ? `1px solid #eee` : 'none' }"
						>
							<img :src="$judgeFile(item1.COVER_URL)" alt="" class="lf" />
							<div class="rf">
								<div class="title nth2">
									{{ item1.NAME }}
								</div>
								<div class="money">商品数量：{{ item1.NUMBER }}</div>
							</div>
						</div>
					</div>
					<div
						class="td2"
						:style="{
							height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px',
							lineHeight: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px'
						}"
					>
						{{ item.billType | billType }}
					</div>
					<div
						class="td3"
						:style="{
							height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px',
							lineHeight: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px'
						}"
					>
						{{ item.billStatus | billStatus }}
					</div>
					<div
						class="td4"
						:style="{
							height: getHeight(item.ORDER_GOODS_DETAILS.length) + 'px'
						}"
					>
						<div v-if="item.applyBill">
							<el-button size="mini" type="primary" plain @click="ApplyBill(item)">
								申请开票
							</el-button>
						</div>
						<div v-if="item.viewBill">
							<el-button
								size="mini"
								@click="$router.push({ path: `/billDetails?id=${item.billApplyId}` })"
							>
								发票详情
							</el-button>
						</div>
						<div>
							<el-button type="text" @click="openSeek(item)">联系卖家</el-button>
						</div>
					</div>
				</div>
			</div>
			<el-empty v-if="tableData.length == 0" description="暂无数据"></el-empty>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<ApplyBill ref="bill" :item="details" @success="getBillApplyList()"></ApplyBill>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="baseInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>
<script>
import contactMessage from '@/components/public/contactMessage.vue';
import ApplyBill from './../components/apply_bill.vue';
export default {
	name: 'BillList',
	components: {
		ApplyBill,
		contactMessage
	},
	filters: {
		billType(i) {
			if (i == 1) {
				return '单位增值税发票';
			} else if (i == 2) {
				return '单位普通发票';
			} else if (i == 3) {
				return '个人发票';
			} else {
				return '-';
			}
		},
		billStatus(i) {
			if (i == 0) {
				return '申请中';
			} else if (i == 1) {
				return '已开票';
			} else if (i == 2) {
				return '已拒绝';
			} else if (i == -1) {
				return '已撤回';
			} else {
				return '未开票';
			}
		}
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			dialogMessageVisible: false,
			page: 1,
			total: 0,
			size: 10,
			search: {
				status: ''
			},
			baseInfo: {},
			offset: 0,
			psize: 10,
			loading: false,
			tableData: [],
			userId: '',
			details: {}
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getBillApplyList();
		}
	},
	methods: {
		// 打开聊天
		openSeek(i) {
			this.baseInfo = {
				SHOP_NAME: i.SHOP_NAME,
				SHOP_LOG: i.SHOP_LOG,
				COVER_URL: i.ORDER_GOODS_DETAILS[0].COVER_URL,
				ORI_PRICE: i.ORDER_GOODS_DETAILS[0].ORI_PRICE,
				SELLER_ID: i.SELL_ID,
				PRODUCT_NAME: i.ORDER_NAME,
				ID: i.SHOP_ID
			};
			this.dialogMessageVisible = true;
		},
		// 页数
		handleCurrentChange(i) {
			this.offset = this.psize * i - this.psize;
			if (i == 1) {
				this.offset = 0;
			}
			this.getBillApplyList();
		},
		// 条数
		handleSizeChange(i) {
			this.offset = 0;
			this.pageSize = i;
			this.psize = i;
			this.restPage();
			this.getBillApplyList();
		},
		// 开票弹窗
		ApplyBill(i) {
			this.details = i;
			this.$refs.bill.visible = true;
		},
		getHeight(i) {
			return i * 100;
		},
		// 获取列表
		getBillApplyList() {
			this.loading = true;
			let data = {
				userId: this.userId,
				billType: this.search.billType || '',
				billStatus: this.search.billStatus || '',
				keyword: this.search.keyword || '',
				offset: 0,
				psize: 10,
				type: this.search.type || '',
				siteId: this.getSiteId()
			};
			this.$api.shop_api.getBillOrderList(data).then(res => {
				if (res.state) {
					this.tableData = res.result || [];
					this.total = res.totalNum;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 左侧高度
		lineLeft(i) {
			return 100 * i + 'px';
		}
	}
};
</script>

<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.bill_list {
	padding: 20px;
	margin: 0 auto;
	background: #ffffff;
	.head {
		height: 48px;
		background: #f4f4f4;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		::v-deep .el-button--primary {
			background: var(--brand-6, '#ca3f3b');
			border-color: var(--brand-6, '#ca3f3b');
		}
		.lf {
			padding-left: 18px;
			font-size: 14px;
			font-weight: 500;
			color: #404040;
		}
		.rf {
			margin-left: auto;
			padding-right: 12px;
		}
	}
	.form {
		display: flex;
		padding-top: 19px;
		justify-content: center;
		box-sizing: border-box;
		height: 79px;
		border: 1px solid #d9d9d9;
		border-top: none;
		::v-deep .el-date-editor {
			width: 220px;
		}
	}
	.btn {
		margin-top: 12px;
	}
	.table {
		margin-top: 20px;
		//min-height: 450px;
		.th {
			display: flex;
			height: 40px;
			align-items: center;
			background: #f4f4f4;
			top: 0;
			.td {
				font-size: 14px;
				font-weight: 500;
				color: #404040;
				padding-left: 28px;
				box-sizing: border-box;
			}
			.td:nth-child(1) {
				width: 414px;
			}
			.td:nth-child(2) {
				width: 176px;
			}
			.td:nth-child(3) {
				width: 182px;
			}
			.td:nth-child(4) {
				width: 163px;
			}
		}
		.tr {
			display: flex;
			align-items: center;
			border: 1px solid #eeeeee;
			//border-right: none;
			.td1 {
				width: 414px;
				border-right: 1px solid #eeeeee;
				.box {
					height: 100px;
					display: flex;
					align-items: center;
					box-sizing: border-box;
					padding: 0 13px 0 11px;
				}
				.lf {
					width: 72px;
					height: 70px;
				}
				.rf {
					margin-left: auto;
					width: calc(100% - 78px);
					.title {
						font-size: 16px;
						font-weight: 400;
						color: #404040;
						line-height: 24px;
					}
					.money {
						margin-top: 6px;
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
					}
				}
			}
			.td2 {
				width: 176px;
				height: auto;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				border-right: 1px solid #eeeeee;
				text-align: center;
			}
			.td3 {
				width: 182px;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				align-items: center;
				border-right: 1px solid #eeeeee;
				text-align: center;
			}
			.td4 {
				width: 163px;
				padding: 0 13px 0 11px;
				border-right: 1px solid #eeeeee;
				box-sizing: border-box;
				height: 100%;
				justify-content: center;
				display: flex;
				align-items: center;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				flex-wrap: wrap;
				> div {
					width: 100%;
					text-align: center;
				}
			}
		}
		.data {
			height: 40px;
			background: #f6f6f6;
			display: flex;
			align-items: center;
			border-top: 1px solid #eeeeee;
			margin-top: 13px;
			.cell {
				display: flex;
				align-items: center;
				padding-left: 13px;
				margin-right: 25px;
				.lf {
					font-size: 14px;
					font-weight: 400;
					color: #8c8c8c;
					margin-right: 5px;
				}
				.rf {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
				}
			}
		}
	}
	.page {
		margin-top: 41px;
		::v-deep .el-pagination {
			display: flex;
			.btn-prev {
				margin-left: auto;
			}
		}
	}
}
</style>
