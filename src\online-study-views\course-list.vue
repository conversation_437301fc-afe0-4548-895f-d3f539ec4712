<template>
	<div class="lessonList">
		<!-- <FloatBtn ref="child" /> -->
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="inner">
				<div class="chooseType">
					<div class="type flex1">
						<div class="label">课程类型：</div>
						<ul class="flex1">
							<li :class="['all', allactive ? 'all-active' : '']" @click="allChoose">全部</li>
							<li
								v-for="(item, index) in typeList"
								:key="index"
								:class="{ liActive: findValue(item, typeValue) }"
								@click="typeClick(item, typeValue)"
							>
								{{ item.name }}
								<!-- <i class="iconfont icon-guanbi1" @click.stop="deleteValue(item, typeValue)"></i> -->
							</li>
						</ul>
					</div>
					<!-- <div class="type flex1">
						<div class="label">付费类型：</div>
						<div :class="['all', allactive1 ? 'all-active' : '']" @click="allChoose1">全部</div>
						<ul class="flex1">
							<li
								v-for="(item, index) in type2"
								:key="index"
								:class="{ liActive: findValue(item, type2Value) }"
								@click="typeClick(item, type2Value)"
							>
								{{ item }}
								<i class="iconfont icon-guanbi1" @click.stop="deleteValue(item, type2Value)"></i>
							</li>
						</ul>
					</div> -->
					<div class="input flex1">
						<div class="label">关键字：</div>
						<el-input v-model="form.name"></el-input>
						<el-button type="primary" class="search-btn" @click="handleSearch">搜索</el-button>
						<el-button class="reset-btn" @click="handleReset">重置</el-button>
					</div>
				</div>
				<div class="upDown flex2">
					<div class="left flex1">
						<ul class="flex1">
							<li
								v-for="(ele, index) in upList"
								:key="index"
								class="tUp flex1"
								:class="{ liActive: upValue == ele }"
								@click="upFn(ele, index + 1)"
							>
								{{ ele }}
								<i class="el-icon el-icon-bottom iconfont"></i>
							</li>
						</ul>
					</div>
				</div>
				<div v-loading="loading" class="lesson">
					<div class="safeMain">
						<div
							v-for="(item, index) in lessonList"
							:key="index"
							class="temp"
							@click="goLessonInfo(item.id)"
						>
							<div class="imgBox">
								<img :src="item.coverImg" class="insureImg" />
								<span class="time-box">{{ item.time }}</span>
								<div class="mask-box">
									<img class="mask-icon" src="@/assets/study_images/play-icon.png" alt="" />
								</div>
							</div>
							<div class="infoBox">
								<div class="name fs14 c6 bold over2">
									{{ item.name }}
								</div>
								<div class="desc fs14 c6 bold over1">
									{{ item.name }}
								</div>
								<div class="star flex1">
									<el-rate v-model="item.score" disabled></el-rate>
									<div class="score">{{ Number(item.score).toFixed(2) }}分</div>
								</div>
							</div>
						</div>
					</div>
					<Empty v-if="lessonList.length == 0" :tips="'暂无数据'" />
				</div>
				<div class="page">
					<el-pagination
						background
						:current-page="form.pageNum"
						:page-size="10"
						layout="prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
// import FloatBtn from './components/floatBtn.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
// import config from '@/config';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			isNo: false,
			typeList: [],
			typeValue: [],
			upList: ['综合', '人气', '评论', '新品'],
			upValue: '综合',
			score: 5,
			allactive: false,
			form: {
				name: '',
				courseClassId: '',
				courseClassIds: [],
				orderByColumn: 1,
				isAsc: 'desc',
				isFree: '',
				pageNum: 1,
				pageSize: 10
			},
			totalPage: 0,
			total: 0,
			lessonList: [],
			loading: true
		};
	},
	computed: {},
	mounted() {
		this.form.name = this.$route.query.searchValue;
		this.form.courseClassId = this.$route.query.childId;
		this.form.courseClassIds.push(this.$route.query.id);
		if (this.$route.query.orderByColumn) {
			this.form.orderByColumn = this.$route.query.orderByColumn;
			switch (this.form.orderByColumn) {
				case 1:
					this.upValue = '综合';
					break;
				case 2:
					this.upValue = '人气';
					break;
				case 3:
					this.upValue = '评论';
					break;
				case 4:
					this.upValue = '新品';
					break;
			}
		}
		if (this.$route.query.isFree) {
			this.form.isFree = this.$route.query.isFree;
			// this.type2Value.push('免费');
		}
		this.lessonTypeFn();
	},
	methods: {
		// 重置事件
		handleReset() {
			this.form.name = '';
			this.form.pageNum = 1;
			this.lessonTypeFn();
		},
		// 搜索事件
		handleSearch() {
			this.form.pageNum = 1;
			this.lessonListFn();
		},
		// 查询是否有选中状态
		findValue(ele, group) {
			if (group.length == 0) return;
			let result = group.indexOf(ele) == '-1' ? false : true;
			return result;
		},
		// 类型选项选择事件
		typeClick(item, group) {
			this.form.courseClassId = '';
			this.form.pageNum = 1;
			if (group.indexOf(item) != '-1') return;
			group.push(item);
			this.lessonListFn();
		},
		// deleteValue(item, group) {
		// 	this.form.courseClassId = '';
		// 	let index;
		// 	for (let i = 0; i < group.length; i++) {
		// 		if (item == group[i]) {
		// 			index = i;
		// 			break;
		// 		}
		// 	}
		// 	group.splice(index, 1);
		// 	this.lessonListFn();
		// },
		// 快捷选项选中事件
		upFn(value, index) {
			this.upValue = value;
			this.form.orderByColumn = index;
			this.form.isAsc = 'desc';
			this.lessonListFn();
		},
		// 每页展示条数修改
		handleSizeChange(val) {
			// console.log(`每页 ${val} 条`);
		},
		// 当前页码修改
		handleCurrentChange(val) {
			// console.log(`当前页: ${val}`);
			this.form.pageNum = val;
			// console.log(this.form);
			this.lessonListFn();
		},
		// 获取课程类型数据
		async lessonTypeFn() {
			const { data } = await this.$api.study_api.getCourseClassTree();
			this.typeList = data;
			for (let item of this.typeList) {
				if (item.id == this.$route.query.id) {
					this.typeValue.push(item);
				}
			}
			this.lessonListFn();
		},
		// 获取课程列表数据
		async lessonListFn() {
			this.loading = true;
			// if (this.type2Value.length == 1) {
			// 	if (this.type2Value[0] == '付费') {
			// 		this.form.isFree = '0';
			// 	} else {
			// 		this.form.isFree = '1';
			// 	}
			// } else {
			// 	this.form.isFree = '';
			// }

			this.form.courseClassIds = this.typeValue.map(item => {
				return item.id;
			});
			try {
				const { data } = await this.$api.study_api.pageList(this.form);
				this.lessonList = data.items;
				this.loading = false;
				this.total = data.total;
				this.totalPage = data.totalPage;
			} catch (error) {
				this.loading = false;
			}
		},
		// 选中全部事件
		allChoose() {
			this.allactive = !this.allactive;
			if (this.allactive) {
				this.typeValue = [...this.typeList];
			} else {
				this.typeValue = [];
			}
			this.lessonListFn();
		},
		// 跳转课程详情页
		goLessonInfo(id) {
			this.$router.push({ path: '/freecourses', query: { id } });
		}
	}
};
</script>
<style scoped lang="scss">
.lessonList {
	background: #ffffff;
	padding-top: 20px;
	.imgBox {
		width: 100%;
		border-radius: 6px;
		position: relative;
		.mask-box {
			display: none;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.1);
			align-items: center;
			justify-content: center;
		}
		.mask-icon {
			width: 50px;
			height: 50px;
		}
		.time-box {
			position: absolute;
			right: 4px;
			bottom: 10px;
			padding: 2px 6px;
			border-radius: 4px;
			color: #fff;
			font-size: 12px;
			background: rgba(0, 0, 0, 0.4);
		}
	}
	.main {
		margin-top: 20px;
		.inner {
			margin: 0 auto;
			width: 1200px;
			.chooseType {
				// height: 150px;
				background: #f7f9fb;
				border-radius: 10px;
				padding: 10px 20px 22px;
				.type {
					// min-height: 50px;
					box-sizing: border-box;
					align-items: baseline;
					margin-top: 20px;
					// flex-wrap: wrap;
					// border-bottom: 1px solid #e8eaf0;
					.label {
						font-size: 16px;
						font-weight: 400;
						color: #7b7b7b;
						flex-shrink: 0;
					}
					.all {
						padding: 2px 16px;
						color: #333333;
						font-size: 16px;
						cursor: pointer;
						margin: 0 10px;
						flex-shrink: 0;
						line-height: normal;
						height: auto;
					}
					.all-active {
						background: #4f85ff;
						border-radius: 14px;
						color: #ffffff;
						box-sizing: content-box;
						border: 1px solid #4f85ff;
					}
					ul {
						flex-wrap: wrap;
						li {
							font-weight: 400;
							cursor: pointer;
							color: #333333;
							font-size: 16px;
							margin: 0 10px;
							padding: 0 15px;
							height: 30px;
							line-height: 30px;
							border-radius: 4px;
							position: relative;
							overflow: hidden;
						}
						.liActive {
							color: #4f85ff;
						}
					}
				}
				.input {
					margin-top: 20px;
					.label {
						font-size: 16px;
						font-weight: 400;
						color: #7b7b7b;
					}
					.el-input {
						margin: 0 10px 0 30px;
						width: 220px;
					}
					.search-btn {
						background: #4f85ff;
						border-radius: 16px;
					}
					.reset-btn {
						background: #707d99;
						color: #ffffff;
						border-radius: 16px;
					}
				}
			}
			.upDown {
				margin-top: 40px;
				.left {
					ul {
						li {
							margin-right: 25px;
							cursor: pointer;
							background: #f7f9fb;
							padding: 5px 12px;
							height: 26px;
							border-radius: 13px;
							font-size: 16px;
							color: #333333;
						}
						.liActive {
							font-weight: 400;
							color: #4f85ff;
							background: #f7f9fb;
							border: 1px solid #4f85ff;
							.iconfont {
								transform: rotate(180deg);
							}
						}
					}
				}
				.right {
					.line {
						width: 1px;
						height: 16px;
						background: #999;
						margin: 0 10px;
					}
					.page {
						margin-right: 22px;
					}
					.btn {
						cursor: pointer;
						width: 30px;
						height: 30px;
						background: #ffffff;
						border: 1px solid #e8eaf0;
						border-radius: 4px;
						text-align: center;
						line-height: 30px;
						color: #bbb;
						margin-right: 10px;
					}
				}
			}
			.lesson {
				box-sizing: border-box;
				margin-top: 30px;
				.safeMain {
					display: flex;
					flex-basis: row;
					flex-wrap: wrap;
					.temp {
						margin-right: 20px;
						position: relative;
						margin-bottom: 20px;
						width: 224px;
						height: 250px;
						background: #ffffff;
						box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.08);
						border-radius: 10px;
						overflow: hidden;
						// .insureImg {
						// 	width: 224px;
						// 	height: 139px;
						// }
						.imgBox {
							height: 139px;
						}
						&:hover {
							box-shadow: 0px 0px 20px 0px rgba(35, 56, 77, 0.4);
							.mask-box {
								display: flex;
							}
						}
						.insureImg {
							width: 100%;
							height: 100%;
						}
						.infoBox {
							cursor: pointer;
							box-sizing: border-box;
							padding: 6px 10px 14px;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.name {
								color: #666;
								height: 36px;
							}
							.desc {
								font-size: 12px;
								color: #8c8c8c;
								margin-top: 14px;
								line-height: 12px;
							}
							.star {
								margin-top: 15px;
								::v-deep .el-rate__icon {
									margin-right: 0;
								}
								.score {
									font-size: 12px;
									font-weight: 400;
									color: #ffa82a;
									margin: 0 7px;
								}
								.line {
									width: 1px;
									height: 9px;
									background: #bcc1c8;
									margin-right: 8px;
								}
								.study {
									font-size: 12px;
									font-weight: 400;
									color: #7a8392;
								}
							}
						}
					}
					.temp:nth-child(5n) {
						margin-right: 0;
					}
				}
			}
			.page {
				text-align: center;
				padding: 30px 0;
				::v-deep .btn-prev {
					background: #fff;
				}
				::v-deep .btn-next {
					background: #fff;
				}
			}
		}
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	.sub-breadcrumb {
		width: 1240px !important;
		height: 40px;
	}
}
</style>
