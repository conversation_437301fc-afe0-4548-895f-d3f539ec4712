<!--
 * @Description:
 * @Version:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-16 16:32:30
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-09-20 10:30:42
-->
<template>
	<div class="base-tooltip">
		<el-tooltip
			popper-class="base-tooltip-container"
			:popper-options="{ boundariesElement: 'viewport' }"
			:effect="effect"
			:disabled="isDisabledTooltip"
			:content="String(content)"
			:placement="placement"
		>
			<div
				:id="elId"
				class="ellipsis"
				:class="className"
				:style="{ width: wrapWidth + 'px' }"
				@mouseover="onMouseOver(refName)"
			>
				<span :ref="refName">{{ content || '-' }}</span>
			</div>
		</el-tooltip>
	</div>
</template>

<script>
export default {
	name: 'BaseTooltip',
	props: {
		// 显示的文字内容
		content: {
			type: [String, Number],
			default: ''
		},
		// 设置父元素的样式：比如宽度字体等，需可以自己在组件内部配置样式比如字体大小
		className: {
			type: String,
			default: ''
		},
		// 子元素标识（如在同一页面中调用多次组件，此参数不可重复）
		refName: {
			type: String,
			default: ''
		},
		// 默认提供的主题 dark/light
		effect: {
			type: String,
			default: () => {
				return 'dark';
			}
		},
		// Tooltip 的出现位置top/top-start/top-end/bottom/bottom-start/bottom-end/left/left-start/left-end/right/right-start/right-end
		placement: {
			type: String,
			default: () => {
				return 'top';
			}
		},
		wrapWidth: {
			type: [Number, String],
			default: null
		},
		elId: {
			type: String,
			default: 'tooltip'
		}
	},
	data() {
		return {
			isDisabledTooltip: true // 是否需要禁止提示
		};
	},
	methods: {
		// 移入事件: 判断内容的宽度contentWidth是否大于父级的宽度
		onMouseOver(str) {
			let parentWidth = this.$refs?.[str]?.parentNode?.offsetWidth ?? this.wrapWidth;
			let contentWidth =
				this.$refs?.[str]?.offsetWidth ??
				document.querySelector(`#${this.elId} .ellipsis-text`)?.offsetWidth;
			// 判断是否禁用tooltip功能
			this.isDisabledTooltip = contentWidth <= parentWidth || contentWidth - parentWidth == 1;
		}
	}
};
</script>
<style lang="scss" scoped>
.base-tooltip {
	width: 100%;
	color: inherit;
	/* 文字超出宽度显示省略号 单行 */
	.ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
</style>
