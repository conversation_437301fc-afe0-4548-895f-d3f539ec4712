<template>
	<div class="main">
		<h3 class="form-title">在线咨询</h3>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="recipientName" label="咨询人">
				<template slot-scope="scope">
					<span>{{ scope.row | chatName(_userinfo) }}</span>
				</template>
			</el-table-column>
			<el-table-column prop="update_time" label="咨询时间"></el-table-column>
			<el-table-column fixed="right" label="操作" width="280">
				<template slot-scope="scope">
					<el-button type="text" size="small" @click="handleOpenMsg(scope.row)">回复</el-button>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList"
		/>
		<!--沟通-->
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="contactDialogInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import contactMessage from '@/components/public/contactMessageEnterprise.vue';
import { mapState } from 'vuex';

export default {
	name: 'Interest',
	components: {
		contactMessage
	},
	filters: {
		chatName(item, userinfo) {
			// 判断用户是发方还是收方
			if (item.send_id == userinfo.id) {
				return item.recipientName;
			} else {
				return item.sendName;
			}
		}
	},
	data() {
		return {
			contactDialogInfo: {},
			dialogMessageVisible: false,
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [] //职位列表
		};
	},
	computed: {
		...mapState('user', ['userInfo'])
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.chatList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		/**
		 * @description 沟通
		 * */
		handleOpenMsg(row) {
			// 判断用户是发方还是收方
			let obj = {
				headImg: '',
				userName: '',
				userId: ''
			};
			if (row.send_id == this._userinfo.id) {
				obj.headImg = row.recipientPhoto;
				obj.userName = row.recipientName;
				obj.userId = row.recipient_id;
			} else {
				obj.headImg = row.sendPhoto;
				obj.userName = row.sendName;
				obj.userId = row.send_id;
			}
			this.contactDialogInfo = {
				SHOP_LOG: obj.headImg,
				SHOP_NAME: obj.userName,
				SELLER_ID: obj.userId,
				isGoods: true
			};
			this.dialogMessageVisible = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
</style>
