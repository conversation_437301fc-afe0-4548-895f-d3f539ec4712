<template>
	<div v-loading="loading" class="course-list">
		<div v-for="(item, index) in list" :key="index" class="course-item" @click="handleClick(item)">
			<img class="course-img" :src="item.coverImg" alt="" />
			<div class="info">
				<div class="name">{{ item.examName }}</div>
				<div class="desc-box">
					<span class="desc">{{ item.name }}</span>
					<span v-if="item.exam" class="tag tag-succes">已完成</span>
					<span v-else class="tag tag-not">未完成</span>
				</div>
			</div>
		</div>
		<el-pagination
			v-if="total"
			class="pagination"
			background
			:current-page="pageNum"
			:page-size="pageSize"
			layout="prev, pager, next, jumper"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
		<Empty v-if="list.length == 0" :tips="'暂无数据'" />
	</div>
</template>
<script>
export default {
	data() {
		return {
			loading: false,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			list: []
		};
	},
	mounted() {
		this.getExamList();
	},
	methods: {
		/**
		 * @description 获取课程考试
		 */
		getExamList() {
			this.loading = true;
			let param = {
				pageSize: this.pageSize,
				pageNum: this.pageNum
			};
			this.$api.study_api
				.examList(param)
				.then(({ code, data }) => {
					this.loading = false;
					if (code == 200) {
						this.list = data?.items || [];
						this.total = data?.total || 0;
					}
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 查看操作
		 * */
		handleClick(item) {
			let query = { courseId: item.id, examId: item.examId, exam: item.exam ? 'true' : 'false' };

			this.$router.push({ path: '/examination', query: query });
		},
		handleCurrentChange(value) {
			this.pageNum = value;
			this.getExamList();
		}
	}
};
</script>

<style lang="scss" scoped>
.course-list {
	display: flex;
	flex-wrap: wrap;
	.course-item {
		width: 224px;
		height: 204px;
		background: #ffffff;
		box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.08);
		border-radius: 10px;
		margin-right: 20px;
		margin-top: 20px;
		.course-img {
			width: 100%;
			height: 139px;
			object-fit: cover;
		}
		.info {
			padding: 6px 10px;
		}
		.name {
			font-size: 14px;
			font-weight: bold;
			color: #222222;
		}
		.desc-box {
			font-size: 12px;
			margin-top: 10px;
			color: #8c8c8c;
			display: flex;
			justify-content: space-between;
		}
		.tag {
			width: 46px;
			height: 18px;
			border-radius: 2px;
			font-size: 12px;
			flex-shrink: 0;
			text-align: center;
		}
		.tag-succes {
			color: #16c076;
			background: #eefff7;
			border: 1px solid #16c076;
		}
		.tag-not {
			color: #fe6f63;
			background: #fff3f3;
			border: 1px solid #fe6f63;
		}
	}
	.course-item:nth-child(4n) {
		margin-right: 0;
	}
	.pagination {
		width: 100%;
		margin-top: 20px;
		text-align: center;
	}
}
</style>
