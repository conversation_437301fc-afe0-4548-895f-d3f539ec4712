/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

import { queryDict } from '@/api/model/personal';

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
	if (arguments.length === 0 || !time) {
		return null;
	}
	const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
	let date;
	if (typeof time === 'object') {
		date = time;
	} else {
		if (typeof time === 'string') {
			if (/^[0-9]+$/.test(time)) {
				// support "1548221490638"
				time = parseInt(time);
			} else {
				// support safari
				// https://stackoverflow.com/questions/4310953/invalid-date-in-safari
				time = time.replace(new RegExp(/-/gm), '/');
			}
		}

		if (typeof time === 'number' && time.toString().length === 10) {
			time = time * 1000;
		}
		date = new Date(time);
	}
	const formatObj = {
		y: date.getFullYear(),
		m: date.getMonth() + 1,
		d: date.getDate(),
		h: date.getHours(),
		i: date.getMinutes(),
		s: date.getSeconds(),
		a: date.getDay()
	};
	const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
		const value = formatObj[key];
		// Note: getDay() returns 0 on Sunday
		if (key === 'a') {
			return ['日', '一', '二', '三', '四', '五', '六'][value];
		}
		return value.toString().padStart(2, '0');
	});
	return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
	if (('' + time).length === 10) {
		time = parseInt(time) * 1000;
	} else {
		time = +time;
	}
	const d = new Date(time);
	const now = Date.now();

	const diff = (now - d) / 1000;

	if (diff < 30) {
		return '刚刚';
	} else if (diff < 3600) {
		// less 1 hour
		return Math.ceil(diff / 60) + '分钟前';
	} else if (diff < 3600 * 24) {
		return Math.ceil(diff / 3600) + '小时前';
	} else if (diff < 3600 * 24 * 2) {
		return '1天前';
	}
	if (option) {
		return parseTime(time, option);
	} else {
		return (
			d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
		);
	}
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
	const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
	if (!search) {
		return {};
	}
	const obj = {};
	const searchArr = search.split('&');
	searchArr.forEach(v => {
		const index = v.indexOf('=');
		if (index !== -1) {
			const name = v.substring(0, index);
			const val = v.substring(index + 1, v.length);
			obj[name] = val;
		}
	});
	return obj;
}
//转意符换成普通字符
function convertIdeogramToNormalCharacter(val) {
	const arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };
	return val.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all, t) {
		return arrEntities[t];
	});
}
export function getPlainText(richCont) {
	let value = richCont;
	if (richCont) {
		value = value.replace(/\s*/g, ''); //去掉空格
		value = value.replace(/<[^>]+>/g, ''); //去掉所有的html标记
		value = value.replace(/↵/g, ''); //去掉所有的↵符号
		value = value.replace(/[\r\n]/g, ''); //去掉回车换行
		value = value.replace(/&nbsp;/g, ''); //去掉空格
		value = convertIdeogramToNormalCharacter(value);
		return value;
	} else {
		return null;
	}
}

// 公共的获取数据字典方法
export const getDictionaryByCode = code => {
	return new Promise((resolve, reject) => {
		if (code.length < 1) return {};
		const codeObj = {};
		const newCodes = [];
		// 获取缓存中的数据字典
		code.forEach(item => {
			const cacheDict = sessionStorage.getItem(item);
			if (cacheDict) {
				codeObj[item] = JSON.parse(cacheDict);
			} else {
				// 未获取到缓存的数据进行接口请求
				newCodes.push(item);
			}
		});
		const codes = newCodes.join(',');
		if (codes.length > 0) {
			queryDict({ sysAppCodes: codes })
				.then(res => {
					newCodes.forEach(dict => {
						res.results[dict].forEach(i => {
							// 缓存数据字典数据
							sessionStorage.setItem(dict, JSON.stringify(res.results[dict]));
							codeObj[dict] = res.results[dict];
						});
					});
					resolve(codeObj);
				})
				.catch(err => {
					reject(err);
				});
		} else {
			resolve(codeObj);
		}
	});
};
