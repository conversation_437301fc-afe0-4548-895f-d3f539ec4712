<template>
	<el-dialog
		v-loading="loading"
		:visible="visible"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		append-to-body
		lock-scroll
		:popper-append-to-body="false"
		title=""
		width="840px"
		center
		class="point"
		top="10vh"
		@open="open"
		@close="close"
	>
		<div class="content">
			<div class="content-title">查看记录</div>
			<el-tabs v-model="type" class="content-tabs" @tab-click="handleClick">
				<el-tab-pane
					v-for="(item, index) of list"
					:key="index"
					:label="item.name"
					:name="item.type"
				></el-tab-pane>
			</el-tabs>
			<div v-if="type === 'all'" class="content-subTabs">
				<div
					v-for="(item, index) of subTabs"
					:key="index"
					class="content-subTabs-item"
					:class="changeType === item.type ? 'select' : ''"
					@click="selectCurrent(item.type)"
				>
					{{ item.name }}
				</div>
			</div>
			<div class="table-box">
				<div v-for="(item, index) of dataList" :key="index" class="content-item">
					<div v-if="item.objId" class="content-item-title">订单编号：{{ item.objId }}</div>
					<div v-else class="content-item-title">积分{{ item.typeDesc }}</div>
					<div class="content-item-point">{{ item.pointChange }}</div>
				</div>
			</div>
			<Empty v-if="dataList.length === 0"></Empty>
		</div>
		<span slot="footer" class="footer">
			<el-button type="primary" @click="close">确 定</el-button>
		</span>
	</el-dialog>
</template>

<script>
export default {
	name: 'AllPoint',
	props: {
		visible: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			changeType: '',
			loading: false,
			type: 'all',
			subTabs: [
				{
					name: '全部',
					type: ''
				},
				{
					name: '已获取',
					type: '1'
				},
				{
					name: '已消耗',
					type: '-1'
				}
			], //积分收支明细状态选项
			dataList: [],
			list: [
				{ name: '积分收支明细', type: 'all' },
				{ name: '积分兑换记录', type: '3' }
			],
			pageSize: 10,
			pageNum: 1
		};
	},
	methods: {
		/**获取数据*/
		getList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				changeType: this.changeType,
				type: this.type === 'all' ? '' : this.type,
				rentId: this.getSiteId()
			};
			this.$api.shop_api.getPointList(params).then(res => {
				this.loading = false;
				let { code, msg, results } = res;
				if (code === 200) {
					this.dataList = results.list;
				} else {
					this.$message.error(msg);
				}
			});
		},
		/**选择子tab*/
		selectCurrent(type) {
			this.changeType = type;
			this.getList();
		},
		/**打开弹窗*/
		open() {
			this.getList();
		},
		/**关闭弹窗*/
		close() {
			this.$emit('update:visible', false);
		},
		/**切换tab*/
		handleClick() {
			this.getList();
		}
	}
};
</script>

<style scoped lang="scss">
.point {
	::v-deep .el-dialog__body {
		padding: 0 76px;
	}
	.content {
		&-title {
			text-align: center;
			font-size: 24px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
		}
		&-tabs {
			margin-top: 14px;
			::v-deep .el-tabs__nav-wrap {
				overflow: visible;
				&::after {
					margin: 0 -76px;
					width: 840px;
				}
			}
		}
		&-subTabs {
			display: flex;
			align-items: center;
			&-item {
				margin-right: 24px;
				width: 64px;
				height: 32px;
				background: transparent;
				font-size: 16px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #404040;
				line-height: 32px;
				border-radius: 21px;
				cursor: pointer;
			}
			.select {
				background: var(--brand-6, #0076e8);
				color: #ffffff;
				text-align: center;
			}
		}
		.table-box {
			max-height: 400px;
			overflow: auto;
			margin-top: 6px;
		}
		&-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 72px;
			border-bottom: 1px solid #f0f0f0;
			&-title {
				font-size: 16px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 24px;
			}
			&-point {
				font-size: 20px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #f95f55;
				line-height: 24px;
			}
		}
	}
	.footer {
	}
}
</style>
