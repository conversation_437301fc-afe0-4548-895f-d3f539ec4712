<template>
	<!--新建/更新岗位-->
	<el-dialog
		:title="updateJobDialog.title"
		width="80%"
		top="7vh"
		:visible.sync="updateJobDialog.visible"
		:close-on-click-modal="false"
		append-to-body
		@close="closeDialog"
	>
		<div class="content">
			<el-form
				ref="form"
				:model="updateJobDialog.form"
				:rules="updateJobDialog.rules"
				:disabled="updateJobDialog.title === '查看职位'"
				label-width="110px"
			>
				<div class="business-info">
					<el-row :gutter="20">
						<el-col :span="8">
							<el-form-item label="职位名称" prop="name">
								<el-input
									v-model="updateJobDialog.form.name"
									placeholder="请填写职位名称"
									type="input"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="学历要求" prop="education">
								<el-select v-model="updateJobDialog.form.education" placeholder="请选择学历要求">
									<el-option
										v-for="item in educationOptions"
										:key="item.id"
										:label="item.shortName"
										:value="item.cciValue"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8">
							<el-form-item label="工作经验" prop="workExperience">
								<el-select
									v-model="updateJobDialog.form.workExperience"
									placeholder="请选择工作经验要求"
								>
									<el-option
										v-for="item in workExperienceOptions"
										:key="item.id"
										:label="item.shortName"
										:value="item.cciValue"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="24">
							<el-form-item label="岗位介绍" prop="introduce">
								<tinymce
									v-if="updateJobDialog.visible"
									ref="tinymce"
									:disabled="updateJobDialog.title === '查看职位'"
									:value.sync="updateJobDialog.form.introduce"
									:toolbar="tinymceToolbar"
									:plugins="tinymcePlugins"
								></tinymce>
							</el-form-item>
						</el-col>
						<!-- 职位表格 -->
						<el-col :key="updateJobDialog.visible" :span="24">
							<el-form-item label="职位面向" prop="targetColSchMaj">
								<el-table :data="targetColSchMaj" border size="mini" style="width: 100%">
									<el-table-column type="index" label="序号" width="46"></el-table-column>
									<el-table-column label="面向学校" align="center">
										<template slot-scope="{ row }">
											<el-form-item>
												<el-select v-model="row.targetSchool" disabled placeholder="请选择面向学校">
													<el-option
														v-for="item in schoolList"
														:key="item.id"
														:label="item.name"
														:value="item.value"
													></el-option>
												</el-select>
											</el-form-item>
										</template>
									</el-table-column>
									<el-table-column label="面向学院" align="center">
										<template slot-scope="{ row, $index }">
											<el-select v-model="row.targetCollege" placeholder="请选择面向学院">
												<el-option
													v-for="item in collegeList"
													:key="item.id"
													:label="item.name"
													:value="item.value"
													@click.native.prevent="collegeClick(item, $index)"
												></el-option>
											</el-select>
										</template>
									</el-table-column>
									<el-table-column label="面向专业" align="center">
										<template slot-scope="{ row, $index }">
											<el-select
												ref="selectRef"
												:key="row.targetCollegeCode"
												v-model="row.targetMajor"
												multiple
												filterable
												:filter-method="query => handleFilter(query, $index, row.targetCollegeCode)"
												reserve-keyword
												:placeholder="
													specialityObj[row.targetCollegeCode] &&
													specialityObj[row.targetCollegeCode].length > 0
														? '请选择面向专业'
														: '无'
												"
											>
												<el-option
													v-for="item in filteredOptions"
													:key="item.id"
													:label="item.label"
													:value="item.value"
												></el-option>
											</el-select>
										</template>
									</el-table-column>
									<el-table-column
										v-if="updateJobDialog.title === '查看职位'"
										label="审核状态"
										align="center"
										width="110px"
									>
										<!-- '审核状态：0:待审核,1:已审核,2:审核未通过,9草稿' -->
										<!-- 审核状态auditStatus：0:待审核（二级学院审核）, 11:待招就处审核（二级学院审核通过）, 1:已审核（招就处审核通过）, 2:审核未通过, 9:草稿稿  -->
										<template slot-scope="{ row }">
											<el-tag
												:type="
													row.auditStatus === 1
														? 'success'
														: row.auditStatus === 2
														? 'danger'
														: row.auditStatus === 9
														? 'info'
														: row.auditStatus === 11
														? 'warning'
														: 'warning'
												"
											>
												{{
													row.auditStatus === 0
														? '待学院审核'
														: row.auditStatus === 11
														? '待招就处审核'
														: row.auditStatus === 1
														? '审核通过'
														: row.auditStatus === 2
														? '审核未通过'
														: row.auditStatus === 9
														? '草稿'
														: '未知状态'
												}}
											</el-tag>
										</template>
									</el-table-column>
									<el-table-column
										v-if="updateJobDialog.title === '查看职位'"
										label="学院审核意见"
										prop="collegeAuditComment"
										align="center"
									></el-table-column>
									<el-table-column
										v-if="updateJobDialog.title === '查看职位'"
										label="招就处审核意见"
										prop="auditOpinion"
										align="center"
									></el-table-column>
									<el-table-column
										v-if="updateJobDialog.title !== '查看职位'"
										align="center"
										width="45px"
									>
										<template slot="header" slot-scope="{}">
											<i class="el-icon-circle-plus-outline add" @click="addTarget" />
										</template>
										<template slot-scope="{ row }">
											<i class="el-icon-remove-outline add" @click="delRow(row.id)" />
										</template>
									</el-table-column>
								</el-table>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="最低薪资" prop="minMoney">
								<el-input
									v-model="updateJobDialog.form.minMoney"
									v-input-money:4_2.short
									placeholder="请填写最低薪资"
									:max="Number(updateJobDialog.form.maxMoney || 9999)"
									@clear="$refs.form.validateField('minMoney')"
								>
									<template slot="append">千元</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="最高薪资" prop="maxMoney">
								<el-input
									v-model="updateJobDialog.form.maxMoney"
									v-input-money:4_2.short
									placeholder="请填写最高薪资"
									@clear="$refs.form.validateField('maxMoney')"
								>
									<template slot="append">千元</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="薪资补充说明" prop="payment">
								<el-input
									v-model="updateJobDialog.form.payment"
									placeholder="请填写薪资补充说明"
									maxlength="100"
								></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="工作性质" prop="jobNature">
								<el-select v-model="updateJobDialog.form.jobNature" placeholder="请选择工作性质">
									<el-option
										v-for="item in jobNatureOptions"
										:key="item.id"
										:label="item.shortName"
										:value="item.cciValue"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="岗位类型" prop="postType">
								<el-select v-model="updateJobDialog.form.postType" placeholder="请选择岗位类型">
									<el-option
										v-for="item in jobTypeOptions"
										:key="item.id"
										:label="item.shortName"
										:value="item.cciValue"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="项目地点" prop="area">
								<el-cascader
									:key="updateJobDialog.form.id"
									v-model="updateJobDialog.form.area"
									:options="addressOptions"
									placeholder="请选择项目地点"
								></el-cascader>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="招聘人数" prop="recruitNum">
								<el-input-number
									v-model="updateJobDialog.form.recruitNum"
									:min="1"
									:max="10000"
									size="medium"
									placeholder="请输入"
								></el-input-number>
							</el-form-item>
						</el-col>
						<el-col :span="16">
							<el-form-item label="招聘形式" prop="lineType">
								<el-radio-group v-model="updateJobDialog.form.lineType">
									<el-radio :label="0">线上</el-radio>
									<el-radio :label="1">线下</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>

						<template v-if="updateJobDialog.form.lineType === 1">
							<el-col :span="16">
								<el-form-item label="企业拟进校时间" prop="enterSchoolTime" label-width="150px">
									<el-date-picker
										v-model="updateJobDialog.form.enterSchoolTime"
										type="datetime"
										value-format="yyyy-MM-dd HH:mm:ss"
										format="yyyy-MM-dd HH:mm:ss"
										placeholder="请选择进校时间（提前2个工作日）"
										:picker-options="pickerOptions"
									></el-date-picker>
								</el-form-item>
							</el-col>
							<!--<el-col :span="8">-->
							<!--  <el-form-item label="招聘地点" prop="offlinePlace">-->
							<!--    <el-input-->
							<!--      v-model="updateJobDialog.form.offlinePlace"-->
							<!--      placeholder="请填写校内招聘地点"-->
							<!--      maxlength="100"-->
							<!--    ></el-input>-->
							<!--  </el-form-item>-->
							<!--</el-col>-->
						</template>
						<el-col :span="10">
							<el-form-item label="工作时间" prop="workTimeType">
								<el-radio-group v-model="updateJobDialog.form.workTimeType">
									<el-radio :label="0">长白班</el-radio>
									<el-radio :label="1">长夜班</el-radio>
									<el-radio :label="2">两班倒</el-radio>
									<el-radio :label="3">三班倒</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="14">
							<el-form-item label-width="0" prop="workTime">
								<el-select
									v-model="updateJobDialog.form.workTime"
									style="width: 50%"
									filterable
									allow-create
									default-first-option
									placeholder="请选择或输入工作时间"
								>
									<el-option
										v-for="item in workTimeOptions"
										:key="item"
										:label="item"
										:value="item"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="24">
							<el-form-item label="保障待遇" prop="tag">
								<el-select
									v-model="updateJobDialog.form.tag"
									style="width: 100%"
									multiple
									filterable
									allow-create
									default-first-option
									placeholder="请选择或输入保障待遇"
								>
									<el-option
										v-for="item in tagOptions"
										:key="item"
										:label="item"
										:value="item"
									></el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="24">
							<el-form-item label="公司福利" prop="benefit">
								<el-input
									v-model="updateJobDialog.form.benefit"
									placeholder="请填写benefit"
									type="input"
								></el-input>
								<span class="tips">提示：下午茶、生日礼物，节日福利，带薪年假等</span>
							</el-form-item>
						</el-col>
					</el-row>
				</div>
			</el-form>
			<div v-if="updateJobDialog.title !== '查看职位'" class="info-save">
				<el-button
					class="info-save-confirm"
					type="primary"
					size="large"
					:loading="updateJobDialog.submitLoading"
					@click="handlerSubmit"
				>
					保存
				</el-button>
				<!-- <el-button class="info-save-staging" size="large" @click="resetForm">重置</el-button> -->
				<el-button class="info-save-staging" size="large" @click="updateJobDialog.visible = false">
					关闭
				</el-button>
			</div>
			<div v-else class="info-save">
				<el-button class="info-save-staging" size="large" @click="updateJobDialog.visible = false">
					关闭
				</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import { getDictionaryByCode } from '@/utils';
import Tinymce from '@/components/tinymce';
import { v4 as uuidv4 } from 'uuid';
const INIT_JOB_DETAIL = {
	name: '', //职位名称
	tag: '', //保障待遇
	updateJobDialog: '',
	targetColSchMaj: [],
	lineType: 0, // 参与招聘形式：0线上；1线下
	workTimeType: 0, //0长白班、1长夜班、2两班倒、3三班倒
	offlinePlace: '', // 招聘地点
	enterSchoolTime: '' // 进校时间
};
export default {
	components: { Tinymce },
	props: {
		// dialogDetail: {
		// 	type: Object
		// }
	},
	data() {
		var validateName = (rule, value, callback) => {
			if (this.targetColSchMaj.length < 1) {
				callback(new Error('请添加职业面向'));
			}
			// 检查是否有完全一样的数据 targetCollege targetSchool targetMajor
			for (let i = 0; i < this.targetColSchMaj.length; i++) {
				for (let j = i + 1; j < this.targetColSchMaj.length; j++) {
					if (
						this.targetColSchMaj[i].targetCollege === this.targetColSchMaj[j].targetCollege &&
						this.targetColSchMaj[i].targetSchool === this.targetColSchMaj[j].targetSchool
					) {
						callback(new Error(`序号${i + 1}、${j + 1}选项学院重复，请检查`));
						return;
					}
				}
			}
			callback();
		};
		return {
			workTimeOptions: ['周末双休', '周末单休'],
			tagOptions: [
				'基本养老保险',
				'基本医疗保险',
				'失业保险',
				'工伤保险',
				'生育保险',
				'住房公积金',

				'‌补充医疗保险',
				'长期护理险‌',
				'企业年金',
				'职业年金'
			],
			targetColSchMaj: [],
			educationOptions: [],
			jobNatureOptions: [],
			jobTypeOptions: [],
			workExperienceOptions: [],
			addressOptions: [],
			pickerOptions: {
				disabledDate(time) {
					// 禁用今天之前的日期和今天后两个工作日内的日期
					const today = new Date();
					today.setHours(0, 0, 0, 0);

					// 计算两个工作日后的日期（简单处理，不考虑周末和节假日）
					const twoDaysLater = new Date(today);
					twoDaysLater.setDate(today.getDate() + 2);

					return time.getTime() < twoDaysLater.getTime();
				}
			},
			updateJobDialog: {
				visible: false,
				title: '新增职位',
				submitLoading: false,
				form: { ...INIT_JOB_DETAIL },
				rules: {
					name: [{ required: true, message: '请输入职位名称', trigger: 'blur' }],
					area: [{ required: true, message: '请选择项目地点', trigger: 'change' }],
					education: [{ required: true, message: '请选择学历要求', trigger: 'change' }],
					contact: [{ required: true, message: '请选择联系人', trigger: 'change' }],
					introduce: [{ required: true, message: '请输入岗位介绍', trigger: 'blur' }],
					targetColSchMaj: [
						{ required: true, validator: validateName, trigger: 'change' } // 错误提示会显示validatePrice的Error信息
					],
					jobNature: [{ required: true, message: '请选择工作性质', trigger: 'change' }],
					maxMoney: [
						{ required: true, message: '请输入最高薪资', trigger: 'blur' },
						{ validator: this.validateMaxMoney, trigger: 'blur' }
					],
					minMoney: [
						{ required: true, message: '请输入最低薪资', trigger: 'blur' },
						{ validator: this.validateMinMoney, trigger: 'blur' }
					],
					payment: [{ required: true, message: '请输入薪资补充说明', trigger: 'blur' }],
					postType: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
					workExperience: [{ required: true, message: '请输入工作经验要求', trigger: 'blur' }],
					address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
					lineType: [{ required: true, message: '请选择招聘形式', trigger: 'change' }],
					enterSchoolTime: [{ required: true, message: '请选择进校时间', trigger: 'change' }],
					offlinePlace: [{ required: true, message: '请填写招聘地点', trigger: 'blur' }],
					recruitNum: [{ required: true, message: '请输入招聘人数', trigger: 'blur' }],
					workTime: [{ required: true, message: '请输入工作时间', trigger: 'blur' }],
					tag: [{ required: true, message: '请选择保障待遇', trigger: 'blur' }]
				}
			},
			tinymceToolbar: [
				'undo redo |  alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist  | forecolor backcolor casechange   removeformat | pagebreak | charmap emoticons | '
			],
			tinymcePlugins: [],
			schoolList: [], //面对学校
			collegeList: [], //面对学院
			specialityObj: {}, //面对专业,
			filteredOptions: [],
			tempQuery: '' // 临时存储筛选词
		};
	},
	created() {
		this.getEducationList();
		this.getJobNatureList();
		this.getJobTypeList();
		this.getWorkExperienceList();
		// this.getJobList();
		this.getRegion();
		this.getOrgListF(); //获取学校学院专业信息
	},
	methods: {
		handleFilter(query, index, targetCollegeCode) {
			if (this.filterTimeout) clearTimeout(this.filterTimeout);
			this.tempQuery = query; // 记录当前筛选词
			this.filterTimeout = setTimeout(() => {
				const options = this.specialityObj[targetCollegeCode] || [];
				if (query) {
					const filtered = options.filter(item => item.label.includes(query));

					if (filtered.length > 0) {
						const currentMajors = this.targetColSchMaj[index].targetMajor || [];
						if (!currentMajors.includes(filtered[0].value)) {
							this.$set(
								this.targetColSchMaj[index],
								'targetMajor',
								[...currentMajors, filtered[0].value].filter((v, i, a) => a.indexOf(v) === i)
							);
						}
					}
					this.filteredOptions = filtered;
				} else {
					this.filteredOptions = options;
				}
			}, 100);
		},
		/**
		 * @description 新增操作
		 */
		handlerNew() {
			this.updateJobDialog.visible = true;
			this.updateJobDialog.title = '新增职位';
			this.updateJobDialog.form = { ...INIT_JOB_DETAIL };
			this.targetColSchMaj = [];
			// this.$set(this.updateJobDialog.form, 'targetCollege', this.schoolList?.[0]?.value || ''); //默认选中第一个学校
			this.addTarget();
		},
		/**
		 * @description 编辑操作
		 * */
		handleEdit(row, title) {
			this.$api.enterprise_center.getPostInfo({ id: row.id }).then(res => {
				if (res.success) {
					this.updateJobDialog.visible = true;
					this.updateJobDialog.title = title === '查看职位' ? title : '编辑职位';
					this.updateJobDialog.form = {
						// ...row,
						...res.results,
						area: res.results.area.split(','),
						tag: res.results.tag.split(',')
					};
					// let targetColSchMaj = JSON.parse(JSON.stringify(row.targetColSchMaj || []));
					let targetColSchMaj = JSON.parse(JSON.stringify(res.results.targetColSchMaj || []));
					// 面向学校，面向学院，面向专业的下拉数据填充
					// 由于学校和学院数据是固定的，现只需要处理面向专业的下拉数据填充
					if (targetColSchMaj.length > 0) {
						targetColSchMaj = targetColSchMaj.map(elem => {
							let collegeItem = this.collegeList.filter((item, index) => {
								return item.value == elem.targetCollege;
							});
							this.getMajorList(collegeItem[0].code);
							return {
								...elem,
								targetMajor: elem?.targetMajor?.split?.(',') || [], // 专业名称
								id: uuidv4(), // 生成不同的id
								targetCollegeCode: collegeItem?.[0]?.code || '' // 学院code用于查找用
							};
						});
					}
					this.targetColSchMaj = targetColSchMaj;
				} else {
					this.$message.warning(res.msg);
				}
			});
		},
		// 获取地区数据
		async getRegion() {
			const { results } = await this.$api.employment_api.getSubRegion();
			this.addressOptions = this.handleDealTree(results);
		},
		handleDealTree(nodes) {
			return nodes.map(item => {
				if (Array.isArray(item.children) && item.children.length > 0) {
					item.children = this.handleDealTree(item.children);
				} else {
					delete item.children; // 删除空的 children 属性
				}
				return item;
			});
		},
		validateMinMoney(rule, value, callback) {
			if (Number(value) > Number(this.updateJobDialog.form.maxMoney)) {
				callback(new Error('最低薪资不能大于最高薪资'));
			}
			callback();
		},
		validateMaxMoney(rule, value, callback) {
			if (Number(value) < Number(this.updateJobDialog.form.minMoney)) {
				callback(new Error('最高薪资不能小于最低薪资'));
			}
			callback();
		},
		// 数据字典查询学历要求
		async getEducationList() {
			const dicts = await getDictionaryByCode(['post_education']);
			this.educationOptions = dicts.post_education;
		},
		// 数据字典查询工作性质
		async getJobNatureList() {
			const dicts = await getDictionaryByCode(['post_job_nature']);
			this.jobNatureOptions = dicts.post_job_nature;
		},
		// 数据字典查询岗位类型
		async getJobTypeList() {
			const dicts = await getDictionaryByCode(['post_job_type']);
			this.jobTypeOptions = dicts.post_job_type;
		},
		// 数据字典查询工作经验
		async getWorkExperienceList() {
			const dicts = await getDictionaryByCode(['post_work_experience']);
			this.workExperienceOptions = dicts.post_work_experience;
		},
		handlerSubmit() {
			// 更新表单中的介绍字段
			this.$set(this.updateJobDialog.form, 'introduce', this.$refs.tinymce.getContent() || '');

			// 根据招聘形式动态设置验证规则
			if (this.updateJobDialog.form.lineType === 0) {
				// 线上招聘，移除线下招聘相关字段的验证
				this.updateJobDialog.rules.enterSchoolTime = [];
				this.updateJobDialog.rules.offlinePlace = [];
			} else {
				// 线下招聘，添加相关字段的验证
				this.updateJobDialog.rules.enterSchoolTime = [
					{ required: true, message: '请选择进校时间', trigger: 'change' }
				];
				this.updateJobDialog.rules.offlinePlace = [
					{ required: true, message: '请填写招聘地点', trigger: 'blur' }
				];
			}

			// 验证表单
			this.$refs.form.validate(valid => {
				if (!valid) {
					// 如果表单验证失败，直接返回
					return;
				}

				// 检查目标学校、学院和专业是否为空
				const hasError = this.targetColSchMaj.some((item, i) => {
					if (!item.targetSchool) {
						this.$message.warning(`职位面向-序号${i + 1}（面向学校为必选）`);
						return true; // 中断循环
					}
					if (!item.targetCollege) {
						this.$message.warning(`职位面向-序号${i + 1}（面向学院为必选）`);
						return true; // 中断循环
					}
					if (item.targetMajor?.length === 0) {
						this.$message.warning(`职位面向-序号${i + 1}（面向专业为必选）`);
						return true; // 中断循环
					}
					return false; // 继续循环
				});

				if (hasError) {
					// 如果存在错误，则直接返回，不执行提交操作
					return;
				}

				// 提交表单
				this.updateJobDialog.submitLoading = true;
				this.$api.enterprise_center
					.updateJobInfo({
						...this.updateJobDialog.form,
						area: this.updateJobDialog.form.area.join(','),
						tag: this.updateJobDialog.form.tag.join(','),
						targetColSchMaj: this.targetColSchMaj.map(item => {
							return {
								...item,
								targetMajor: Array.isArray(item.targetMajor) ? item.targetMajor.join(',') : ''
							};
						})
					})
					.then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.updateJobDialog.visible = false;
							// this.getJobList();
							// 编辑新建成功之后的回调函数
							this.$emit('positionCallback', this.updateJobDialog.form);
						} else {
							this.$message.error(res.msg);
						}
					})
					.catch(error => {
						console.error('提交出错:', error);
						this.$message.error('提交出错，请稍后重试');
					})
					.finally(() => {
						this.updateJobDialog.submitLoading = false;
					});
			});
		},
		resetForm() {
			this.$refs.form.resetFields();
			// this.$set(this.updateJobDialog.form, 'targetCollege', this.schoolList?.[0]?.value || ''); //默认选中第一个学校
		},
		// @--表单区域
		closeDialog() {
			this.updateJobDialog.visible = false;
			this.resetForm();
		},
		addTarget() {
			const schoolObj = this.schoolList?.[0] || {}; // 默认学校
			this.targetColSchMaj.push({
				id: uuidv4(), // 生成不同的id
				targetSchool: schoolObj.value || '',
				targetCollege: '',
				targetMajor: ''
			});
		},
		delRow(id) {
			const index = this.targetColSchMaj.findIndex(item => item.id == id);
			this.targetColSchMaj.splice(index, 1);
		},
		// 获取二级学校学院数据
		getOrgListF() {
			this.$api.personal_api.getColSchSelectList().then(({ rCode, msg, results }) => {
				if (rCode == 0) {
					this.schoolList = results || [];
					// 默认获取第一个学校的学院数据
					const excludeCodes = ['220', '002301', '099', '201', '202'];
					this.collegeList =
						this.schoolList?.[0]?.children?.filter(item => !excludeCodes.includes(item.code)) || [];
				}
			});
		},
		// 获取专业数据
		getMajorList(code) {
			if (this.specialityObj[code + '']) return;
			let data = {
				schoolCode: code, //学院编号
				pageSize: 1000 //每页记录数
			};
			this.$api.personal_api.getMajorList(data).then(({ rCode, msg, results }) => {
				if (rCode == 0) {
					this.$set(this.specialityObj, code + '', results?.records || []);
					this.filteredOptions = results?.records || [];
					console.log(this.specialityObj, 'specialityObj');
				}
			});
		},
		collegeClick(item, index) {
			this.targetColSchMaj[index].targetMajor = '';
			this.targetColSchMaj[index].targetMajorName = '';
			this.targetColSchMaj[index].targetCollegeCode = item.code;
			this.getMajorList(item.code);
		}
	}
};
</script>
<style lang="scss" scoped>
.tips {
	font-size: 12px;
	color: #333;
	height: 20px;
	line-height: 20px;
	display: flex;
	align-items: center;
}

.info-save {
	text-align: center;
}

.el-form-item__content .el-input-group {
	vertical-align: middle;
}

::v-deep .el-input.is-disabled .el-input__inner {
	color: rgba(0, 0, 0, 0.4);
}
</style>
