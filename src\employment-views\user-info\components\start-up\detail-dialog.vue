<!--
 @desc:创业项目发布 发布弹窗
 @author: WH
 @date: 2023/9/25
 -->
<template>
	<el-dialog
		:title="title"
		:visible.sync="visible"
		:before-close="beforeClose"
		width="60%"
		:loading="loading"
		:close-on-click-modal="false"
	>
		<el-form ref="form" class="is-form" :rules="rules" :model="form" label-width="140px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="项目名称" prop="name">
						<el-input v-model="form.name" :readonly="type == 'look'"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="封面" prop="cover">
						<uploadItem
							:only-read="type == 'look'"
							:file-number="1"
							:code="formData.coverCode"
							:own-id="formData.uploadId"
							@fileChange="coverFileChange"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="电话号码" prop="telephone">
						<el-input v-model="form.telephone" :readonly="type == 'look'"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="电子邮箱" prop="email">
						<el-input v-model="form.email" :readonly="type == 'look'"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="项目简介" prop="profiles">
						<el-input v-model="form.profiles" :readonly="type == 'look'"></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-form-item label="项目介绍" prop="introduction">
				<!-- <el-input v-model="form.introduction" type="textarea" :rows="2"></el-input> -->
				<Tinymce
					ref="tinymce"
					:value.sync="form.introduction"
					:form-data="formData"
					:image-upload-url="updUrl"
					:disabled="type == 'look'"
					@imageUploadSuccess="handleImageUploadSuccess"
				></Tinymce>
			</el-form-item>

			<el-form-item label="项目地址" prop="address">
				<el-input v-model="form.address" :readonly="type == 'look'"></el-input>
			</el-form-item>
			<el-form-item label="地址经纬度" prop="lngLat">
				<lng-lat v-model="form.lngLat" :readonly="type == 'look'"></lng-lat>
			</el-form-item>
			<!-- <el-form-item label="发起人" prop="createUserName">
				<el-input v-model="form.createUserName"></el-input>
			</el-form-item> -->

			<!-- <el-form-item label="状态" prop="status">
				<el-switch v-model="form.status" active-text="启用" inactive-text="停用"></el-switch>
			</el-form-item>
			<el-form-item label="审核状态">
				<el-radio-group v-model="form.auditStatus">
					<el-radio :label="0">待审核</el-radio>
					<el-radio :label="1">已通过</el-radio>
					<el-radio :label="2">已驳回</el-radio>
					<el-radio :label="3">已结束</el-radio>
				</el-radio-group>
			</el-form-item> -->
			<el-form-item label="附件上传">
				<!-- <el-form-item label="附件上传" prop="uploadFile"> -->
				<uploadItem
					:only-read="type == 'look'"
					accept=".pdf, .doc, .docx"
					:file-number="10"
					:code="formData.code"
					:own-id="formData.uploadId"
					@fileChange="fileChange"
				/>
			</el-form-item>
		</el-form>
		<div v-if="type !== 'look'" slot="footer" class="dialog-footer">
			<el-button type="primary" :loading="loading" @click="submit">保 存</el-button>
			<el-button @click="beforeClose">取 消</el-button>
		</div>
	</el-dialog>
</template>

<script>
import uploadItem from '@/alumni-association-views/user-info/components/uploadItem.vue';
import LngLat from '@/components/lnglat';
import Tinymce from '@/components/tinymce';
import { baseUrl } from '@/config';

import { v4 as uuidv4 } from 'uuid';

export default {
	components: { uploadItem, LngLat, Tinymce },
	props: {
		type: {
			type: String,
			required: true
		},
		id: {
			type: String,
			default: '',
			required: false
		},
		visible: {
			type: Boolean,
			required: true
		}
	},

	data() {
		return {
			loading: false,
			updUrl: baseUrl + '/ybzy/mecpfileManagement/front/upload', // 文件上传地址
			formData: {
				uploadId: '',
				coverCode: 'job_co_startup_cover',
				code: 'job_student_project_adjunct'
			},
			//设置introduction的默认值 防止验证的时候报错
			form: { lngLat: [], introduction: '' },
			rules: {
				name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
				cover: [{ required: true, message: '请上传封面', trigger: 'change' }],
				introduction: [{ required: true, message: '请输入项目介绍', trigger: 'blur' }],
				address: [{ required: true, message: '请输入项目地址', trigger: 'blur' }],
				createUserName: [{ required: true, message: '请输入发起人姓名', trigger: 'blur' }],
				lngLat: [{ required: true, message: '请选择定位', trigger: 'change' }],
				profiles: [{ required: true, message: '项目简介', trigger: 'change' }],
				email: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!value) {
								return callback(new Error('电子邮箱不能为空'));
							}
							if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确的电子邮箱'));
							}
						}
					}
				],
				telephone: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (!value) {
								return callback(new Error('联系方式不能为空'));
							}
							if (/^1[3456789]\d{9}$/.test(value)) {
								callback();
							} else {
								callback(new Error('请输入正确手机格式'));
							}
						}
					}
				]
			}
		};
	},
	computed: {
		title() {
			const KEY = {
				look: '查看',
				edit: '编辑',
				add: '新增'
			};
			return KEY[this.type];
		}
	},
	watch: {
		visible(newVal) {
			if (newVal && this.type == 'add') {
				this.formData.uploadId = uuidv4();
				// this.beforeClose();
			} else if (newVal && ['edit', 'look'].includes(this.type)) {
				this.formData.uploadId = this.id;
				this.getStartUpById();
			}
		}
	},
	methods: {
		coverFileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					this.form.cover = file[0]?.adjunctId;
					// this.$set(this.form, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('cover');
				});
			}
		},
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					let fileIdStr = file.map(item => item.adjunctId).join(',');
					this.form.uploadFile = fileIdStr;
					// this.$set(this.form, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('uploadFile');
				});
			}
		},
		submit() {
			this.form.introduction = this.$refs.tinymce.getContent() || '';
			this.$refs.form.validate(valid => {
				if (valid) {
					this.submitFn();
				}
			});
		},
		beforeClose() {
			this.$emit('update:visible', false);
			this.$refs.form.resetFields();
		},
		async getStartUpById() {
			this.loading = true;
			try {
				let { rCode, results } = await this.$api.employment_api.getStartUpById({
					id: this.id
				});
				if (rCode == 0) {
					results.lngLat = [results.longitude, results.latitude];
					this.form = { ...results };
				}
				console.log('>>>over');

				// else {
				// 	this.$message.warning(msg);
				// }
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		async submitFn() {
			this.loading = true;
			try {
				let request = '';
				let data = { ...this.form };
				data.longitude = data.lngLat[0];
				data.latitude = data.lngLat[1];
				delete data.lngLat;
				if (this.type === 'add') {
					data.id = this.formData.uploadId;
					request = 'addStartUp';
				} else if (this.type == 'edit') {
					request = 'updateStartUp';
				}
				console.log(
					'>>>',
					this.$api.employment_api[request],
					this.$api.employment_api['updateStartUp']
				);
				let { rCode, msg } = await this.$api.employment_api[request](data);
				if (rCode == 0) {
					this.$message.success(msg);
					this.form = {};
					this.beforeClose();
					this.$emit('callback');
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		// 富文本框上传成功
		handleImageUploadSuccess(res, success, failure) {
			if (res.success && res.rCode === 0) {
				success(
					`${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${res.results[0].adjunctId}`
				);
			} else {
				failure(res.msg);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.is-form {
	height: 60vh;
	overflow-y: auto;
	overflow-x: hidden;
	padding-right: 20px;
}
</style>
