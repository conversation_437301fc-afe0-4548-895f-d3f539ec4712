<!--
 @desc:我的咨询卡片
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- <img class="is-img" src="@/assets/shop-images/default-avatar.png" alt="" /> -->
		<img :src="cardData | chatPhoto(_userinfo, getImgUrl)" class="is-img" alt="" />
		<header>
			<div class="hr-info">
				<span>{{ cardData | chatName(_userinfo) }}</span>
				<span>HR</span>
			</div>
			<el-button type="primary" @click.stop="handle">继续沟通</el-button>
		</header>
		<article>
			<p>{{ cardData.enterpriseName }}</p>
			<p>{{ cardData.newTime }}</p>
		</article>
	</div>
</template>

<script>
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	filters: {
		chatName(item, userinfo) {
			// 判断用户是发方还是收方
			let name = '-';
			if (item.send_id == userinfo.id) {
				name = item.recipientName;
			} else {
				name = item.sendName;
			}
			return name;
		},
		chatPhoto(item, userinfo, fun) {
			// 判断用户是发方还是收方
			let photo = '';
			if (item.send_id == userinfo.id) {
				photo = item.recipientPhoto;
			} else {
				photo = item.sendPhoto;
			}
			if (!photo) {
				return require('@/assets/shop-images/default-avatar.png');
			}
			return fun(photo);
		}
	},
	mixins: [PreviewAdjunctMixin],
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					imgUrl: '',
					hrName: '王女士',
					hrType: 'HR',
					conpany: '扭博科技有限公司',
					interTime: '2023.06.30 13:30'
				};
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			const TEXT = ['待面试', '已面试', '职位已关闭'];
			return TEXT[this.cardData.state - 0];
		}
	},
	methods: {
		handle() {
			this.$emit('handle', this.cardData);
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(space-evenly, flex-start);
	flex-direction: column;
	position: relative;
	width: 100%;
	height: 90px;
	margin-bottom: 20px;
	padding-left: 88px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	border-bottom: 1px solid #e8eaf0;
	.is-img {
		position: absolute;
		top: 50%;
		left: 0;
		margin-top: -36px;
		width: 72px;
		height: 72px;
		border-radius: 50%;
	}
	header {
		@include flexBox(space-between);
		width: 100%;
		.hr-info {
			@include flexBox(flex-start);

			span {
				display: inline-block;
				font-size: 16px;
				color: #8390a3;
				&:nth-child(1) {
					border-right: 1px solid #8390a3;
					padding-right: 10px;
					margin-right: 10px;
				}
			}
		}
		.hr-state {
			font-size: 16px;
			color: #333333;
		}
		.close {
			color: #999999;
		}
	}
	article {
		@include flexBox(space-between);
		width: 100%;
		font-size: 16px;
		color: #8390a3;
		p {
			&:nth-child(2) {
				color: #999999;
			}
		}
	}
}
</style>
