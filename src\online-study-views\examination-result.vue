<template>
	<div class="result-box">
		<div v-loading="loading" class="main">
			<div class="img-box">
				<span class="img-text">考试</span>
				<img class="top-img" src="@/assets/study_images/examination-bg.png" alt="" />
			</div>
			<div class="back-box" @click="back">
				<img class="back-img" src="@/assets/study_images/examination-back.png" alt="" />
				<span>返回课程</span>
			</div>
			<div class="result-info">
				<div class="certificate">
					<div class="info">
						<p class="text1">{{ userInfo.username }}学员:</p>
						<p class="text2">恭喜您完成了《{{ userInfo.courseName }}》在线课程，获得结业证书</p>
						<p class="text3">技状元数字校园</p>
						<p class="text4">{{ userInfo.birthday }}</p>
					</div>
				</div>
				<!-- <img class="result-img" src="@/assets/study_images/result-img.png" alt="" /> -->
				<span class="text">考试结果</span>
				<span class="result-text">正确率：{{ resulit }}</span>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			loading: false, //加载动画
			resulit: 0, //考试结果
			examinationId: '', //试卷id
			courseId: '' //课程id
		};
	},
	computed: {
		userInfo() {
			const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
			// 获取当前时间2024年9月30日
			const date = new Date();
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			userInfo.birthday = `${year}年${month}月${day}日`;
			userInfo.courseName = localStorage.getItem('courseName');
			return userInfo;
		}
	},
	mounted() {
		this.examinationId = this.$route.query.examId || '';
		this.courseId = this.$route.query.courseId || '';
		this.examinationResult();
	},
	methods: {
		// 查看结果
		examinationResult() {
			this.loading = true;
			let param = {
				examId: this.examinationId,
				courseId: this.courseId
			};
			this.$api.study_api
				.examinationResult(param)
				.then(({ code, msg }) => {
					this.loading = false;
					if (code == 200) {
						this.resulit = msg || 0;
					}
				})
				.catch(() => {
					this.loading = false;
				});
		},
		// 返回课程
		back() {
			this.$router.push({
				path: '/freecourses',
				query: { id: this.courseId }
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.result-box {
	width: 100%;
	height: 100%;
	background: #ffffff;
	font-family: SourceHanSansCN-Regular, SourceHanSansCN;
	.main {
		width: 1200px;
		margin: 0 auto;
		height: 100%;
		padding: 20px 0;
	}
	.img-box {
		width: 100%;
		height: 180px;
		position: relative;
	}
	.img-text {
		position: absolute;
		display: inline-block;
		font-size: 60px;
		font-family: DOUYUFont;
		color: #378cef;
		line-height: 79px;
		left: 104px;
		top: 60px;
	}
	.top-img {
		width: 100%;
		height: 180px;
	}
	.back-box {
		height: 16px;
		font-size: 16px;
		font-weight: 400;
		color: #0374ec;
		margin-top: 40px;
		cursor: pointer;
	}
	.back-img {
		width: 14px;
		height: 14px;
		margin-right: 9px;
	}
	.result-info {
		margin-top: 110px;
		margin-bottom: 100px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		.certificate {
			padding: 238px 45px 0;
			font-size: 15px;
			width: calc(1561px - 860px);
			height: calc(1126px - 635px);
			color: #000;
			background-image: url(~@/assets/study_images/certificate-bg.png);
			background-size: 100% 100%;
			// 加点阴影
			box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
			.info {
				.text1 {
					margin-bottom: 20px;
				}
				.text2 {
					margin-left: 32px;
					margin-bottom: 20px;
				}
				.text3 {
					text-align: right;
				}
				.text4 {
					text-align: right;
				}
			}
		}
	}
	.result-img {
		width: 160px;
		height: 152px;
	}
	.text {
		font-size: 24px;
		font-weight: 500;
		color: #222222;
		margin-top: 37px;
	}
	.result-text {
		font-size: 20px;
		color: #0076e9;
		margin-top: 18px;
	}
}
</style>
