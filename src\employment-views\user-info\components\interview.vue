<!--
 @desc:个人中心 我的面试邀请  
 @author: WH
 @date: 2023/9/6
 -->
<template>
	<div class="main">
		<div v-loading="loading" class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无面试邀请信息',
					detail: '你还没有面试邀请信息，或者前往',
					clickText: '招聘信息'
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<interview-card v-for="(item, index) in list" :key="index" :card-data="item" />
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import <PERSON><PERSON><PERSON> from './interview/interview-card.vue';
import NoData from './no-data.vue';

export default {
	components: { InterviewCard, NoData },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			list: [],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10
		};
	},

	mounted() {
		this.getInterviewList();
	},
	methods: {
		async getInterviewList() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.getInterviewList({
					pageNum: this.page,
					pageSize: this.size
				});
				if (rCode == 0) {
					this.list = results.records;
					this.total = this.total.total;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		noDataFn() {
			this.$router.push('/job-list');
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.getInterviewList();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getInterviewList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
