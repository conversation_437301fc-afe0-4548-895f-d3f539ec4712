<!--
 @desc:个人中心 我要招人
 @author: WH
 @date: 2023/9/8
 -->
<template>
	<div class="main">
		<div class="list-box">
			<!-- <no-data
				v-if="list.length == 0"
				@noDataFn="noDataFn"
				:tips="{
					title: '暂无面试邀请信息',
					detail: '你还没有面试邀请信息，或者前往',
					clickText: '招聘信息'
				}"
			/>
			<template v-else>
			</template> -->
			<no-data
				:tips="{
					title: '',
					detail: '点击前往',
					clickText: '我要招聘'
				}"
				@noDataFn="noDataFn"
			/>
			<roleTip ref="roleTip" :tip-text="tipText" :button="buttonText" @onClick="clickTip"></roleTip>
		</div>
		<!-- <div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div> -->
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
// import NoData from './no-data.vue';
import roleTip from '@/components/role-tip';
import NoData from './no-data.vue';

export default {
	components: { roleTip, NoData },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			// list: [1, 1, 1, 1, 1],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10,
			tipText: '您还未进行企业认证，相关功能服务暂无权限！',
			buttonText: '立即前往企业认证',
			role: [3, 4] //企业认证都得数值
		};
	},
	computed: {
		...mapGetters(['roles'])
	},
	mounted() {
		this.quickMenusRole();

		// this.handlerGetRegion();
		// this.handlerQueryDict();
	},
	methods: {
		// 判断当前用户是否有权限跳转企业管理端
		quickMenusRole() {
			let isRole = false;
			for (let role of this.roles) {
				if (this.role.includes(role)) {
					isRole = true;
				}
			}
			if (isRole) {
				this.$router.push('/independentPersonal/enterprise?type=position');
			} else {
				this.$refs.roleTip.visible = true;
			}
		},
		noDataFn() {
			this.quickMenusRole();
			// this.$router.push('/independentPersonal/enterprise?type=position');
		},
		/**提示弹窗中的按钮触发事件*/
		clickTip() {
			this.toSubMenu();
		},
		/**根据路由跳到对应的组件*/
		toSubMenu() {
			this.$router.push('/personal?subMenu=5-1');
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
