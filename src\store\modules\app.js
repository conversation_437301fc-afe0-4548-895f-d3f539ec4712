import Cookies from 'js-cookie';

const state = {
	sidebar: {
		opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
		withoutAnimation: false
	},
	device: 'desktop',
	subList: [
		{
			name: '个人中心',
			url: '/personal'
		}
	], // 子菜单集合
	routePath: [], // 面包屑
	current: 0, // tab选中的索引
	tenantId: localStorage.getItem('tenantId') || '', //租户id，接口请求时需要的
	logoBj: localStorage.getItem('logoBj') ? JSON.parse(localStorage.getItem('logoBj')) : {} //logo图片，登录图片等配置图片
};

const mutations = {
	TOGGLE_SIDEBAR: state => {
		state.sidebar.opened = !state.sidebar.opened;
		state.sidebar.withoutAnimation = false;
		if (state.sidebar.opened) {
			Cookies.set('sidebarStatus', 1);
		} else {
			Cookies.set('sidebarStatus', 0);
		}
	},
	CLOSE_SIDEBAR: (state, withoutAnimation) => {
		Cookies.set('sidebarStatus', 0);
		state.sidebar.opened = false;
		state.sidebar.withoutAnimation = withoutAnimation;
	},
	TOGGLE_DEVICE: (state, device) => {
		state.device = device;
	},
	/**更新tab菜单集合*/
	UPDATE_SUB_LIST: (state, subList) => {
		state.subList = state.subList.concat(subList);
	},
	/**更新路由历史*/
	UPDATE_ROUTE_PATH: (state, routePath) => {
		state.routePath = routePath;
		localStorage.setItem('routePath', JSON.stringify(routePath)); // 避免原地刷新网页
	},
	/**更新tab菜单选中索引*/
	SET_CURRENT: (state, current) => {
		state.current = current;
		localStorage.setItem('current', current.toString()); // 避免原地刷新网页
	},
	/**更新tab菜单选中索引*/
	SET_TENANTID: (state, current) => {
		console.log('SET_TENANTID-------------', current);
		state.tenantId = current?.tenantId;
		state.logoBj = current || {};
		localStorage.setItem('logoBj', JSON.stringify(current));
		localStorage.setItem('tenantId', current?.tenantId);
	}
};

const actions = {
	toggleSideBar({ commit }) {
		commit('TOGGLE_SIDEBAR');
	},
	closeSideBar({ commit }, { withoutAnimation }) {
		commit('CLOSE_SIDEBAR', withoutAnimation);
	},
	toggleDevice({ commit }, device) {
		commit('TOGGLE_DEVICE', device);
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
