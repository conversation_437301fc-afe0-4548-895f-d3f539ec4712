<template>
	<div class="hotel-time">
		<div class="hotel-time-top">
			<div class="line"></div>
			<div calss="tile">入住日期</div>
		</div>
		<div class="hotel-time-con">
			<div class="hotel-time-con-top">
				<span class="date-title">入住日期：</span>
				<el-date-picker
					v-model="startDate"
					type="date"
					style="margin-right: 30px"
					:picker-options="pickerOptions"
					placeholder="选择日期"
				></el-date-picker>
				<span class="date-title">离店日期：</span>
				<el-date-picker
					v-model="endDate"
					type="date"
					style="margin-right: 16px"
					placeholder="选择日期"
					:picker-options="pickerEndOptions"
				></el-date-picker>
				<span style="margin-right: 40px">{{ count }}晚</span>
				<span class="date-title">房间数</span>
				<el-input-number
					v-model="num"
					:min="1"
					:max="10"
					label="描述文字"
					@change="changeNum"
				></el-input-number>
			</div>
			<div v-if="skus[0]" class="hotel-time-con-bottom">
				<div v-for="(item, index) of skus[0].ATTRS" :key="index" class="item">{{ item }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import dateInterval from '@/utils/dateInterval';

export default {
	name: 'JoinHotelTime',
	props: {
		tip: {
			type: String,
			default: () => {
				return '';
			}
		},
		skus: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			num: 1,
			startDate: '',
			endDate: '',
			defaultEndDate: '',
			pickerOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
				}
			}
		};
	},
	computed: {
		count() {
			return dateInterval(this.startDate, this.endDate);
		},
		/**计算离店日期必须大于入住日期*/
		pickerEndOptions() {
			let startDate = new Date(this.startDate).getTime() + 24 * 60 * 60 * 1000;
			return {
				disabledDate(time) {
					return time.getTime() < startDate;
				}
			};
		}
	},
	watch: {
		count(newVal) {
			this.$emit(
				'changeSkuNum',
				this.num,
				new Date(this.startDate).toLocaleDateString(),
				new Date(this.endDate).toLocaleDateString()
			);
		},
		/**监听入住日期改变，离店日期必须大于入住日期*/
		startDate(newVal) {
			if (new Date(newVal).getTime() >= new Date(this.endDate)) {
				this.endDate = new Date(
					new Date(newVal).getTime() + 24 * 60 * 60 * 1000
				).toLocaleDateString();
			}
		}
	},
	created() {
		this.num = this.$route.query.skuNum || 1;
		this.startDate = this.$route.query.startDate || new Date().toLocaleDateString();
		this.endDate =
			this.$route.query.endDate ||
			new Date(new Date(this.startDate).getTime() + 24 * 60 * 60 * 1000).toLocaleDateString();
	},
	methods: {
		/**同步数量*/
		changeNum() {
			this.$emit(
				'changeSkuNum',
				this.num,
				new Date(this.startDate).toLocaleDateString(),
				new Date(this.endDate).toLocaleDateString()
			);
		}
	}
};
</script>

<style scoped lang="scss">
.hotel-time {
	margin: 16px 0;
	padding: 16px 23px 20px 20px;
	background: #ffffff;
	&-top {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		.line {
			width: 6px;
			height: 20px;
			background: var(--brand-6, #0076e8);
			margin-right: 7px;
		}
		.title {
			font-size: 16px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-con {
		background: #ffffff;
		border: 1px solid #d9d9d9;
		padding: 20px;
		margin-right: 14px;
		font-size: 14px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 22px;
		&-top {
			display: flex;
			align-items: center;
			margin-bottom: 24px;
			.date-title {
				margin-right: 7px;
			}
		}
		&-bottom {
			display: flex;
			align-items: center;
			.item {
				margin-right: 30px;
			}
		}
	}
}
</style>
