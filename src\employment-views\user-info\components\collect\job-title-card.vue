<!--
 @desc:收藏  职位卡片
 @author: WH
 @date: 2023/9/6
 -->
<template>
	<div class="card" @click="clickCard">
		<header>
			<div class="hr-info">
				<img :src="getImgUrl(cardData.HRPhoto)" alt="" />
				<span>{{ cardData.username }}</span>
				<span>HR</span>
			</div>
			<p class="hr-state" @click.stop="handle">
				<i class="el-icon-star-on"></i>
				{{ stateText }}
			</p>
		</header>
		<article>
			<div class="left">
				<div class="title-box">
					<p>
						{{ cardData.name }}
					</p>
					<!-- <span>{{ cardData.side }}</span> -->
				</div>
				<div class="monye-box">
					<p>{{ cardData.min_money }}~{{ cardData.max_money }}K</p>
					<ul class="tags-box">
						<li v-for="(item, index) in cardData.tag.split(',')" :key="index">{{ item }}</li>
					</ul>
				</div>
			</div>
			<div class="right">
				<div class="img-box">
					<img src="@/assets/employment-images/tool-enterprise.png" alt="" />
				</div>
				<div class="company-info">
					<p>{{ cardData.enterpriseName }}</p>
					<ul class="tags-box">
						<li v-for="(item, index) in cardData.enterpriseTags.split(',')" :key="index">
							{{ item }}
						</li>
					</ul>
				</div>
			</div>
		</article>
	</div>
</template>

<script>
// import { alumniUrl } from '@/config';
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	mixins: [PreviewAdjunctMixin],
	props: {
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			// const TEXT = ['取消收藏'];
			return '取消收藏';
			// return TEXT[this.cardData.state - 0];
		}
	},

	methods: {
		/**获取logo*/
		// getImgUrl(url) {
		// 	if (url.includes('http')) {
		// 		return url;
		// 	}
		// 	return `${alumniUrl}${url}`;
		// },
		handle() {
			this.$emit('handle', this.cardData);
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 100%;
	min-height: 150px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	header {
		@include flexBox(space-between);
		margin-bottom: 12px;
		// border: 1px solid red;
		.hr-info {
			@include flexBox(flex-start);

			img {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				margin-right: 6px;
			}
			span {
				display: inline-block;
				font-size: 16px;
				color: #8390a3;
				padding: 0 10px;
				&:nth-child(2) {
					border-right: 1px solid #8390a3;
				}
			}
		}
		.hr-state {
			font-size: 14px;
			font-weight: 400;
			color: #0076e8;
		}
	}
	article {
		@include flexBox(space-between);
		.left {
			.title-box {
				@include flexBox(flex-start);
				margin-bottom: 10px;
				font-size: 16px;
				color: #0076e8;
				span {
					font-size: 14px;
					margin-left: 10px;
					color: #999999;
				}
			}
			.monye-box {
				@include flexBox(flex-start);
				align-items: flex-start;
				font-size: 18px;
				color: #fe574a;
				p {
					margin-right: 16px;
				}
			}
		}
		.right {
			@include flexBox(space-between);
			width: 310px;
			.img-box {
				@include flexBox();
				width: 72px;
				height: 72px;
				border: 1px solid #f0f3f7;
				border-radius: 5px;
				img {
					width: 46px;
					height: 46px;
					border-radius: 6px;
				}
			}
			.company-info {
				p {
					font-size: 16px;
					color: #333333;
					margin-bottom: 24px;
				}
			}
		}
	}
}
.tags-box {
	@include flexBox(flex-start);
	flex-wrap: wrap;
	li {
		margin-right: 10px;
		font-size: 14px;
		padding: 4px 10px;
		background: #f1f3f8;
		color: #999;
		border-radius: 2px;
		margin-bottom: 4px;
	}
}
</style>
