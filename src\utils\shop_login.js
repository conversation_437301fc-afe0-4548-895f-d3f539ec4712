import { checkLogin } from '@/api/user';
import { codeLoginShop } from '@/api/model/shop';

async function codeToLogin(code) {
	let data = {
		authType: 'onlyCheck',
		code
	};
	await codeLoginShop(data);
}
export async function loginShop(res) {
	let checkRes = await checkLogin();
	console.log('loginShop', checkRes);
	if (checkRes.rCode === 0) {
		await codeToLogin(checkRes?.results?.code || '');
		console.log('codeToLogin', checkRes);
	} else {
		// this.$message.error(checkRes.msg);
	}
}
