<template>
	<el-form
		ref="ruleForm"
		:model="loginForm"
		:rules="loginRules"
		auto-complete="on"
		label-position="left"
	>
		<el-form-item prop="phone">
			<span class="svg-container">
				<i class="iconfont icon-user"></i>
			</span>
			<el-input
				ref="phone"
				v-model="loginForm.phone"
				placeholder="请输入手机号码"
				name="phone"
				type="text"
				tabindex="1"
				auto-complete="on"
			/>
		</el-form-item>
		<el-form-item prop="password">
			<span class="svg-container">
				<i class="iconfont icon-lock-on"></i>
			</span>
			<el-input
				ref="password"
				v-model="loginForm.password"
				placeholder="请输入密码，8-12位数字字母特殊字符符号"
				name="password"
				type="password"
				readonly
				tabindex="1"
				auto-complete="on"
				onfocus="this.removeAttribute('readonly')"
			/>
		</el-form-item>
		<el-form-item prop="isPassword">
			<span class="svg-container">
				<i class="iconfont icon-lock-on"></i>
			</span>
			<el-input
				ref="isPassword"
				v-model="loginForm.isPassword"
				placeholder="请再次输入密码"
				name="isPassword"
				type="password"
				readonly
				tabindex="1"
				auto-complete="on"
				onfocus="this.removeAttribute('readonly')"
			/>
		</el-form-item>
		<div class="loginCode">
			<el-form-item prop="verifyCode">
				<span class="svg-container">
					<i class="iconfont icon-secured"></i>
				</span>
				<el-input
					ref="verifyCode"
					v-model="loginForm.verifyCode"
					type="text"
					placeholder="请输入验证码"
					name="verifyCode"
					tabindex="2"
					auto-complete="on"
					@keyup.enter.native="handleRegister"
				/>
			</el-form-item>
			<el-button :disabled="codeText != '获取验证码'" class="getCode" plain @click="handlerGetCode">
				{{ codeText }}
			</el-button>
		</div>
		<div v-if="!plain" class="remember"></div>
		<el-button
			v-if="!plain"
			:loading="loading"
			class="submit_btn"
			type="primary"
			style="width: 100%; margin-bottom: 13px"
			@click.native.prevent="handleRetrieve"
		>
			下一步
		</el-button>
	</el-form>
</template>

<script>
import { validPhone, validPassWord } from '@/utils/validate';
export default {
	name: 'RetrieveForm',
	props: {
		// 朴素模式，不展示下一步按钮，兼容密码修改使用
		plain: { type: Boolean, default: false }
	},
	data() {
		const validatePhone = (rule, value, callback) => {
			if (!validPhone(value)) {
				callback(new Error('请输入正确的手机号'));
			} else {
				callback();
			}
		};
		const validatePassword = (rule, value, callback) => {
			if (!validPassWord(value)) {
				callback(new Error('密码需为8~16位非空字符，并包含数字、大小写字母、特殊字符'));
			} else {
				callback();
			}
		};
		const validateIsPassword = (rule, value, callback) => {
			if (value != this.loginForm.password) {
				callback(new Error('密码输入不一致'));
			} else {
				callback();
			}
		};
		return {
			loginForm: {
				phone: '',
				password: '',
				isPassword: '',
				verifyCode: ''
			},
			loginRules: {
				phone: [{ required: true, trigger: 'blur', validator: validatePhone }],
				password: [{ required: true, trigger: 'blur', validator: validatePassword }],
				isPassword: [{ required: true, trigger: 'blur', validator: validateIsPassword }],
				verifyCode: [{ required: true, trigger: 'blur', message: '请输入验证码' }]
			},
			codeTime: 60,
			loading: false,
			redirect: undefined,
			codeText: '获取验证码',
			codeTimer: null
		};
	},
	watch: {
		$route: {
			handler: function (route) {
				this.redirect = route.query && route.query.redirect;
			},
			immediate: true
		}
	},
	methods: {
		// 找回密码
		handleRetrieve() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					this.loading = true;
					this.$api.personal_api
						.codeResetPwd({
							phone: this.loginForm.phone,
							// 密码注册base64编码
							password: this.loginForm.password,
							verifyCode: this.loginForm.verifyCode
						})
						.then(() => {
							this.$message({
								message: '重置成功',
								type: 'success'
							});
							this.loading = false;
							// 进入成功面板
							this.$emit('comp-change', 2);
						})
						.catch(() => {
							this.loading = false;
						});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},
		handlerGetCode() {
			// 验证手机号
			this.$refs.ruleForm.validateField('phone');
			// 手机号正确时
			if (this.loginForm.phone) {
				this.codeText = '正在获取...';
				this.$api.personal_api
					.sendResetCode({
						phone: this.loginForm.phone
					})
					.then(res => {
						this.$message({
							message: '验证码获取成功',
							type: 'success'
						});
						// 响应成功，开始倒计时
						this.handleCodeTime();
					})
					.catch(() => {
						this.codeText = '获取验证码';
					});
			}
		},
		handleCodeTime() {
			this.codeTimer = setInterval(() => {
				this.codeTime -= 1;
				// 倒计时结束展示文字
				if (this.codeTime <= 0) {
					clearInterval(this.codeTimer);
					this.codeTime = 60;
					this.codeText = '获取验证码';
				} else {
					// 开始倒计时
					this.codeText = `${this.codeTime} s`;
				}
			}, 1000);
		}
	}
};
</script>
<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.tips {
	font-size: 14px;
	color: #fff;
	margin-bottom: 10px;
	span {
		&:first-of-type {
			margin-right: 16px;
		}
	}
}
.show-pwd {
	position: absolute;
	right: 10px;
	top: 0;
	font-size: 16px;
	color: $dark_gray;
	cursor: pointer;
	user-select: none;
}
.remember {
	height: 28px;
	margin-bottom: 16px;
}
.loginCode {
	display: flex;
	justify-content: space-between;
	::v-deep .el-form-item {
		width: 210px;
	}
}
.getCode {
	width: 132px;
	height: 40px;
}
</style>
