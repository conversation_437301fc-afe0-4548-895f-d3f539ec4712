<!--
 @desc:标题栏
 @author: WH
 @date: 2023/8/22
 -->
<template>
	<div class="title-bar">
		<header>
			<div class="head-title">
				<p>{{ title }}</p>
				<span>{{ info }}</span>
				<ul v-if="tabData.list.length !== 0" class="tabs">
					<li
						v-for="item in tabData.list"
						:key="item.id"
						:class="{ 'tab-active': item.id == tabData.active }"
						@click="changeTab(item.id)"
					>
						{{ item.name }}
					</li>
				</ul>
			</div>
			<div class="more-btn" @click="moreBtn">
				更多
				<i class="el-icon-d-arrow-right"></i>
			</div>
			<!-- <div class="pagination">
				<div
					:class="{ 'last-page': true, disabled: page.num == 1 }"
					@click.stop="pagination('last')"
				>
					<img src="@imgs/home/<USER>" alt="" />
				</div>
				<div class="more-page">
					<input
						type="text"
						:value="page.num"
						@blur.stop="inputPage($event)"
						@keyup.enter="inputPage($event)"
					/>
					<span>/</span>
					<span>{{ total }}</span>
				</div>
				<div
					:class="{ 'next-page': true, disabled: page.num == total }"
					@click.stop="pagination('next')"
				>
					<img src="@imgs/home/<USER>" alt="" />
				</div>
			</div> -->
		</header>
		<article>
			<Empty v-if="showSlot" :tips="'暂无商品信息'" />
			<slot v-else />
		</article>
	</div>
</template>

<script>
export default {
	props: {
		showSlot: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			required: true,
			default: '标题'
		},
		code: {
			type: String,
			required: true,
			default: ''
		},
		info: {
			type: String,
			required: true,
			default: '简介'
		},
		page: {
			type: Object,
			// required: true,
			default: () => {
				return {
					num: 1,
					size: 10,
					total: 1
				};
			}
		},
		tabData: {
			type: Object,
			default: () => {
				return {
					list: [],
					active: '0'
				};
			}
		}
	},
	data() {
		return {
			// tabActive: this.tabs[0].id
		};
	},
	computed: {
		total() {
			return Math.ceil(this.page.total / this.page.size);
		}
	},

	methods: {
		inputPage(el) {
			const nowPage = el.target.value;
			const isValid = /^[1-9]\d*$/.test(nowPage) && nowPage <= this.total;
			if (nowPage == this.page.num) return;
			this.$emit('titleBarFn', {
				clickType: 'changePage',
				code: this.code,
				type: isValid ? Number(nowPage) : this.total //有效数字就用 非有效数字就跳转最后一页
			});
			// console.log('>>>', el.target.value,isValid);
		},
		pagination(type) {
			// this[name] = index;
			if (type == 'last' && this.page.num == 1) return;
			if (type == 'next' && this.page.num == this.total) return;
			this.$emit('titleBarFn', { clickType: 'changePage', code: this.code, type });
		},
		changeTab(id) {
			// this[name] = index;
			if (this.tabData.active == id) return;
			this.$emit('titleBarFn', { clickType: 'changeTab', code: this.code, id });
		},
		moreBtn() {
			this.$emit('moreBtn');
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.title-bar {
	width: 1260px;
	margin: 0 auto;

	// border: 1px solid #000;
	header {
		@include flexBox(space-between);
		margin: 36px 0 30px 0;
		.head-title {
			@include flexBox(flex-start);

			p {
				font-size: 24px;
				font-weight: bold;
				color: #000000;
			}
			span {
				font-size: 14px;
				font-weight: 400;
				color: #999999;
				margin-left: 10px;
			}
		}
	}
	.tabs {
		@include flexBox();
		li {
			padding: 12px 20px;
			border-radius: 18px;
			font-weight: 400;
			font-size: 14px;
			margin-left: 20px;
			cursor: pointer;
			background: #ffffff;
			color: #747d85;
		}
		.tab-active {
			color: var(--brand-6, #0076e8);
		}
	}
	// .pagination {
	// 	@include flexBox(space-between);
	// 	width: 170px;
	// 	.last-page,
	// 	.next-page {
	// 		&:hover {
	// 			background: #99999994;
	// 			transition: all 0.3s ease;
	// 		}
	// 	}
	// 	.last-page,
	// 	.next-page {
	// 		@include flexBox();
	// 		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	// 		width: 36px;
	// 		height: 36px;
	// 		background: #ffffff;
	// 		border-radius: 50%;

	// 		img {
	// 			width: 70%;
	// 			height: 70%;
	// 			// border: 1px solid red;
	// 		}
	// 	}
	// 	.next-page {
	// 		img {
	// 			transform: rotateZ(180deg);
	// 		}
	// 	}
	// 	.more-page {
	// 		@include flexBox();
	// 		width: 80px;
	// 		height: 36px;
	// 		border-radius: 18px;
	// 		font-size: 14px;
	// 		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	// 		background: #ffffff;
	// 		// &:hover {
	// 		// 	position: absolute;
	// 		// 	top: 0;
	// 		// 	left: 50px;
	// 		// 	height: 100px;
	// 		// 	transition: all 0.3s ease;
	// 		// }
	// 		input {
	// 			width: 24px;
	// 			text-align: right;
	// 			color: #409eff;
	// 			border: none;
	// 			&:focus {
	// 				border: 1px solid #409eff;
	// 				text-align: left;
	// 			}
	// 		}
	// 		span {
	// 			&:nth-of-type(1) {
	// 				margin: 0 6px;
	// 			}
	// 			&:nth-of-type(2) {
	// 				width: 24px;
	// 			}
	// 		}
	// 		i {
	// 			display: inline-block;
	// 			width: 4px;
	// 			height: 4px;
	// 			border-radius: 50%;
	// 			background: #747d85;
	// 			&:nth-child(2) {
	// 				margin: 0 6px;
	// 			}
	// 		}
	// 	}
	// 	.disabled {
	// 		position: relative;
	// 		cursor: not-allowed;
	// 		&::before {
	// 			content: '';
	// 			position: absolute;
	// 			top: 0;
	// 			left: 0;
	// 			width: 100%;
	// 			height: 100%;
	// 			border-radius: 50%;
	// 			background-color: #999;
	// 			opacity: 0.3;
	// 		}
	// 		img {
	// 			opacity: 0.3;
	// 		}
	// 	}
	// }
	.more-btn {
		width: 70px;
		height: 36px;
		background: #ffffff;
		box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
		border-radius: 18px;
		font-size: 14px;
		color: #747d85;
		text-align: center;
		line-height: 36px;
		cursor: pointer;
		&:hover {
			color: #0076e8;
		}
	}
	article {
		@include flexBox(space-between);
		flex-wrap: wrap;
	}
}
</style>
