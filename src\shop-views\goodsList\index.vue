<template>
	<div v-loading="loading" class="GoodsLsit">
		<goods-screen @getList="getList" />
		<goods-search :classify-info="classifyInfo" @search="getList" />
		<goods ref="goodsList" :classify-info="classifyInfo" />
	</div>
</template>

<script>
import GoodsScreen from './components/goods-screen.vue';
import GoodsSearch from './components/goods-search.vue';
import goods from './components/goods.vue';
// import { globalData } from '@/mixins/globalData.js';

export default {
	name: 'GoodsLsit',
	components: {
		GoodsScreen,
		GoodsSearch,
		goods
	},
	// mixins: [globalData],
	data() {
		return {
			classifyInfo: {
				type1: 1,
				type: '',
				typeId: '',
				orderRule: 'all',
				minPrice: '',
				maxPrice: '',
				keywords: ''
			},
			loading: false
		};
	},
	methods: {
		/**获取商品数据*/
		getList(data) {
			this.$nextTick(() => {
				// 有搜索数据，赋值搜索数据
				if (data) {
					Object.keys(this.classifyInfo).forEach(key => {
						if (data[key] !== undefined) {
							this.$set(this.classifyInfo, key, data[key]);
						}
					});
				}
				this.$refs.goodsList.offset = 0;
				this.$refs.goodsList.psize = 12;
				this.loading = true;
				this.$refs.goodsList.getList();
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.GoodsLsit {
	width: 1200px;
	overflow: hidden;
	background: #f4f5f8;
	margin: 0 auto 60px;
	min-height: 100%;
}
</style>
