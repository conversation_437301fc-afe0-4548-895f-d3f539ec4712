<template>
	<div class="product-map">
		<descriptions :data="productSiteInfo" />
		<div class="sourcemap">
			<amap
				class="amap-box"
				img-url="https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png"
				:logistics="firstArr"
				:vid="'amap-vue'"
				:offset="[0, 0]"
				:angle="0"
				:zoom="8"
			></amap>
		</div>
	</div>
</template>

<script>
import Descriptions from './descriptions.vue';
import Amap from '@/components/amap/index.vue';

export default {
	name: 'ProductMap',
	components: { Descriptions, Amap },
	props: {
		info: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			productSiteInfo: {
				title: '产地地图',
				data: [{ key: '产地地址：', val: '四川省泸州市' }]
			},
			firstArr: []
		};
	},
	created() {
		if (this.info.baseInfo.LAT && this.info.baseInfo.LON) {
			let longitude = this.info.baseInfo?.goodsPlace?.lng || this.info.baseInfo.LON;
			let latitude = this.info.baseInfo?.goodsPlace?.lat || this.info.baseInfo.LAT;
			this.firstArr = [
				{
					longitude: Number(longitude),
					latitude: Number(latitude)
				}
			];
		}
		this.productSiteInfo.data[0].val =
			this.info.baseInfo?.goodsPlace?.address || this.info.baseInfo?.SHOP_DETAIL;
	}
};
</script>

<style lang="scss" scoped>
.product-map {
	padding: 29px 23px;
	background: #fff;
	.sourcemap {
		padding: 0 23px;
		flex: 1;
		height: 407px;
	}
}
::v-deep .descriptions_item {
	width: 100%;
}
</style>
