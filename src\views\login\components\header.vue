<template>
	<div class="login-header">
		<div></div>
		<!-- <img src="@/assets/images/login/login-logo.png" alt="" class="login-header-left" /> -->
		<div class="login-header-right">
			<img
				src="@/assets/images/login/info.png"
				class="login-header-right-img"
				alt=""
				style="border: none; width: 30px; height: 30px"
				@click="openInfo"
			/>
			<div class="login-header-right-text" @click="openInfo">关于</div>
			<div class="qrcode-container">
				<img src="@/assets/images/login/download.png" class="login-header-right-img" alt="" />
				<div class="login-header-right-text">移动应用</div>
				<div class="qrcode-popup">
					<img src="@/assets/images/login/yzy.png" alt="下载二维码" class="qrcode-img" />
					<p>扫码下载移动应用</p>
				</div>
			</div>
			<img src="@/assets/images/login/phone.png" class="login-header-right-img" alt="" />
			<div class="login-header-right-phone">0831-8275466</div>
		</div>
		<!-- <img src="@/assets/images/login/login-logo.png" alt="" class="login-header-left" /> -->
	</div>
</template>

<script>
export default {
	name: 'Header',
	data() {
		return {};
	},
	methods: {
		openInfo() {
			this.$emit('openInfo');
		}
	}
};
</script>

<style scoped lang="scss">
.login-header {
	position: absolute;
	top: 19px;
	left: 40px;
	width: calc(100% - 80px);
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	&-left {
		position: absolute;
		height: 78px;
		top: -20px;
		left: 50%;
		transform: translate(-50%, 0);
	}
	&-right {
		display: flex;
		align-items: center;
		&-img {
			margin-right: 11px;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			border: 1px solid #fff;
			padding: 3px;
			cursor: pointer;
		}
		&-text {
			font-size: 14px;
			font-family: MicrosoftYaHei;
			color: #ffffff;
			line-height: 22px;
			margin-right: 38px;
		}
		&-phone {
			font-size: 14px;
			font-family: MicrosoftYaHei;
			color: #ffffff;
			line-height: 22px;
		}
	}
}

.qrcode-container {
	position: relative;
	display: flex;
	align-items: center;
	cursor: pointer;

	&:hover .qrcode-popup {
		display: block;
	}
}

.qrcode-popup {
	display: none;
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	background: white;
	padding: 10px;
	border-radius: 4px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
	z-index: 10;
	margin-top: 10px;
	text-align: center;

	&::before {
		content: '';
		position: absolute;
		top: -6px;
		left: 50%;
		transform: translateX(-50%);
		border-width: 0 6px 6px 6px;
		border-style: solid;
		border-color: transparent transparent white transparent;
	}

	.qrcode-img {
		width: 120px;
		height: 120px;
		margin-bottom: 8px;
	}

	p {
		margin: 0;
		font-size: 12px;
		color: #666;
	}
}
</style>
