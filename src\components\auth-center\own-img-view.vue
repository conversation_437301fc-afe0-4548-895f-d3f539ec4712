<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-04-28 13:46:41
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-06-01 19:09:44
-->
<template>
	<el-image class="img" :src="files[0].imgUrl" lazy>
		<div slot="error" class="image-slot">
			<img :src="errImg" alt="" srcset="" />
		</div>
	</el-image>
</template>

<script>
import { getFileInfo, previewFile } from '@/api/file';
export default {
	name: 'OwnImgView',
	props: {
		accept: {
			type: String,
			default: 'image/jpeg,image/png,image/jpg'
		},
		code: {
			type: String,
			default: ''
		},
		ownId: {
			type: String,
			default: ''
		},
		errImg: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			files: [{ imgUrl: '' }]
		};
	},
	computed: {
		userInfo() {
			return this.$store.state.user.userInfo;
		}
	},
	watch: {
		ownId(val) {
			if (val) {
				this.getFiles(val);
			}
		}
	},
	mounted() {
		this.getFiles();
	},
	methods: {
		getFiles(ownId) {
			getFileInfo({ code: this.code, ownId: ownId || this.ownId, userId: this.userInfo.id }).then(
				async res => {
					if (res.rCode == 0) {
						const imgReqAll = [];
						res.results.forEach((item, index) => {
							if (['png', 'jpg'].indexOf(item.suffix) > -1) {
								// 获取到blob数据
								const req = previewFile({ adjunctId: item.adjunctId });
								imgReqAll.push(req);
							} else {
								imgReqAll.push(null);
							}
						});
						const resAll = await Promise.all(imgReqAll);
						resAll.forEach(async (item, index) => {
							if (item) {
								this.changeImgToBase64(item, index);
							}
						});
					}
				}
			);
		},
		changeImgToBase64(item, index) {
			const that = this;
			let reader = new FileReader();
			reader.onload = function (e) {
				const oItem = that.files[index];
				oItem.imgUrl = e.target.result;
				that.files.splice(index, 1, oItem);
			};
			reader.readAsDataURL(item.data);
		}
	}
};
</script>

<style lang="scss" scoped>
.img {
	width: 100%;
	height: 100%;
}
</style>
