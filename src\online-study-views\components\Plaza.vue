<template>
	<div class="Plaza">
		<Title :title="'好物广场'" />
		<!-- top -->
		<div class="top">
			<div class="left">
				<div class="left1">
					<a href="">
						<div class="left-link">
							<div class="left-title">化学产品</div>
							<div class="left-dec">精选化学产品满减活动</div>
							<div>
								<img src="@/assets/img/image_163.png" alt="" />
							</div>
						</div>
					</a>
				</div>
				<div class="left2">
					<a href="">
						<div class="left-link">
							<div style="color: #a476a8" class="left-title">化学产品</div>
							<div style="color: #a476a8" class="left-dec">精选化学产品满减活动</div>
							<div>
								<img src="@/assets/img/image_164.png" alt="" />
							</div>
						</div>
					</a>
				</div>
			</div>
			<div class="right">
				<div class="right-item">
					<div class="top1">
						<a href="">
							<div style="color: #798365" class="right-title">农业产品</div>
							<div style="color: #c8b18e" class="right-dec">精选产品满减活动</div>
							<div>
								<img src="@/assets/img/image_165.png" alt="" />
							</div>
						</a>
					</div>
					<div class="top2">
						<a href="">
							<div style="color: #a17c67" class="right-title">饲养产品</div>
							<div style="color: #98847a" class="right-dec">精选产品满减活动</div>
							<div>
								<img src="@/assets/img/image_167.png" alt="" />
							</div>
						</a>
					</div>
				</div>
				<div class="right-item bottom">
					<div class="top1 bottom1">
						<a href="">
							<div style="color: #9e8560" class="right-title">有色金属</div>
							<div style="color: #c8b18e" class="right-dec">精选产品满减活动</div>
							<div>
								<img src="@/assets/img/image_166.png" alt="" />
							</div>
						</a>
					</div>
					<div class="top2 bottom2">
						<a href="">
							<div style="color: #7e6fb6" class="right-title">农副食品</div>
							<div style="color: #a596da" class="right-dec">精选产品满减活动</div>
							<div>
								<img src="@/assets/img/image_168.png" alt="" />
							</div>
						</a>
					</div>
				</div>
			</div>
		</div>
		<div class="bottom">
			<div class="item bottom_1">
				<a href="">
					<div class="bottom-title">石油天然气</div>
					<div class="bottom-dec">精选产品满减活动</div>
					<div>
						<img src="@/assets/img/Group_1221.png" alt="" />
					</div>
				</a>
			</div>
			<div class="item bottom_2">
				<a href="">
					<div class="bottom-title">非金属矿物制品</div>
					<div class="bottom-dec">精选产品满减活动</div>
					<div>
						<img src="@/assets/img/image_171.png" alt="" />
					</div>
				</a>
			</div>
			<div class="item bottom_3">
				<a href="">
					<div class="bottom-title">饮料、酒</div>
					<div class="bottom-dec">精选产品满减活动</div>
					<div>
						<img src="@/assets/img/image_169.png" alt="" />
					</div>
				</a>
			</div>
			<div class="item bottom_4">
				<a href="">
					<div class="bottom-title">纸制品</div>
					<div class="bottom-dec">精选产品满减活动</div>
					<div>
						<img src="@/assets/img/image_170.png" alt="" />
					</div>
				</a>
			</div>
		</div>
	</div>
</template>

<script>
import Title from './Title.vue';
export default {
	name: 'Plaza',
	components: { Title }
};
</script>

<style lang="scss" scoped>
.Plaza {
	width: 100%;
}
.top {
	width: 100%;
	position: relative;
	.left {
		position: relative;
		text-align: center;

		.left1 {
			width: 293px;
			height: 380px;
			background: #dae2f1;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;

			img {
				width: 264px;
				height: 264px;
			}
			.left-title {
				padding-top: 27px;
				font-size: 30px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #516b9f;
				line-height: 44px;
			}
			.left-dec {
				font-size: 18px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				line-height: 32px;
				color: #6579a0;
			}
		}
		.left-title {
			padding-top: 27px;
			font-size: 30px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #516b9f;
			line-height: 44px;
		}
		.left-dec {
			font-size: 18px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			line-height: 32px;
			color: #6579a0;
		}
		.left2 {
			position: absolute;
			left: 298px;
			top: 0;
			width: 293px;
			height: 380px;
			background: #e5d8e6;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.left-link {
			width: 100%;
			height: 100%;
		}
		a {
			background-color: red;
		}
	}
	.right {
		position: absolute;
		right: 0;
		top: 0;
		.right-item {
			position: relative;
			.top1 {
				width: 293px;
				height: 186px;
				background: #d0dfb1;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				position: absolute;
				right: 298px;
				top: 0px;
				img {
					width: 179px;
					height: 121px;
					position: absolute;
					bottom: 0;
					right: 0;
				}
			}
			.right-title {
				padding: 21px 0 0 11px;
				font-size: 24px;
				font-family: Source Han Sans SC-Bold, Source Han Sans SC;
				font-weight: bold;
				color: #798365;
				line-height: 28px;
			}
			.right-dec {
				padding-left: 11px;
				font-size: 18px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #99ab74;
				line-height: 32px;
			}
			.top2 {
				width: 293px;
				height: 186px;
				background: #e2d2c9;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				img {
					width: 121px;
					height: 100%;
					position: absolute;
					bottom: 0;
					right: 0;
				}
			}
		}
		.bottom {
			margin-top: 8px;
			.bottom1 {
				background: #f5e3c8;
				img {
					width: 139px;
					height: 139px;
				}
			}
			.bottom2 {
				background: #dedce5;
				img {
					width: 182px;
					height: 139px;
				}
			}
		}
	}
}
.bottom {
	position: relative;
	margin-top: 8px;
	.item {
		padding: 21px 27px;
		width: 293px;
		height: 168px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
	}
	.bottom_1 {
		position: relative;
		img {
			position: absolute;
			width: 86px;
			height: 122px;
			right: 22px;
			bottom: 12px;
		}
	}
	.bottom_2 {
		position: absolute;
		left: 298px;
		bottom: 0;
		img {
			position: absolute;
			width: 96px;
			height: 92px;
			right: 24px;
			bottom: 22px;
		}
	}
	.bottom_3 {
		position: absolute;
		left: 599px;
		bottom: 0;
		img {
			position: absolute;
			width: 70px;
			height: 146px;
			right: 26px;
			bottom: 5px;
		}
	}
	.bottom_4 {
		position: absolute;
		right: 0;
		bottom: 0;
		img {
			position: absolute;
			width: 138px;
			height: 97px;
			right: 23px;
			bottom: -5px;
		}
	}
	.bottom-title {
		font-size: 24px;
		font-family: Source Han Sans SC-Bold, Source Han Sans SC;
		font-weight: bold;
		color: #404040;
		line-height: 28px;
	}
	.bottom-dec {
		font-size: 18px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #bfbfbf;
		line-height: 32px;
	}
}
</style>
