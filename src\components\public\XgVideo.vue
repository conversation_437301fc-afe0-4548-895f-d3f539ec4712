<template>
	<div id="player-con"></div>
</template>

<script>
import Player from 'xgplayer/dist/simple_player';
// import volume from 'xgplayer/dist/controls/volume'
// import playbackRate from 'xgplayer/dist/controls/playbackRate'
// import poster from 'xgplayer/dist/controls/poster'
// import progress from 'xgplayer/dist/controls/progress'
// import reload from 'xgplayer/dist/controls/reload'
// import replay from 'xgplayer/dist/controls/replay'
import VIDEO_RESOLUTION from '@/constants/resolution';
export default {
	name: 'XgVideo',
	props: {
		vodUrl: {
			// 视频源
			type: String,
			default() {
				return '//sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4';
				// return '//sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/music/audio.mp3'
			}
		},
		fluid: {
			type: Boolean,
			default() {
				return false;
			}
		},
		autoInitPlay: {
			// 是否自动初始化video对象
			type: <PERSON>olean,
			default() {
				return true;
			}
		},
		volume: {
			// 音量
			type: Number,
			default() {
				return 1;
			}
		},
		poster: {
			// 海报封面
			type: String,
			default() {
				return 'https://cdn.shupian.cn/sp-pt/wap/images/frp82g3cd9c0000.png';
			}
		},
		fitVideoSize: {
			// 自适应视频内容宽高
			type: String,
			default() {
				return 'auto';
			}
		},
		lastPlayTime: {
			// 视频起播时间（单位：秒）
			type: Number,
			default() {
				return 0;
			}
		},
		playbackRate: {
			// 传入倍速可选数组
			type: Array,
			default() {
				return [0.5, 0.75, 1, 1.5, 2];
			}
		},
		size: {
			// 视频的宽高
			type: Object,
			default() {
				return null;
			}
		},
		// 清晰度数组
		clarityArr: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			player: null
		};
	},
	watch: {
		vodUrl(url) {
			// this.player.destroy(true)
			// this.destroyVideo().then(() => {
			//   this.initXgVideo()
			// })
			// this.player.reload()
			// this.destroyVideo()
			this.player.poster = this.poster;
			if (this.player.hasStart) {
				this.player.reload();
				// this.player.poster = this.poster
				this.player.src = url;
			} else {
				this.player.start(url);
				// console.log(this.player)
			}
		}
	},
	mounted() {
		if (this.autoInitPlay) {
			this.initXgVideo();
		}
	},
	methods: {
		destroyVideo() {
			new Promise(resolve => {
				this.player.destroy(true);
				resolve();
			}).then(() => {
				this.initXgVideo();
			});
		},
		initXgVideo() {
			let videoConfig = {
				id: 'player-con',
				volume: this.volume,
				url: this.vodUrl,
				poster: this.poster,
				fluid: this.fluid,
				// fitVideoSize: this.fitVideoSize,
				lastPlayTime: this.lastPlayTime, //视频起播时间（单位：秒）
				videoInit: true,
				closeVideoDblclick: false,
				autoplay: false,
				// ignores: ['fullscreen'],
				controlPlugins: [
					// volume,
					// playbackRate,
					// poster,
					// progress,
					// reload,
					// replay,
				],
				playbackRate: this.playbackRate // 传入倍速可选数组
			};
			if (this.size) {
				videoConfig.width = this.size.width;
				videoConfig.height = this.size.height;
			}
			this.player = new Player(videoConfig);
			if (this.clarityArr.length) {
				this.player.emit(
					'resourceReady',
					this.clarityArr.map(item => {
						return {
							name: VIDEO_RESOLUTION[item.definition],
							url: item.playURL
						};
					})
				);
			}
			// 将player对象抛出
			this.$emit('player', this.player);
		}
	},
	beforeDestroy() {
		this.player.destroy(true);
	}
};
</script>

<style scoped></style>
