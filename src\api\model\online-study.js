import request from '@/utils/request';
// import qs from 'qs';

// 大课堂登录
export function loginFront(params) {
	return request({
		url: `/yb-zxxx/auth/loginFront`,
		method: 'post',
		type: 'JSON',
		data: params
	});
}
// 大课堂code登录
export function loginFrontByCode(params) {
	return request({
		url: `/yb-zxxx/auth/loginFrontByCode`,
		method: 'get',
		params
	});
}
// 广告
// export function getAdvertsByCode(params) {
// 	return request({
// 		url: `/yb-zxxx/trip-api/api/advert/getAdvertsByCode`,
// 		method: 'post',
// 		data: params
// 	});
// }
// 物流课程分类
export function getCourseClassTree(params) {
	return request({
		url: `/yb-zxxx/learn/courseclass/api/open/getCourseClassTree`,
		method: 'get',
		params
	});
}
// 推荐好课
export function tjCourseList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/tjCourseList`,
		method: 'get',
		params
	});
}
// 新上好课
export function xsCourseList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/xsCourseList`,
		method: 'get',
		params
	});
}
// 热销好课
export function rxCourseList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/rxCourseList`,
		method: 'get',
		params
	});
}
// 精品免费
export function jpCourseList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/jpCourseList`,
		method: 'get',
		params
	});
}
// 畅销榜
export function cxRank(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/cxRank`,
		method: 'get',
		params
	});
}
// 新课榜
export function xkRank(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/xkRank`,
		method: 'get',
		params
	});
}
// 学习榜
export function xxRank(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/xxRank`,
		method: 'get',
		params
	});
}
// 课程列表查询
export function pageList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/pageList`,
		method: 'post',
		type: 'JSON',
		data: params
	});
}
// 课程详细信息
export function getCourseDetailInfo(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/getCourseDetailInfo`,
		needAuthorization: true,
		method: 'get',
		params
	});
}

// 课程大纲
export function getCourseDirectory(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/getCourseDirectory`,
		method: 'get',
		params
	});
}

// 课程评论
export function getAssessmentPageList(params) {
	return request({
		url: `/yb-zxxx/learn/assessment/api/open/getAssessmentPageList`,
		method: 'post',
		type: 'JSON',
		data: params
	});
}
// 收藏
export function collectCourse(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/collectCourse`,
		method: 'get',
		needAuthorization: true,
		params
	});
}
// 获取评价标签列表
export function getAssessmentTagList(params) {
	return request({
		url: `/yb-zxxx/learn/assessment/api/open/getAssessmentTagList`,
		method: 'get',
		params
	});
}
// 获取评价标签数量
export function getAssessmentTagNums(params) {
	return request({
		url: `/yb-zxxx/learn/assessment/api/open/getAssessmentTagNums`,
		method: 'get',
		params
	});
}
//发布评论
export function addAssessment(params) {
	return request({
		url: `/yb-zxxx/learn/assessment/api/add`,
		needAuthorization: true,
		method: 'post',
		type: 'JSON',
		data: params
	});
}
//播放列表
export function getCourseVideoList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/getCourseVideoList`,
		method: 'get',
		params
	});
}
//查询对应课程
export function getCourseHourVideo(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/getCourseHourVideo`,
		method: 'get',
		needAuthorization: true,
		params
	});
}

//推荐课
export function tjCourse(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/tjCourse`,
		method: 'get',
		params
	});
}

//相关课
export function xgCourse(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/xgCourse`,
		method: 'get',
		params
	});
}
//添加到购物车
export function addCartGood(params) {
	return request({
		url: `/yb-zxxx/learn/shoppingcart/api/addCartGood`,
		method: 'get',
		params
	});
}
//删除购物车
export function delCartGoods(params) {
	return request({
		url: `/yb-zxxx/learn/shoppingcart/api/delCartGoods`,
		method: 'get',
		params
	});
}
//查询购物车
export function getCartGoods(params) {
	return request({
		url: `/yb-zxxx/learn/shoppingcart/api/getCartGoods`,
		method: 'get',
		params
	});
}
//创建订单
export function createOrder(params) {
	return request({
		url: `/yb-zxxx/learn/order/api/createOrder`,
		method: 'get',
		needAuthorization: true,
		params
	});
}
//订单列表
export function getOrderPageList(params) {
	return request({
		url: `/yb-zxxx/learn/order/api/getOrderPageList`,
		method: 'post',
		type: 'JSON',
		needAuthorization: true,
		data: params
	});
}
//订单列表
export function getUserCoursePageList(params) {
	return request({
		url: `/yb-zxxx/learn/order/api/getUserCoursePageList`,
		method: 'post',
		type: 'JSON',
		needAuthorization: true,
		data: params
	});
}
//订单支付
export function orderPay(params) {
	return request({
		url: `/yb-zxxx/learn/order/api/orderPay`,
		method: 'get',
		params
	});
}
//取消订单
export function orderCancel(params) {
	return request({
		url: `/yb-zxxx/learn/order/api/orderCancel`,
		method: 'get',
		needAuthorization: true,
		params
	});
}
//我的收藏列表
export function collectList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/collectList`,
		method: 'get',
		params
	});
}
// 获取信息列表
// export function paging(params) {
// 	return request({
// 		url: `/yb-zxxxzy/cmsinfo/front/paging`,
// 		method: 'get',
// 		params
// 	});
// }
// 获取信息详情
export function detail(params) {
	return request({
		url: `/scswl/cmsinfo/front/detail`,
		method: 'get',
		params
	});
}
//增加足迹
export function footmarkAdd(params) {
	return request({
		url: `/trip-api/api/favorite/footmarkAdd`,
		method: 'post',
		// type: 'JSON',
		params
	});
}
//浏览记录
export function readLogList(params) {
	return request({
		url: `/yb-zxxx/learn/readLog/api/readLogList`,
		method: 'get',
		needAuthorization: true,
		params
	});
}
//浏览记录-删除
export function readLogDelete(params) {
	return request({
		url: `/yb-zxxx/learn/readLog/api/delete`,
		method: 'get',
		needAuthorization: true,
		params
	});
}
//职业教育
export function zyjyCourseList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/open/zyjyCourseList`,
		method: 'get',
		params
	});
}
//查询热门主题分类
export function getTopClassList(params) {
	return request({
		url: `/yb-zxxx/learn/courseclass/api/open/getTopClassList`,
		method: 'get',
		params
	});
}
//精彩评论
export function getTopAssessmentList(params) {
	return request({
		url: `/yb-zxxx/learn/assessment/api/open/getTopAssessmentList`,
		method: 'get',
		params
	});
}
///课程考试
export function examList(params) {
	return request({
		url: `/yb-zxxx/learn/course/api/examList`,
		method: 'get',
		params,
		needAuthorization: true
	});
}
//查看结果
export function examinationResult(params) {
	return request({
		url: `/yb-zxxx/learn/examination/api/result`,
		method: 'get',
		params,
		needAuthorization: true
	});
}
//开始答题
export function startExam(params) {
	return request({
		url: `/yb-zxxx/learn/examination/api/startExam`,
		method: 'get',
		params,
		needAuthorization: true
	});
}
//查看解析
export function analysisExam(params) {
	return request({
		url: `/yb-zxxx/learn/examination/api/analysis`,
		method: 'get',
		params,
		needAuthorization: true
	});
}
//我的考试，每日答题是否完成
export function examinationIsFinish(params) {
	return request({
		url: `/yb-zxxx/learn/everydayExamination/api/isFinish`,
		method: 'get',
		params,
		needAuthorization: true
	});
}
//考试提交
export function submitExamination(params) {
	return request({
		url: `/yb-zxxx/learn/examination/api/submit`,
		method: 'post',
		type: 'JSON',
		data: params,
		needAuthorization: true
	});
}

// 超星视频列表

export function superStarList(params) {
	return request({
		url: '/ybzy/fanya/meeting/page',
		method: 'get',
		params
		// data: params,
		// needAuthorization: true
	});
}
