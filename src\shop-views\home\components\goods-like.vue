<template>
	<div class="list">
		<div class="list-title">
			<img class="list-title-img" src="@/assets/shop-images/title-icon.png" alt="" />
			<div class="list-title-name">猜你喜欢</div>
			<img class="list-title-img" src="@/assets/shop-images/title-icon.png" alt="" />
		</div>
		<div v-loading="loading" class="list-con">
			<div
				v-for="(item, index) of goodsList"
				:key="index"
				class="list-con-item"
				@click="toDetail(item)"
			>
				<img class="list-con-item-img" :src="$judgeFile(item.coverUrl)" alt="" />
				<div class="list-con-item-name">{{ item.productName }}</div>
				<div class="list-con-item-price">￥{{ item.sellPrice.toFixed(2) }}</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodsLike',
	data() {
		return {
			goodsList: [],
			loading: false
		};
	},
	created() {
		this.getList();
	},
	methods: {
		/**获取猜你喜欢数据*/
		getList() {
			let data = {
				offset: 0,
				psize: 12,
				noType: 1,
				type1: 1,
				siteId: this.getSiteId(),
				categoryId: ''
			};
			this.loading = true;
			this.$api.shop_api.getAllProduct(data).then(res => {
				this.loading = false;
				if (res.state) {
					this.goodsList = res.result || [];
				}
			});
		},
		/**跳转详情*/
		toDetail(t) {
			this.$router.push({
				path: '/shopDetail',
				query: {
					id: t.productId || t.id,
					type: t.regCode
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.list {
	margin: 16px 0 60px;
	&-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 32px;
		&-img {
			width: 16px;
			height: 12px;
			object-fit: cover;
		}
		&-name {
			margin: 0 16px;
			font-size: 20px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #262626;
			line-height: 44px;
		}
	}
	&-con {
		width: 1200px;
		background: #ffffff;
		border-radius: 4px 4px 4px 4px;
		margin: 0 auto;
		padding-top: 12px;
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 200px;
			height: 265px;
			padding: 11px 20px 16px;
			box-sizing: border-box;
			cursor: pointer;
			&-img {
				width: 160px;
				height: 160px;
				object-fit: cover;
			}
			&-name {
				margin: 6px 0 2px;
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #404040;
				line-height: 22px;
				overflow: hidden; //超出的文本隐藏
				text-overflow: ellipsis; //溢出用省略号显示
				display: -webkit-box;
				-webkit-line-clamp: 2; // 超出多少行
				-webkit-box-orient: vertical;
			}
			&-price {
				font-size: 11px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #ca3f3b;
				line-height: 20px;
			}
		}
	}
}
</style>
