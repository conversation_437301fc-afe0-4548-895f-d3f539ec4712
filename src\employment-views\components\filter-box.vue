<template>
	<div class="filter-box">
		<div v-for="(filterItem, index) in filterList" :key="index" class="type">
			<div class="label">{{ filterItem.label }}</div>
			<ul class="flex1">
				<li
					v-if="filterItem.needAllBtn"
					:class="['all', allactive.includes(filterItem.porp) ? 'all-active' : '']"
					@click="allChoose(filterItem)"
				>
					{{ filterItem.allBtnText || '全部' }}
				</li>
				<li
					v-for="(item, typeIndex) in filterItem.typeList"
					:key="typeIndex"
					:class="{ liActive: findValue(item, filterForm[filterItem.porp]) }"
					@click="typeClick(item, filterItem.porp)"
				>
					{{ item[filterItem.optionLabel || label] }}
					<template v-if="needDel">
						<span
							v-show="findValue(item, filterForm[filterItem.porp])"
							class="item-icon"
							@click.stop="deleteValue(item, filterItem.porp)"
						></span>
					</template>
				</li>
			</ul>
		</div>
		<div v-if="needInput" class="input flex1">
			<div class="label">关键字：</div>
			<el-input v-model.trim="name" placeholder="请输入关键字"></el-input>
			<el-button type="primary" class="search-btn" @click="search">搜索</el-button>
			<el-button class="reset-btn" @click="reset">重置</el-button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'FilterBox',
	props: {
		filterList: {
			type: [Array, Object],
			default: () => {
				return [];
			}
		},
		filterForm: {
			type: Object,
			default: () => {
				return {};
			}
		},
		label: {
			type: String,
			default: 'name'
		},
		value: {
			type: String,
			default: 'code'
		},
		needInput: {
			type: Boolean,
			default: true
		},
		needDel: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			name: '', //搜索关键字
			allactive: [] //选择全选的数据集合
		};
	},
	methods: {
		findValue(ele, group) {
			// console.log(ele,group,group== ele)
			if (group == '') return;
			// let result = group.indexOf(ele) == '-1' ? false : true;
			let result = group == ele || group == ele.id ? true : false;
			return result;
		},
		/**
		 * @description 类型点击事件
		 */
		typeClick(item, porp) {
			// if (group.indexOf(item) != '-1') return;
			// group.push(item);

			if (this.filterForm[porp] == item) return;
			this.filterForm[porp] = item;
			// console.log(item, this.filterForm,"item, group")
			this.filterChange();
		},
		/**
		 * @description 类型删除事件
		 */
		deleteValue(item, porp) {
			// let index;
			// for (let i = 0; i < group.length; i++) {
			// 	if (item == group[i]) {
			// 		index = i;
			// 		break;
			// 	}
			// }
			// group.splice(index, 1);
			this.filterForm[porp] = '';
			this.filterChange();
		},
		filterChange() {
			this.$emit('filterChange', this.filterForm);
		},
		allChoose(ele) {
			let index = this.allactive.indexOf(ele.porp);
			if (index == -1) {
				// this.allactive.push(ele.porp);
				// this.filterForm[ele.porp] = [...ele.typeList];
				this.filterForm[ele.porp] = '';
			} else {
				// this.allactive.splice(index, 1);
				// this.filterForm[ele.porp] = [];
				this.filterForm[ele.porp] = '';
			}
			this.$emit('filterChange', this.filterForm);
		},
		search() {
			this.$emit('search', this.name);
		},
		reset() {
			this.name = '';
			this.$emit('search', this.name);
		}
	}
};
</script>

<style lang="scss" scoped>
.filter-box {
	width: 100%;
	min-height: 102px;
	padding: 0 20px;
	background: #ffffff;
	.type {
		min-height: 50px;
		box-sizing: border-box;
		border-bottom: 1px solid #e8eaf0;
		display: flex;
		padding: 10px 0;
		.all {
			color: #333333;
			font-size: 14px;
			cursor: pointer;
		}
		.all-active {
			background: #4f85ff;
			border-radius: 14px;
			color: #ffffff;
			box-sizing: content-box;
			// border: 1px solid #4f85ff;
		}
		ul {
			flex-wrap: wrap;
			li {
				font-weight: 400;
				cursor: pointer;
				color: #333333;
				font-size: 16px;
				margin: 2px 10px;
				padding: 0 15px;
				height: 30px;
				line-height: 30px;
				border-radius: 4px;
				position: relative;
				overflow: hidden;
				position: relative;
			}
			.liActive {
				color: #0076e8;
				border: 1px solid #0076e8;
				border-radius: 4px;
			}
			.item-icon {
				position: absolute;
				top: -6px;
				right: -6px;
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #0076e8;
				display: inline-block;
				&::before {
					content: 'X';
					position: absolute;
					z-index: 3;
					color: #fff;
					left: 4px;
					top: -3px;
					font-size: 10px;
				}
			}
		}
	}
	.label {
		font-size: 14px;
		font-weight: bold;
		color: #666666;
		flex-shrink: 0;
		height: 30px;
		line-height: 30px;
	}
	.input {
		height: 50px;
		.el-input {
			margin: 0 10px 0 30px;
			width: 220px;
		}
		.search-btn {
			background: #0076e8;
			border-radius: 4px;
		}
		.reset-btn {
			background: #ffffff;
			color: #666666;
			border-radius: 4px;
		}
	}
}
</style>
