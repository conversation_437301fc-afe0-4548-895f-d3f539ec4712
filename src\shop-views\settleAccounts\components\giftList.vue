<template>
	<div class="list">
		<div
			v-for="(item, index) of giftInfo"
			:key="index"
			:style="{
				backgroundImage: `url(${bg})`,
				marginRight: (index + 1) % 3 === 0 ? '0' : ''
			}"
			class="list-item"
		>
			<img class="list-item-img" :src="$judgeFile(item.coverImage)" alt="" />
			<div class="list-item-con">
				<div class="name">{{ item.name }}</div>
				<div class="center">
					<div class="center-title">余额：</div>
					<div class="center-dollar">￥</div>
					<div class="center-money">{{ item.realValue }}</div>
				</div>
				<div class="time">{{ getTime(item.effectTime) }}至{{ getTime(item.expireTime) }}</div>
			</div>
			<div class="line"></div>
			<div class="right">
				<div
					class="button"
					:class="giftIndex === index ? 'select-button' : ''"
					@click="getGift(item, index)"
				>
					<AlIcon v-show="giftIndex === index" name="el-icon-check" color="#ffffff"></AlIcon>
				</div>
			</div>
		</div>
		<Empty v-if="giftInfo.length === 0"></Empty>
	</div>
</template>

<script>
export default {
	name: 'GiftList',
	props: {
		giftInfo: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			subTabs: ['可用优惠券', '不可用优惠券'],
			subTab: 0,
			bg: require('@/assets/shop-images/coupons-bg.png'),
			giftIndex: ''
		};
	},
	methods: {
		/**领取优惠券*/
		getGift(item, i) {
			if (this.giftIndex === i) {
				this.giftIndex = '';
			} else {
				this.giftIndex = i;
			}
			this.$emit('changeGift', this.giftIndex !== '' ? item.id : '', item);
		},
		/**返回时间*/
		getTime(time) {
			let newTime = time.split(' ')[0].replace(/-/gi, '.');
			return newTime;
		}
	}
};
</script>

<style scoped lang="scss">
.list {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	margin-top: 16px;
	max-height: 210px;
	overflow-y: scroll;
	&-item {
		width: 350px;
		height: 90px;
		background-size: 100% 100%;
		position: relative;
		padding: 21px 0 18px 12px;
		display: flex;
		align-items: center;
		margin: 0 15px 26px 0;
		&-img {
			width: 114px;
			height: 64px;
			border-radius: 8px;
			margin-right: 11px;
		}
		&-con {
			flex: 1;
			.name {
				font-size: 15px;
				font-family: PingFang SC-Heavy, PingFang SC;
				font-weight: 800;
				color: #333333;
				line-height: 16px;
			}
			.center {
				font-family: PingFang SC-Heavy, PingFang SC;
				color: #f95f55;
				line-height: 16px;
				display: flex;
				align-items: center;
				margin-bottom: 5px;
				&-title {
					font-size: 12px;
					font-weight: 400;
					color: #f95f55;
				}
				&-dollar {
					font-size: 10px;
					font-weight: 800;
					color: #f95f55;
				}
				&-money {
					font-size: 15px;
					font-weight: 800;
				}
			}
			.time {
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 17px;
			}
		}
		.money {
			flex-shrink: 0;
			min-width: 66px;
			font-size: 24px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #f95f55;
			margin-right: 14px;
			text-align: center;
		}
		.name {
			flex: 1;
			&-top {
				font-size: 15px;
				font-family: PingFang SC-Heavy, PingFang SC;
				font-weight: 800;
				color: #404040;
				line-height: 26px;
				margin-bottom: 5px;
			}
			&-bottom {
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 17px;
				word-wrap: anywhere;
			}
		}
		.line {
			width: 1px;
			height: 65px;
			background: #bdd3f9;
			margin-left: 8px;
		}
		.right {
			flex-shrink: 0;
			width: 60px;
			display: flex;
			align-items: center;
			justify-content: center;
			.button {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #ffffff;
				border: 1px solid #3274e0;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
			}
			.select-button {
				background: #3274e0;
			}
		}
	}
}
::v-deep .empty {
	padding: 30px 0;
}
</style>
