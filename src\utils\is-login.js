import { getCookie } from '@/utils/auth';
import { Message } from 'element-eoss';
import Qs from 'qs';
//（目前里面判断的是电商登录返回的token）
const isLogin = () => {
	if (getCookie('token')) {
		return getCookie('user_id');
	} else {
		setTimeout(() => {
			let route = window._VUE.$route;
			let redirect = route.path + '?' + Qs.stringify(route.query);
			window._VUE.$router.replace(`/login?redirect=${redirect}`);
		}, 1000);
		Message({
			message: '未登录，请先登录！',
			type: 'error',
			duration: 1000
		});
	}
};

export default isLogin;
