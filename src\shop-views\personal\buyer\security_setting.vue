<template>
	<div class="security_setting">
		<div class="head">
			<img src="@/assets/shop-images/aq-tx.png" alt="" class="img" />
			<div class="title">
				<div class="top">安全等级：一般</div>
				<div class="btm">您与安全达人仅一步之遥</div>
			</div>
			<div class="box">
				<div class="btn">修改登录密码</div>
				<div class="btn">设置手机号</div>
				<div class="btn">设置邮箱</div>
			</div>
		</div>
		<div class="list-box">
			<div v-for="(item, index) in list" :key="index" class="list">
				<img class="img" :src="item.img" alt="" />
				<div class="name">{{ item.name }}</div>
				<div v-if="item.state == 0" class="state">未认证</div>
				<div v-else class="tag">
					<el-tag :type="item.state == 1 ? 'success' : 'danger'">
						{{ item.state == 1 ? '认证通过' : '认证失败' }}
					</el-tag>
				</div>
				<div v-if="item.state != 1" class="btn">
					<el-button>去认证</el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'SecuritySetting',
	data() {
		return {
			list: [
				{ name: '个人用户', img: require('@/assets/shop-images/aq1.png'), state: 1 },
				{ name: '企业认证', img: require('@/assets/shop-images/aq2.png'), state: 0 },
				{ name: '入驻商家', img: require('@/assets/shop-images/aq3.png'), state: 0 },
				{ name: '注册用户', img: require('@/assets/shop-images/aq4.png'), state: 1 },
				{ name: '企业认证', img: require('@/assets/shop-images/aq5.png'), state: 1 },
				{ name: '入驻商家', img: require('@/assets/shop-images/aq6.png'), state: 2 }
			]
		};
	}
};
</script>

<style lang="scss" scoped>
.security_setting {
	width: 100%;
	.head {
		height: 130px;
		background: url('./../../../assets/shop-images/aq-bg.png') no-repeat;
		background-size: 100% 100%;
		padding: 0 28px;
		display: flex;
		align-items: center;
		.img {
			width: 89px;
			height: 89px;
		}
		.title {
			margin-left: 21px;
			.top {
				font-size: 20px;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
				margin-bottom: 12px;
			}
			.btm {
				font-size: 14px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
		}
		.box {
			margin-left: auto;
			display: flex;
			.btn {
				margin-right: 16px;
				font-size: 14px;
				font-weight: 400;
				color: #262626;
				line-height: 22px;
				cursor: pointer;
				padding: 8px 20px;
			}
			.btn:last-child {
				margin-right: 0;
			}
		}
	}
	.list-box {
		margin-top: 23px;
		display: flex;
		flex-wrap: wrap;
		width: 80%;
		.list {
			width: 181px;
			height: 206px;
			background: #ffffff;
			border-radius: 6px;
			border: 1px solid #d9d9d9;
			margin: 0 50px 68px 0;
			box-sizing: border-box;
			padding-top: 33px;
			.img {
				display: block;
				width: 76px;
				height: 57px;
				margin: 0 auto;
			}
			.name {
				margin-top: 6px;
				text-align: center;
				font-size: 16px;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
			}
			.state {
				width: 52px;
				height: 24px;
				background: #eeeeee;
				border-radius: 3px;
				font-size: 12px;
				font-weight: 400;
				color: #9aa3ba;
				line-height: 24px;
				text-align: center;
				margin: 3px auto 0 auto;
			}
			.tag {
				text-align: center;
				margin: 3px auto 0 auto;
			}
			::v-deep.el-tag {
				height: 24px;
				line-height: 24px;
				box-sizing: border-box;
			}
			.btn {
				margin-top: 14px;
				text-align: center;
			}
		}
	}
}
</style>
