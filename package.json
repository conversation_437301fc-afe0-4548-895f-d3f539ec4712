{"name": "cdzs-admin", "version": "4.4.0", "description": "川大智胜集成一套基于Vue+element的后台管理脚手架", "author": "cdzs-team", "scripts": {"dev:development": "cross-env vue-cli-service serve --port=8080 --mode development", "dev:release": "cross-env vue-cli-service serve --port=8081 --mode release", "dev:production": "cross-env vue-cli-service serve --port=8081 --mode production", "build:development": "cross-env vue-cli-service build  --mode development", "build:release": "cross-env vue-cli-service build  --mode release", "build:production": "cross-env vue-cli-service build  --mode production", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "lint-fix": "eslint --fix --ext .js,.vue src", "ls-lint": "ls-lint", "cz": "git add . && git cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -e $HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue}": ["eslint --ext .js,.jsx,.vue src", "prettier --write ./src"]}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@tinymce/tinymce-vue": "^3.2.8", "axios": "0.18.1", "bignumber.js": "^9.1.1", "core-js": "^3.23.3", "crypto-js": "^4.1.1", "d3": "7.8.4", "element-eoss": "2.14.3", "js-cookie": "2.2.0", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "raw-loader": "^4.0.2", "sortablejs": "^1.15.0", "swiper": "5.4.5", "three": "^0.156.1", "tinymce": "5.1.0", "uuid": "^9.0.1", "vue": "2.6.10", "vue-awesome-swiper": "4.1.1", "vue-router": "3.0.6", "vuex": "3.1.0", "webpack-merge": "^5.9.0"}, "devDependencies": {"@commitlint/cli": "^14.1.0", "@commitlint/config-conventional": "^14.1.0", "@ls-lint/ls-lint": "^1.10.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "commitizen": "^4.2.4", "connect": "3.6.6", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "eslint": "7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "husky": "^4.3.8", "lint-staged": "^12.0.2", "mockjs": "1.0.1-beta3", "prettier": "^2.4.1", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "video.js": "^8.0.4", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}