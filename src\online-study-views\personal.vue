<template>
	<div class="person">
		<div class="person-left">
			<div class="person-left-con">
				<el-menu
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
				>
					<el-menu-item v-for="menu of menus" :key="menu.index" :index="menu.index">
						<span slot="title">{{ menu.name }}</span>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<!-- <subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb> -->
			<!-- <personHeader :title="activeName"></personHeader> -->
			<component
				:is="activeComponent"
				:key="params.key || ''"
				:is-main="false"
				:params="params"
			></component>
		</div>
	</div>
</template>

<script>
import History from '@/online-study-views/history';
import courseExamination from '@/online-study-views/course-examination';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import personHeader from '@/components/person-header';

export default {
	components: {
		History,
		courseExamination,
		subBreadcrumb,
		personHeader
	},
	data() {
		return {
			activeMenu: '2-1',
			activeComponent: 'History',
			activeName: '浏览记录',
			menus: [
				{
					name: '浏览记录',
					index: '2-1',
					path: 'History',
					params: { key: 3, showActiveCode: 'works' }
				},
				{
					name: '课程考试',
					index: '3-1',
					path: 'courseExamination'
				}
			],
			params: { key: 3, showActiveCode: 'works' }
		};
	},
	created() {},
	methods: {
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.menus.find(item => {
				return item.index === index;
			});
			if (currentObj.params) {
				this.params = currentObj.params;
			} else {
				this.params = {};
			}
			this.activeMenu = currentObj.index;
			this.activeName = currentObj.name;
			this.activeComponent = currentObj.path;
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1200px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
	}
	&-right {
		width: calc(100% - 236px);
	}
}
::v-deep .el-menu-item {
	padding-left: 40px !important;
}
::v-deep .is-active {
	position: relative;
	&::before {
		content: '';
		display: inline-block;
		position: absolute;
		left: 20px;
		top: calc(50% - 7px);
		width: 16px;
		height: 16px;
		border-radius: 50%;
		background: #3274e0;
	}
}
</style>
