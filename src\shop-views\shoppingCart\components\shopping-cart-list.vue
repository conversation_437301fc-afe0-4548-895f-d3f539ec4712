<template>
	<div class="shopping-cart-list">
		<div class="store">
			<el-checkbox v-model="infoDate.checked" @change="changeCheckedAll">
				{{ infoDate.shop_name }}
			</el-checkbox>
		</div>
		<div
			v-for="item of infoDate.List"
			:key="item.id"
			:class="['goods', item.checked ? 'goods-active' : '']"
		>
			<!-- <div :class="['goods', infoDate.checked ? 'goods-active' : '']"> -->
			<table class="goods-table">
				<tr>
					<td class="checkbox">
						<el-checkbox
							v-model="item.checked"
							:disabled="!item.state"
							@change="changeChecked(item)"
						></el-checkbox>
					</td>
					<td class="details">
						<div class="info">
							<div><img class="img" :src="$judgeFile(item.cover_url)" alt="" /></div>
							<div>
								<a href="javascript:void(0)" @click="details(item)">
									<div class="name">{{ item.product_name }}</div>
								</a>
								<div>
									<!-- <span class="tag">川货</span> -->
									<span class="dispatching">物流配送</span>
									<span class="dispatching">买家自提</span>
								</div>
							</div>
						</div>
					</td>
					<td class="specification">
						<el-select v-model="item.sku_id" placeholder="请选择" @change="changeSku(item)">
							<el-option
								v-for="optionsItem in item.skus"
								:key="optionsItem.id"
								:value="optionsItem.id"
								:label="optionsItem.skuName"
							></el-option>
						</el-select>
					</td>
					<td class="price">
						<div>¥{{ item.sell_price.toFixed(2) }}</div>
					</td>
					<td class="num">
						<el-input-number
							v-model="item.sku_num"
							:min="item.minNum"
							:max="item.stockNum"
							label="描述文字"
							@change="numberChange(item)"
						></el-input-number>
					</td>
					<td class="total">
						<div>¥{{ setBigNumber(Number(item.sell_price), Number(item.sku_num), item) }}</div>
					</td>
					<td class="button">
						<div>
							<a href="javascript:void(0)">
								<div class="delete" @click="onButton(1, item)">
									<AlIcon name="icon-delete" size="16" color="#8C8C8C"></AlIcon>
									删除
								</div>
							</a>
							<a href="javascript:void(0)">
								<div @click="onButton(2, item)">移入收藏夹</div>
							</a>
						</div>
					</td>
				</tr>
			</table>
		</div>
	</div>
</template>

<script>
import BigNumber from 'bignumber.js';
export default {
	name: 'ShoppingCartList',
	props: {
		info: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			checked: false,
			num: 1,
			value: '',
			infoDate: {}
		};
	},
	created() {
		this.infoDate = this.info;
		this.infoDate.List.forEach(element => {
			element.skus.forEach(item => {
				if (element.sku_id == item.id) {
					element.sell_price = item.price;
					element.minNum = Number(item.minNum);
					element.stockNum = Number(item.stockNum);
				}
			});
		});
	},
	methods: {
		setBigNumber(a, b, item) {
			item.totalPrice = BigNumber(a).times(b).toString();
			return Number(item.totalPrice).toFixed(2);
		},
		numberChange(item) {
			this.$parent.loading = true;
			let data = {
				skuNum: item.sku_num,
				id: item.id
			};
			this.$api.shop_api.updateNum(data).then(res => {
				if (res.state) {
					// this.$message.success(res.msg);
				} else {
					this.$message.close();
					this.$message.warning(res.msg);
				}
				this.$parent.loading = false;
				this.$parent.getList();
			});
		},
		details(t) {
			this.$router.push({
				path: '/shopDetail',
				query: {
					id: t.product_id
				}
			});
		},
		changeSku(item) {
			this.$parent.loading = true;
			let data = {
				skuId: item.sku_id,
				id: item.id
			};
			this.$api.shop_api.updateSkuId(data).then(res => {
				if (res.state) {
					// this.$message.success(res.msg);
				} else {
					this.$message.close();
					this.$message.warning(res.msg);
				}
				this.$parent.loading = false;
				this.$parent.getList();
			});
			this.infoDate.List.forEach(element => {
				element.skus.forEach(event => {
					if (element.sku_id == event.id) {
						element.sell_price = event.price;
						element.minNum = Number(event.minNum);
						element.stockNum = Number(event.stockNum);
					}
				});
			});
		},
		changeCheckedAll(item) {
			this.infoDate.List.forEach(element => {
				if (this.infoDate.checked && !element.state) {
					element.checked = true;
				} else {
					element.checked = false;
				}
			});
			this.$emit('changeChecked', this.infoDate, item);
		},
		onButton(type, data) {
			this.$confirm(`确定要${type == 1 ? '删除' : '收藏'}当前商品？`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$emit('onButton', type, data);
				})
				.catch(() => {});
		},
		changeChecked(item) {
			console.log(item);
			let checked = true;
			this.infoDate.List.forEach(element => {
				if (!element.checked) {
					checked = false;
				}
			});
			this.$forceUpdate();
			this.infoDate.checked = checked;
			this.$emit('changeChecked', this.infoDate, item);
		}
	}
};
</script>

<style lang="scss" scoped>
.shopping-cart-list {
	.store {
		padding: 9px 16px;
	}
	.goods-active {
		background: linear-gradient(90deg, #ccdffd 0%, rgba(255, 255, 255, 0) 100%);
		border: 1px solid #3274e0 !important;
	}
	.goods {
		margin-top: 10px;
		padding: 9px 13px;
		border: 1px solid #d9d9d9;
		&-table {
			width: 100%;
		}
		.checkbox {
			width: 26px;
			position: relative;
			.el-checkbox {
				position: absolute;
				top: 9px;
				left: 0;
			}
		}
		.specification {
			width: 140px;
			.el-select {
				width: 140px;
			}
		}
		.details {
			width: 404px;
		}
		.price {
			width: 140px;
			div {
				text-align: center;
			}
		}
		.num {
			width: 130px;
			.el-input-number {
				width: 122px;
			}
		}
		.button {
			div {
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #262626;
				text-align: center;
				.delete {
					margin-bottom: 16px;
				}
			}
		}
		.total {
			width: 156px;
			color: #f95f55;
			div {
				text-align: center;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				line-height: 22px;
			}
		}
		.info {
			display: flex;
			.img {
				width: 70px;
				height: 70px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				margin-right: 20px;
				object-fit: contain;
			}
			.name {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
				margin-bottom: 13px;
			}
			.tag {
				background: #ffeeee;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #ffeeee;
				padding: 0 6px;
				height: 20px;
				width: 24px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 20px;
			}
			.dispatching {
				margin-left: 6px;
				height: 20px;
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 20px;
				padding: 0 6px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid var(--brand-6, '#ca3f3b');
			}
		}
	}
}
::v-deep .el-icon-plus {
	color: var(--brand-6, '#ca3f3b');
}
::v-deep .el-icon-minus {
	color: var(--brand-6, '#ca3f3b');
}
</style>

<style lang="scss">
.el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
.el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
	border-color: var(--brand-6, '#ca3f3b') !important;
}
.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
	border-color: var(--brand-6, '#ca3f3b') !important;
}
.el-input__inner:hover,
.el-input__inner:focus {
	border-color: var(--brand-6, '#ca3f3b') !important;
}
</style>
