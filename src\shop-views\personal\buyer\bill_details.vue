<template>
	<el-skeleton class="bill_details" :loading="skeleton" animated>
		<div>
			<div class="head">
				<div class="icon">
					<AlIcon v-if="data.status == 0" name="icon-daifukuanb" color="#fff" size="40" />
					<i v-else :class="data.status == 1 ? 'el-icon-circle-check' : 'el-icon-circle-close'"></i>
				</div>
				<div class="state">
					发票状态：
					<span v-if="data.status == 0">申请中</span>
					<span v-else-if="data.status == 1">已处理</span>
					<span v-else-if="data.status == 2">已拒绝</span>
					<span v-else>未开票</span>
				</div>
				<img class="rf" src="@/assets/shop-images/order-details-head-bg.png" alt="" />
			</div>
			<div class="data">
				<div class="lf">订单编号：</div>
				<div class="rf">{{ data.order_id }}</div>
				<div class="lf">订单金额：</div>
				<div class="rf red">{{ data.bill_money }}</div>
			</div>
			<div class="box">
				<Title title="发票信息"></Title>
				<div class="content">
					<div class="cell">
						<div class="lf">发票类型：</div>
						<div class="rf">
							<span v-if="data.type == 1">单位增值税发票</span>
							<span v-else-if="data.type == 2">单位普通发票</span>
							<span v-else>个人发票</span>
						</div>
					</div>
					<div class="cell">
						<div class="lf">邮箱地址：</div>
						<div class="rf">{{ data.receive_email || '-' }}</div>
					</div>
					<div class="cell">
						<div class="lf">发票金额：</div>
						<div class="rf">{{ data.bill_money || '-' }}</div>
					</div>
					<div class="cell">
						<div class="lf">申请日期：</div>
						<div class="rf">{{ data.create_time || '-' }}</div>
					</div>
					<div class="cell">
						<div class="lf">开票{{ data.type === 3 ? '人' : '单位' }}：</div>
						<div class="rf">{{ data.bill_header || '-' }}</div>
					</div>
					<div v-if="data.type != 3" class="cell">
						<div class="lf">单位税号：</div>
						<div class="rf">{{ data.duty_paragraph || '-' }}</div>
					</div>
					<div v-if="data.type != 3" class="cell">
						<div class="lf">注册电话：</div>
						<div class="rf">{{ data.reg_phone_number || '-' }}</div>
					</div>
					<div v-if="data.type != 3" class="cell">
						<div class="lf">注册地址：</div>
						<div class="rf">{{ data.reg_address || '-' }}</div>
					</div>
					<div v-if="data.type != 3" class="cell">
						<div class="lf">开户银行：</div>
						<div class="rf">{{ data.bank || '-' }}</div>
					</div>
					<div v-if="data.type != 3" class="cell">
						<div class="lf">银行账号：</div>
						<div class="rf">{{ data.bank_account || '-' }}</div>
					</div>
				</div>
				<div v-if="data.process_content" class="state">
					<div class="name">商家意见：</div>
					<div class="desc">{{ data.process_content }}</div>
					<div class="date">{{ data.process_time }}</div>
				</div>
			</div>
			<!--			<div class="btn">-->
			<!--				<el-button @click="$router.go(-1)">返回</el-button>-->
			<!--			</div>-->
		</div>
	</el-skeleton>
</template>

<script>
import Title from './../components/title.vue';
export default {
	name: 'BillDetails',
	components: {
		Title
	},
	data() {
		return {
			id: '',
			skeleton: false,
			data: {}
		};
	},
	created() {
		if (!this.$route.query.id) {
			this.$message.close();
			this.$message.error('参数错误！');
			this.$router.go(-1);
		} else {
			this.id = this.$route.query.id;
			this.getBillApplyById();
		}
	},
	methods: {
		// 获取详情
		getBillApplyById() {
			this.skeleton = true;
			this.$api.shop_api.getBillApplyById({ id: this.id }).then(res => {
				if (res.state) {
					this.data = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.skeleton = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.bill_details {
	width: 1200px;
	margin: 0 auto;
	background: #ffffff;
	padding: 20px 24px;
	.head {
		height: 60px;
		background: linear-gradient(91deg, #eafbff 0%, #f0e9fc 100%);
		display: flex;
		align-items: center;
		padding: 0 15px;
		margin-bottom: 20px;
		.icon {
			width: 46px;
			height: 46px;
			background: linear-gradient(180deg, #74a8fc 0%, #3274e0 100%);
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
			i {
				font-size: 30px;
				color: #fff;
			}
		}
		.state {
			font-size: 20px;
			font-weight: 500;
			color: #3274e0;
			line-height: 24px;
			margin-left: 11px;
		}
		.rf {
			width: 81px;
			height: 59px;
			margin-left: auto;
		}
	}
	.data {
		height: 52px;
		background: #f4f5f8;
		border-radius: 4px;
		display: flex;
		align-items: center;
		padding: 0 12px;
		.lf {
			font-size: 14px;
			font-weight: 400;
			color: #9da5b7;
			line-height: 22px;
		}
		.rf {
			width: 206px;
			font-size: 14px;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			margin-right: 12px;
		}
		.red {
			color: #ca3f3b;
		}
	}
	.box {
		margin-top: 12px;
		padding: 16px 23px 20px 23px;
		border: 1px solid #d9d9d9;
		.content {
			margin-top: 17px;
			.cell {
				display: flex;
				align-items: center;
				height: 36px;
				font-size: 14px;
				font-weight: 400;
				color: #9da5b7;
				.lf {
					width: 80px;
				}
				.rf {
					width: calc(100% - 100px);
					color: #404040;
				}
			}
		}
		.state {
			margin-top: 20px;
			background: #f7f8fa;
			border-radius: 8px;
			padding: 12px;
			.name {
				font-size: 16px;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
			}
			.desc {
				margin-top: 7px;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				line-height: 22px;
			}
			.date {
				font-size: 12px;
				margin-top: 4px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
			}
		}
	}
	.btn {
		margin-top: 30px;
	}
}
</style>
