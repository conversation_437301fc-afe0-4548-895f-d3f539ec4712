<template>
	<el-dialog
		ref="visible"
		title="测试"
		width="639px"
		top="5vh"
		:visible.sync="visible"
		:before-close="handleclose"
		@close="handleclose"
	>
		<div v-loading="loadingView" class="edit-profile">
			<div class="top">
				<div class="top-left">修改资料</div>
				<div
					v-if="loginForm.isDefaultPasswork !== true || isLocalHost"
					class="top-right"
					@click="visible = false"
				>
					<svg
						t="1720158613326"
						class="icon"
						viewBox="0 0 1482 1024"
						version="1.1"
						xmlns="http://www.w3.org/2000/svg"
						p-id="23461"
						width="200"
						height="200"
					>
						<path
							d="M584.884104 272.714912V0L0 477.152657l584.884104 477.073906V674.817811c406.119203 0 690.568023 108.991464 893.588249 347.528417-81.271091-340.755826-324.926863-681.354149-893.588249-749.631316"
							fill="#0175e8"
							p-id="23462"
						></path>
					</svg>
					<span class="top-right-text">返回主页</span>
				</div>
			</div>
			<div :key="visible" class="content">
				<el-form
					ref="ruleForm"
					:model="loginForm"
					:rules="loginRules"
					:hide-required-asterisk="true"
					auto-complete="on"
					label-position="right"
					label-width="100px"
				>
					<el-form-item prop="name" label="姓名:">
						<el-input
							v-model="loginForm.name"
							placeholder="请输入姓名"
							readonly
							required
							name="name"
							type="text"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item label="部门:">
						<el-input
							v-model="loginForm.deptName"
							placeholder=""
							readonly
							type="text"
							tabindex="1"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item prop="idcard" label="身份证号:">
						<!-- 如果有身份证信息就不允许修改 -->
						<el-input
							v-model="loginForm.idcard"
							placeholder="请输入身份证号"
							:readonly="oldForm.idcard !== ''"
							name="idcard"
							type="text"
							tabindex="1"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item
						v-if="loginForm.platPerson.zglx === 'student'"
						prop="bankCardNumber"
						label="银行卡号:"
					>
						<el-input
							v-model="loginForm.bankCardNumber"
							placeholder="请输入银行卡号"
							name="idcard"
							type="text"
							tabindex="1"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item
						v-if="loginForm.platPerson.zglx === 'student'"
						prop="bankInformation"
						label="开户行信息:"
					>
						<el-input
							v-model="loginForm.bankInformation"
							placeholder="请输入开户行信息"
							name="idcard"
							type="text"
							tabindex="1"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item prop="password" label="新密码:">
						<el-input
							ref="password"
							v-model="loginForm.password"
							placeholder="请输入密码，8-12位数字字母特殊字符符号"
							name="password"
							type="password"
							tabindex="1"
						/>
					</el-form-item>
					<el-form-item prop="isPassword" label="确认密码:">
						<el-input
							ref="isPassword"
							v-model="loginForm.isPassword"
							placeholder="请再次输入密码"
							name="isPassword"
							type="password"
							tabindex="1"
						/>
					</el-form-item>
					<el-form-item prop="phone" label="手机号:">
						<el-input
							ref="phone"
							v-model="loginForm.phone"
							placeholder="请输入手机号码"
							name="phone"
							type="text"
							tabindex="1"
							auto-complete="on"
						/>
					</el-form-item>
					<el-form-item prop="verifyCode" class="loginCode" label="短信验证码:">
						<el-input
							ref="verifyCode"
							v-model="loginForm.verifyCode"
							type="text"
							placeholder="请输入短信验证码"
							name="verifyCode"
							tabindex="2"
							auto-complete="on"
							@keyup.enter.native="handleRegister"
						>
							<template slot="append">
								<span
									:loading="loading"
									class="code_btn"
									type="primary"
									style="width: 100%; margin-bottom: 13px"
									@click="handlerGetCode"
								>
									{{ codeText }}
								</span>
							</template>
						</el-input>
					</el-form-item>

					<el-form-item prop="imageCode" class="imageCode" label="验证码:">
						<el-input
							ref="verifyCode"
							v-model="loginForm.imageCode"
							type="text"
							placeholder="请输入验证码不区分大小写"
							name="imageCode"
							tabindex="2"
							style="width: 320px"
							@keyup.enter.native="handleRegister"
						>
							<template slot="append">
								<div class="img-code"><VerificationImage :img-code.sync="imgCode" /></div>
							</template>
						</el-input>
					</el-form-item>

					<el-button
						:loading="loading"
						class="submit_btn"
						type="primary"
						@click.native.prevent="handleRegister"
					>
						确认修改
					</el-button>
				</el-form>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import VerificationImage from './VerificationImage.vue';
export default {
	components: {
		VerificationImage
	},
	data() {
		function validPhone(phone) {
			return /(^(\d{3,4}-)?\d{7,8})$|(1[0-9]{10})/.test(phone);
		}
		function validPassWord(password) {
			return /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,16}$/.test(
				password
			);
		}
		const validatePhone = (rule, value, callback) => {
			if (!validPhone(value)) {
				callback(new Error('请输入正确的手机号'));
			} else {
				callback();
			}
		};
		const validatePassword = (rule, value, callback) => {
			if (!validPassWord(value)) {
				callback(new Error('密码需为8~16位非空字符，并包含数字、大小写字母、特殊字符'));
			} else {
				callback();
			}
		};
		const validateIsPassword = (rule, value, callback) => {
			if (value != this.loginForm.password) {
				callback(new Error('密码输入不一致'));
			} else {
				callback();
			}
		};
		const validateIdcard = (rule, value, callback) => {
			if (
				!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
					value
				)
			) {
				callback(new Error('请输入正确的身份证号'));
			} else {
				callback();
			}
		};
		return {
			// userId、phone、verifyCode、idcard、password
			loginForm: {
				userId: '',
				name: '', // 用户名
				deptName: '', // 部门
				idcard: '', // 身份证
				phone: '',
				password: '',
				isPassword: '',
				verifyCode: '',
				isDefaultPasswork: false,
				platPerson: { zglx: '' },
				imageCode: ''
			},
			loginRules: {
				name: [{ required: true, trigger: 'blur', message: '请输入名字' }],
				verifyCode: [{ required: true, trigger: 'blur', message: '请输入短信验证码' }],
				idcard: [{ required: true, trigger: 'blur', validator: validateIdcard }],
				bankCardNumber: [{ required: true, trigger: 'blur', message: '请输入银行卡号' }],
				bankInformation: [{ required: true, trigger: 'blur', message: '请输入开户行信息' }],
				phone: [{ required: true, trigger: 'blur', validator: validatePhone }],
				password: [{ required: true, trigger: 'blur', validator: validatePassword }],
				isPassword: [{ required: true, trigger: 'blur', validator: validateIsPassword }],
				imageCode: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							const LowerImgCode = this.imgCode.toLowerCase();
							const LowerValue = value.toLowerCase();
							if (!LowerValue) {
								callback(new Error('请输入验证码'));
							} else if (LowerValue !== LowerImgCode) {
								callback(new Error('验证码错误'));
							} else {
								callback();
							}
						}
					}
				]
			},
			imgCode: null,
			codeTime: 60,
			loading: false,
			loadingView: false,
			redirect: undefined,
			codeText: '获取验证码',
			codeTimer: null,
			visible: false,
			oldForm: { idcard: '' }
		};
	},
	computed: {
		// 本地显示关闭按钮，方便调试
		isLocalHost() {
			return (
				window.location.hostname === 'localhost' ||
				window.location.hostname === '127.0.0.1' ||
				window.location.hostname === '************'
			);
		}
	},
	mounted() {
		// 获取登录用户信息判断是否为弱密码
		const userInfo = JSON.parse(localStorage.getItem('userInfo'));
		this.loginForm = {
			userId: '',
			name: '', // 用户名
			deptName: '', // 部门
			idcard: '', // 身份证
			phone: '',
			password: '',
			isPassword: '',
			verifyCode: '',
			platPerson: { zglx: '' },
			imageCode: ''
		};

		if (userInfo?.isDefaultPasswork === true) {
			this.visible = true;
			this.$api.getLoginUserInfo().then(res => {
				this.$notify({
					title: '警告',
					message: '当前密码较弱，请重新修改！',
					type: 'warning'
				});
				this.loginForm = {
					...this.loginForm,
					...res.results
				};
				this.oldForm = JSON.parse(JSON.stringify(this.loginForm));
			});
		}
	},
	methods: {
		// 手机号注册
		handleRegister() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					const loginForm = JSON.parse(JSON.stringify(this.loginForm));
					loginForm.password = btoa(loginForm.password);
					delete loginForm.isPassword;
					delete loginForm.imageCode;

					this.loadingView = true;
					this.$api
						.updateLoginUser(loginForm)
						.then(res => {
							this.loadingView = false;
							if (res.success) {
								this.$message({
									message: res.msg,
									type: 'success'
								});
								this.visible = false;
								// 清空个人信息，重新登录
								this.$loginOut();
							} else {
								this.$message({
									message: res.msg,
									type: 'error'
								});
							}
						})
						.catch(() => {
							this.loadingView = false;
							this.$message({
								message: '网络异常,请检查网络！',
								type: 'error'
							});
						});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		},
		handlerGetCode() {
			// 验证手机号
			this.$refs.ruleForm.validateField('phone');
			// 手机号正确时
			if (this.loginForm.phone) {
				this.codeText = '正在获取...';
				this.$api
					.sendUpdateUserInfoVerifyCode({
						phone: this.loginForm.phone
						// phone: '18728307801'
					})
					.then(res => {
						if (res.success) {
							this.$message({
								message: '验证码获取成功',
								type: 'success'
							});
							// 响应成功，开始倒计时
							this.handleCodeTime();
						} else {
							this.$message({
								message: res.msg,
								type: 'error'
							});
							this.codeText = '获取验证码';
						}
					})
					.catch(() => {
						this.codeText = '获取验证码';
					});
			}
		},
		handleCodeTime() {
			this.codeTimer = setInterval(() => {
				this.codeTime -= 1;
				// 倒计时结束展示文字
				if (this.codeTime <= 0) {
					clearInterval(this.codeTimer);
					this.codeTime = 60;
					this.codeText = '获取验证码';
				} else {
					// 开始倒计时
					this.codeText = `${this.codeTime} s`;
				}
			}, 1000);
		},
		handleclose() {
			this.loadingView = false;
			this.$emit('close');
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
	padding-top: 0px;
	position: relative;
	border-radius: 6px;
	overflow: hidden;
	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		padding: 21px 24px 36px;
		overflow-y: auto;
	}
}

.edit-profile {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	max-height: 78vh;
	// overflow-y: auto;

	.top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 0 17px 43px;
		width: 100%;
		border-bottom: 1px solid #ebebeb;
		// position: sticky;
		// top: 0;
		// background: #fff;
		z-index: 3;
		.top-left {
			height: 26px;
			font-weight: bold;
			font-size: 20px;
			color: #454545;
			line-height: 26px;
			position: relative;
			&::after {
				position: absolute;
				content: '';
				display: inline-block;
				width: 4px;
				height: 16px;
				background: #0175e8;
				border-radius: 3px;
				top: calc(50% - 8px);
				left: -17px;
			}
		}

		.top-right {
			display: flex;
			cursor: pointer;
			.icon {
				width: 23px;
				height: 15px;
				margin-right: 8px;
			}

			.top-right-text {
				font-size: 16px;
				color: #0175e8;
				line-height: 1;
			}
		}
	}
	.content {
		padding: 23px 22px 0;
		::v-deep {
			.el-input__inner {
				height: 40px;
				background: #f9f9f9;
				border-color: #e5e5e5 !important;
			}
			.loginCode {
				.el-input__inner {
					border-right: 0;
				}
				.el-input-group__append {
					background-color: #f9f9f9;
					border-color: #e5e5e5 !important;
				}
				.code_btn {
					cursor: pointer;
					font-size: 16px;
					color: #0175e8;
					line-height: 1;
					text-align: left;
					font-style: normal;
				}
			}
			.imageCode {
				.el-input-group__append {
					padding: 0;
					.img-code {
						height: 38px;
						overflow: hidden;
					}
				}
			}
			.submit_btn {
				margin: 16px auto 32px;
				width: 280px;
				height: 52px;
				transform: translateX(50%);
				border-radius: 4px;
			}
		}
	}
}
</style>
