<template>
	<div class="good_list">
		<div v-for="(item, index) in list" :key="index" class="list" @click="goDeta(item.productId)">
			<img :src="$judgeFile(item.coverUrl)" alt="" />
			<div class="mid">
				<div class="name nth1">{{ item.productName }}</div>
				<div class="tit">库存：{{ item.stockNum }}</div>
				<el-tooltip class="item" effect="dark" :content="item.address" placement="top-start">
					<div class="tit nth1">产地：{{ item.address }}</div>
				</el-tooltip>
			</div>
			<div class="rf">¥{{ item.sellPrice.toFixed(2) }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodList',
	props: {
		list: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		goDeta(productId) {
			this.$router.push({
				path: `/goodsList/details?id=${productId}`
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.nth1 {
	overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
	text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
	white-space: nowrap; /*文本不换行*/
}
.good_list {
	width: 100%;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	justify-content: space-between;
	.list {
		margin-bottom: 2px;
		padding: 9px;
		width: 390px;
		display: flex;
		cursor: pointer;
		img {
			width: 72px;
			height: 72px;
		}
		.mid {
			margin-left: 9px;
			width: 200px;
			.name {
				font-size: 14px;
				font-weight: 400;
				color: #262626;
				line-height: 22px;
			}
			.tit {
				margin-top: 6px;
				font-size: 12px;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
			}
		}
		.rf {
			margin-left: auto;
			width: 80px;
			font-size: 14px;
			align-self: flex-end;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 22px;
		}
	}
}
</style>
