<!--
 @desc:个人中心 就业创业 我的简历 教育背景
 @author: WH
 @date: 2023/9/5
 -->
<template>
	<div>
		<el-table :data="list" border style="width: 100%">
			<el-table-column fixed prop="university" label="学校"></el-table-column>
			<el-table-column prop="major" label="专业" width="120"></el-table-column>
			<el-table-column prop="time" label="学习时间" width="160">
				<!-- <template slot-scope="scope">
					<span>{{ scope.row.time[0] }}/{{ scope.row.time[1] }}</span>
				</template> -->
			</el-table-column>
			<el-table-column prop="education" label="学历"></el-table-column>
			<el-table-column
				prop="study"
				label="学习经历"
				width="300"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column fixed="right" label="操作" width="100">
				<template slot-scope="scope">
					<el-button
						icon="el-icon-edit"
						type="text"
						size="small"
						@click="educationFn('edit', scope.$index, scope.row)"
					></el-button>
					<el-popconfirm title="确定删除吗？" @confirm="delRow(scope.$index)">
						<el-button slot="reference" icon="el-icon-delete" type="text" size="small"></el-button>
					</el-popconfirm>
				</template>
			</el-table-column>
		</el-table>
		<div class="add-btn-box">
			<el-button type="primary" icon="el-icon-plus" @click="educationFn('add')">
				新增教育背景
			</el-button>
		</div>
		<el-dialog title="教育背景" :visible.sync="visible">
			<el-form ref="form" :rules="rules" :model="form" label-width="140px">
				<el-form-item label="学校" prop="university">
					<el-input v-model="form.university" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="专业" prop="major">
					<el-input v-model="form.major" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="学历" prop="education">
					<el-input v-model="form.education" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="学习时间" prop="time">
					<!-- year/month/date/dates/week/ datetime/datetimerange/daterange/ monthrange/quarter/halfyear -->
					<el-date-picker
						v-model="form.time"
						type="monthrange"
						align="right"
						unlink-panels
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					></el-date-picker>
				</el-form-item>

				<el-form-item label="学习经历" prop="study">
					<el-input v-model="form.study" autocomplete="off" type="textarea" :rows="2"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button type="primary" @click="educationSubmit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { parseTime } from '@/utils';
export default {
	props: {
		tableData: {
			type: [Array, null],
			default: () => []
		}
	},
	data() {
		//localhost:8081/#/personal?type=alumniAssociation
		return {
			visible: false,
			list: [
				// {
				// 	major: '网络工程',
				// 	startTime: '2023-02',
				// 	study: '宜宾职院学习',
				// 	time: '2023-02~2024-04',
				// 	university: '宜宾职院'
				// }
			],
			editIdx: null,
			form: { university: '', major: '', education: '', time: '', study: '' },
			rules: {
				university: [{ required: true, message: '请输入学校名称', trigger: 'blur' }],
				major: [{ required: true, message: '请输入专业名称', trigger: 'blur' }],
				education: [{ required: true, message: '请输入学历', trigger: 'blur' }],
				time: [{ required: true, message: '请选择学习时间', trigger: 'change' }],
				study: [{ required: true, message: '请输入学习经历', trigger: 'blur' }]
			}
		};
	},
	watch: {
		tableData(newVal) {
			this.list = [...newVal];
		}
	},

	mounted() {
		// this.handlerGetRegion();
		// this.handlerQueryDict();
	},
	methods: {
		educationSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.visible = false;
					const { time } = this.form;
					let startTime = parseTime(time[0], '{y}-{m}');
					let endTime = parseTime(time[1], '{y}-{m}');
					let timeStr = startTime + '~' + endTime;
					let row = { ...this.form, time: timeStr, startTime, endTime };
					//编辑
					if (this.editIdx !== null) {
						this.$set(this.list, this.editIdx, row);
					} else {
						//新增
						this.list.push(row);
					}
					this.form = {};
				}
			});
		},
		educationFn(type, index = null, row = {}) {
			this.visible = true;
			this.editIdx = index;
			if (type == 'edit') {
				this.form = {
					...row,
					time: [row.startTime, row.endTime]
				};
			}
		},

		delRow(index) {
			this.$delete(this.list, index);
		}
	}
};
</script>

<style lang="scss" scoped>
.add-btn-box {
	margin-top: 16px !important;
	text-align: center;
}
.el-input,
.el-select {
	width: 300px;
}
</style>
