/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-09-18 14:46:20
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-09-23 14:15:09
 * @FilePath: /ybzy-zhxy-pc/src/api/model/enterprise-center.js
 * @Description:
 */
// 就业创业
import request from '@/utils/request';
// /   gwapi/ybzy/
// 获取企业信息
export function getMyEnterpriseInfo(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/getMyEnterprise`,
		method: 'get',
		params
	});
}

// 保存企业信息
export function saveMyEnterpriseInfo(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/updateEnterpriseInfo`,
		method: 'post',
		data
	});
}
// 获取岗位列表
export function getJobList(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/postList`,
		method: 'get',
		params
	});
}

// 更新岗位信息
export function updateJobInfo(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/saveOrUpdatePost`,
		method: 'post',
		type: 'JSON',
		data
	});
}

// 删除岗位
export function delJobInfo(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/delPost`,
		method: 'post',
		data
	});
}

// 更新岗位状态
export function updateJobStatus(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/updatePostStatus`,
		method: 'post',
		data
	});
}

// 创业服务

// 获取创业服务类型
export function getJobServiceType(params) {
	return request({
		url: `/gwapi/ybzy/front/jobServiceType/selectData`,
		method: 'get',
		params
	});
}
// 获取创业服务列表
export function getJobServiceList(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/jobServiceList`,
		method: 'get',
		params
	});
}
// 保存创业服务信息
export function saveJobService(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/saveJobService`,
		method: 'post',
		data
	});
}
// 更新创业服务信息
export function updateJobService(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/updateJobService`,
		method: 'post',
		data
	});
}

// 删除创业服务
export function delJobService(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/delJobService`,
		method: 'post',
		data
	});
}
// 修改创业服务启用状态
export function changeJobServiceStatus(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/changeJobServiceStatus`,
		method: 'post',
		data
	});
}

// 简历管理
// 对岗位感兴趣列表
export function getFollowPostList(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/followPostList`,
		method: 'get',
		params
	});
}

// 企业收到的投递简历列表(参数--isView 1 正常显示，2 回收站列表)
export function getPostResumeList(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/getPostResumeList`,
		method: 'get',
		params
	});
}
// 根据简历id获取简历信息
export function getResumeInfo(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/getResumeInfo`,
		method: 'get',
		params
	});
}

// 面试邀请分页接口
export function getInterviewList(params) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/interviewList`,
		method: 'get',
		params
	});
}
// 恢复投递信息
export function recoverPostResume(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/recoverPostResume`,
		method: 'post',
		data
	});
}
// 删除投递信息（移动到回收站）
export function removePostResume(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/removePostResume`,
		method: 'post',
		data
	});
}
// 邀请面试
export function addInterview(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/addInterview`,
		method: 'post',
		data
	});
}
// 修改面试邀请状态
export function updateInterviewStatus(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/updateInterviewStatus`,
		method: 'post',
		data
	});
}
// 从回收站中彻底删除投递信息
export function delPostResume(data) {
	return request({
		url: `/gwapi/ybzy/front/jobCoPerson/delPostResume`,
		method: 'post',
		data
	});
}

// 聊天
// 聊天列表
export function chatList(params) {
	return request({
		// url: `/gwapi/ybzy/front/job/chatMsg/chatList`,
		url: `/gwapi/ybzy/front/job/chatMsg/userMsgList`,
		method: 'get',
		params
	});
}
// 聊天框记录列表
export function chatRecords(params) {
	return request({
		url: `/gwapi/ybzy/front/job/chatMsg/chatRecords`,
		method: 'get',
		params
	});
}
// 发送消息
export function sendMsg(data) {
	return request({
		url: '/gwapi/ybzy/front/job/chatMsg/send',
		method: 'post',
		data,
		type: 'JSON'
	});
}
// 双选会邀请列表
export function jobDualSelectList(params) {
	return request({
		url: '/ybzy/front/jobDualSelect/listJson',
		method: 'get',
		params
	});
}
// 双选会邀请详情
export function jobDualSelectInfo(params) {
	return request({
		url: '/ybzy/front/jobDualSelect/info',
		method: 'get',
		params
	});
}
// 双选会邀请回复
export function jobDualSelectReply(data) {
	return request({
		url: '/ybzy/front/jobDualSelect/reply',
		method: 'post',
		data
	});
}
// 双选会邀请回复撤回
export function jobDualSelectRecallReply(data) {
	return request({
		url: '/ybzy/front/jobDualSelect/recallReply',
		method: 'post',
		data
	});
}
// 双选会邀请根据岗位ids查询岗位信息
export function selectPostList(params) {
	return request({
		url: '/ybzy/front/jobDualSelect/selectPostList',
		method: 'get',
		params
	});
}
// 获取岗位详情
export function getPostInfo(params) {
	return request({
		url: '/ybzy/front/jobCoPerson/getPostInfo',
		method: 'get',
		params
	});
}
