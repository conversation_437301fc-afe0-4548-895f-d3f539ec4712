<template>
	<div class="skill-treasure-details">
		<!-- 内容区域 -->
		<div v-loading="loading" class="content-box">
			<div class="content-box-carousel">
				<!-- 视频展示 -->
				<video
					v-if="detail.video"
					ref="video"
					class="video-box"
					controls="controls"
					@pause="pause"
					@play="play"
				>
					<source :src="$judgeFile(detail.video)" type="video/ogg" />
					<source :src="$judgeFile(detail.video)" type="video/mp4" />
					<source :src="$judgeFile(detail.video)" type="video/webm" />
				</video>
				<template v-else>
					<!-- 右上角展示的轮播进度 -->
					<span v-if="detail.imagesList && detail.imagesList.length" class="carousel-top">
						{{ carouselIndex }}/{{ detail.imagesList.length }}
					</span>
					<!-- 图集展示轮播 -->
					<el-carousel
						v-if="detail.imagesList && detail.imagesList.length"
						:interval="5000"
						arrow="always"
						height="882px"
						@change="carouselChange"
					>
						<el-carousel-item v-for="item in detail.imagesList" :key="item">
							<img :src="$judgeFile(item)" class="carousel-img" />
						</el-carousel-item>
					</el-carousel>
					<!-- 封面展示 -->
					<img
						v-if="detail.imagesList && !detail.imagesList.length"
						:src="$judgeFile(detail.coverImg)"
						class="carousel-img"
					/>
				</template>
			</div>
			<div class="content-right">
				<!-- 右边展示详情内容区域 -->
				<div class="content-right-top">
					<div
						class="top-author"
						@click="jumpPage(`/personal-homepage?memberId=${detail.memberId}`)"
					>
						<!-- {{detail.coverImg}} -->
						<!-- <headAvator class="author-img" :own-id="getOwnId(detail.memberId)" /> -->
						<img :src="$judgeFile(detail.coverImg)" class="author-img" />
						<span class="author u-line-1">{{ detail.memberName }}</span>
					</div>
					<!-- 为个人主页时图标展示为编辑删除操作 -->
					<div v-if="userType == 'user'" class="person-btns">
						<el-dropdown trigger="click" @command="handleCommand">
							<span class="el-dropdown-link">
								<i class="el-icon-more"></i>
							</span>
							<el-dropdown-menu slot="dropdown">
								<el-dropdown-item icon="el-icon-edit-outline" command="editFun">
									编辑
								</el-dropdown-item>
								<el-dropdown-item icon="el-icon-delete" command="deleteFun">删除</el-dropdown-item>
							</el-dropdown-menu>
						</el-dropdown>
					</div>
					<!-- 为用户主页时，图标展示为关注操作 -->
					<div v-else class="person-btns">
						<span
							v-if="detail.isInterest"
							class="interest-active"
							@click="memberInteract(6, detail.memberId)"
						>
							取消关注
						</span>
						<span v-else class="interest-btn" @click="memberInteract(6, detail.memberId)">
							关注
						</span>
					</div>
				</div>
				<!-- 中间滚动区域 -->
				<div
					v-infinite-scroll="load"
					class="content-right-middle"
					style="overflow: auto"
					infinite-scroll-distance="5"
				>
					<div class="middle-content">
						<p class="title">
							{{ detail.title }}
						</p>
						<div class="content">
							{{ detail.content }}
						</div>
						<span class="time">
							{{ detail.createTimeStr }}
						</span>
					</div>
					<!-- 相关商品区域 -->
					<div v-if="shopList.length" class="shop-box">
						<span class="title">作品提到的商品</span>
						<div class="shop-list">
							<div v-for="(item, index) in shopList" :key="index" class="list-item">
								<img :src="$judgeFile(item.coverUrl)" class="item-img" />
								<div class="item-right">
									<p class="item-title u-line-2">{{ item.productName }}</p>
									<span class="item-price">￥{{ item.sellPrice || 0 }}</span>
									<span class="item-btn" @click="shopJumpPage(item)">去看看</span>
								</div>
							</div>
						</div>
					</div>
					<div class="commits-box">
						<!-- 留言区域 -->
						<span class="total-commits">共{{ commentsTotal }}条评论</span>
						<Empty v-if="comments.length == 0" :tips="'暂无评论'" />
						<div v-else class="commits-list">
							<div v-for="(item, index) in comments" :key="index" class="commit-item">
								<img
									:src="item.coverImg ? $judgeFile(item.coverImg) : defaulAvatar"
									class="commit-img"
								/>
								<div class="item-right">
									<span class="item-author">
										{{ item.memberName }}
										<span v-if="item.memberId == detail.memberId" class="author-span">作者</span>
									</span>
									<span class="item-commit">{{ item.content }}</span>
									<div class="item-time-icon">
										<span class="item-time">{{ item.createTimeStr }}</span>
										<div class="item-icons">
											<span class="item-icon" @click="memberInteract(11, item, index)">
												<img
													v-if="item.isDiggs"
													src="@/assets/skill-treasure/heart-small-active.png"
													class="nums-icon"
												/>
												<img
													v-else
													src="@/assets/skill-treasure/heart-small.png"
													class="nums-icon"
												/>
												{{ item.diggs || 0 }}
											</span>
											<!-- heart-small -->
											<!-- <span class="item-icon">
												<img src="@/assets/skill-treasure/message-light.png" class="nums-icon" />
												{{ detail.digs || 0 }}
											</span> -->
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="content-right-bottom">
					<div class="nums-box">
						<div class="nums-box-left">
							<span class="item-nums" @click="memberInteract(1)">
								<img
									v-if="detail.isDiggs"
									src="@/assets/skill-treasure/heart-big-active.png"
									class="nums-icon"
								/>
								<img v-else src="@/assets/skill-treasure/heart-big.png" class="nums-icon" />
								{{ detail.diggs || 0 }}
							</span>
							<span class="item-nums" @click="memberInteract(2)">
								<img
									v-if="detail.isCollect"
									src="@/assets/skill-treasure/collect-active-icon.png"
									class="nums-icon"
								/>
								<img v-else src="@/assets/skill-treasure/collect-icon.png" class="nums-icon" />
								{{ detail.collects || 0 }}
							</span>
							<span class="item-nums">
								<img src="@/assets/skill-treasure/message.png" class="nums-icon" />
								{{ commentsTotal || 0 }}
							</span>
							<span class="item-nums" @click="copyAddress">
								<AlIcon name="el-icon-share" size="24" color="#404040"></AlIcon>
							</span>
						</div>
						<!-- <img src="@/assets/skill-treasure/forward-icon.png" class="forward-icon" /> -->
					</div>
					<div class="send-box">
						<el-input
							v-model.trim="commitInput"
							class="send-input"
							placeholder="说点什么..."
						></el-input>
						<span v-loading="btnLoading" class="send-btn" @click="addFeedback">发送</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			detailId: '', // id
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			// 内容部分数据
			detail: {},
			carouselIndex: 1, //当前幻灯片的下标
			pageNum: 1,
			pageSize: 10,
			// 评论列表
			comments: [],
			commentsTotal: 0,
			// 商品列表
			shopList: [],
			loading: false, //页面加载动画
			commitInput: '', //发送的输入框内容
			btnLoading: false, //发送按钮的loading，防止重复点击
			dydUserinfo: '',
			loadingFlag: true, //是否可以滚动
			userType: '' //用户类型，用于区分是否作者本人， ''为非本人，user为本人
		};
	},
	mounted() {
		this.detailId = this.$route.query.id || '';
		this.dydUserinfo = this.$getDydUserinfo();
		this.loading = true;
		this.getDetail(); //获取详情数据
		this.interactFeedbackList(); //获取评论列表
	},
	methods: {
		copyAddress() {
			let copyInput = document.createElement('input'); // 创建元素
			copyInput.value = window.location.href;
			document.body.appendChild(copyInput); // 添加元素
			copyInput.select();
			document.execCommand('Copy'); // 执行浏览器复制命令
			document.body.removeChild(copyInput); // 移除刚创建的元素
			this.$message.close();
			this.$message.success('复制成功');
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
			// this.$router.push({
			// 	path: url,
			// 	title: '测试标题'
			// });
		},
		/**
		 * @description 相关商品跳转详情
		 * */
		shopJumpPage(item) {
			// 判断商品类型
			this.jumpPage(`/shopDetail?id=${item.productId}&type=${item.regCode}`);
		},
		handleCommand(fun) {
			this[fun]();
		},
		/**
		 * @description 幻灯片轮播触发事件
		 * */
		carouselChange(index) {
			this.carouselIndex = index + 1;
		},
		/**
		 * @description 获取详情数据
		 * */
		getDetail() {
			this.$api.treasure_api
				.actStoryInfo(this.detailId)
				.then(res => {
					if (res.code == 200) {
						this.detail = res.results || {};
						// 判断当前用户是否作者
						this.userType = this.detail.memberId == this.dydUserinfo.id ? 'user' : '';
						if (this.detail.productIds && this.detail.productIds.length) {
							let productIds = this.detail.productIds.join(',');
							this.getProductsByIds(productIds); //获取关联商品数据
						}
					}
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 获取详情关联商品数据
		 * */
		getProductsByIds(productIds) {
			let param = {
				productIds
			};
			this.$api.treasure_api.getProductsByIds(param).then(res => {
				if (res.state) {
					this.shopList = res.result || [];
				}
			});
		},
		load() {
			if (!this.loadingFlag) {
				return;
			}
			this.pageNum++;
			this.interactFeedbackList();
		},
		/**
		 * @description 获取评论列表
		 * */
		interactFeedbackList() {
			let param = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				objId: this.detailId,
				actType: 3
			};
			this.$api.treasure_api.interactFeedbackList(param).then(res => {
				if (res.code == 200) {
					if (this.pageNum >= res.results.pages) {
						this.loadingFlag = false;
					}
					this.comments = this.comments.concat(res.results?.records || []);
					this.commentsTotal = res.results?.total || 0;
				}
			});
		},
		/**
		 * @description 添加评论
		 * */
		addFeedback() {
			// 未登录不继续执行
			if (!this.isShopLogin()) {
				return;
			}
			if (!this.commitInput) {
				return this.$message({
					type: 'info',
					message: `未填写内容`
				});
			}
			if (this.detail.auditStatus !== 1) {
				// 未通过审核状态
				return this.$message({
					type: 'info',
					message: `审核通过之后才能评论`
				});
			}
			this.btnLoading = true;
			let param = {
				content: this.commitInput,
				rentId: this.getSiteId(), // 租户id
				objId: this.detailId,
				actType: 3
			};
			this.$api.treasure_api
				.addFeedback(param)
				.then(res => {
					if (res.code == 200) {
						this.$message({
							type: 'success',
							message: `评论成功`
						});
						this.commitInput = ''; // 清空输入内容
						// 重新获取评论列表
						this.pageNum = 1;
						this.comments = [];
						this.interactFeedbackList();
					} else {
						this.$message({
							type: 'error',
							message: `${res.msg}`
						});
					}
					this.btnLoading = false;
				})
				.catch(() => {
					this.btnLoading = false;
				});
		},
		/**
		 * @description 编辑
		 * */
		editFun() {
			this.jumpPage(`/publication-works?id=${this.detail.id}`);
		},
		/**
		 * @description 删除
		 * */
		deleteFun() {
			let param = {
				id: this.detailId
			};
			this.$api.treasure_api.actStoryDel(param).then(res => {
				if (res.code == 200) {
					this.$message({
						type: 'success',
						message: `操作成功`
					});
					this.$router.go(-1);
				} else {
					this.$message({
						type: 'error',
						message: `${res.msg}`
					});
				}
			});
		},
		/**
		 * @description 点击关注作者事件
		 * */
		interestClick() {},
		/**
		 * @description 1点赞攻略    2收藏攻略  6关注用户   11点赞评论 操作
		 * @param {code} 1点赞攻略    2收藏攻略  6关注用户   11点赞评论
		 * */
		memberInteract(code, item, index) {
			// 未登录不继续执行
			if (!this.isShopLogin()) {
				return;
			}
			if (this.detail.auditStatus !== 1) {
				// 未通过审核状态
				return this.$message({
					type: 'info',
					message: `审核通过之后才能操作`
				});
			}
			let objId = this.detailId;
			if (code == 11) {
				objId = item.id;
			} else if (code == 6) {
				objId = item;
			}
			let param = {
				rentId: this.getSiteId(), // 租户id
				objId,
				actType: code
			};
			this.$api.treasure_api.memberInteract(param).then(res => {
				if (res.code == 200) {
					if (code == 11) {
						// 需要手动更新评论列表
						this.comments[index].isDiggs = !this.comments[index].isDiggs;
						if (this.comments[index].isDiggs) {
							this.comments[index].diggs++;
						} else {
							this.comments[index].diggs--;
						}
					} else {
						this.getDetail(); //获取详情数据
					}
				}
			});
		},
		/**
		 * @description 监听播放事件
		 * */
		play(e) {
			// this.playStatus = true;
			// if (this.firstPlay) {
			// 	this.playNum();
			// 	this.firstPlay = false;
			// }
		},
		/**
		 * @description 监听暂停事件
		 * */
		pause(e) {
			// this.playStatus = false;
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1200px;
* {
	margin: 0;
}
.skill-treasure-details {
	width: $max-width;
	margin: 0 auto;
	padding: 0 0 16px;
	font-family: PingFang SC-Regular, PingFang SC;
	.content-box {
		margin-top: 16px;
		display: flex;
		border-radius: 4px;
		overflow: hidden;
		.content-box-carousel {
			width: 700px;
			height: 882px;
			background: #dedede;
			border-radius: 4px 0px 0px 4px;
			flex-shrink: 0;
			position: relative;
		}
		.video-box {
			width: 100%;
			height: 100%;
		}
		.carousel-img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
		.carousel-top {
			position: absolute;
			top: 30px;
			right: 30px;
			z-index: 10;
			display: inline-block;
			padding: 0 8px;
			height: 24px;
			background: rgba(38, 38, 38, 0.8);
			border-radius: 3px;
			font-size: 12px;
			font-weight: 400;
			color: #ffffff;
			line-height: 24px;
		}
		.content-right {
			width: 500px;
			height: 882px;
			background: #ffffff;
			border-radius: 4px 0px 0px 4px;
			&-top {
				position: relative;
				padding: 30px 40px 12px;
				.top-author {
					display: flex;
					align-items: center;
					cursor: pointer;
					width: calc(100% - 100px);
				}
				.author-img {
					width: 44px;
					height: 44px;
					border-radius: 50%;
					flex-shrink: 0;
				}
				.author {
					font-size: 16px;
					color: #404040;
					line-height: 32px;
					margin-left: 8px;
				}
				// 右侧操作按钮
				.person-btns {
					position: absolute;
					top: 36px;
					right: 40px;
					display: flex;
					.interest-btn {
						width: 64px;
						height: 32px;
						background: var(--brand-6, #0076e8);
						border-radius: 21px;
						font-size: 16px;
						color: #ffffff;
						line-height: 32px;
						text-align: center;
						cursor: pointer;
					}
					.interest-active {
						width: 96px;
						padding: 4px 0;
						border-radius: 21px;
						border: 1px solid #d9d9d9;
						font-size: 16px;
						color: #8c8c8c;
						line-height: 24px;
						text-align: center;
						cursor: pointer;
					}
				}
			}
			&-middle {
				height: calc(100% - 220px);
				overflow: auto;
				padding: 0px 40px;
				.middle-content {
					border-bottom: 1px solid #f0f0f0;
					padding-bottom: 12px;
					.title {
						font-size: 20px;
						font-weight: bold;
						color: #262626;
						line-height: 32px;
						margin: 0;
						margin-top: 18px;
					}
					.content {
						overflow: auto;
						font-size: 16px;
						color: #404040;
						line-height: 32px;
						padding: 20px 0;
					}
					.time {
						font-size: 12px;
						color: #bfbfbf;
						line-height: 14px;
					}
				}
				.shop-box {
					height: 136px;
					background: #f5f6fa;
					border-radius: 8px;
					padding: 12px 8px;
					.title {
						font-size: 14px;
						font-weight: 500;
						color: #262626;
						line-height: 24px;
					}
					.shop-list {
						margin-top: 8px;
						overflow: auto;
						display: flex;
						flex-wrap: nowrap;
					}
					.list-item {
						width: 278px;
						height: 80px;
						background: #ffffff;
						border-radius: 12px;
						padding: 8px 7px;
						flex-shrink: 0;
						margin-right: 8px;
						display: flex;
					}
					.item-img {
						width: 64px;
						height: 64px;
						border-radius: 8px;
						flex-shrink: 0;
					}
					.item-right {
						margin-left: 7px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						position: relative;
						width: 100%;
					}
					.item-title {
						font-size: 14px;
						font-weight: 500;
						color: #262626;
						line-height: 18px;
					}
					.item-price {
						font-size: 14px;
						font-weight: 500;
						color: #f95f55;
						line-height: 24px;
					}
					.item-btn {
						position: absolute;
						right: 2px;
						bottom: 0;
						display: inline-block;
						width: 60px;
						height: 24px;
						background: #ffffff;
						border-radius: 22px 22px 22px 22px;
						border: 1px solid #3274e0;
						font-size: 12px;
						font-family: PingFang SC-Medium, PingFang SC;
						font-weight: 500;
						color: #3274e0;
						line-height: 24px;
						text-align: center;
						cursor: pointer;
					}
				}
				.commits-box {
					padding-top: 18px;
				}
				.total-commits {
					font-size: 14px;
					color: #404040;
					line-height: 16px;
				}
				.commit-list {
					margin-top: 4px;
				}
				.commit-item {
					margin-top: 16px;
					display: flex;
					min-height: 86px;
					.commit-img {
						width: 32px;
						height: 32px;
						border-radius: 50%;
						flex-shrink: 0;
						margin-right: 16px;
					}
					.item-right {
						width: 100%;
						// display: flex;
						border-bottom: 1px solid #f0f0f0;
					}
					.item-author {
						font-size: 14px;
						color: #8c8c8c;
						line-height: 16px;
						display: block;
					}
					.author-span {
						display: inline-block;
						width: 40px;
						height: 20px;
						background: #f0f0f0;
						border-radius: 21px;
						font-size: 12px;
						color: #8c8c8c;
						line-height: 20px;
						text-align: center;
						margin-left: 8px;
					}
					.item-commit {
						font-size: 14px;
						color: #404040;
						line-height: 16px;
						display: block;
						margin-top: 8px;
					}
					.item-time-icon {
						display: flex;
						justify-content: space-between;
					}
					.item-icons {
						display: flex;
						align-items: center;
					}
					.item-icon {
						font-size: 12px;
						color: #8c8c8c;
						line-height: 22px;
						margin-left: 20px;
						display: flex;
						align-items: center;
						.nums-icon {
							height: 20px;
							width: 20px;
							margin-right: 8px;
						}
					}
					.item-time {
						font-size: 12px;
						color: #bfbfbf;
						margin-top: 16px;
						line-height: 14px;
					}
				}
			}
			&-bottom {
				padding: 25px 40px 0;
				border-top: 1px solid #f0f0f0;
				.send-box {
					height: 32px;
					display: flex;
					.send-btn {
						width: 60px;
						height: 32px;
						line-height: 32px;
						background: var(--brand-6, #0076e8);
						border-radius: 3px 3px 3px 3px;
						font-size: 14px;
						color: #ffffff;
						text-align: center;
						margin-left: 4px;
						cursor: pointer;
					}
				}
				.nums-box {
					display: flex;
					justify-content: space-between;
					margin-bottom: 19px;
					&-left {
						display: flex;
					}
					.item-nums {
						margin-right: 26px;
						font-size: 12px;
						color: #404040;
						line-height: 22px;
						display: flex;
						align-items: center;
						.nums-icon {
							width: 30px;
							height: 30px;
							margin-right: 9px;
							cursor: pointer;
						}
					}
					.forward-icon {
						width: 24px;
						height: 24px;
					}
				}
			}
		}
	}
	::v-deep .el-input__inner {
		background: #f0f0f0;
		border: none;
	}
	::v-deep .el-carousel__button {
		width: 8px;
		height: 8px;
		background: rgba(255, 255, 255, 0.5);
		border-radius: 50%;
	}
	::v-deep .el-carousel__indicators .is-active {
		.el-carousel__button {
			background: #ffffff;
		}
	}
}
</style>
<style scoped>
.el-dropdown-menu {
	background-color: #262626 !important ;
	border-radius: 0px;
}
.el-dropdown-menu__item {
	color: #fff;
}

.el-dropdown-menu__item:not(:last-of-type) {
	border-bottom: 1px solid rgba(240, 240, 240, 0.2);
}
/* 消除小三角 */
::v-deep.el-popper[x-placement^='bottom'] .popper__arrow {
	border: none !important;
}
::v-deep.el-popper[x-placement^='bottom'] .popper__arrow::after {
	border: none !important;
}
::v-deep.el-popper[x-placement^='top'] .popper__arrow {
	border: none !important;
}
::v-deep.el-popper[x-placement^='top'] .popper__arrow::after {
	border: none !important;
}
</style>
