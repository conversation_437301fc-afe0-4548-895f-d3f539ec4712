<template>
	<div class="Body">
		<h3 class="form-title">最近直播</h3>
		<div class="lesson-recently">
			<div v-loading="loading_recently" class="inner">
				<div class="safeMain">
					<div
						v-for="(item, index) in liveList"
						:key="index"
						v-loading.fullscreen.lock="liveLoading"
						class="temp"
						@click="openLiveBox(item)"
					>
						<img class="insureImg" :src="testUrl + item.showPictures" />
						<div class="infoBox">
							<div class="name over2">
								{{ item.title }}
							</div>
							<div class="time">{{ item.liveTimeBegin + ' 至 ' + item.liveTimeEnd }}</div>
							<div class="desc over1">
								{{ item.description }}
							</div>
						</div>
						<span :class="['live-tag', checkInLiveSpan(item) ? 'tag-active' : 'tag-gray']">
							{{ checkInLiveSpan(item) ? '直播中' : '未开始' }}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import config from '@/config';

export default {
	data() {
		return {
			loading_recently: false, //最近直播加载动画
			liveList: [], //直播列表
			testUrl: `${config.alumniUrl}/ybzyfile/` //合作结构图片域名前缀
		};
	},
	mounted() {
		this.onlineLearningLive(); //最近直播
	},
	methods: {
		async onlineLearningLive() {
			const { results } = await this.$api.information_api.paging({
				tenantId: this._userinfo.tenantId || this.$tenantId,
				nodeCode: 'technicalAbilityLive',
				pageSize: 20
			});

			// let test = results?.records;
			// let test2 = results?.records;
			// console.log(test);
			// test = test.concat(test2).concat(test).concat(test);
			this.liveList = results?.records || [];
			// this.liveList = test;
		},

		//检查是否在时段
		checkInLiveSpan(data) {
			let sTime = data.liveTimeBegin;
			let eTime = data.liveTimeEnd;
			let curDate = new Date();
			if (sTime) {
				let sDate = new Date(sTime);
				if (sDate > curDate) {
					return false;
				}
			}
			if (eTime) {
				let eDate = new Date(eTime);
				if (eDate < curDate) {
					return false;
				}
			}
			return true;
		},

		async openLiveBox(data) {
			// 打开新页面
			window.open(data.livePath);

			// this.liveTitle = data.title;
			// this.liveLoading = true;
			// const resp = await this.$api.treasure_api.getLivePathAndCheck({
			// 	liveSource: data.liveSource,
			// 	liveCode: data.liveCode,
			// 	livePath: data.livePath
			// });
			// this.liveLoading = false;
			// if (resp.code !== 200) {
			// 	this.$message({ message: resp.msg, type: 'warning' });
			// 	return;
			// }
			// this.liveBoxShow = true;
			// this.$nextTick(() => {
			// 	this.loadLiveInfo(resp.results);
			// });
		}
	}
};
</script>

<style scoped lang="scss">
.Body {
	background: #f6f7f9;
	width: 100%;
	height: 100%;
	padding: 20px;
	.form-title {
		height: 28px;
		font-size: 20px;
		font-family: Source Han Sans SC-Bold, Source Han Sans SC;
		font-weight: bold;
		color: #404040;
		line-height: 28px;
		padding-left: 13px;
		position: relative;
		margin-top: 0;
		margin-bottom: 34px;
		&::after {
			display: inline-block;
			content: '';
			width: 6px;
			height: 20px;
			background: var(--brand-6, #0076e8);
			position: absolute;
			left: 0;
			top: 50%;
			margin-top: -10px;
		}
	}

	.lesson-recently {
		margin: 0 auto;
		width: 100%;
		background: #f6f7f9;
		box-sizing: border-box;
		.safeMain {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			.temp {
				margin: 15px;
				padding: 28px 10px 14px 24px;
				position: relative;
				width: 280px;
				// height: 104px;
				background: #ffffff;
				border-radius: 10px;
				overflow: hidden;
				cursor: pointer;
				display: flex;
				align-items: center;
				.insureImg {
					width: 54px;
					height: 54px;
					border-radius: 50%;
					margin-right: 16px;
				}
				.infoBox {
					box-sizing: border-box;
					width: 100%;
					overflow: hidden;
					.name {
						font-size: 14px;
						font-weight: 400;
						color: #222222;
						line-height: 18px;
						margin-bottom: 6px;
					}
					.time {
						font-size: 12px;
						color: #666;
						margin-bottom: 6px;
					}
					.desc {
						font-size: 12px;
						color: #8c8c8c;
						line-height: 18px;
					}
				}
				.live-tag {
					position: absolute;
					top: 0;
					left: 0;
					display: inline-block;
					width: 55px;
					height: 20px;
					font-size: 12px;
					color: #ffffff;
					border-radius: 9px 0px 8px 0px;
					text-align: center;
					line-height: 20px;
				}
				.tag-gray {
					background: #545454;
				}
				.tag-active {
					background: #ea9f12;
				}
			}
		}
	}
}
</style>
