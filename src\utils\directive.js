/**
 * 触发事件
 * @param {string} name 事件名
 */
function event(name) {
	var evt = document.createEvent('Event');
	evt.initEvent(name, true, true);
	return evt;
}
/**
 * 修改光标位置
 * @param {*} el
 * @param {*} position
 */
function setCursor(el, position) {
	var setSelectionRange = function () {
		el.setSelectionRange(position, position);
	};
	if (el === document.activeElement) {
		setSelectionRange();
		setTimeout(setSelectionRange, 1); // Android Fix
	}
}

/**
 * 格式化value
 * @param {*} fv 最初的value
 * @param {string} intLength 整数长度
 * @param {string} decimalLength 小数长度
 * @param {boolean}  negative  修饰符标识
 * @param {boolean}  short  修饰符标识
 * @param {boolean}  b  blur失去焦点时的格式化标识
 */
function formatVal(fv, intLength, decimalLength, negative, short, b) {
	// console.log(negative)
	let value = fv;
	let lose = '';
	if (fv.indexOf('-') > -1) {
		lose = '-';
	}
	if (fv.indexOf('+') > -1) {
		lose = '';
	}
	const v = fv.replace(/[^\d.]/g, '');
	const vArr = v.split('.');
	vArr.length = vArr.length > 2 ? 2 : vArr.length;
	if (intLength || intLength === '0') {
		vArr[0] = vArr[0].slice(0, intLength); // 裁剪 整数部分
	}
	if (b && !vArr[0]) {
		// 失去焦点后 整数部分为空， 默认给整数0
		vArr[0] = '0';
	}
	if (decimalLength && vArr[1]) {
		vArr[1] = vArr[1].slice(0, decimalLength); // 裁剪 小数部分
	} else if ((b || decimalLength === '0') && !vArr[1]) {
		vArr.length = 1;
	}
	if (!negative) {
		lose = '';
	} // 默认正数
	const strNum = vArr.join('.');
	if (b && !strNum) {
		value = '';
	} else {
		value = lose + strNum;
	}
	if (b && value) {
		value = parseFloat(value).toFixed(decimalLength);
		if (short && /\.\d/.test(value)) {
			value = parseFloat(value).toString();
		}
	}
	return value;
}
const directives = [
	{
		/*
      只能输入正整数
    */
		key: 'input-int', //数字限制指令
		value: {
			bind(el, binding, vNode) {
				// let input = vNode.elm;
				let input = vNode.elm.children[0];
				if (input.tagName !== 'INPUT') {
					input = input.querySelector('input');
				}
				if (!input) return;
				input.addEventListener('compositionstart', () => {
					vNode.inputLocking = true;
				});
				input.addEventListener('compositionend', () => {
					vNode.inputLocking = false;
					input.dispatchEvent(new Event('input'));
				});
				const isObject = val => {
					return Object.prototype.toString.call(val) === '[object Object]';
				};
				const handleNum = (rules, val) => {
					let num = Number(val).toString();
					if (num > rules?.max) {
						num = Number(rules.max).toString();
					}
					if (num < rules?.min) {
						num = Number(rules.min).toString();
					}
					// 正整数
					if (rules?.format === 'positiveInteger') {
						num = num == '0' ? '' : num;
					}
					// 数字长度
					if (rules?.digitalLength) {
						num = num.slice(0, rules.digitalLength);
					}
					return num;
				};
				input.addEventListener(
					'input',
					e => {
						e.preventDefault(); // 阻止掉默认的change事件
						if (vNode.inputLocking) {
							return;
						}
						let oldValue = input.value;
						let newValue = input.value.replace(/[^\d]/g, '');
						if (newValue) {
							switch (binding.value) {
								case 'zeroBefore':
									break; // 数字随意输，不做处理，如 000013
								case 'zeroCan':
									newValue = Number(newValue).toString(); // 去掉开头0 正整数 + 0
									break;
								default:
									newValue = newValue.replace(/^\b(0+)/gi, ''); // （默认）去掉开头0 正整数
							}
							if (isObject(binding.value)) {
								newValue = handleNum(binding.value, newValue);
							}
						}
						// 判断是否需要更新，避免进入死循环
						if (newValue !== oldValue) {
							input.value = newValue;
							input.dispatchEvent(new Event('input')); // 通知v-model更新 vue底层双向绑定实现的原理是基于监听input事件
							input.dispatchEvent(new Event('change')); // 手动触发change事件
						}
					},
					true // 在捕获阶段处理，目的是赶在change事件之前阻止change事件(非法输入在触发指令之前先触发了change，需要干掉)
				);
			}
		}
	},

	// 按钮权限
	// {
	//   key: 'btn-show',
	//   value: {
	//     inserted: (el, binding) => {
	//       let permission = store.app.authButtons;
	//       if (!permission[binding.value]) {
	//         el.parentNode.removeChild(el);
	//       }
	//     },
	//   },
	// },
	{
		/**
		 * input 限制数字长度
		 * @use
		 * 使用方式： el-input 或 input 添加指令
		 *   v-input-money:9_4.negative
		 *   说明：
		 *     :9_4 : 指令参数 9:整数的位数，取值范围[1-9]；4:小数的位数，取值范围[0-9]
		 *     .negative : 指令修饰符 允许输入负数，否则默认整数（含小数）
		 *     .canEmpty : 指令修饰符 可以为空 加上后为空字符串时不格式化为0
		 *     .short : 指令修饰符 小数位不补零
		 *   特殊使用形式：
		 *      1   v-input-money  整数长度默认9  小数长度默认4  只能是正数
		 *      2   v-input-money.negative  整数长度默认9  小数长度默认4  允许输入负数
		 *      3   v-input-money:5_2   整数长度5  小数长度2  只能是正数
		 *
		 */
		key: 'input-money',
		value: {
			bind: (el, binding) => {
				let inputEl = null;
				let intLength = '9'; // 整数长度默认值(亿级别)
				let decimalLength = '4'; // 小数长度默认值
				if (el.tagName === 'INPUT') {
					inputEl = el; // 支持el-input
				} else {
					inputEl = el.getElementsByTagName('input')[0]; // 支持原生input
				}
				if (inputEl) {
					const arg = (binding.arg || '').split('_');
					const modifiers = binding.modifiers;

					intLength = arg[0] || intLength;
					decimalLength = arg[1] || decimalLength;
					let flag = true;
					inputEl.addEventListener('compositionstart', () => {
						flag = false;
					});
					inputEl.addEventListener('compositionend', () => {
						flag = true;
						inputEl.dispatchEvent(event('input'));
					});
					inputEl.oninput = (e, b) => {
						if (flag) {
							var positionFromEnd = inputEl.value.length - inputEl.selectionEnd;
							const fv = e.target.value || ''; // 原始value
							if (fv === '' && modifiers.canEmpty) {
								return;
							}
							const lv = formatVal(
								fv,
								intLength,
								decimalLength,
								modifiers.negative,
								modifiers.short,
								b
							); // 最终 value
							if (lv !== fv) {
								// 防死循环
								e.target.value = lv;
								positionFromEnd = lv.length - positionFromEnd;
								setCursor(inputEl, positionFromEnd);
								inputEl.dispatchEvent(event('input'));
								// inputEl.dispatchEvent(event('change'))
							}
						}
					};
					inputEl.onblur = e => {
						inputEl.oninput(e, true);
						// inputEl.dispatchEvent(event('input'))
					};
				}
			}
		}
	}
];

export default directives;
