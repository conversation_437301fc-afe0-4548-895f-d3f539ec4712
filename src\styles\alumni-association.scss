.mini-title {
	@include flexBox(space-between);
	margin: 20px 0;
	font-size: 14px;
	font-weight: bold;
	border-bottom: 1px solid #e8eaf0;
	color: #ffffff;
	span {
		position: relative;
		width: 120px;
		padding: 8px 0 8px 20px;
		display: block;
		background: #0076e8;
		&::before,
		&::after {
			position: absolute;
			right: 0px;
			top: 0;
			display: inline-block;
			content: '';
			width: 16px;
			height: 100%;
			background: #479cee;
			transform: skew(30deg, 0);
		}
		&::after {
			right: -16px;
			background: #b2d6f8;
		}
	}
	p {
		font-size: 14px;
		margin-right: 10px;
		color: #0076e8;
		cursor: pointer;
	}
}
