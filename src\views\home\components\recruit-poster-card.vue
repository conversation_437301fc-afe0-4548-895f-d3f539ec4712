<!--
 @desc:名企招聘海报卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div ref="card" class="card" @click="clickCard">
		<el-image class="img" :src="cardData.coverImg" lazy fit="contain"></el-image>
		<div class="info">
			<div>
				<span
					v-for="item in cardData.bottomList"
					:key="item.id"
					@click.stop="toOtherWindow(item.src)"
				>
					{{ item.name }}
				</span>
			</div>
		</div>
	</div>
</template>

<script>
import config from '@/config';
export default {
	props: {
		index: {
			type: Number,
			required: true,
			default: 0
		},
		cardData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},
	mounted() {
		// this.setBackground();
	},
	methods: {
		toOtherWindow(url) {
			window.open(`${config.alumniUrl}${url}`);
			// console.log(config.alumniUrl);
			// alert(url);
		},
		setBackground() {
			// const BACK_LIST = [
			// 	'linear-gradient(0deg, #0076E8 0%, #0076E8 0%, rgba(0,118,232,0.2) 100%)',
			// 	'linear-gradient(0deg, #0076E8 0%, #A4867C 0%, rgba(164,134,124,0.2) 100%)',
			// 	'linear-gradient(0deg, #0076E8 0%, #5B1B00 0%, rgba(91,27,0,0.21) 99%)',
			// 	'linear-gradient(0deg, #3A5DAD 0%, rgba(58,93,173,0.2) 100%)'
			// ];
			// const TAG_COLOR_LIST = ['#0076E8', '#333333', '#F87205', '#3A5DAD'];
			// let el = this.$refs.card;
			// let infoEl = this.$refs.info;
			// el.style.background = BACK_LIST[this.index];
			// infoEl.style.color = TAG_COLOR_LIST[this.index];
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit-poster', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	position: relative;
	width: 300px;
	height: 430px;
	// background: linear-gradient(0deg, #0076e8 0%, #0076e8 0%, rgba(0, 118, 232, 0.2) 100%);
	// opacity: 0.5;
	border-radius: 5px;
	cursor: pointer;
	// font-family: Microsoft YaHei;

	// padding: 22px 20px 0 22px
	.img {
		position: absolute;
		// z-index: -1;
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
	.tags {
		position: relative;
		top: 50px;
		width: 130px;
		height: 40px;
		line-height: 40px;
		font-size: 18px;
		text-align: center;
		font-weight: bold;
		color: var(--brand-6, #0076e8);
		background: #ffffff;
		z-index: 99;
		&::after {
			position: absolute;
			right: -20px;
			top: 0;
			display: inline-block;
			content: '';
			width: 0;
			height: 0;
			border-top: 0px solid transparent;
			border-right: 0px solid transparent;
			border-left: 20px solid #fff;
			border-bottom: 40px solid transparent;
			// width: 40px;
			// height: 40px;
			// background: gold;
			// clip-path: polygon(0 0, 0 100%, 70% 100%);
		}
	}
	.info {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		position: absolute;
		bottom: 44px;
		font-size: 14px;
		color: #ffffff;
		div {
			span {
				// margin: 0 12px;
				// &:nth-child(2)::before {
				// 	content: '|';
				// }
				// &:nth-child(2)::after {
				// 	content: '|';
				// }
				&:nth-child(2) {
					margin: 0 12px;
					padding: 0 12px;
					border-left: 1px solid #ffffff;
					border-right: 1px solid #ffffff;
				}
			}
		}
	}
}
</style>
