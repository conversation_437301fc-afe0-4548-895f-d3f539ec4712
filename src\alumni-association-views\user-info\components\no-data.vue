<!--
 @desc:个人中心 暂无数据
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="empty">
		<div>
			<img class="img" :src="imgUrl ? imgUrl : require(`@/assets/shop-images/empty.png`)" alt="" />
			<p>{{ tips.title }}</p>
			<p>{{ tips.detail }}</p>
			<p @click="noDataFn">{{ tips.clickText }}</p>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		imgUrl: {
			type: String,
			default: ''
		},
		tips: {
			type: Object,
			default: () => {
				return {
					title: '暂无信息',
					detail: '你还没有创建相关信息，或者',
					clickText: '立即创建'
				};
			}
		}
	},
	methods: {
		noDataFn() {
			this.$emit('noDataFn');
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.empty {
	width: 100%;
	height: 100%;
	@include flexBox();
	// border: 1px solid red;
	.img {
		display: block;
		margin: 0 auto;
		width: 127px;
		height: 122px;
	}
	p {
		text-align: center;
		&:nth-of-type(1) {
			font-size: 18px;
			font-weight: bold;
			color: #0076e8;
			line-height: 46px;
		}
		&:nth-of-type(2) {
			font-size: 14px;
			color: #b0bbd4;
			line-height: 24px;
		}
		&:nth-of-type(3) {
			font-size: 14px;
			color: #0076e8;
			line-height: 24px;
			cursor: pointer;
		}
	}
}
</style>
