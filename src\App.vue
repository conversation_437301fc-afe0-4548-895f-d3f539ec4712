<template>
	<div id="app">
		<keep-alive :include="['Personal']">
			<router-view />
		</keep-alive>
	</div>
</template>

<script>
import { handleSetThemeVar, handleColorLoad } from '@/utils/color.js';
import { mapMutations } from 'vuex';

const defaultColor = '#1890ff';
const themeColor = '#0076e8';
export default {
	name: 'App',
	watch: {
		$route(to, from) {
			const tenantId = localStorage.getItem('tenantId');
			const logoBj = localStorage.getItem('logoBj');
			if (!tenantId || !logoBj) {
				this.getTenantId();
			}
		}
	},
	async created() {
		// this.getTenantId();
		this.handlerReShow();
		this.getSite();
	},
	// beforeDestroy() {
	// 	this.$loginOut();
	// },
	destroyed() {
		// sessionStorage.removeItem('islog');
		// this.$loginOut();
	},
	methods: {
		...mapMutations('app', ['SET_TENANTID']),
		handlerReShow() {
			localStorage.setItem('admin-theme', themeColor);
			const reTheme = localStorage.getItem('admin-theme');
			// 获取到主题配置时,设置回显
			if (reTheme) {
				// 全局颜色变量同步
				handleSetThemeVar(reTheme);
				// 全局组件换肤色彩同步
				handleColorLoad(reTheme, defaultColor);
				// 应用对应的色系
				document.documentElement.setAttribute('theme-color', reTheme);
			} else {
				// 使用默认CSS全局色彩变量
				handleSetThemeVar(defaultColor);
			}
		},
		// 站点
		getSite() {
			// this.$api.shop_api.getAreaJson().then(res => {
			// 	if (res.code != 500) {
			// 		this.siteList = res;
			// 		this.$addStorageEvent(2, 'siteList', JSON.stringify(this.siteList));
			// 		localStorage.setItem('siteList', JSON.stringify(this.siteList));
			// 	}
			// });
		},
		// 获取用户配置信息
		getTenantId() {
			this.$api.getTenantId().then(res => {
				if (res.rCode == 0) {
					this.SET_TENANTID(res.results || {});
				}
			});
		}
	}
};
</script>
<style>
::-webkit-scrollbar-track-piece {
	background: #d3dce6;
}

::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-thumb {
	background: #99a9bf;
	border-radius: 20px;
}
</style>
<style lang="scss" scoped>
#app {
	//height: 100vh;
	//overflow-y: scroll;
}
</style>
