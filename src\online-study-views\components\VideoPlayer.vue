<template>
	<div>
		<video ref="videoPlayer" style="height: 100%; width: 100%" class="video-js"></video>
	</div>
</template>
<script>
import videojs from 'video.js';
//不要忘记包括视频.js CSS，位于 .<code>video.js/dist/video-js.css</code>

export default {
	name: 'VideoPlayer',
	props: {
		options: {
			type: Object,
			default() {
				return {};
			}
		}
	},
	data() {
		return {
			player: null
		};
	},
	mounted() {
		this.player = videojs(this.$refs.videoPlayer, this.options, function onPlayerReady() {
			console.log('onPlayerReady', this);
		});
	},
	beforeDestroy() {
		if (this.player) {
			this.player.dispose();
		}
	}
};
</script>
