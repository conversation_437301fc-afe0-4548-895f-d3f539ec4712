/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.0 (2019-10-17)
 */
!(function (l) {
	'use strict';
	function e() {}
	function m(e) {
		return function () {
			return e;
		};
	}
	function n() {
		return s;
	}
	var r,
		t = function (e) {
			function n() {
				return r;
			}
			var r = e;
			return {
				get: n,
				set: function (e) {
					r = e;
				},
				clone: function () {
					return t(n());
				}
			};
		},
		o = tinymce.util.Tools.resolve('tinymce.PluginManager'),
		i = function (e) {
			return {
				isFullscreen: function () {
					return null !== e.get();
				}
			};
		},
		c = m(!1),
		u = m(!0),
		s =
			((r = {
				fold: function (e, n) {
					return e();
				},
				is: c,
				isSome: c,
				isNone: u,
				getOr: d,
				getOrThunk: a,
				getOrDie: function (e) {
					throw new Error(e || 'error: getOrDie called on none.');
				},
				getOrNull: m(null),
				getOrUndefined: m(undefined),
				or: d,
				orThunk: a,
				map: n,
				each: e,
				bind: n,
				exists: c,
				forall: u,
				filter: n,
				equals: f,
				equals_: f,
				toArray: function () {
					return [];
				},
				toString: m('none()')
			}),
			Object.freeze && Object.freeze(r),
			r);
	function f(e) {
		return e.isNone();
	}
	function a(e) {
		return e();
	}
	function d(e) {
		return e;
	}
	function h(n) {
		return function (e) {
			return (
				(function (e) {
					if (null === e) return 'null';
					var n = typeof e;
					return 'object' == n &&
						(Array.prototype.isPrototypeOf(e) || (e.constructor && 'Array' === e.constructor.name))
						? 'array'
						: 'object' == n &&
						  (String.prototype.isPrototypeOf(e) ||
								(e.constructor && 'String' === e.constructor.name))
						? 'string'
						: n;
				})(e) === n
			);
		};
	}
	function v(e, n) {
		for (var r = e.length, t = new Array(r), o = 0; o < r; o++) {
			var i = e[o];
			t[o] = n(i, o);
		}
		return t;
	}
	function g(e, n) {
		for (var r = 0, t = e.length; r < t; r++) {
			n(e[r], r);
		}
	}
	function p(e, n) {
		for (var r = [], t = 0, o = e.length; t < o; t++) {
			var i = e[t];
			n(i, t) && r.push(i);
		}
		return r;
	}
	function w(e, n) {
		return (function (e) {
			for (var n = [], r = 0, t = e.length; r < t; ++r) {
				if (!Y(e[r])) throw new Error('Arr.flatten item ' + r + ' was not an array, input: ' + e);
				Q.apply(n, e[r]);
			}
			return n;
		})(v(e, n));
	}
	function y(e, n) {
		return -1 !== e.indexOf(n);
	}
	function S(e) {
		return e.style !== undefined && $(e.style.getPropertyValue);
	}
	function E(e, n, r) {
		!(function (e, n, r) {
			if (!(X(r) || G(r) || K(r)))
				throw (
					(l.console.error('Invalid call to Attr.set. Key ', n, ':: Value ', r, ':: Element ', e),
					new Error('Attribute value was not simple'))
				);
			e.setAttribute(n, r + '');
		})(e.dom(), n, r);
	}
	function O(e, n) {
		var r = e.dom().getAttribute(n);
		return null === r ? undefined : r;
	}
	function N(e, n) {
		e.dom().removeAttribute(n);
	}
	function T(e, n) {
		var r = e.dom();
		!(function (e, n) {
			for (var r = Z(e), t = 0, o = r.length; t < o; t++) {
				var i = r[t];
				n(e[i], i);
			}
		})(n, function (e, n) {
			!(function (e, n, r) {
				if (!X(r))
					throw (
						(l.console.error(
							'Invalid call to CSS.set. Property ',
							n,
							':: Value ',
							r,
							':: Element ',
							e
						),
						new Error('CSS value must be a string: ' + r))
					);
				S(e) && e.style.setProperty(n, r);
			})(r, n, e);
		});
	}
	function x(e, n) {
		var r = e.dom(),
			t = l.window.getComputedStyle(r).getPropertyValue(n),
			o =
				'' !== t ||
				(function (e) {
					var n = ie(e) ? e.dom().parentNode : e.dom();
					return n !== undefined && null !== n && n.ownerDocument.body.contains(n);
				})(e)
					? t
					: ue(r, n);
		return null === o ? undefined : o;
	}
	function b(e, n) {
		var r = (function (e, n) {
			for (var r = 0; r < e.length; r++) {
				var t = e[r];
				if (t.test(n)) return t;
			}
			return undefined;
		})(e, n);
		if (!r) return { major: 0, minor: 0 };
		function t(e) {
			return Number(n.replace(r, '$' + e));
		}
		return se(t(1), t(2));
	}
	function C(e, n) {
		return function () {
			return n === e;
		};
	}
	function D(e, n) {
		return function () {
			return n === e;
		};
	}
	function A(e, n) {
		var r = String(n).toLowerCase();
		return (function (e, n) {
			for (var r = 0, t = e.length; r < t; r++) {
				var o = e[r];
				if (n(o, r)) return z.some(o);
			}
			return z.none();
		})(e, function (e) {
			return e.search(r);
		});
	}
	function _(n) {
		return function (e) {
			return y(e, n);
		};
	}
	function M() {
		return xe.get();
	}
	function k(e, n, r) {
		return 0 != (e.compareDocumentPosition(n) & r);
	}
	function F(e, n) {
		var r = e.dom();
		if (r.nodeType !== Ce) return !1;
		var t = r;
		if (t.matches !== undefined) return t.matches(n);
		if (t.msMatchesSelector !== undefined) return t.msMatchesSelector(n);
		if (t.webkitMatchesSelector !== undefined) return t.webkitMatchesSelector(n);
		if (t.mozMatchesSelector !== undefined) return t.mozMatchesSelector(n);
		throw new Error('Browser lacks native selectors');
	}
	function R(e, n) {
		var r = n === undefined ? l.document : n.dom();
		return (function (e) {
			return (e.nodeType !== Ce && e.nodeType !== De) || 0 === e.childElementCount;
		})(r)
			? []
			: v(r.querySelectorAll(e), ne.fromDom);
	}
	function L(n) {
		return (function (e) {
			return z.from(e.dom().parentNode).map(ne.fromDom);
		})(n)
			.map(Ae)
			.map(function (e) {
				return p(e, function (e) {
					return !(function (e, n) {
						return e.dom() === n.dom();
					})(n, e);
				});
			})
			.getOr([]);
	}
	function I(e, n, r, t) {
		return { x: m(e), y: m(n), width: m(r), height: m(t), right: m(e + r), bottom: m(n + t) };
	}
	function P(e) {
		var n = e === undefined ? l.window : e,
			r = n.visualViewport;
		if (r !== undefined) return I(r.pageLeft, r.pageTop, r.width, r.height);
		var t = ne.fromDom(n.document),
			o = n.document.documentElement,
			i = (function (e) {
				var n = e !== undefined ? e.dom() : l.document,
					r = n.body.scrollLeft || n.documentElement.scrollLeft,
					t = n.body.scrollTop || n.documentElement.scrollTop;
				return Me(r, t);
			})(t),
			u = o.clientWidth,
			c = o.clientHeight;
		return I(i.left(), i.top(), u, c);
	}
	function H(e, n, r) {
		return p(
			(function (e, n) {
				for (
					var r = $(n) ? n : c, t = e.dom(), o = [];
					null !== t.parentNode && t.parentNode !== undefined;

				) {
					var i = t.parentNode,
						u = ne.fromDom(i);
					if ((o.push(u), !0 === r(u))) break;
					t = i;
				}
				return o;
			})(e, r),
			n
		);
	}
	function U(e, n) {
		return (function (e, n) {
			return p(L(e), n);
		})(e, function (e) {
			return F(e, n);
		});
	}
	function W(r, t) {
		return function (n) {
			n.setActive(null !== t.get());
			function e(e) {
				return n.setActive(e.state);
			}
			return (
				r.on('FullscreenStateChanged', e),
				function () {
					return r.off('FullscreenStateChanged', e);
				}
			);
		};
	}
	var B,
		V,
		j,
		q = function (r) {
			function e() {
				return o;
			}
			function n(e) {
				return e(r);
			}
			var t = m(r),
				o = {
					fold: function (e, n) {
						return n(r);
					},
					is: function (e) {
						return r === e;
					},
					isSome: u,
					isNone: c,
					getOr: t,
					getOrThunk: t,
					getOrDie: t,
					getOrNull: t,
					getOrUndefined: t,
					or: e,
					orThunk: e,
					map: function (e) {
						return q(e(r));
					},
					each: function (e) {
						e(r);
					},
					bind: n,
					exists: n,
					forall: n,
					filter: function (e) {
						return e(r) ? o : s;
					},
					toArray: function () {
						return [r];
					},
					toString: function () {
						return 'some(' + r + ')';
					},
					equals: function (e) {
						return e.is(r);
					},
					equals_: function (e, n) {
						return e.fold(c, function (e) {
							return n(r, e);
						});
					}
				};
			return o;
		},
		z = {
			some: q,
			none: n,
			from: function (e) {
				return null === e || e === undefined ? s : q(e);
			}
		},
		X = h('string'),
		Y = h('array'),
		G = h('boolean'),
		$ = h('function'),
		K = h('number'),
		J = Array.prototype.slice,
		Q = Array.prototype.push,
		Z = ($(Array.from) && Array.from, Object.keys),
		ee = function (e) {
			if (null === e || e === undefined) throw new Error('Node cannot be null or undefined');
			return { dom: m(e) };
		},
		ne = {
			fromHtml: function (e, n) {
				var r = (n || l.document).createElement('div');
				if (((r.innerHTML = e), !r.hasChildNodes() || 1 < r.childNodes.length))
					throw (
						(l.console.error('HTML does not have a single root node', e),
						new Error('HTML must have a single root node'))
					);
				return ee(r.childNodes[0]);
			},
			fromTag: function (e, n) {
				var r = (n || l.document).createElement(e);
				return ee(r);
			},
			fromText: function (e, n) {
				var r = (n || l.document).createTextNode(e);
				return ee(r);
			},
			fromDom: ee,
			fromPoint: function (e, n, r) {
				var t = e.dom();
				return z.from(t.elementFromPoint(n, r)).map(ee);
			}
		},
		re =
			(l.Node.ATTRIBUTE_NODE, l.Node.CDATA_SECTION_NODE, l.Node.COMMENT_NODE, l.Node.DOCUMENT_NODE),
		te = (l.Node.DOCUMENT_TYPE_NODE, l.Node.DOCUMENT_FRAGMENT_NODE, l.Node.ELEMENT_NODE),
		oe = l.Node.TEXT_NODE,
		ie =
			(l.Node.PROCESSING_INSTRUCTION_NODE,
			l.Node.ENTITY_REFERENCE_NODE,
			l.Node.ENTITY_NODE,
			l.Node.NOTATION_NODE,
			'undefined' != typeof l.window ? l.window : Function('return this;')(),
			(B = oe),
			function (e) {
				return (
					(function (e) {
						return e.dom().nodeType;
					})(e) === B
				);
			}),
		ue = function (e, n) {
			return S(e) ? e.style.getPropertyValue(n) : '';
		},
		ce = function () {
			return se(0, 0);
		},
		se = function (e, n) {
			return { major: e, minor: n };
		},
		fe = {
			nu: se,
			detect: function (e, n) {
				var r = String(n).toLowerCase();
				return 0 === e.length ? ce() : b(e, r);
			},
			unknown: ce
		},
		ae = 'Firefox',
		de = function (e) {
			var n = e.current;
			return {
				current: n,
				version: e.version,
				isEdge: C('Edge', n),
				isChrome: C('Chrome', n),
				isIE: C('IE', n),
				isOpera: C('Opera', n),
				isFirefox: C(ae, n),
				isSafari: C('Safari', n)
			};
		},
		le = {
			unknown: function () {
				return de({ current: undefined, version: fe.unknown() });
			},
			nu: de,
			edge: m('Edge'),
			chrome: m('Chrome'),
			ie: m('IE'),
			opera: m('Opera'),
			firefox: m(ae),
			safari: m('Safari')
		},
		me = 'Windows',
		he = 'Android',
		ve = 'Solaris',
		ge = 'FreeBSD',
		pe = function (e) {
			var n = e.current;
			return {
				current: n,
				version: e.version,
				isWindows: D(me, n),
				isiOS: D('iOS', n),
				isAndroid: D(he, n),
				isOSX: D('OSX', n),
				isLinux: D('Linux', n),
				isSolaris: D(ve, n),
				isFreeBSD: D(ge, n)
			};
		},
		we = {
			unknown: function () {
				return pe({ current: undefined, version: fe.unknown() });
			},
			nu: pe,
			windows: m(me),
			ios: m('iOS'),
			android: m(he),
			linux: m('Linux'),
			osx: m('OSX'),
			solaris: m(ve),
			freebsd: m(ge)
		},
		ye = function (e, r) {
			return A(e, r).map(function (e) {
				var n = fe.detect(e.versionRegexes, r);
				return { current: e.name, version: n };
			});
		},
		Se = function (e, r) {
			return A(e, r).map(function (e) {
				var n = fe.detect(e.versionRegexes, r);
				return { current: e.name, version: n };
			});
		},
		Ee = /.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
		Oe = [
			{
				name: 'Edge',
				versionRegexes: [/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],
				search: function (e) {
					return y(e, 'edge/') && y(e, 'chrome') && y(e, 'safari') && y(e, 'applewebkit');
				}
			},
			{
				name: 'Chrome',
				versionRegexes: [/.*?chrome\/([0-9]+)\.([0-9]+).*/, Ee],
				search: function (e) {
					return y(e, 'chrome') && !y(e, 'chromeframe');
				}
			},
			{
				name: 'IE',
				versionRegexes: [/.*?msie\ ?([0-9]+)\.([0-9]+).*/, /.*?rv:([0-9]+)\.([0-9]+).*/],
				search: function (e) {
					return y(e, 'msie') || y(e, 'trident');
				}
			},
			{ name: 'Opera', versionRegexes: [Ee, /.*?opera\/([0-9]+)\.([0-9]+).*/], search: _('opera') },
			{
				name: 'Firefox',
				versionRegexes: [/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],
				search: _('firefox')
			},
			{
				name: 'Safari',
				versionRegexes: [Ee, /.*?cpu os ([0-9]+)_([0-9]+).*/],
				search: function (e) {
					return (y(e, 'safari') || y(e, 'mobile/')) && y(e, 'applewebkit');
				}
			}
		],
		Ne = [
			{
				name: 'Windows',
				search: _('win'),
				versionRegexes: [/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]
			},
			{
				name: 'iOS',
				search: function (e) {
					return y(e, 'iphone') || y(e, 'ipad');
				},
				versionRegexes: [
					/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
					/.*cpu os ([0-9]+)_([0-9]+).*/,
					/.*cpu iphone os ([0-9]+)_([0-9]+).*/
				]
			},
			{
				name: 'Android',
				search: _('android'),
				versionRegexes: [/.*?android\ ?([0-9]+)\.([0-9]+).*/]
			},
			{ name: 'OSX', search: _('os x'), versionRegexes: [/.*?os\ x\ ?([0-9]+)_([0-9]+).*/] },
			{ name: 'Linux', search: _('linux'), versionRegexes: [] },
			{ name: 'Solaris', search: _('sunos'), versionRegexes: [] },
			{ name: 'FreeBSD', search: _('freebsd'), versionRegexes: [] }
		],
		Te = { browsers: m(Oe), oses: m(Ne) },
		xe = t(
			(function (e, n) {
				var r = Te.browsers(),
					t = Te.oses(),
					o = ye(r, e).fold(le.unknown, le.nu),
					i = Se(t, e).fold(we.unknown, we.nu);
				return {
					browser: o,
					os: i,
					deviceType: (function (e, n, r, t) {
						var o = e.isiOS() && !0 === /ipad/i.test(r),
							i = e.isiOS() && !o,
							u = e.isiOS() || e.isAndroid(),
							c = u || t('(pointer:coarse)'),
							s = o || (!i && u && t('(min-device-width:768px)')),
							f = i || (u && !s),
							a = n.isSafari() && e.isiOS() && !1 === /safari/i.test(r),
							d = !f && !s && !a;
						return {
							isiPad: m(o),
							isiPhone: m(i),
							isTablet: m(s),
							isPhone: m(f),
							isTouch: m(c),
							isAndroid: e.isAndroid,
							isiOS: e.isiOS,
							isWebView: m(a),
							isDesktop: m(d)
						};
					})(i, o, e, n)
				};
			})(l.navigator.userAgent, function (e) {
				return l.window.matchMedia(e).matches;
			})
		),
		be = function (e, n) {
			return k(e, n, l.Node.DOCUMENT_POSITION_CONTAINED_BY);
		},
		Ce = te,
		De = re,
		Ae =
			(M().browser.isIE(),
			function (e) {
				return v(e.dom().childNodes, ne.fromDom);
			}),
		_e =
			((function () {
				for (var e = [], n = 0; n < arguments.length; n++) e[n] = arguments[n];
			})('element', 'offset'),
			function (r, t) {
				return {
					left: m(r),
					top: m(t),
					translate: function (e, n) {
						return _e(r + e, t + n);
					}
				};
			}),
		Me = _e,
		ke =
			(M().browser.isSafari(),
			function (e, n) {
				e.fire('FullscreenStateChanged', { state: n });
			}),
		Fe = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils'),
		Re = tinymce.util.Tools.resolve('tinymce.Env'),
		Le = tinymce.util.Tools.resolve('tinymce.util.Delay'),
		Ie = 'data-ephox-mobile-fullscreen-style',
		Pe = 'position:absolute!important;',
		He =
			'top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;',
		Ue = Re.os.isAndroid(),
		We = function (e, n) {
			function r(t) {
				return function (e) {
					var n = O(e, 'style'),
						r = n === undefined ? 'no-styles' : n.trim();
					r !== t && (E(e, Ie, r), E(e, 'style', t));
				};
			}
			var t = (function (e, n, r) {
					return H(
						e,
						function (e) {
							return F(e, n);
						},
						r
					);
				})(e, '*'),
				o = w(t, function (e) {
					return U(e, '*:not(.tox-silver-sink)');
				}),
				i = (function (e) {
					var n = x(e, 'background-color');
					return n !== undefined && '' !== n
						? 'background-color:' + n + '!important'
						: 'background-color:rgb(255,255,255)!important;';
				})(n);
			g(o, r('display:none!important;')),
				g(t, r(Pe + He + i)),
				r((!0 === Ue ? '' : Pe) + He + i)(e);
		},
		Be = function () {
			var e = (function (e) {
				return R(e);
			})('[' + Ie + ']');
			g(e, function (e) {
				var n = O(e, Ie);
				'no-styles' !== n ? E(e, 'style', n) : N(e, 'style'), N(e, Ie);
			});
		},
		Ve = Fe.DOM,
		je = l.window.visualViewport,
		qe =
			Re.browser.isSafari() && je !== undefined
				? ((V = (function () {
						var n = t(z.none());
						return {
							clear: function () {
								n.set(z.none());
							},
							set: function (e) {
								n.set(z.some(e));
							},
							isSet: function () {
								return n.get().isSome();
							},
							on: function (e) {
								n.get().each(e);
							}
						};
				  })()),
				  (j = Le.throttle(function () {
						(l.document.body.scrollTop = 0),
							(l.document.documentElement.scrollTop = 0),
							l.window.requestAnimationFrame(function () {
								V.on(function (e) {
									return T(e, {
										top: je.offsetTop + 'px',
										left: je.offsetLeft + 'px',
										height: je.height + 'px',
										width: je.width + 'px'
									});
								});
							});
				  }, 50)),
				  {
						bind: function (e) {
							V.set(e), j(), je.addEventListener('resize', j), je.addEventListener('scroll', j);
						},
						unbind: function () {
							V.on(function () {
								je.removeEventListener('scroll', j), je.removeEventListener('resize', j);
							}),
								V.clear();
						}
				  })
				: { bind: e, unbind: e, update: e },
		ze = function (e, n) {
			var r,
				t,
				o,
				i = l.document.body,
				u = l.document.documentElement;
			t = e.getContainer();
			var c = ne.fromDom(t),
				s = n.get(),
				f = ne.fromDom(e.getBody()),
				a = Re.deviceType.isTouch();
			if (((r = t.style), (o = e.getContentAreaContainer().firstChild.style), s))
				(o.width = s.iframeWidth),
					(o.height = s.iframeHeight),
					(r.width = s.containerWidth),
					(r.height = s.containerHeight),
					(r.top = s.containerTop),
					(r.left = s.containerLeft),
					a && Be(),
					Ve.removeClass(i, 'tox-fullscreen'),
					Ve.removeClass(u, 'tox-fullscreen'),
					Ve.removeClass(t, 'tox-fullscreen'),
					(function (e) {
						l.window.scrollTo(e.x, e.y);
					})(s.scrollPos),
					n.set(null),
					ke(e, !1),
					qe.unbind(),
					e.off('remove', qe.unbind);
			else {
				var d = {
					scrollPos: (function () {
						var e = P(l.window);
						return { x: e.x(), y: e.y() };
					})(),
					containerWidth: r.width,
					containerHeight: r.height,
					containerTop: r.top,
					containerLeft: r.left,
					iframeWidth: o.width,
					iframeHeight: o.height
				};
				a && We(c, f),
					(o.width = o.height = '100%'),
					(r.width = r.height = ''),
					Ve.addClass(i, 'tox-fullscreen'),
					Ve.addClass(u, 'tox-fullscreen'),
					Ve.addClass(t, 'tox-fullscreen'),
					qe.bind(c),
					e.on('remove', qe.unbind),
					n.set(d),
					ke(e, !0);
			}
		},
		Xe = function (e, n) {
			e.addCommand('mceFullScreen', function () {
				ze(e, n);
			});
		},
		Ye = function (e, n) {
			e.ui.registry.addToggleMenuItem('fullscreen', {
				text: 'Fullscreen',
				shortcut: 'Meta+Shift+F',
				onAction: function () {
					return e.execCommand('mceFullScreen');
				},
				onSetup: W(e, n)
			}),
				e.ui.registry.addToggleButton('fullscreen', {
					tooltip: 'Fullscreen',
					icon: 'fullscreen',
					onAction: function () {
						return e.execCommand('mceFullScreen');
					},
					onSetup: W(e, n)
				});
		};
	!(function Ge() {
		o.add('fullscreen', function (e) {
			var n = t(null);
			return (
				e.settings.inline ||
					(Xe(e, n), Ye(e, n), e.addShortcut('Meta+Shift+F', '', 'mceFullScreen')),
				i(n)
			);
		});
	})();
})(window);
