/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
	return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
	const valid_map = ['admin', 'editor'];
	return valid_map.indexOf(str.trim()) >= 0;
}
/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validPhone(phone) {
	return /(^(\d{3,4}-)?\d{7,8})$|(1[0-9]{10})/.test(phone);
}

/**
 * @param {string} password
 * @returns {Boolean}
 */
export function validPassWord(password) {
	return /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,16}$/.test(
		password
	);
}
