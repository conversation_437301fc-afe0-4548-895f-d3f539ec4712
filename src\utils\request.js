import axios from 'axios';
import { getCookie } from '@/utils/auth';
import { getToken } from '@/utils/auth';
import config from '@/config';
import { tenantId } from '@/utils/global_method';
import store from '@/store';
import { error, response } from '@/utils/response-handle';
const getSiteId = () => {
	let siteId = '';
	siteId = getCookie('siteId');
	return siteId || config.siteId;
};
const baseURL = origin.includes('localhost') || origin.includes('10.40') ? '/dev-api' : '';
const service = axios.create({
	baseURL: baseURL,
	timeout: 20000,
	withCredentials: true
});

// 请求对象
service.interceptors.request.use(
	config => {
		// 电商租户id
		if (getSiteId()) {
			config.headers['siteId'] = getSiteId();
		}
		if (getToken()) {
			config.headers['X-Token'] = getToken();
		}
		// 接口统一的头部
		let info = store.getters.userinfo;
		if (info) {
			config.headers['code'] = info.authCode;
			config.headers['ybzycode'] = info.authCode;
			config.headers['token'] = info.authCode;
			config.headers['user_id'] = info.id;
			// 设置token
			config.headers['userToken'] = info.authCode;
			if (config.needAuthorization && localStorage.getItem('lessonToken')) {
				config.headers['Authorization'] = 'Bearer ' + localStorage.getItem('lessonToken') || '';
				config.headers['token'] = localStorage.getItem('lessonToken');
				// + getCookie('token')
			}
			if (config.needLessonToken) {
				config.headers['token'] = localStorage.getItem('lessonToken');
			}
		}
		// 宜宾职院租户id
		config.headers['tenantId'] = tenantId();

		// 自定义的post请求头，用type区分。默认为form-data合适
		if (config.method === 'post') {
			if (config.type === 'JSON') {
				config.headers['Content-Type'] = 'application/json;charset=utf-8';
			} else {
				config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8;';
				// 数据格式不是formData的时候要转换
				if (config.data instanceof FormData === false) {
					// form-data格式的请求需要将参数转换为FormData
					let data = config.data;
					let formData = new FormData();
					if (data) {
						Object.keys(data).forEach(key => {
							if (data[key]) {
								formData.append(key, data[key]);
							}
						});
					}
					config.data = formData;
				}
			}
		}

		return config;
	},
	error => {
		console.log(error);
		return Promise.reject(error);
	}
);
// 响应对象
service.interceptors.response.use(response, error);

export default service;
