<template>
	<div>
		<el-dialog
			:title="baseInfo.SHOP_NAME"
			width="1200px"
			height="700px"
			:visible.sync="dialogFormVisible"
			:close-on-press-escape="false"
			:before-close="closeDialog"
		>
			<div class="dialog-content">
				<div class="dialog-content-left">
					<div class="dialog-conten-left-content">
						<div v-for="(item, index) of list" :key="index">
							<!-- 回复的 -->
							<div v-show="item.align == 'left'" class="dialog-conten-left-content-left">
								<div>
									<div class="dialog-conten-left-content-left-title">
										<span class="dialog-conten-left-content-left-title-name">
											{{ (item.sendUser && item.sendUser.username) || '-' }}
										</span>
										<span>{{ item.createTime }}</span>
									</div>
									<div class="msg">{{ item.content }}</div>
								</div>
							</div>
							<!-- 发送的 -->
							<div v-show="item.align == 'right'" class="dialog-conten-left-content-right">
								<el-image
									v-if="item.sendUser && item.sendUser.photoUrl"
									class="dialog-conten-left-content-right-logo"
									:src="item.sendUser.photoUrl"
									alt=""
								/>
								<img
									v-else
									src="@/assets/shop-images/default-avatar.png"
									class="dialog-conten-left-content-right-logo"
									alt=""
									srcset=""
								/>
								<div>
									<div class="dialog-conten-left-content-right-title">
										<span class="dialog-conten-left-content-right-title-name">
											{{ item.sendUser && item.sendUser.username }}
										</span>
										<span>{{ item.createTime }}</span>
									</div>
									<div class="msg">{{ item.content }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="message-input">
						<el-input v-model="input" type="textarea" placeholder="请输入内容"></el-input>
						<div class="dialog-conten-button">
							<a href="javascript:void(0)" @click.stop="addMessage">
								<div class="send-button">发送</div>
							</a>
						</div>
					</div>
				</div>
				<div class="dialog-content-right">
					<div class="right-top">
						<div>
							<headAvator :own-id="baseInfo.SELLER_ID" class="right-top-logo" />
						</div>
						<div class="">
							<div class="title">{{ baseInfo.SHOP_NAME }}</div>
							<div v-if="!baseInfo.isGoods" class="back" @click="goShop(baseInfo)">进入店铺</div>
						</div>
					</div>
					<div class="goods" v-if="!baseInfo.isGoods">
						<div class="goods-title">正在咨询的宝贝</div>
						<div class="goods-list">
							<div class="goods-list-item">
								<img :src="baseInfo.COVER_URL" alt="" class="img" />
								<div class="item-dec">
									<div class="item-title">{{ baseInfo.PRODUCT_NAME }}</div>
									<div class="price">{{ baseInfo.ORI_PRICE }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	name: 'ContactMessage',
	props: {
		dialogFormVisible: {
			type: Boolean,
			default: false
		},
		baseInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			input: '',
			list: [],
			userId: '',
			timer: null
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getList();
		}
	},
	beforeDestroy() {
		clearTimeout(this.timer);
	},
	methods: {
		addMessage() {
			// 如果空的或者空格就不发送
			if (!this.input.trim()) return;
			let data = {
				content: this.input,
				recipientId: this.baseInfo?.SELLER_ID
			};
			this.$api.enterprise_center.sendMsg(data).then(res => {
				this.input = '';
				// this.getList();
			});
		},
		goShop(t) {
			this.$router.push(`/shopHomePage?id=${t.ID}`);
		},
		getList() {
			let data = {
				recipientId: this.baseInfo?.SELLER_ID
			};
			this.$api.enterprise_center.chatRecords(data).then(res => {
				this.list = res?.results?.records || [];
			});
			this.timer = setTimeout(() => {
				this.getList();
			}, 1000);
		},
		closeDialog(item) {
			this.$parent.dialogMessageVisible = false;
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
	padding: 0px;
	border: 1px solid #d9d9d9;
	height: 679px;
}
::v-deep .message-input {
	width: 100%;
	height: 200px;
	background: #fcfcfd;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-top: 1px solid #d9d9d9;
}
.dialog-content {
	display: flex;
	justify-content: space-between;
	height: 581px;
	&-left {
		width: 902px;
		// width: 100%;
		.dialog-conten-left-content {
			padding: 26px 29px 0px;
			height: 476px;
			overflow-y: auto;
			width: 100%;
			background-color: #f0f2f5;
			.dialog-conten-left-content-left {
				display: flex;
				margin-bottom: 15px;
				align-items: center;
				&-logo {
					width: 48px;
					height: 48px;
					opacity: 1;
					border-radius: 50%;
					border: 1px solid #cfd8ec;
					margin-right: 16px;
				}
				&-title {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #9da5b7;
					line-height: 22px;
					&-name {
						margin-right: 10px;
					}
				}
				.msg {
					background: #ffffff;
					border-radius: 0px 8px 8px 8px;
					opacity: 1;
					padding: 12px 18px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #262626;
					word-break: break-word;
				}
			}
			.dialog-conten-left-content-right {
				margin-bottom: 15px;
				display: flex;
				align-items: center;
				flex-direction: row-reverse;
				&-logo {
					width: 48px;
					height: 48px;
					opacity: 1;
					border-radius: 50%;
					border: 1px solid #cfd8ec;
					margin-left: 16px;
				}
				&-title {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #9da5b7;
					line-height: 22px;
					&-name {
						margin-right: 10px;
					}
				}
				.msg {
					background: #ffffff;
					border-radius: 0px 8px 8px 8px;
					opacity: 1;
					padding: 12px 18px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #262626;
					word-break: break-word;
				}
			}
		}
		&-button {
			height: 200px;
			background: #fcfcfd;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid #d9d9d9;
		}
		.dialog-conten-button {
			display: flex;
			width: 100%;
			flex-direction: row-reverse;
			padding-right: 20px;
		}
		.send-button {
			margin-top: 5px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #ffffff;
			padding: 6px 20px;
		}
		::v-deep .el-textarea {
			.el-textarea__inner {
				height: 144px !important;
				// border-left: none;
				// outline: none !important;
				border: none !important;
				background: #fcfcfd;
			}
		}
		::v-deep .el-textarea__inner:focus {
			box-shadow: none !important;
		}
	}
	&-right {
		width: 298px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		padding: 27px 20px;
		.right-top {
			display: flex;
			align-items: center;
			.right-top-logo {
				width: 60px;
				height: 60px;
				opacity: 1;
				border: 1px solid #cfd8ec;
				border-radius: 50%;
				margin-right: 8px;
			}
			.title {
				font-size: 18px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #000000;
				line-height: 32px;
			}
			.back {
				background: #ffffff;
				border-radius: 19px 19px 19px 19px;
				opacity: 1;
				text-align: center;
				width: 78px;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 30px;
				border: 1px solid var(--brand-6, '#ca3f3b');
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
			}
		}
		.goods {
			margin-top: 70px;
			&-title {
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
				margin-bottom: 12px;
			}
			.goods-list-item {
				width: 250px;
				height: 88px;
				background: #f5f5f5;
				border-radius: 8px 8px 8px 8px;
				opacity: 1;
				padding: 12px 11px;
				display: flex;
				justify-content: space-between;
				.item-dec {
					padding-left: 7px;
				}
				.img {
					width: 64px;
					height: 64px;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
				}

				.item-title {
					width: 163px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					overflow: hidden; //超出的文本隐藏
					text-overflow: ellipsis; //溢出用省略号显示
					display: -webkit-box;
					-webkit-line-clamp: 2; // 超出多少行
					-webkit-box-orient: vertical;
				}
				.price {
					font-size: 11px;
					font-family: Rany-Medium, Rany;
					font-weight: 500;
					color: var(--brand-6, '#ca3f3b');
					line-height: 20px;
				}
			}
		}
	}
}
</style>
