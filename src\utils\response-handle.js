import { Message } from 'element-eoss';
import Qs from 'qs';

export const response = response => {
	const res = response.data;
	// 请求拦截，权限校验
	if (res.code === 401) {
		setTimeout(() => {
			let route = window._VUE.$route;
			let redirect = route.path + '?' + Qs.stringify(route.query);
			window._VUE.$router.replace(`/login?redirect=${redirect}`);
		}, 1000);
		Message({
			message: '未登录，请先登录！',
			type: 'error',
			duration: 1000
		});
	} else {
		return res;
	}
};
export const error = error => {
	// 4xx 5xx 6xx会进来
	let { message } = error;
	if (message == 'Network Error') {
		message = '网络不给力';
	} else if (message.includes('timeout')) {
		message = '系统接口请求超时';
	} else if (message.includes('Request failed with status code')) {
		// message = '系统接口' + message.substr(message.length - 3) + '异常';
		console.error('系统接口' + message.substr(message.length - 3) + '异常');
		if (error.response.data.code == 401) {
			Message({
				message: '正在登录子系统',
				type: 'warning',
				duration: 5 * 1000
			});
			return Promise.reject(error);
		}
	}
	Message({
		message: message,
		type: 'error',
		duration: 5 * 1000
	});
	return Promise.reject(error);
};
// {
// 	Message({
// 		message: error.message,
// 		type: 'error',
// 		duration: 5 * 1000
// 	});
// 	return Promise.reject(error);
// };
