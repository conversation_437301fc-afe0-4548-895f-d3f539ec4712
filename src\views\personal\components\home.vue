<template>
	<div class="main-page home-page">
		<div class="area area1">
			<div class="user-avator">
				<headAvator v-if="avatorShow" class="user-avator-img" :own-id="_userinfo.id" />
			</div>
			<div class="company">
				<div class="company-name">
					{{ _userinfo.nickname }}
					<div class="company-name-icon">{{ identityNames }}</div>
				</div>
				<div class="company-phone">手机号：{{ _userinfo.phone || '-' }}</div>
				<div class="company-phone">邮箱：{{ _userinfo.email || '-' }}</div>
				<div class="company-phone" style="margin-bottom: 0">账号：{{ accountInfo }}</div>
				<div class="company-button" style="margin-top: 10px" @click="toEdit">修改个人信息 >></div>
			</div>
		</div>
		<div class="area area2">
			<div class="left-area">
				<div class="inner">
					<div v-if="hasEnpCert">
						<p class="title">{{ epData.corpName }}</p>
						<p class="years">{{ handlerGetYear(epData.establishDate) }}年成立</p>
						<p class="address">公司地址：{{ epData.registeredAddress }}</p>
						<p>
							<el-button class="btn" @click="handleToAuth">查看认证信息</el-button>
						</p>
					</div>
					<div v-else>
						<p class="title">企业信息</p>
						<p class="desc">完成企业/商家认证，享受更多权限~</p>
						<p>
							<el-button type="primary" @click="handleToAuth">去认证</el-button>
						</p>
					</div>
				</div>
			</div>
			<!-- 公告 -->
			<div class="right-area">
				<p class="title">
					<span>系统公告</span>
					<span class="el-icon-more more" @click="handleToNoticeLink"></span>
				</p>
				<ul v-if="noticeData.length > 0">
					<li v-for="item in noticeData" :key="item.id" @click="handleToDetail(item)">
						<span>{{ item.title }}</span>
						<span>{{ parseTime(item.publishTime, '{m}/{d}') }}</span>
					</li>
				</ul>
				<div v-else class="empty">
					<el-empty :image-size="60" description="暂无更多内容"></el-empty>
				</div>
			</div>
		</div>
		<div v-if="_userinfo.isBusiness" class="area area3">
			<div class="item">
				<p class="title">
					交易量
					<i class="iconfont icon-help-circle"></i>
				</p>
				<p class="num"><el-number :value="businessData.number" count-up /></p>
				<p class="statistics">
					今日新增
					<span class="to-day">+{{ businessData.todayNumber }}</span>
				</p>
			</div>
			<div class="item">
				<p class="title">
					交易额（元）
					<i class="iconfont icon-help-circle"></i>
				</p>
				<p class="num">
					<el-number :precision="2" :value="businessData.orderMoney" count-up />
				</p>
				<p class="statistics">
					今日新增
					<span class="to-day">+{{ businessData.todayOrderMoney }}</span>
				</p>
			</div>
			<div class="item">
				<p class="title">
					商品数
					<i class="iconfont icon-help-circle"></i>
				</p>
				<p class="num"><el-number :value="businessData.spuNum" count-up /></p>
				<p class="statistics">
					今日新增
					<span class="to-day">+{{ businessData.todaySpuNum }}</span>
				</p>
			</div>
			<div class="item">
				<p class="title">
					浏览量
					<i class="iconfont icon-help-circle"></i>
				</p>
				<p class="num"><el-number :value="businessData.browsingNum" count-up /></p>
				<!-- <p class="statistics">
          今日新增
          <span class="to-day">+1</span>
        </p> -->
			</div>
		</div>
		<!-- 暂时屏蔽 -->
		<div v-if="false" class="area area4">
			<div class="title">
				<div class="title-line"></div>
				我的应用
			</div>
			<div class="list">
				<div
					v-for="(app, index) in applist"
					:key="index"
					:class="{
						item: true,
						noRole: !app.url
					}"
					@click="handleHref(app)"
				>
					<div class="item-left">
						<div class="info">
							<p class="name">{{ app.name }}</p>
							<p class="des">{{ app.remark ? app.remark : `提供${app.name}服务` }}</p>
						</div>
						<!-- 应用 logo -->
						<div class="img">
							<el-image class="el-image" :src="app.logo ? app.logo : app.icon" fit="cover" />
						</div>
					</div>
					<!-- 认证按钮 -->
					<div class="noRole-auth">
						<el-button type="primary" @click="handleShowAuth(app.enterpriseTypeList)">
							去认证
						</el-button>
					</div>
				</div>
				<!-- 缺省组件 -->
				<div v-if="applist.length == 0" class="empty">
					<el-empty description="暂无应用"></el-empty>
				</div>
			</div>
		</div>
		<el-dialog title="应用认证" :visible.sync="dialogVisible" width="800px">
			<div>
				<div class="auth-tips">请选择希望申请的认证类型：</div>
				<div class="auth">
					<el-radio-group v-model="authType">
						<div
							v-for="list in enterpriseTypeList"
							:key="list.id"
							:class="{ 'auth-list': true, action: list.code == authType }"
						>
							<div class="auth-radio">
								<el-radio :label="list.code">
									<div class="auth-box">
										<div class="auth-img">
											<el-image
												style="width: 100%; height: 100%"
												:src="getImgUrl(list.logo)"
												fit="cover"
											></el-image>
										</div>
										<div class="auth-info">
											<span class="auth-info-title">{{ list.title }}</span>
											<div class="auth-info-text">{{ list.remark }}</div>
										</div>
									</div>
								</el-radio>
							</div>
						</div>
					</el-radio-group>
					<el-empty
						v-if="enterpriseTypeList.length == 0"
						description="暂未开通其他认证方式"
					></el-empty>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleToAuthPage">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import defaultAvatar from '@/assets/shop-images/default-avatar.png';
import { baseUrl } from '@/config';
import { parseTime } from '@/utils';
import { mapGetters } from 'vuex';

export default {
	name: 'Home',
	data() {
		return {
			parseTime,
			defaultAvatar,
			hasEnpCert: null,
			avatorShow: true, //是否更新头像信息
			applist: [],
			// 企业信息
			epData: {
				corpName: '',
				establishDate: '',
				registeredAddress: ''
			},
			// 公告信息
			noticeData: [],
			// 商家信息
			businessData: {},
			dialogVisible: false,
			// 认证类型
			authType: null,
			enterpriseTypeList: []
		};
	},
	computed: {
		...mapGetters(['identity']),
		identityNames() {
			let arr = this.identity.map(item => {
				return item.name;
			});
			return arr.join('/');
		},
		accountInfo() {
			let account = '';
			if (this._userinfo.identityInfo[this._userinfo.identityInfo.length - 1].code == 'teacher') {
				account = '511025199309214666';
			} else if (
				this._userinfo.identityInfo[this._userinfo.identityInfo.length - 1].code == 'student'
			) {
				account = '511362200609264765';
			} else {
				account = '91511500MA7DG0NA78';
			}
			return account;
		}
	},
	watch: {
		'_userinfo.isBusiness': {
			immediate: true,
			deep: true,
			handler: function (newVal, oldVal) {
				if (newVal) {
					// 获取商家信息
					this.handleGetshopAddressManagement();
				}
			}
		},
		'_userinfo.hasEnpCert': {
			handler(newVal) {
				this.hasEnpCert = newVal;
			},
			deep: true, // 深度观察监听 设置为 true
			immediate: true // 第一次初始化渲染就可以监听到
		},
		'_userinfo.id': {
			handler(newVal) {
				this.handlerGetEnterpriseInfo();
			},
			deep: true, // 深度观察监听 设置为 true
			immediate: true // 第一次初始化渲染就可以监听到
		}
	},
	activated() {
		this.avatorShow = false;
		setTimeout(() => {
			this.avatorShow = true;
		}, 100);
	},
	mounted() {
		// 获取应用
		// this.handleGetExapp();
		// 企业信息获取
		this.handlerGetEnterpriseInfo();
		// 公告获取
		this.handleGetCmsInfo();
		console.log(this._userinfo, 8778);
	},
	methods: {
		/**去认证*/
		handleToAuth() {
			// this.$message.warning('开发中，敬请期待...');
			// this.$router.push('/enterpriseAuth');
			this.$emit('changePage', 'enterprise');
		},
		/**获取应用*/
		handleGetExapp() {
			this.$api.personal_api.allAppList().then(res => {
				this.applist = res.results;
			});
		},
		/**跳转app链接*/
		handleHref(app) {
			if (app.url.includes('redirectUri=/project-ybzy/ybzy/index.html')) {
				window.location.href = app.url + '&isTeacher=true';
			} else {
				window.open(app.url);
			}
		},
		/**获取企业详情*/
		handlerGetEnterpriseInfo() {
			this.$api.personal_api.getEnterpriseInfo().then(res => {
				this.epData = res.results;
				this.hasEnpCert = res.results.status == 1;
			});
		},
		/**时间格式化*/
		handlerGetYear(date) {
			return new Date(date).getFullYear();
		},
		/**跳转公告详情*/
		handleToDetail({ id, nodeCode }) {
			this.$emit('changePage', 'noticeList', {
				detail: true,
				id,
				nodeCode,
				tenantId: this.$tenantId
			});
		},
		/**获取公告列表*/
		handleGetCmsInfo() {
			this.$api.personal_api
				.findCmsInfo({
					nodeCode: 'businessCenterNotice',
					tenantId: this.$tenantId,
					pageNum: 1,
					pageSize: 10
				})
				.then(res => {
					this.noticeData = res.results.records;
				});
		},
		/**跳转公告列表*/
		handleToNoticeLink() {
			this.$emit('changePage', 'noticeList');
			// this.$router.push('/notice/list');
		},
		/**获取商家数据*/
		handleGetshopAddressManagement() {
			this.$api.personal_api
				.shopAddressManagement({
					siteId: 'rent128',
					identityType: 'scswl_open_id',
					identityId: this._userinfo.id
				})
				.then(res => {
					if (res.state) {
						this.businessData = res.result;
					}
				});
		},
		/**打开认证列表弹窗*/
		handleShowAuth(data) {
			if (this._userinfo.hasEnpCert) {
				this.dialogVisible = true;
				this.enterpriseTypeList = data;
			} else {
				this.$message.warning('请先完成企业认证！');
			}
		},
		/**跳转到认证页面*/
		handleToAuthPage() {
			const authType = this.authType;
			if (authType) {
				this.$router.push({
					path: '/auth-center/authInfo',
					query: {
						code: authType
					}
				});
			} else {
				this.$message.warning('您暂时不能进行该项认证');
			}
		},
		/**获取应用logo*/
		getImgUrl(id) {
			return `${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		/**跳转到基本信息编辑页面*/
		toEdit() {
			this.$emit('changePage', 'baseInfo');
		}
	}
};
</script>

<style lang="scss" scoped>
.home-page {
	width: 100%;
	margin: 0 auto;
	padding: 20px;
	background: #ffffff;
}
.area {
	border-radius: 0px 0px 0px 0px;
	margin-bottom: 12px;
}
.area1 {
	height: 200px;
	justify-content: space-around;
	padding: 34px 0 0 52px;
	// background: linear-gradient(to right, #ffffff, #e3f1fe);
	background: url('../../../assets/images/person/user_car_bg.png') no-repeat;
	background-size: 100% auto;
	.user-avator {
		width: 96px;
		height: 96px;
		float: left;
		border-radius: 50%;
		overflow: hidden;
		border: 1px solid #d9d9d9;
		&-img {
			width: 100%;
			height: 100%;
		}
		img,
		::v-deep .img {
			border: 5px solid #fff;
		}
	}
	.company {
		margin-left: 24px;
		float: left;
		color: #262626;
		font-size: 20px;
		font-weight: 500;
		&-name {
			display: flex;
			align-items: center;
			font-size: 24px;
			line-height: 24px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #495267;
			margin-bottom: 20px;
			&-icon {
				margin-left: 11px;
				background: #ffffff;
				border-radius: 2px;
				padding: 4px 5px;
				font-size: 12px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: var(--brand-6, #0076e8);
			}
		}
		&-phone {
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #495267;
			margin-bottom: 15px;
			line-height: 14px;
		}
		&-button {
			margin-top: 27px;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: var(--brand-6, #0076e8);
			line-height: 14px;
			cursor: pointer;
		}
	}
}
.area2 {
	display: flex;
	justify-content: space-around;
	height: 200px;
	.left-area {
		flex: 1;
		height: 100%;
		overflow: hidden;
		padding: 31px 31px 0 31px;
		position: relative;
		background: url('../../../assets/images/person/auth_icon.png');
		background-size: 100% 100%;
		.inner {
			p {
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				color: #8c8c8c;
				font-size: 14px;
				font-weight: 500;
				line-height: 22px;
				.btn {
					border-radius: 3px 3px 3px 3px;
					width: 124px;
					height: 34px;
					line-height: 34px;
					font-size: 14px;
					color: var(--brand-6, #0076e8);
					border: 1px solid var(--brand-6, #0076e8);
					padding-top: 0;
					padding-bottom: 0;
					text-align: center;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}
			.title {
				margin-bottom: 16px;
				font-size: 18px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #495267;
			}
			.years {
				margin-bottom: 10px;
			}
			.address {
				margin-bottom: 18px;
			}
			.desc {
				margin-bottom: 52px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #8390a3;
			}
		}
	}
	.right-area {
		margin-left: 20px;
		width: 360px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		padding: 14px 13px 13px 14px;
		height: 100%;
		overflow: hidden;
		.title {
			color: #262626;
			font-size: 16px;
			font-weight: 500;
			height: 24px;
			line-height: 24px;
			margin: 0;
			padding: 0;
			margin-bottom: 8px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.more {
				width: 25px;
				margin-left: 12px;
				font-size: 14px;
				color: #8c8c8c;
				text-align: left;
				cursor: pointer;
				&:hover {
					color: var(--brand-6, #0076e8);
				}
			}
		}
		ul {
			height: 155px;
			overflow-y: scroll;
			padding: 0;
			margin: 0;
			li {
				list-style: none;
				line-height: 33px;
				display: flex;
				justify-content: space-around;
				padding-left: 6px;
				position: relative;
				cursor: pointer;
				transition: all 0.2s;
				&:before {
					content: '';
					width: 3px;
					height: 3px;
					background: var(--brand-6, #0076e8);
					position: absolute;
					left: 0;
					top: 50%;
					margin-top: -1.5px;
					border-radius: 50%;
				}
				span {
					display: block;
				}
				span:nth-child(1) {
					flex: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-size: 14px;
					color: #404040;
				}
				span:nth-child(2) {
					width: 40px;
					margin-left: 12px;
					font-size: 14px;
					color: #8c8c8c;
				}
				&:hover {
					span {
						color: var(--brand-6, #0076e8);
					}
				}
			}
			&::-webkit-scrollbar {
				width: 4px;
			}
			&::-webkit-scrollbar-thumb {
				border-radius: 2px;
				background: var(--brand-6, #0076e8);
			}
		}
		.empty {
			::v-deep .el-empty {
				padding: 30px 0;
			}
		}
	}
}
.area3 {
	height: 179px;
	background: #ffffff;
	padding: 34px 50px;
	.item {
		width: 25%;
		float: left;
		p {
			text-align: center;
			margin-bottom: 16px;
			font-size: 16px;
			color: #8c8c8c;
		}
		p:nth-child(1) {
			color: #404040;
		}
		.num {
			font-size: 36px;
			color: #262626;
		}
		.to-day {
			color: #ff4936;
			font-size: 20px;
		}
		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			i {
				font-size: 16px;
				color: #8c8c8c;
				margin-left: 4px;
			}
		}
		.statistics {
			font-size: 14px;
			display: flex;
			justify-content: center;
			align-items: center;
			.to-day {
				font-size: 20px;
				margin-left: 6px;
			}
		}
	}
}
.area4 {
	padding: 20px;
	min-height: 200px;
	background: #f5f6fb;
	border-radius: 5px;
	.title {
		display: flex;
		align-items: center;
		font-size: 18px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #495267;
		margin-bottom: 16px;
		&-line {
			width: 4px;
			height: 15px;
			background: var(--brand-6, #0076e8);
			border-radius: 2px;
			margin-right: 9px;
		}
	}
	.list {
		height: auto;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		.item {
			cursor: pointer;
			width: 200px;
			height: 83px;
			background: #ffffff;
			border-radius: 5px;
			padding: 14px;
			margin: 0 20px 20px 0;
			transition: all 0.2s;
			position: relative;
			overflow: hidden;
			&:hover {
				background: #f5fafe;
				box-shadow: 0px 3px 16px 0px rgba(66, 89, 158, 0.15);
				border-radius: 6px 6px 6px 6px;
			}
			&-left {
				display: flex;
				align-items: center;
				.info {
					flex: 1;
					overflow: hidden;
					.name {
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						color: #262626;
						font-size: 14px;
						margin-bottom: 12px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.des {
						font-size: 12px;
						color: #9da5b7;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}
				.img {
					display: block;
					flex-shrink: 0;
					width: 56px;
					height: 56px;
					border-radius: 5px;
					img {
						width: 100%;
						height: 100%;
					}
				}
			}
			.noRole-auth {
				display: none;
			}
			&.noRole {
				.info,
				.img {
					filter: grayscale(100%);
				}
				background: #f1f2f2;
				.noRole-auth {
					position: absolute;
					width: 100%;
					height: 100%;
					top: 0;
					left: 0;
					background: rgba(54, 60, 67, 0.4);
					justify-content: center;
					align-items: center;
				}
				&:hover {
					.noRole-auth {
						display: flex;
					}
				}
			}
		}
	}
}
.auth {
	max-height: 400px;
	overflow-y: auto;
	&-tips {
		padding-bottom: 14px;
	}
	&-list {
		margin-bottom: 16px;
		display: flex;
		align-items: center;
		height: 120px;
		border-radius: 3px 3px 3px 3px;
		border: 1px solid #d9d9d9;
		padding-left: 12px;
		transition: all 0.2s;
		&.action {
			background: #f6fbff;
			border: 1px solid var(--brand-6, #0076e8);
		}
	}
	&-radio {
		text-align: center;
		::v-deep .el-radio {
			display: flex;
			align-items: center;
		}
		::v-deep .el-radio__label {
			padding-left: 12px;
		}
	}
	&-img {
		width: 80px;
		height: 80px;
		img {
			width: 80px;
			height: 80px;
		}
	}
	&-info {
		width: 612px;
		padding-left: 12px;
		text-align: left;
		font-weight: 400;
		&-title {
			padding-top: 4px;
			height: 22px;
			font-size: 14px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #404040;
			line-height: 22px;
		}
		&-text {
			padding-top: 8px;
			padding-right: 12px;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			-webkit-line-clamp: 2;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: pre-wrap;
		}
	}
	&-box {
		display: flex;
	}
}
.empty {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.el-image {
	width: 100%;
	height: 100%;
}
::v-deep .el-dialog__body {
	padding-top: 14px;
}
::v-deep .el-radio__input.is-checked .el-radio__inner::after {
	content: '';
	width: 7px;
	height: 4px;
	border: 1.5px solid white;
	border-top: transparent;
	border-right: transparent;
	text-align: center;
	display: block;
	position: absolute;
	top: 3px;
	left: 2.5px;
	vertical-align: middle;
	transform: rotate(-45deg);
	border-radius: 0px;
	background: none;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
	background: var(--brand-6, #0076e8);
}
</style>
