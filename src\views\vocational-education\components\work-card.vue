<!--
 @desc:职教视野（资讯）卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<img src="@imgs/home/<USER>" alt="" />
		<p>{{ cardData.name }}</p>
		<p>{{ cardData.detail }}</p>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					imgUrl: '',
					name: '故障报修',
					detail: '图书借阅查询 | 图书借阅预约 | 图书续借 | 期刊查询 | …'
				};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'information', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 285px;
	height: 200px;

	border-radius: 10px;
	padding: 30px 40px;
	margin-bottom: 20px;
	text-align: center;
	font-family: Microsoft YaHei;
	background: #ffffff;
	border: 2px solid #e8eaf0;
	cursor: pointer;
	img {
		width: 60px;
		height: 60px;
		border-radius: 20px;
		border: 1px solid red;
	}
	p {
		&:nth-of-type(1) {
			font-size: 16px;
			font-weight: bold;
			color: #333333;
			margin: 20px 0 10px 0;
		}
		&:nth-of-type(2) {
			font-size: 12px;
			color: #999999;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			-webkit-line-clamp: 2;
		}
	}
}
</style>
