<template>
	<div v-loading="loading" class="refund_order">
		<div class="head">
			<div class="lf">筛选查询</div>
			<div class="rf">
				<el-button icon="el-icon-search" @click="handleSearch">搜索</el-button>
				<el-button type="primary" @click="exportUserOrderList">导出订单</el-button>
			</div>
		</div>
		<el-form :inline="true" :model="search" class="form">
			<el-form-item label="订单号">
				<el-input v-model="search.code" placeholder="请输入订单编号"></el-input>
			</el-form-item>
			<el-form-item label="店铺名称">
				<el-input v-model="search.name" placeholder="请输入店铺名称"></el-input>
			</el-form-item>
			<el-form-item label="下单时间">
				<el-date-picker
					v-model="search.time"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					value-format="yyyy-MM-dd"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<div class="tab-box">
			<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">待处理退款</div>
			<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">退款中</div>
			<div class="tab" :class="tabIndex == 2 ? 'act' : ''" @click="handleTab(2)">退款失败</div>
			<div class="tab" :class="tabIndex == 3 ? 'act' : ''" @click="handleTab(3)">已退款</div>
			<div class="tab" :class="tabIndex == 4 ? 'act' : ''" @click="handleTab(4)">退款驳回</div>
			<div class="line" :style="{ left: lineLeft(tabIndex) }"></div>
		</div>
		<div class="table">
			<orderTable
				ref="table"
				:table="orderList"
				:total="total"
				is-refund
				@sizeChange="handleSizeChange"
				@pageChange="handleCurrentChange"
			></orderTable>
		</div>
	</div>
</template>

<script>
import orderTable from './../components/order_table.vue';
import { order, personal } from '@/api';
export default {
	name: 'RefundOrder',
	components: {
		orderTable
	},
	data() {
		return {
			search: {},
			tabIndex: 0,
			total: 0,
			offset: 0,
			psize: 10,
			pageSize: 10,
			orderList: [],
			userId: ''
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getUserOrderList();
		}
	},
	methods: {
		// 导出订单
		exportUserOrderList() {
			this.loading = true;
			let type = this.tabIndex;
			let startTime = this.search.time && this.search.time.length == 2 ? this.search.time[0] : '';
			let endTime = this.search.time && this.search.time.length == 2 ? this.search.time[1] : '';
			if (this.tabIndex == 0) {
				type = 41;
			} else if (this.tabIndex == 1) {
				type = 411;
			} else if (this.tabIndex == 2) {
				type = 43;
			} else if (this.tabIndex == 3) {
				type = 42;
			} else type = 49;
			let data = {
				userId: this.userId,
				offset: this.offset,
				psize: this.psize,
				type,
				siteId: this.getSiteId(),
				keyword: this.search.code,
				shopName: this.search.name,
				startTime,
				endTime
			};
			order.exportUserOrderList(data).then(res => {
				if (!res) {
					this.loading = false;
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
					return;
				}
				this.loading = false;
				const content = res;
				const blob = new Blob([content], { type: 'application/vnd.ms-excel;charset=utf-8' });
				const fileName = '订单列表';
				if ('download' in document.createElement('a')) {
					// 非IE下载
					const elink = document.createElement('a');
					elink.download = fileName;
					elink.style.display = 'none';
					elink.href = URL.createObjectURL(blob);
					document.body.appendChild(elink);
					elink.click();
					URL.revokeObjectURL(elink.href); // 释放URL 对象
					document.body.removeChild(elink);
				} else {
					// IE10+下载
					navigator.msSaveBlob(blob, fileName);
				}
			});
		},
		//  重制页数
		restPage() {
			this.$refs['table'].page = 1;
		},
		// 搜索
		handleSearch() {
			this.restPage();
			this.offset = 0;
			this.psize = this.pageSize;
			this.getUserOrderList();
		},
		// 获取订单列表
		getUserOrderList() {
			this.loading = true;
			let type = this.tabIndex;
			let startTime =
				this.search.time && this.search.time.length == 2 ? this.search.time[0] + ' 00:00:00' : '';
			let endTime =
				this.search.time && this.search.time.length == 2 ? this.search.time[1] + ' 23:59:59' : '';
			if (this.tabIndex == 0) {
				type = 41;
			} else if (this.tabIndex == 1) {
				type = 411;
			} else if (this.tabIndex == 2) {
				type = 43;
			} else if (this.tabIndex == 3) {
				type = 42;
			} else type = 49;
			let data = {
				userId: this.userId,
				offset: this.offset,
				psize: this.psize,
				type,
				siteId: this.getSiteId(),
				keyword: this.search.code,
				shopName: this.search.name,
				startTime,
				endTime
			};
			order.getUserOrderList(data).then(async res => {
				if (res.state) {
					this.total = res.totalNum;
					this.loading = false;
					if (res.result) {
						for (let i = 0; i < res.result.length; i += 1) {
							if (res.result[i].OTHER_PARAMS && res.result[i].OTHER_PARAMS.contractData) {
								let item = {
									path: '/plat/scswl/platContract/openapi/sign/signStatus',
									instanceId: res.result[i].OTHER_PARAMS.contractData.instanceId,
									targeIds: [
										res.result[i].OTHER_PARAMS.contractData.buyer_targetId,
										res.result[i].OTHER_PARAMS.contractData.seller_targetId
									]
								};
								const results = await personal.doPostJson(item);
								if (results.rCode == 0) {
									for (let a = 0; a < results.results.length; a += 1) {
										if (
											res.result[i].OTHER_PARAMS.contractData.buyer_targetId ==
											results.results[a].targetId
										) {
											res.result[i].jr = results.results[a];
										}
									}
									res.result[i].payState = results.results;
								}
							}
						}
						this.orderList = res.result;
						console.log(this.orderList);
					} else {
						this.orderList = [];
					}
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
					this.loading = false;
				}
				this.$forceUpdate();
			});
		},
		// 页数
		handleCurrentChange(i) {
			this.offset = this.psize * i - this.psize;
			if (i == 1) {
				this.offset = 0;
			}
			// this.psize = this.offset + this.pageSize - 1;
			this.getUserOrderList();
		},
		// 条数
		handleSizeChange(i) {
			this.offset = 0;
			this.pageSize = i;
			this.psize = i;
			this.restPage();
			this.getUserOrderList();
		},
		// 点击导航栏
		handleTab(i) {
			if (i !== this.tabIndex) {
				this.option = 0;
				this.tabIndex = i;
				this.restPage();
				this.offset = 0;
				this.orderList = [];
				this.getUserOrderList();
			}
		},
		// 左侧高度
		lineLeft(i) {
			return 110 * i + 'px';
		}
	}
};
</script>

<style lang="scss" scoped>
.refund_order {
	width: 100%;
	.head {
		height: 48px;
		background: #f4f4f4;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		::v-deep .el-button--primary {
			background: var(--brand-6, '#ca3f3b');
			border-color: var(--brand-6, '#ca3f3b');
		}
		.lf {
			padding-left: 18px;
			font-size: 14px;
			font-weight: 500;
			color: #404040;
		}
		.rf {
			margin-left: auto;
			padding-right: 12px;
		}
	}
	.form {
		display: flex;
		padding-top: 19px;
		justify-content: center;
		box-sizing: border-box;
		height: 79px;
		border: 1px solid #d9d9d9;
		border-top: none;
		::v-deep .el-date-editor {
			width: 255px;
		}
	}
	.tab-box {
		margin-top: 20px;
		height: 40px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		position: relative;
		.tab {
			width: 70px;
			font-size: 14px;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: #404040;
			margin-right: 40px;
			cursor: pointer;
			text-align: center;
		}
		.act {
			color: var(--brand-6, '#ca3f3b');
		}
		.line {
			width: 70px;
			height: 1px;
			background: var(--brand-6, '#ca3f3b');
			position: absolute;
			left: 0;
			bottom: -1px;
			transition: all 0.3s;
		}
	}
	.table {
		margin-top: 20px;
	}
}
</style>
