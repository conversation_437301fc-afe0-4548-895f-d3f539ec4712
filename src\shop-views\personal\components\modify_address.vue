<template>
	<div class="modify_address">
		<el-dialog
			v-loading="loading"
			:visible.sync="visible"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			append-to-body
			lock-scroll
			:popper-append-to-body="false"
			title="修改收货地址"
			width="800px"
			@open="open"
			@close="close"
		>
			<div v-if="visible" class="box">
				<el-form ref="form" :model="form" :rules="rules" label-width="80px">
					<el-form-item label="收货人" prop="contact">
						<el-input v-model="form.contact" placeholder="请输入收货人"></el-input>
					</el-form-item>
					<el-form-item label="所在地区" prop="expectedAddr">
						<el-cascader
							v-model="form.expectedAddr"
							:options="addresOptions"
							placeholder="请选择期所在地区"
							:props="{ label: 'name', value: 'value' }"
						></el-cascader>
					</el-form-item>
					<el-form-item label="详细地址" prop="personAddress">
						<el-input v-model="form.personAddress" placeholder="请输入详细地址"></el-input>
					</el-form-item>
					<el-form-item label="手机号码" placeholder="请输入手机号码" prop="phoneNum">
						<el-input v-model="form.phoneNum" placeholder="请输入手机号码"></el-input>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button type="primary" @click="updateAddress">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	name: 'ModifyAddress',
	props: {
		item: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		let phone = (rule, value, callback) => {
			var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('收货人电话不能为空'));
			} else if (!reg.test(value)) {
				return callback(new Error('请输入正确的电话'));
			}
			callback();
		};
		return {
			form: {
				contact: '',
				expectedAddr: '',
				phoneNum: '',
				personAddress: ''
			},
			rules: {
				contact: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
				expectedAddr: [{ required: true, message: '请选择期所在地区', trigger: 'blur' }],
				phoneNum: [{ validator: phone, trigger: 'blur' }],
				personAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
			},
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			visible: false,
			loading: false
		};
	},
	created() {
		this.userId = this.isShopLogin();
	},
	methods: {
		close() {},
		// 打开弹窗注入参数
		open() {
			this.form.orderId = this.item.ORDER_ID;
			this.form.userId = this.userId;
			this.form.expectedAddr = [];
			this.form.expectedAddr[0] = this.item.addressInfo.provinceId;
			this.form.expectedAddr[1] = this.item.addressInfo.cityId;
			this.form.expectedAddr[2] = this.item.addressInfo.countyId;
			this.form.personAddress = this.item.addressInfo ? this.item.addressInfo.detailAddress : '';
			this.form.contact = this.item.addressInfo ? this.item.addressInfo.recipients : '';
			this.form.phoneNum = this.item.addressInfo ? this.item.addressInfo.mobilePhone : '';
			this.$forceUpdate();
		},
		updateAddress() {
			this.form.provinceId = this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					const { userId, orderId, contact, phoneNum, personAddress } = this.form;
					let data = {
						userId,
						orderId,
						contact,
						phoneNum,
						provinceId: this.form.expectedAddr[0],
						cityId: this.form.expectedAddr[1],
						countyId: this.form.expectedAddr[2],
						personAddress
					};
					this.$api.shop_api.updateAddress(data).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '设置成功',
								type: 'success'
							});
							this.loading = false;
							this.visible = false;
							this.$emit('success');
						} else {
							this.loading = false;
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				} else {
					return false;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep.el-dialog__body {
	padding: 0 0;
	.box {
		padding: 10px 20px;
		border-top: 1px solid #eeeeee;
		border-bottom: 1px solid #eeeeee;
	}
	.el-form-item {
		margin-bottom: 19px;
	}
	.el-form-item__label {
		font-size: 14px;
		font-weight: 400;
		color: #404040;
		line-height: 40px;
	}
	.el-input {
		width: 380px;
	}
}
</style>
