<template>
	<div class="main">
		<div class="pic-box">
			<article>
				<section>
					<h1>推荐服务</h1>
					<div v-loading="serveLoading" class="serve-card-box">
						<serve-card
							v-for="(item, index) in serveList"
							:key="index"
							:card-data="item"
							:class="{ mg: [4, 9, 14].includes(index) }"
							@clickCard="clickCard"
						/>
					</div>
				</section>
				<section>
					<h1>办事指南</h1>
					<div v-if="isLogin" v-loading="frameLoading" class="serve-card-box">
						<iframe
							src="http://ybzy.eoss.wisesoft.net.cn/project-ybzy/ybzy/index.html#/guidance?serverId=ybzyDtcSso&authType=6&openType=self"
							width="100%"
							height="100%"
							frameborder="0"
							allowfullscreen
							allow-same-origin
						>
							<div>浏览器不兼容</div>
						</iframe>
					</div>
					<div v-if="!isLogin" class="guidance-box-pic">
						<img
							src="@/assets/images/serveHall/guidance-pic.png"
							alt=""
							width="100%"
							style="width: 100%"
						/>
					</div>
				</section>
			</article>
			<div style="height: 1px"></div>
		</div>
		<el-dialog title="" :visible.sync="dialogVisible" width="90%">
			<iframe
				ref="iframeDo"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</el-dialog>
	</div>
</template>

<script>
window.setIframeHeight = function (height) {
	let setDom = document.getElementsByClassName('serve-card-box');
	setDom[1].style.height = `${height}px`;
};
import ServeCard from './components/serve-card.vue';
export default {
	name: 'VEhall',
	components: { ServeCard },
	data() {
		return {
			resultsData: [
				{
					id: '7055712092669612032',
					parentId: '0',
					name: '会员中心',
					url: '#',
					urlPe: '',
					logo: 'https://dayuding.wisesoft.net.cn/group1/M00/01/A9/rBAJQmTmt16AVcKkAAAPfD8_924333.png',
					openType: '1',
					sort: 0,
					resourceType: 'app',
					icon: '#',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7109744518685003776',
					parentId: '0',
					name: '实验室管理',
					url: 'http://127.0.0.1:8080/framework/auth/ybzyUserAuthPage.dhtml',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=5fb318fc-4dc3-4ffd-8360-814e032c0d21',
					openType: '1',
					sort: 1,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7056440970329460736',
					parentId: '0',
					name: '宿舍管理',
					url: 'http://ybzy.eoss.wisesoft.net.cn/project-ybzy/auth_page/views/authuser.html?redirect_uri=http%3A%2F%2F220.167.103.103%3A8081&appid=aad77fa56bbc47a5&clientType=pc',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=370243b3-28ba-42f3-8cae-8381eb5f38f5',
					openType: '2',
					sort: 2,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '提供宿舍管理功能',
					enterpriseTypeList: null
				},
				{
					id: '7056441010456367104',
					parentId: '0',
					name: '迎新管理',
					url: 'http://ybzy.eoss.wisesoft.net.cn/project-ybzy/auth_page/views/authuser.html?redirect_uri=http%3A%2F%2F220.167.103.103%3A8081&appid=aad77fa56bbc47a5&clientType=pc',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=9fdc618a-b5ab-44ba-898a-1aae5b80cf31',
					openType: '2',
					sort: 3,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '迎新系统',
					enterpriseTypeList: null
				},
				{
					id: '7105754101320781824',
					parentId: '0',
					name: '社团管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=40b8cb105b28430283a211cf92f54360',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=0f0d977f-443e-4e41-bbdb-86db3435374c',
					openType: '2',
					sort: 4,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105474229969096704',
					parentId: '0',
					name: '考勤管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=283fe3ba35a146d0b8484f94a3c01a9a',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=57a10e33-9f85-40ef-b33f-92a482b56eff',
					openType: '2',
					sort: 5,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105754664250904576',
					parentId: '0',
					name: '工会管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=e7aae5396f464b329d58487a23ae8c7f',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=7eba9c9f-7572-42cb-b417-c3059fcef725',
					openType: '2',
					sort: 6,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105474670601703424',
					parentId: '0',
					name: '退休人员管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=20dbe38686a74198b831b39d164222e1',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=edd4fa59-5f50-4e79-afa1-e80de69d45c6',
					openType: '2',
					sort: 7,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105475014631100416',
					parentId: '0',
					name: '成果管理',
					url: '/xxx',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=ac74a597-b250-43c0-929a-9310cbf3cb3e',
					openType: '2',
					sort: 8,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105754891154362368',
					parentId: '0',
					name: '绩效管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=e4d6e39f38f2454db14464e7de6614d1',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=0789eccb-ffac-4cae-99be-2d6bd3bd3538',
					openType: '2',
					sort: 9,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105754990232211456',
					parentId: '0',
					name: '项目管理',
					url: '/xxx',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=5ecd9286-2522-4f56-bf84-d12717268665',
					openType: '2',
					sort: 10,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105755143722766336',
					parentId: '0',
					name: '报表管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=e8dad8975e4942f495e23e33491ce26b',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=e26830e9-dd70-408f-b4d7-2605b504fb91',
					openType: '2',
					sort: 11,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7105755221858455552',
					parentId: '0',
					name: '第二课堂活动管理',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=d5487c0a084044f4ba757e9345393ae9',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=b04d884f-95f2-473f-9f95-6eea1b456a59',
					openType: '2',
					sort: 12,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7097750933475430400',
					parentId: '0',
					name: '资料管理',
					url: 'http://ybzy.eoss.wisesoft.net.cn/project-ybzy/museum-front/backFrame/page/media/resource/archives_list.html',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=a95f1e16-1bc2-4b43-aad5-5e962d83c984',
					openType: '2',
					sort: 13,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7107628943968047104',
					parentId: '0',
					name: '在线学习',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=b8403090dfd6456399ab90311ecac8b1',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=3d59a59b-2c6d-4e7a-a7b6-4f3ed5f56432',
					openType: '2',
					sort: 14,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				},
				{
					id: '7056440870328864768',
					parentId: '0',
					name: '商品及服务提供商端',
					url: 'http://ybzy.eoss.wisesoft.net.cn/trip-seller/appSellerCenter/businessCenterInfo?auth=ybzy',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=9c0d29cf-9e0a-4e69-981f-e1e2f6ad3e29',
					openType: '2',
					sort: 15,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '提供商品交易服务管理功能',
					enterpriseTypeList: null
				},
				{
					id: '7107599877122560000',
					parentId: '0',
					name: '无纸化会议',
					url: '/ybzy/platauth/front/loginEoss?redirectUri=/project-ybzy/ybzy/index.html#/main?cookie=1&applicationId=6ee016fb179b4b62a1e1361720b5eb66',
					urlPe: '',
					logo: 'http://ybzy.eoss.wisesoft.net.cn/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=5e6a2fd6-9be4-4256-9d5e-171e519cb9f6',
					openType: '2',
					sort: 16,
					resourceType: 'app',
					icon: '',
					componentUrl: null,
					redirect: null,
					activeMenu: null,
					isHidden: '0',
					remark: '',
					enterpriseTypeList: null
				}
			],
			frameLoading: true,
			frameShow: false,
			tabs: [
				{ id: 0, name: '学生办事' },
				{ id: 1, name: '教师办事' },
				{ id: 2, name: '单位办事' },
				{ id: 3, name: '游客办事' }
			],
			tabActive: 0,
			tags: [
				{ id: 0, name: '按主题分类' },
				{ id: 1, name: '按学院部门' },
				{ id: 2, name: '按服务场景' },
				{ id: 3, name: '按标签分类' }
			],
			tagActive: 0,
			serveList: [],
			serveLoading: false,
			isLogin: false,
			iframeUrl: '',
			dialogVisible: false
		};
	},
	computed: {},
	mounted() {
		// 监听子页面想父页面的传参
		window.addEventListener('message', event => {
			//此处执行事件
			if (event.data == 'frameStart') {
				this.frameShow = true;
				this.frameLoading = false;
			} else {
				this.iframeUrl = event.data;
				this.dialogVisible = true;
				this.$nextTick(() => {
					this.$refs.iframeDo.src = this.iframeUrl;
				});
			}
		});
	},
	created() {
		if (Object.prototype.hasOwnProperty.call(this._userinfo, 'id')) {
			this.getServeList();
			this.isLogin = true;
		} else {
			this.serveList = this.resultsData;
		}
	},
	methods: {
		clickCard(cardData) {
			if (!this.isLogin) {
				return;
			}
			const { cardName } = cardData;
			const stack = {
				serve: () => {
					const { url } = cardData;
					if (url == '#' || !url) {
						this.$message.warning('未配置连接');
						return;
					}
					if (url) {
						window.open(url);
					}
				}
			};
			stack[cardName]();
		},
		changeTab(id) {
			this.tabActive = id;
		},
		changeTag(id) {
			this.tagActive = id;
		},

		//获取推荐技能列表数据
		async getServeList() {
			this.serveLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.getOutLink();
				if (rCode == 0) {
					this.serveList = results;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				this.serveList = [];
				console.error('>>>', error);
			} finally {
				this.serveLoading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.main {
	width: 100%;
	height: 100%;
	font-family: Microsoft YaHei;
	// background: #1da3fca1;
	min-height: calc(100vh - 350px);
	.pic-box {
		width: 100%;
		min-height: 800px;
		background: url('~@/assets/alumni-association-images/zjdt-bg.png') center no-repeat;
		background-color: #ffffff;
		background-position: top;
		background-size: 100% auto;
		// border-top: 1px solid transparent;
		padding: 1px 0 0 0;
	}
	article {
		width: 1260px;
		margin: 350px auto 50px;
		background: #fff;
		padding: 0 30px;
		border-radius: 10px;
		section {
			position: relative;
			h1 {
				position: relative;
				font-size: 24px;
				padding: 26px 0 20px 0;
				margin-bottom: 20px;
				font-weight: bold;
				color: #000000;
				border-bottom: 2px solid #e8eaf0;
				&::after {
					position: absolute;
					bottom: -2px;
					display: block;
					content: '';
					width: 96px;
					height: 4px;
					background: var(--brand-6, #0076e8);
				}
			}
			ul {
				@include flexBox(space-between);
				width: 420px;
				position: absolute;
				right: 0;
				top: 20px;
				li {
					width: 90px;
					border-radius: 18px;
					font-size: 14px;
					padding: 12px 0;
					text-align: center;
					color: #747d85;
					background: #ffffff;
					box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
					cursor: pointer;
				}
				.tab-actice {
					color: var(--brand-6, #0076e8);
				}
			}
			.tags-box {
				@include flexBox(flex-start);
				margin: 28px 0 22px 0;
				span {
					font-size: 14px;
					margin-right: 20px;
					cursor: pointer;
				}
				.tag-actice {
					color: var(--brand-6, #0076e8);
				}
			}
			.serve-card-box {
				@include flexBox(flex-start);
				flex-wrap: wrap;
				min-height: 54px;
				.card {
					margin-right: 20px;
				}
				.mg {
					margin-right: 0;
				}
			}
		}
	}
	.guidance-box-pic {
		width: 100%;
	}
}

::v-deep .el-dialog {
	height: 90vh;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	margin-top: 0 !important;
	overflow: hidden;
}
::v-deep .el-dialog__body {
	height: calc(100% - 30px);
}
</style>
