<template>
	<div class="page">
		<div class="coupons">
			<div
				v-for="(item, index) of couponsList"
				:key="index"
				:style="{ backgroundImage: `url(${bg})`, marginRight: (index + 1) % 3 === 0 ? '0' : '' }"
				class="coupons-item"
			>
				<div class="coupons-item-icon">店铺优惠券</div>
				<div class="money">{{ item.couponType == 2 ? `${item.quota}折` : `￥${item.quota}` }}</div>
				<div class="name">
					<div class="name-top">满{{ item.basic }}{{ item.couponType == 2 ? '享' : '使用' }}</div>
					<div class="name-bottom">有效期{{ item.effectiveStart }}至{{ item.effectiveEnd }}</div>
				</div>
				<div class="line"></div>
				<div class="right"><div class="button" @click="coupon(item.id)">立即领取</div></div>
			</div>
		</div>
		<Empty v-if="couponsList.length === 0"></Empty>
		<el-pagination
			v-else
			class="page-bottom"
			:current-page="page"
			:page-sizes="[10, 20, 50, 100]"
			:page-size="size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
export default {
	name: 'Coupons',
	props: {
		couponsList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		total: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			bg: require('@/assets/shop-images/coupons-bg.png'),
			page: 1,
			size: 10
		};
	},
	methods: {
		/**每页数据改变*/
		handleSizeChange(size) {
			this.$emit('getCouponList', this.page, size);
		},
		/**页码改变*/
		handleCurrentChange(page) {
			this.$emit('getCouponList', page, this.size);
		},
		/**领取优惠券*/
		coupon(couponId) {
			let data = {
				couponId: couponId,
				rentId: this.getSiteId(),
				userAccount: this._userinfo.phone || ''
			};
			this.$api.shop_api.getCoupon(data).then(async ({ code, msg, result }) => {
				if (code == 200) {
					this.$message.success('领取成功');
				} else {
					this.$message.error(msg);
				}
			});
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	width: 1200px;
	margin: 0 auto;
	.coupons {
		padding-top: 52px;
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 390px;
			height: 98px;
			background-size: 100% 100%;
			position: relative;
			padding: 28px 11px 22px 7px;
			display: flex;
			align-items: center;
			margin: 0 15px 26px 0;
			&-icon {
				position: absolute;
				top: 0;
				left: 0;
				width: 76px;
				height: 18px;
				background: #ccdffd;
				border-radius: 8px 0px 8px 0px;
				font-size: 12px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #3274e0;
				line-height: 18px;
				text-align: center;
			}
			.money {
				flex-shrink: 0;
				min-width: 100px;
				font-size: 32px;
				font-family: PingFang SC-Heavy, PingFang SC;
				font-weight: 800;
				color: #f95f55;
				line-height: 16px;
				margin-right: 14px;
				text-align: center;
			}
			.name {
				flex: 1;
				&-top {
					font-size: 18px;
					font-family: PingFang SC-Heavy, PingFang SC;
					font-weight: 800;
					color: #404040;
					line-height: 26px;
					margin-bottom: 5px;
				}
				&-bottom {
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 17px;
					word-wrap: anywhere;
				}
			}
			.line {
				width: 1px;
				height: 65px;
				background: #bdd3f9;
				margin: 0 10px 0 8px;
			}
			.right {
				flex-shrink: 0;
				width: 97px;
				display: flex;
				align-items: center;
				justify-content: center;
				.button {
					width: 76px;
					height: 24px;
					background: #3274e0;
					border-radius: 22px 22px 22px 22px;
					border: 1px solid #3274e0;
					font-size: 14px;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 20px;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;
				}
			}
		}
	}
	&-bottom {
		margin: 20px 0;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
