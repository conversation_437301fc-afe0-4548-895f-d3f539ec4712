<template>
	<div class="alumni-active">
		<div
			v-for="(item, index) in alumniActiveList.slice(0, number)"
			:key="index"
			:class="
				(index + 1) % 3 == 0
					? 'alumni-active-box-right-margin alumni-active-box-item'
					: 'alumni-active-box-item'
			"
			@click="toDetail(item.id)"
		>
			<el-image class="pic" :src="item.pictureurl" lazy fit="cover"></el-image>

			<div class="title">{{ item.activity_title }}</div>
			<div class="addrise">
				<i class="el-icon-location-outline icon"></i>
				{{ item.activity_address }}
			</div>
			<div class="time">
				<i class="el-icon-time icon"></i>
				{{ handleTimeChange(item.activity_start_time) }}
				{{ cutString(item.activity_time) }}~
				{{ cutStringEnd(item.activity_time) }}
				{{ handleTimeChange(item.activity_end_date) }}
			</div>
			<div class="bottom">
				<div class="price">
					<!-- {{ item.pay ? `${item.pay}` : '免费' }} -->
					<template v-if="item.pay == 1">
						<span>￥</span>
						<span class="price-text">{{ item.payment_amount || 0 }}</span>
					</template>
					<span v-else class="price-text">免费</span>
				</div>
				<div :class="item.status == 1 ? 'apply ' : 'apply color-grey'">
					{{ statuStranslate(item.status) }}
					<i class="el-icon-arrow-right iconWidth"></i>
					<i class="el-icon-arrow-right iconWidth"></i>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		number: {
			type: Number,
			default: 6
		},
		alumniActiveList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {};
	},
	mounted() {},
	methods: {
		jumpPage(url) {
			this.$router.push(url);
		},
		// 跳转详情页面
		toDetail(id) {
			// 显示类型(2:普通、1:外链)
			// if (item.infoType == 2) {
			// 	this.jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`);
			// } else {
			// 	window.open(item.linkUrl);
			// }
			this.jumpPage(`alumni-association-detail?id=${id}`);
		},
		//把时间戳转换成时间格式
		handleTimeChange(time) {
			let timestamp = time;
			let date = new Date(timestamp);
			let Year = date.getFullYear();
			let Moth = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
			let Day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
			let GMT = Year + '-' + Moth + '-' + Day;
			return GMT;
		},
		//截取字符串
		cutString(string) {
			let str = string;
			let arr = str.split('-');
			return arr[0];
		},
		//截取字符串
		cutStringEnd(string) {
			let str = string;
			let arr = str.split('-');
			return arr[1];
		},
		//状态
		statuStranslate(status) {
			// 全部：0待开放；1预约中；2已结束；3已取消；4已预约
			switch (status) {
				case 0:
					return '待开放';
				case 1:
					// 预约中
					return '点击报名';
				case 2:
					// 已结束
					return '报名结束';
				case 3:
					return '已取消';
				case 4:
					return '已预约';
				default:
					break;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.alumni-active {
	display: flex;
	flex-wrap: wrap;
	.alumni-active-box-item {
		cursor: pointer;
		width: 400px;
		height: 400px;
		margin: 0 30px 30px 0;
		overflow: hidden;
		background: #ffffff;
		box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
		border-radius: 8px;
		.pic {
			width: 100%;
			height: 230px;
			object-fit: cover;
		}
		.title {
			padding: 0 21px;
			height: 50px;
			line-height: 50px;
			font-size: 18px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #333333;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.addrise {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			height: 20px;
			line-height: 20px;
			padding: 0 21px;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #7a8392;
		}
		.icon {
			font-size: 18px;
		}
		.time {
			height: 40px;
			line-height: 40px;
			padding: 0 21px;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #7a8392;
		}
		.bottom {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 56px;
			padding: 0 21px;
			.price {
				font-size: 12px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #ff0000;
				.price-text {
					font-size: 16px;
				}
			}
			.apply {
				cursor: pointer;
				width: 90px;
				height: 30px;
				background: #0076e8;
				border-radius: 4px;
				text-align: center;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #ffffff;
				line-height: 30px;
				.iconWidth {
					width: 6px;
				}
			}
		}
	}
	.alumni-active-box-right-margin {
		margin: 0 0 30px 0;
	}
}
.color-grey {
	background: #dcdcdc !important;
}
</style>
