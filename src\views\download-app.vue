<template>
	<div class="download-container">
		<div v-if="isLoading" class="loading">跳转中...</div>
	</div>
</template>

<script>
export default {
	name: 'DownloadApp',
	data() {
		return {
			isLoading: true,
			// 配置下载地址
			downloadUrls: {
				android: 'http://yszj.ybzy.cn/project-ybzy/yzy.apk',
				harmonyos: 'http://yszj.ybzy.cn/project-ybzy/yzy.apk',
				ios: 'https://www.apple.com.cn/app-store/'
			}
		};
	},
	mounted() {
		this.handleDownload();
	},
	methods: {
		handleDownload() {
			const ua = navigator.userAgent.toLowerCase();
			// 判断设备类型
			if (/iphone|ipad|ipod/.test(ua)) {
				// iOS 设备跳转 App Store
				window.location.href = this.downloadUrls.ios;
			} else if (/harmonyos/.test(ua)) {
				// 鸿蒙设备下载
				window.location.href = this.downloadUrls.harmonyos;
			} else {
				// 安卓设备直接下载
				window.location.href = this.downloadUrls.android;
			}

			// 3秒后如果还没跳转，将 loading 状态设为 false
			setTimeout(() => {
				this.isLoading = false;
			}, 3000);
		}
	}
};
</script>

<style scoped>
.download-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}

.loading {
	font-size: 16px;
	color: #666;
}
</style>
