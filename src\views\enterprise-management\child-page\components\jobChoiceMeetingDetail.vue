<!-- eslint-disable vue/no-v-html -->
<template>
	<div>
		<el-dialog title="回复双选会邀请" width="1000px" :visible.sync="showModel" @close="closeDialog">
			<div v-loading="dialogLoading" class="sketch_content">
				<template v-if="step == 1">
					<!-- 标题 -->
					<div class="title">{{ detail.title }}</div>
					<div class="desc">
						<p>双选会开展地址：{{ detail.address }}</p>
						<p>双选会开展时间：{{ detail.meetingStartEndDes }}</p>
						<p>企业回复时间：{{ detail.replyStartEndDes }}</p>
					</div>
					<!-- 内容展示区域 -->
					<div class="content" v-html="detail.explainDes"></div>
					<!-- 附件下载 -->
					<div v-if="filesList.length" class="download">
						<span>附件下载：</span>
						<span
							v-for="(item, index) in filesList"
							:key="index"
							class="download-text"
							@click="download(item)"
						>
							{{ item.originalName }}
						</span>
						<!-- <el-upload
							ref="videoUpload"
							class="avatar-uploader"
							:action="updUrl"
							:file-list="filesList"
						>
							<i class="el-icon-plus avatar-uploader-icon"></i>
						</el-upload> -->
					</div>
					<!-- 是否参加双选会的操作部分 -->
					<template v-if="modelType != 'view'">
						<div class="">
							<span>是否参加双选会：</span>
							<el-radio v-model="isJoin" label="1">是</el-radio>
							<el-radio v-model="isJoin" label="2">否</el-radio>
						</div>
						<div class="info-save">
							<el-button type="reset" @click="closeDialog">取消</el-button>
							<el-button v-if="isJoin == 2" type="primary" @click="handleFormSubmit('1')">
								提交
							</el-button>
							<el-button v-if="isJoin == 1" type="primary" @click="operateNext">下一步</el-button>
						</div>
					</template>
				</template>
				<template v-if="step == 2 || (infoDisabled && detail.isReply != 0)">
					<!-- 进入下一步时，是否为查看状态，此时，isReply是否回复（0、未回复(不参加)；1、已回复(参加)） -->
					<div v-if="infoDisabled" class="replay-title">回复信息</div>
					<el-form ref="form" :model="formData" class="resume-table" :rules="rules">
						<el-row>
							<el-col :span="12">
								<el-form-item
									label="联系人："
									label-width="136px"
									label-position="left"
									prop="contacts"
								>
									<el-input
										v-model.trim="formData.contacts"
										placeholder="请填写联系人"
										:disabled="infoDisabled"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item
									label="联系方式："
									label-width="136px"
									label-position="left"
									prop="contactNum"
								>
									<el-input
										v-model.trim="formData.contactNum"
										placeholder="请填写联系方式"
										:disabled="infoDisabled"
										maxlength="11"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="10">
								<el-form-item
									label="参加双选会时间："
									label-width="136px"
									label-position="left"
									prop="time"
								>
									<el-date-picker
										v-model="formData.time"
										type="daterange"
										range-separator="至"
										start-placeholder="开始日期"
										end-placeholder="结束日期"
										:picker-options="pickerOptions"
										:disabled="infoDisabled"
										value-format="yyyy-MM-dd"
									></el-date-picker>
								</el-form-item>
							</el-col>
						</el-row>
						<el-button v-if="!infoDisabled" type="primary" @click="choicePosition">
							选择职位
						</el-button>
						<!-- 已选中的职位列表 -->
						<el-table
							:data="jobList"
							style="width: 100%"
							align="center"
							header-align="center"
							header-row-class-name="history-table"
						>
							<el-table-column prop="name" label="职位名称"></el-table-column>
							<el-table-column prop="jobNature" label="工作性质">
								<template #default="{ row }">
									{{ getLabelByValue(jobNatureOptions, row.jobNature) }}
								</template>
							</el-table-column>
							<el-table-column prop="education" label="学历要求">
								<template #default="{ row }">
									{{ getLabelByValue(educationOptions, row.education) }}
								</template>
							</el-table-column>
							<el-table-column prop="workExperience" label="工作经验">
								<template #default="{ row }">
									{{ getLabelByValue(workExperienceOptions, row.workExperience) }}
								</template>
							</el-table-column>
							<el-table-column prop="jobNature" width="120" label="薪资区间(千元)">
								<template #default="{ row }">
									{{ row.minMoney || 0 }}-{{ row.maxMoney || 999999999 }}
								</template>
							</el-table-column>
							<el-table-column prop="payment" width="120" label="薪资补充说明"></el-table-column>
							<el-table-column prop="postType" label="岗位类型">
								<template #default="{ row }">
									{{ getLabelByValue(jobTypeOptions, row.postType) }}
								</template>
							</el-table-column>
							<el-table-column prop="tag" label="保障待遇">
								<template #default="{ row }">
									{{ row.tag || '-' }}
								</template>
							</el-table-column>
							<!-- <el-table-column label="审核状态" width="80">
								<template #default="{ row }">
									<el-tag :type="{ 0: '', 1: 'success', 9: 'info', 2: 'warning' }[row.auditStatus]">
										{{ ['待审核', '发布中', '驳回'][row.auditStatus] }}
									</el-tag>
								</template>
							</el-table-column> -->
							<!-- <el-table-column label="发布状态" width="80">
								<template #default="{ row }">
									<el-tag :type="{ 0: '', 1: 'success' }[row.status]">
										{{ row.statusStr }}
									</el-tag>
								</template>
							</el-table-column> -->
							<el-table-column v-if="!infoDisabled" fixed="right" label="操作" width="190">
								<template #default="{ row, $index }">
									<el-button type="text" size="small" @click="handleView(row)">查看</el-button>
									<el-button
										type="text"
										size="small"
										class="del-btn"
										@click="delClick($index, row)"
									>
										删除
									</el-button>
									<el-button
										type="text"
										size="small"
										class="del-btn"
										@click="handleEdit(row, '编辑职位', $index)"
									>
										编辑
									</el-button>
								</template>
							</el-table-column>
						</el-table>
						<div v-if="!infoDisabled" class="info-save">
							<el-button type="reset" @click="handleFormSubmit('0')">暂存</el-button>
							<el-button type="primary" @click="handleFormSubmit('1')">提交</el-button>
						</div>
					</el-form>
				</template>
				<!--查看岗位信息-->
				<el-dialog
					title="查看岗位信息"
					width="800px"
					:visible.sync="viewJobDialog.visible"
					:close-on-click-modal="false"
				>
					<base-description :column="2" :data-source="viewJobDialog.row"></base-description>
				</el-dialog>
			</div>
		</el-dialog>
		<!-- 调用职位页面 -->
		<el-dialog
			title="选择职位"
			width="1000px"
			:visible.sync="showPosition"
			append-to-body
			@close="positionCloseDialog"
		>
			<position ref="position" :job-choice-meeting="true" @positionCallback="positionCallback" />
			<div class="info-save">
				<el-button type="reset" @click="positionCloseDialog">取消</el-button>
				<el-button type="primary" @click="positionSubmit">确认选中</el-button>
			</div>
		</el-dialog>
		<!-- 职位编辑查看操作弹窗 -->
		<position-handle-dialog ref="positionHandleDialog" @positionCallback="positionCallback" />
	</div>
</template>
<script>
import { getDictionaryByCode } from '@/utils';
import BaseDescription from '@/components/description';
import position from '../position.vue';
import PositionHandleDialog from './positionDialog.vue';
import { alumniUrl } from '@/config';
export default {
	name: 'InfoDetail',
	components: { BaseDescription, position, PositionHandleDialog },
	data() {
		return {
			showModel: false, //是否展示弹窗
			dialogLoading: false, //加载动画
			infoDisabled: false, //表单填写是否禁用
			formData: {
				contacts: '', //联系人
				contactNum: '', //联系电话
				startDate: '', //参加双选会开始时间
				endDate: '', //参加双选会结束时间
				postIds: '', //岗位ids，通过逗号分割
				time: [] //时间组件绑定的值
			}, //表单详情数据
			detail: {}, //双选会详情
			isJoin: '1', //是否参加双选会
			step: 1, //可操作情况下，现在进行到哪一步了
			jobList: [], //已选择的职位列表
			selectedIds: [], //已选择的职位列表ids
			rules: {
				contacts: [{ required: true, message: '请输入联系人', trigger: 'blur' }], //
				contactNum: [
					{
						required: true,
						trigger: 'blur',
						validator: (rule, value, callback) => {
							var reg = /^1[3456789]\d{9}$/;
							if (!value) {
								return callback(new Error('请输入联系方式'));
							} else if (!reg.test(value)) {
								return callback(new Error('请输入正确的电话号码格式'));
							}
							callback();
						}
					}
				], //
				time: [{ required: true, message: '请选择时间', trigger: 'change' }] //
			},
			pickerOptions: {}, // 日期选择中的时间选择范围
			modelType: '', //弹窗类型
			viewJobDialog: {
				visible: false,
				row: []
			}, //岗位查看对象
			educationOptions: [],
			jobNatureOptions: [],
			jobTypeOptions: [],
			workExperienceOptions: [],
			showPosition: false, //职位弹窗内容展示
			positionIndex: 0, //当前操作的职位下标，主要是用于处理编辑之后的职位信息同步更新。
			statusArr: ['待回复', '待审核', '未通过', '已通过', '未参加'], //回复状态
			filesList: [] //附件集合
		};
	},
	computed: {},
	watch: {},
	created() {},
	methods: {
		// 展示弹窗内容
		show(detail, type) {
			this.modelType = type;
			this.step = 1;
			this.showModel = true;
			this.infoDisabled = false;
			this.jobList = [];
			this.getDetailInfo(detail.id);
			this.getEducationList();
		},
		// 获取详情信息
		getDetailInfo(id) {
			this.dialogLoading = true;
			this.$api.enterprise_center
				.jobDualSelectInfo({ id: id })
				.then(res => {
					if (res.success) {
						this.detail = res.results || {};
						if (this.modelType == 'view') {
							// 状态（-1、待回复；0、未提交；1、待审核；2、审核中；3、未通过；4、已通过；5、已过期；9、已拒绝）
							// 当在以下这些状态时，说明回复信息填写过，此时需要将填写的信息操作给禁用
							[0, 1, 2, 3, 4, 9].includes(this.detail.states) &&
								this.detail.contacts &&
								(this.infoDisabled = true);
						}
						// 需要将填写的信息数据进行回显操作
						this.formData = {
							contacts: this.detail.contacts, //联系人
							contactNum: this.detail.contactNum //联系电话
						}; //表单详情数据
						this.$set(this.formData, 'time', []);
						if (this.detail.startDate && this.detail.endDate) {
							this.formData.time[0] = this.detail.startDate.split(' ')[0] || '';
							this.formData.time[1] = this.detail.endDate.split(' ')[0] || '';
						}
						// 获取已选择的职位数据
						this.detail.postIds && this.selectPostList(this.detail.postIds);
						// 获取附件数据
						this.getFiles(this.detail.meetingId);
						let _this = this;
						this.pickerOptions = {
							disabledDate(time) {
								// 选择时间从当前日期开始，结束时间以标准时间为准
								let startTime = new Date(_this.detail.meetingStartDate).setHours(0, 0, 0, 0);

								let maxTime = new Date(_this.detail.meetingEndDate).setHours(0, 0, 0, 0);

								const timeP = new Date(time).setHours(0, 0, 0, 0);
								// 设置时间为00:00:00，比较时间是00:00:00

								// 获取今天时间
								let today = new Date().setHours(0, 0, 0, 0);
								if (timeP >= startTime && timeP <= maxTime && today <= timeP) {
									return false;
								}

								return true;
							}
						};
						// 判断当前时间是否在回复时间之内
					} else {
						this.$message.error(res.msg);
					}
				})
				.finally(() => {
					this.dialogLoading = false;
				});
		},
		// 获取附件信息
		getFiles(ownId) {
			this.$api.personal_api
				.getFileInfo({ code: 'job_dual_select_adjunct', ownId: ownId })
				.then(async res => {
					if (res.rCode === 0) {
						this.filesList = res.results || [];
					}
				});
		},
		// 获取职位列表信息
		selectPostList(postIds) {
			this.$api.enterprise_center.selectPostList({ ids: postIds }).then(res => {
				if (res.success) {
					this.setSelectTable(res.results || []);
				}
			});
		},
		// 数据字典查询学历要求
		async getEducationList() {
			const dicts = await getDictionaryByCode([
				'post_education',
				'post_job_nature',
				'post_job_type',
				'post_work_experience'
			]);
			this.jobNatureOptions = dicts.post_job_nature;
			this.jobTypeOptions = dicts.post_job_type;
			this.workExperienceOptions = dicts.post_work_experience;
			this.educationOptions = dicts.post_education;
		},
		// 下一步操作
		operateNext() {
			this.step = 2;
		},
		// 职位编辑查看操作
		handleEdit(row, title, index) {
			this.positionIndex = index;
			this.$refs.positionHandleDialog.handleEdit(row, title);
		},
		// 编辑职位之后的回调函数
		positionCallback(row) {
			this.getIds();
			this.selectPostList(this.selectedIds.join(','));
			// 主动触发职位列表组件的数据
			this.$refs.position && this.$refs.position.init();
		},
		// g关闭职位选择弹窗
		positionCloseDialog() {
			this.showPosition = false;
		},
		// 确认选中职位，把数据暂存到双选会回复弹窗里面去
		positionSubmit() {
			let selectedData = this.$refs.position.getSelectedData();
			this.positionCloseDialog();
			this.setSelectTable(selectedData);
		},
		// 获取选中职位的id集合
		getIds() {
			// 传递给职位列表的是所选的id集合，主要是传递数据发现职位表格回显选中状态有些问题。
			this.selectedIds = this.jobList.reduce((acc, cur) => {
				acc.push(cur.id);
				return acc;
			}, []);
		},
		// 选择职位触发，打开职位列表弹窗，还需要主动设置职位列表的选中数据回显
		choicePosition() {
			this.getIds();
			this.showPosition = true;
			this.$nextTick(() => {
				this.$refs.position.open(this.detail);

				this.$refs.position.setSelectTable(this.selectedIds || [], this.jobList || []);
			});
		},
		// 提交回复内容
		handleFormSubmit(states) {
			if (this.isJoin == 1) {
				// 参加双选会为是，需要处理填写表单信息和职位信息数据
				this.$refs.form.validate(valid => {
					if (valid) {
						// 职位必选
						if (!this.jobList.length) {
							return this.$message.info('请选择至少一个职位！');
						}
						this.getIds(); //获取职位id集合
						//处理请求数据
						let data = {
							id: this.detail.id,
							states: states, //状态 0为暂存 1为正常提交
							collegeId: this.detail.collegeId, //学院id
							contactNum: this.formData.contactNum, //联系电话
							contacts: this.formData.contacts, //联系人
							enterpriseId: this.detail.enterpriseId, //企业id
							meetingId: this.detail.meetingId, //	双选会id
							isReply: '1', //是否回复（0、未回复(不参加)；1、已回复(参加)）
							postIds: this.selectedIds.join(','), //	岗位ids，通过逗号分割
							startDate: this.formData.time[0] + ' 00:00:00', //参加双选会开始时间
							endDate: this.formData.time[1] + ' 23:59:59' //参加双选会结束时间
						};
						this.dialogLoading = true;
						this.$api.enterprise_center
							.jobDualSelectReply(data)
							.then(res => {
								if (res.success) {
									this.$message.success('操作成功');
									// 回复成功之后的回调
									this.$emit('reply');
									this.closeDialog();
								} else {
									this.$message.error(res.msg);
								}
							})
							.finally(() => {
								this.dialogLoading = false;
							});
					}
				});
			} else {
				// 参加双选会为否，直接请求接口回复
				this.dialogLoading = true;
				//处理请求数据
				let data = {
					id: this.detail.id,
					states: 1, //状态 0为暂存 1为正常提交
					collegeId: this.detail.collegeId, //学院id
					// contactNum: '', //联系电话
					// contacts: '', //联系人
					enterpriseId: this.detail.enterpriseId, //企业id
					meetingId: this.detail.meetingId, //	双选会id
					isReply: '0' //是否回复（0、未回复(不参加)；1、已回复(参加)）
					// postIds: '', //	岗位ids，通过逗号分割
					// startDate: '', //参加双选会开始时间
					// endDate: '' //参加双选会结束时间
				};
				this.$api.enterprise_center
					.jobDualSelectReply(data)
					.then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							// 回复成功之后的回调
							this.$emit('reply');
							this.closeDialog();
						} else {
							this.$message.error(res.msg);
						}
					})
					.finally(() => {
						this.dialogLoading = false;
					});
			}
		},
		// 资料下载
		async download(item) {
			// const res = await this.$api.personal_api.previewFile({ adjunctId: item.adjunctId });
			// console.log(res, '------req--------');
			// if(!res.success){
			// 	return this.$message.info(res.msg);
			// }
			// let isDev = process.env.NODE_ENV === 'development' ? '/dev-api' : alumniUrl;
			let url = `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${item.adjunctId}`;
			var elink = document.createElement('a');
			var event = new MouseEvent('click');
			elink.download = item.originalName || '下载文件';
			elink.href = url;
			elink.dispatchEvent(event);
		},
		// 根据值去找label
		getLabelByValue(dict, value) {
			return dict.find(option => option.cciValue == value)?.shortName || value;
		},
		/**
		 * @description 职位查看操作
		 * */
		handleView(row) {
			this.handleEdit(row, '查看职位');
		},
		/**
		 * @description 删除操作,前端删除，删除暂存的数据
		 * */
		delClick(index, row) {
			this.jobList.splice(index, 1);
		},
		// 关闭弹窗时的操作 用于重置部分变量
		closeDialog() {
			this.step = 1;
			this.$refs.form && this.$refs.form.resetFields();
			this.showModel = false;
			this.infoDisabled = false;
			this.jobList = [];
			this.filesList = [];
			this.detail = {};
		},
		// 设置选中的职位列表，暂存的数据
		setSelectTable(data) {
			this.jobList = data;
		}
	}
};
</script>
<style scoped>
.sketch_content {
	overflow: auto;
}
.title {
	text-align: center;
	font-size: 30px;
	line-height: 48px;
}
.desc {
	text-align: center;
	line-height: 22px;
	font-size: 14px;
	font-weight: 400;
	color: #999;
}
.content {
	margin: 10px 0;
}
.download {
	margin-bottom: 10px;
}
.download-text {
	color: #0076e9;
	cursor: pointer;
}
.replay-title {
	padding: 10px 0;
	line-height: 24px;
	font-size: 18px;
	color: rgba(0, 0, 0, 0.75);
	border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.info-save {
	text-align: center;
	margin-top: 10px;
}
::v-deep .el-input.is-disabled .el-input__inner {
	color: rgba(0, 0, 0, 0.4);
}
::v-deep .el-range-editor.is-disabled .el-range-input {
	color: rgba(0, 0, 0, 0.4);
}
</style>
