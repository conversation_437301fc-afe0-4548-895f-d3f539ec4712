<template>
	<div class="navbar">
		<img src="@/assets/images/serveHall/vocational-logo.png" class="navbar-left" @click="goHome" />

		<div class="navbar-right">
			<headAvator
				:own-id="_userinfo.id"
				class="navbar-right-avator"
				@click="handleButton('draw')"
			/>
			<div class="navbar-right-name" @click="handleButton('draw')">
				<div>{{ _userinfo.username }}</div>
				<div>{{ _userinfo.phone }}</div>
			</div>
			<HeaderDraw v-model="showMore" offset-top="60px" @jumpLink="toRoute"></HeaderDraw>
			<div v-for="(item, index) of list" :key="index" class="navbar-right-list">
				<img
					class="img"
					:style="index === 0 ? 'width:12px;height: 12px;' : ''"
					:src="item.img"
					alt=""
					@click="handleButton(item.type)"
				/>
				<div v-if="index !== list.length - 1" class="line"></div>
			</div>
		</div>
		<el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
			<span>确认退出系统吗？</span>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="sure">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { mapMutations } from 'vuex';
import HeaderDraw from '@/components/header-draw';

export default {
	components: {
		HeaderDraw
	},
	data() {
		return {
			dialogVisible: false,
			list: [
				{ img: require('@/assets/images/home/<USER>'), type: 'draw' },
				{ img: require('@/assets/images/home/<USER>'), type: 'home' },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>'), type: 'open' }
			],
			showMore: false
		};
	},
	computed: {},
	methods: {
		...mapMutations('user', ['SET_IDENTITY', 'SET_ROLES', 'SET_USER_INFO']),
		/**返回首页*/
		goHome() {
			this.$router.push('/home');
		},
		/**点击更多里面的内容*/
		toRoute(tab, i) {
			// 如果更新地址，就往路由历史加一个对象
			if (tab.path) {
				this.$router.push(tab.path);
				//this.addRouteHistory(tab); // 往路由历史添加信息
			} else {
				this.$message.warning('开发中，敬请期待...');
			}
		},
		/**退出系统*/
		sure() {
			this.dialogVisible = false;
			this.$loginOut();
			// this.$router.push('/login');
		},
		/**处理导航栏按钮点击*/
		handleButton(type) {
			if (type === 'open') {
				this.dialogVisible = true;
			} else if (type === 'home') {
				this.$router.push('/home');
			} else if (type === 'draw') {
				this.showMore = !this.showMore;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 60px;
	background: var(--brand-6, #0076e8);
	padding: 0 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	&-left {
		height: 37px;
		display: flex;
		align-items: center;
		cursor: pointer;
		&-icon {
			width: 93px;
			height: 34px;
			margin-right: 58px;
		}
		&-tabs {
			display: flex;
			align-items: center;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			.tab {
				margin-right: 58px;
				position: relative;
				cursor: pointer;
				&-line {
					width: 20px;
					height: 3px;
					background: #eaff00;
					border-radius: 2px;
					position: absolute;
					bottom: -8px;
					left: calc(50% - 10px);
				}
			}
			.select {
				color: #eaff00;
			}
		}
	}
	&-right {
		display: flex;
		align-items: center;
		&-avator {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			margin-right: 9px;
			background: #ffffff;
			cursor: pointer;
			.img {
				width: 100%;
				height: 100%;
			}
		}
		&-name {
			margin-right: 12px;
			font-size: 12px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 20px;
			cursor: pointer;
		}
		&-list {
			display: flex;
			align-items: center;
			.line {
				width: 1px;
				height: 16px;
				background: #000000;
				opacity: 0.2;
				margin: 0 22px;
			}
			.img {
				width: 14px;
				height: 14px;
				width: 21px;
				height: 21px;
				cursor: pointer;
			}
		}
	}
}
</style>
