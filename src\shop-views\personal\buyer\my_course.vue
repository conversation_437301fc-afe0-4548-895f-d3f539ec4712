<template>
	<div
		v-loading="loading"
		class="my_course"
		:style="isMain ? 'width:1200px;margin-bottom: 60rpx;' : 'width:100%'"
	>
		<div class="head">
			<div class="lf">筛选查询</div>
			<div class="rf">
				<el-button icon="el-icon-search" @click="handleSearch">搜索</el-button>
			</div>
		</div>
		<el-form :inline="true" :model="search" class="form">
			<el-form-item label="订单号">
				<el-input v-model="search.orderId" placeholder="请输入订单编号"></el-input>
			</el-form-item>
			<el-form-item label="课程名称">
				<el-input v-model="search.courseName" placeholder="请输入课程名称"></el-input>
			</el-form-item>
			<el-form-item label="下单时间">
				<el-date-picker
					v-model="search.time"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					value-format="yyyy-MM-dd"
				></el-date-picker>
			</el-form-item>
		</el-form>
		<div class="tab-box">
			<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">全部课程</div>
			<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">待付款</div>
			<div class="tab" :class="tabIndex == 2 ? 'act' : ''" @click="handleTab(2)">已付款</div>
			<div class="tab" :class="tabIndex == 3 ? 'act' : ''" @click="handleTab(3)">已取消</div>
			<div class="line" :style="{ left: lineLeft(tabIndex) }"></div>
		</div>
		<div class="table">
			<div class="th">
				<div class="td">课程名称</div>
				<div class="td">价格</div>
				<div class="td">支付方式</div>
				<div class="td">交易状态</div>
				<div class="td">操作</div>
			</div>
			<div
				v-for="(item, index) of courseList"
				:key="index"
				class="list"
				:class="index === 0 ? 'last' : ''"
			>
				<div class="data">
					<div class="cell">
						<div class="lf">订单号：</div>
						<div class="rf">{{ item.orderId }}</div>
					</div>
					<div class="cell">
						<div class="lf">类型：</div>
						<div class="rf">{{ item.className }}</div>
					</div>
					<div class="cell">
						<div class="lf">下单时间：</div>
						<div class="rf">{{ item.orderCreateTime }}</div>
					</div>
				</div>
				<div class="tr">
					<div class="td1">
						<div class="box">
							<img :src="$judgeFile(item.coverImg)" alt="" class="lf" />
							<div class="rf">
								<div class="title nth2">
									{{ item.courseName }}
								</div>
							</div>
						</div>
					</div>
					<div class="td2">
						<div class="box">¥{{ Number(item.goodsPrice).toFixed(2) }}</div>
					</div>
					<div class="td3">
						<div class="state">线下支付</div>
					</div>
					<div class="td4">
						<div class="state">{{ statusLabel(item.orderStatus) }}</div>
					</div>
					<div class="td5">
						<el-button
							v-if="item.orderStatus === '0'"
							size="mini"
							type="primary"
							plain
							@click="cancelOrder(item)"
						>
							取消订单
						</el-button>
						<el-button
							v-if="item.orderStatus === '1'"
							size="mini"
							type="primary"
							plain
							@click="onStudy(item)"
						>
							学习课程
						</el-button>
					</div>
				</div>
			</div>
			<el-empty v-if="courseList.length == 0" description="暂无数据"></el-empty>
			<div class="page">
				<el-pagination
					:current-page="pageNum"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="pageSize"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'MyCourse',
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			loading: false,
			search: {},
			tabIndex: 0,
			pageNum: 1, // 页数
			pageSize: 10, // 条数
			total: 0,
			courseList: []
		};
	},
	computed: {
		statusLabel() {
			return status => {
				if (status === '-1') {
					return '已取消';
				} else if (status === '0') {
					return '待付款';
				} else if (status === '1') {
					return '已付款';
				}
				return status;
			};
		}
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.getUserCourseList();
		}
	},
	methods: {
		// 获取订单列表
		async getUserCourseList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				orderId: this.search.orderId,
				keyWord: this.search.courseName,
				date1: this.search.time && this.search.time.length == 2 ? this.search.time[0] : '',
				date2: this.search.time && this.search.time.length == 2 ? this.search.time[1] : '',
				status: this.getStatusByTabIndex()
			};
			const { code, data } = await this.$api.study_api.getUserCoursePageList(params);
			if (code === 200) {
				this.total = data.total;
				this.courseList = data.items || [];
			} else {
				this.total = 0;
				this.courseList = [];
			}
			this.loading = false;
		},
		getStatusByTabIndex() {
			if (this.tabIndex === 3) {
				return -1;
			} else if (this.tabIndex === 1) {
				return 0;
			} else if (this.tabIndex === 2) {
				return 1;
			}
			return null;
		},
		// 搜索
		handleSearch() {
			this.pageNum = 1;
			this.getUserCourseList();
		},
		// 点击导航栏
		handleTab(i = null) {
			if (i !== this.tabIndex) {
				this.tabIndex = i;
				this.pageNum = 1;
				this.getUserCourseList();
			}
		},
		// 页数
		handleCurrentChange(i) {
			this.pageNum = i;
			this.getUserCourseList();
		},
		// 条数
		handleSizeChange(i) {
			this.pageNum = 1;
			this.pageSize = i;
			this.getUserCourseList();
		},
		// 左侧高度
		lineLeft(i) {
			return 100 * i + 'px';
		},
		// 取消订单
		cancelOrder(item) {
			this.$confirm('确认要取消该课程吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					this.loading = true;
					const res = await this.$api.study_api.orderCancel({
						orderId: item.orderId
					});
					if (res.code === 200) {
						this.$message.close();
						this.$message({
							message: res.msg || '取消成功',
							type: 'success'
						});
						this.getUserCourseList();
					} else {
						this.$message.close();
						this.$message.error(res.msg || '网络错误，请稍候再试！');
					}
					this.loading = false;
				})
				.catch(() => {});
		},
		// 学习课程
		onStudy(item) {
			this.$router.push({
				path: '/freecourses',
				query: {
					id: item.courseId || ''
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
}
.my_course {
	margin: 0 auto 0;
	padding: 20px;
	background: #ffffff;
	.head {
		height: 48px;
		background: #f4f4f4;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		padding: 16px;
		.lf {
			font-size: 14px;
			font-weight: 500;
			color: #404040;
		}
		.rf {
			margin-left: auto;
		}
	}
	.form {
		display: flex;
		padding: 19px 16px 0;
		box-sizing: border-box;
		height: 79px;
		border: 1px solid #d9d9d9;
		border-top: none;
		::v-deep .el-date-editor {
			width: 255px;
		}
	}
	.tab-box {
		margin-top: 20px;
		height: 40px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		position: relative;
		.tab {
			width: 60px;
			font-size: 14px;
			font-family: Noto Sans SC-Medium, Noto Sans SC;
			font-weight: 500;
			color: #404040;
			margin-right: 40px;
			cursor: pointer;
			text-align: center;
		}
		.act {
			color: var(--brand-6, '#ca3f3b');
		}
		.line {
			width: 60px;
			height: 1px;
			background: var(--brand-6, '#ca3f3b');
			position: absolute;
			left: 0;
			bottom: -1px;
			transition: all 0.3s;
		}
	}
	.table {
		margin-top: 20px;
		.th {
			display: flex;
			height: 40px;
			align-items: center;
			background: #f4f4f4;
			top: 0;
			.td {
				font-size: 14px;
				font-weight: 500;
				color: #8c8c8c;
				padding-left: 15px;
				box-sizing: border-box;
			}
			.td:nth-child(1) {
				width: 374px;
			}
			.td:nth-child(2) {
				width: 150px;
			}
			.td:nth-child(3) {
				width: 150px;
			}
			.td:nth-child(4) {
				width: 150px;
			}
			.td:nth-child(5) {
				width: 100px;
			}
		}
		.list {
			border: 1px solid #eeeeee;
			margin-top: 16px;
		}
		.last {
			margin-top: 12px;
		}
		.data {
			height: 40px;
			background: #f6f6f6;
			display: flex;
			align-items: center;
			border-top: 1px solid #eeeeee;
			.cell {
				display: flex;
				align-items: center;
				padding-left: 13px;
				margin-right: 25px;
				.lf {
					font-size: 14px;
					font-weight: 400;
					color: #8c8c8c;
				}
				.rf {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
				}
			}
		}
		.tr {
			display: flex;
			align-items: center;
			border-top: 1px solid #eeeeee;
			.td1 {
				width: 374px;
				border-right: 1px solid #eeeeee;
				.box {
					height: 100px;
					display: flex;
					align-items: center;
					box-sizing: border-box;
					padding: 0 13px 0 11px;
				}
				.lf {
					width: 72px;
					height: 70px;
					object-fit: contain;
				}
				.rf {
					margin-left: auto;
					width: calc(100% - 78px);
					.title {
						font-size: 16px;
						font-weight: 400;
						color: #404040;
						line-height: 24px;
					}
				}
			}
			.td2 {
				width: 150px;
				height: auto;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				border-right: 1px solid #eeeeee;
				.box {
					padding: 0 13px 0 11px;
					box-sizing: border-box;
					height: 100px;
					line-height: 100px;
				}
			}
			.td3 {
				width: 150px;
				height: auto;
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				border-right: 1px solid #eeeeee;
				.state {
					padding: 0 13px 0 11px;
					box-sizing: border-box;
					height: 100px;
					line-height: 100px;
				}
			}
			.td4 {
				width: 150px;
				padding: 0 13px 0 11px;
				border-right: 1px solid #eeeeee;
				box-sizing: border-box;
				height: 100%;
				.state {
					height: 100px;
					font-size: 14px;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
					line-height: 100px;
					text-align: center;
				}
			}
			.td5 {
				width: 100px;
				padding: 0 13px 0 11px;
				text-align: center;
				display: flex;
				align-items: baseline;
				flex-wrap: wrap;
				justify-content: center;
			}
		}
		.page {
			margin-top: 41px;
			::v-deep .el-pagination {
				display: flex;
				.btn-prev {
					margin-left: auto;
				}
			}
		}
	}
}
</style>
