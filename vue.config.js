'use strict';
const path = require('path');
const config = require('./src/config');
const pkg = require('./package.json');

function resolve(dir) {
	return path.join(__dirname, dir);
}

const name = config.title || pkg.name;
const port = process.env.port || 8081;
// const url = 'http://10.44.4.88:9999/'; //测线
const url = 'http://10.44.4.160/'; //正式
// const url = 'http://yszj.ybzy.cn/'; //正式
module.exports = {
	publicPath: './',
	outputDir: 'dist-ybzy_zhxy',
	assetsDir: 'static',
	// 是否开启eslint校验
	lintOnSave: process.env.NODE_ENV === 'development',
	productionSourceMap: false,
	devServer: {
		headers: {
			'Access-Control-Allow-Origin': '*'
		},
		port: port,
		open: true,
		overlay: {
			warnings: false,
			errors: true
		},
		// before: require('./mock/mock-server.js'),
		proxy: {
			'/dev-api': {
				target: url,
				changeOrigin: true,
				ws: true,
				pathRewrite: {
					'/dev-api': ''
				}
			},
			'/study-api': {
				// target: 'http://172.16.128.103:8080',
				target: url,
				// target: 'http://dayuding.wisesoft.net.cn:8099/api',
				// target: 'http://scswl.eimm.wisesoft.net.cn:8099',
				ws: true,
				pathRewrite: {
					'^/study-api': ''
				}
			}
		}
	},
	configureWebpack(config) {
		// name: name,
		//调试JS
		// devtool: 'source-map'
		// resolve: {
		// 	alias: {
		// 		'@': resolve('src')
		// 	}
		// }
		config.cache = {
			type: 'filesystem', //缓存类型
			buildDependencies: {
				//缓存依赖
				config: [__filename] //构建配置文件
			}
		};
		if (process.env.NODE_ENV === 'production') {
			config.name = name;
			config.devtool = 'none';
		} else {
			// 为开发环境修改配置...
			config.name = name;
			config.devtool = 'source-map';
		}
	},
	chainWebpack(config) {
		//定义别名
		config.resolve.alias
			.set('@', resolve('src'))
			.set('@ast', resolve('src/assets'))
			.set('@imgs', resolve('src/assets/images'))
			.set('@cpt', resolve('src/components'));
		config.plugin('preload').tap(() => [
			{
				rel: 'preload',
				fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
				include: 'initial'
			}
		]);
		config.plugins.delete('prefetch');
		config.module.rule('svg').exclude.add(resolve('src/icons')).end();
		config.module
			.rule('icons')
			.test(/\.svg$/)
			.include.add(resolve('src/icons'))
			.end()
			.use('svg-sprite-loader')
			.loader('svg-sprite-loader')
			.options({
				symbolId: 'icon-[name]'
			})
			.end();
		config.when(process.env.NODE_ENV !== 'development', config => {
			config
				.plugin('ScriptExtHtmlWebpackPlugin')
				.after('html')
				.use('script-ext-html-webpack-plugin', [
					{
						inline: /runtime\..*\.js$/
					}
				])
				.end();
			config.optimization.splitChunks({
				chunks: 'all',
				cacheGroups: {
					libs: {
						name: 'chunk-libs',
						test: /[\\/]node_modules[\\/]/,
						priority: 10,
						chunks: 'initial'
					},
					elementUI: {
						name: 'chunk-elementUI',
						priority: 20,
						test: /[\\/]node_modules[\\/]_?element-ui(.*)/
					},
					commons: {
						name: 'chunk-commons',
						test: resolve('src/components'),
						minChunks: 3,
						priority: 5,
						reuseExistingChunk: true
					}
				}
			});
			config.optimization.runtimeChunk('single');
		});
	}
};
