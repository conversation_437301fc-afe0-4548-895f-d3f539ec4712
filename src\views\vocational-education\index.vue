<template>
	<div class="main">
		<div class="pic-box">
			<div class="title-box">
				<p class="title">职教园区-网上办事服务大厅</p>
				<p class="desc">
					我们为你准备了丰富的校园服务，不一young的玩法! 校园办理/校园服务/校园管理/校园协作
				</p>
			</div>
			<article>
				<section v-for="(cardItem, cardIndex) in list" :key="cardIndex">
					<h1>{{ cardItem.name }}</h1>
					<div v-loading="serveLoading" class="serve-card-box">
						<serve-card
							v-for="(item, index) in cardItem.serveList"
							:key="index"
							:card-data="item"
							@clickCard="clickCard"
						/>
					</div>
				</section>
			</article>
			<div style="height: 1px"></div>
		</div>
		<roleTip ref="roleTip" :tip-text="tipText" :button="buttonText" @onClick="clickTip"></roleTip>
		<el-dialog title="" :visible.sync="dialogVisible" width="90%" class="iframe-dialog">
			<iframe
				ref="iframeDo"
				src=""
				width="100%"
				height="100%"
				frameborder="0"
				allowfullscreen
				allow-same-origin
			>
				<div>浏览器不兼容</div>
			</iframe>
		</el-dialog>
		<back-top />
	</div>
</template>

<script>
window.setIframeHeight = function (height) {
	let setDom = document.getElementsByClassName('serve-card-box');
	setDom[1].style.height = `${height}px`;
};
import ServeCard from './components/serve-card.vue';
import roleTip from '@/components/role-tip';
import backTop from '@/components/back-top';
import { alumniUrl, baseUrl } from '@/config';

export default {
	name: 'VEhall',
	components: { ServeCard, roleTip, backTop },
	data() {
		return {
			baseUrl,
			alumniUrl,
			list: [
				{
					name: '教学管理',
					serveList: [
						{
							name: '教务管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jwgl.png'
						},
						{
							name: '实习管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xsgl.png'
						},
						{
							name: '实训管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sxgl.png'
						},
						{
							name: '虚拟仿真管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xnfzgl.png'
						},
						{
							name: '实验室管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sysgl.png'
						},
						{
							name: '图书管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/tsgl.png'
						},
						{
							name: '危化品管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/whpgl.png'
						},
						{
							name: '证书管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/zsgl.png'
						},
						{
							name: '教材更换',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jcgh.png'
						}
					]
				},
				{
					name: '学生管理',
					serveList: [
						{
							name: '学工系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xgxt.png'
						},
						{
							name: '迎新系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/yxxt.png'
						},
						{
							name: '宿管系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sgxt.png'
						},
						{
							name: '就业系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jyxt.png'
						},
						{
							name: '离校办理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/lxbl.png'
						},
						{
							name: '学生资助',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xszz.png'
						},
						{
							name: '社团管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/stgl.png'
						},
						{
							name: '第二课堂',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/dekt.png'
						},
						{
							name: '虚拟校园',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xnxy.png'
						},
						{
							name: '心理咨询预约',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xlzxyy.png'
						}
					]
				},
				{
					name: '科研管理',
					serveList: [
						{
							name: '科研项目管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/kyxmgl.png'
						},
						{
							name: '科研成果管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/kycggl.png'
						},
						{
							name: '科研活动管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/kyhdgl.png'
						}
					]
				},
				{
					name: '人事管理',
					serveList: [
						{
							name: '全国老师管理系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/qglsglxt.png'
						},
						{
							name: '事业编制人员管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sybzrygl.png'
						},
						{
							name: '人事管理系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/rsglxt.png'
						},
						{
							name: '绩效管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jxgl.png'
						},
						{
							name: '退休人员管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/txrygl.png'
						},
						{
							name: '工会管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/ghgl.png'
						},
						{
							name: '校友会管理系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xyhglxt.png'
						}
					]
				},
				{
					name: '国资管理',
					serveList: [
						{
							name: '国资系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/gzxt.png'
						},
						{
							name: '后勤管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/hqgl.png'
						},
						{
							name: '故障报修',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/gzbx.png'
						}
					]
				},
				{
					name: '数字底座',
					serveList: [
						{
							name: '指挥调度系统',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/zhddxt.png'
						},
						{
							name: '数字孪生平台',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sznsxt.png'
						},
						{
							name: '数据可视化平台',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sjkshpt.png'
						},
						{
							name: '大数据管理平台',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/dsjglpt.png'
						}
					]
				},
				{
					name: '物联传感',
					serveList: [
						{
							name: '消防管理平台',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xfglxt.png'
						},
						{
							name: '物联网感知平台',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/wlwgzxt.png'
						},
						{
							name: '门禁管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/mjgl.png'
						},
						{
							name: '安防管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/afgl.png'
						},
						{
							name: '校园一卡通',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xyykt.png'
						},
						{
							name: '水电管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/sdgl.png'
						}
					]
				},
				{
					name: '其他',
					serveList: [
						{
							name: 'OA办公',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/oabg.png'
						},
						{
							name: '报表管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/bbgl.png'
						},
						{
							name: '会议室预约',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/hysyy.png'
						},
						{
							name: '访客预约',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/fkyy.png'
						},
						{
							name: '积分管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jfgl.png'
						},
						{
							name: '物品借用',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/wpjy.png'
						},
						{
							name: '就餐预定',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/jcyd.png'
						},
						{
							name: '请假申请',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/qjsq.png'
						},
						{
							name: '资料管理',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/zlgl.png'
						},
						{
							name: '费用报销',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/fybx.png'
						},
						{
							name: '闲置物品交易',
							url: '#',
							urlPe: '',
							logo: 'project-ybzy/VEhall/xzwpjy.png'
						}
					]
				}
			],
			resultsData: [],
			frameLoading: true,
			frameShow: false,
			tabs: [
				{ id: 0, name: '学生办事' },
				{ id: 1, name: '教师办事' },
				{ id: 2, name: '单位办事' },
				{ id: 3, name: '游客办事' }
			],
			tabActive: 0,
			tags: [
				{ id: 0, name: '按主题分类' },
				{ id: 1, name: '按学院部门' },
				{ id: 2, name: '按服务场景' },
				{ id: 3, name: '按标签分类' }
			],
			tagActive: 0,
			serveList: [],
			serveLoading: false,
			isLogin: false,
			iframeUrl: '',
			dialogVisible: false,
			eventType: '', // 事件类型
			tipText: '', // 没有权限的提示
			buttonText: '' // 提示弹窗里面的按钮
		};
	},
	computed: {},
	mounted() {
		// 监听子页面想父页面的传参
		// window.addEventListener('message', event => {
		// 	//此处执行事件
		// 	console.log('监听子页面向父页面的传参', event);
		// 	if (event.data == 'frameStart') {
		// 		this.frameShow = true;
		// 		this.frameLoading = false;
		// 	} else {
		// 		this.iframeUrl = event.data;
		// 		// this.dialogVisible = true;
		// 		this.$nextTick(() => {
		// 			this.$refs.iframeDo.src = this.iframeUrl;
		// 		});
		// 		console.log(this.iframeUrl, 'iframeUrl');
		// 	}
		// });
	},
	created() {
		if (Object.prototype.hasOwnProperty.call(this._userinfo, 'id')) {
			// this.getServeList();
			this.isLogin = true;
		} else {
			// this.serveList = this.resultsData;
		}
	},
	methods: {
		clickCard(cardData) {
			return false;
			// this.eventType = '';
			// // 判断是否登录
			// if (!this.isLogin) {
			// 	this.tipText = '您还未进行用户登录，相关服务暂无权限！'; // 提示框中的按钮
			// 	this.buttonText = '立即前往登录'; // 提示框中的按钮
			// 	this.$refs.roleTip.visible = true;
			// 	this.eventType = 'login';
			// 	return;
			// }
			// // 判断是否有权限
			// this.eventType = 'serve';
			// const stack = {
			// 	serve: () => {
			// 		const { url } = cardData;
			// 		if (url == '#' || !url) {
			// 			// this.tipText = '您查看的应用服务，暂无权限！'; // 提示框中的按钮
			// 			// this.buttonText = '返回职教园区'; // 提示框中的按钮
			// 			// this.$refs.roleTip.visible = true;
			// 			// return;

			// 			let url = this.alumniUrl + `/project-ybzy/ybzy/#/main`;
			// 			// console.log(url,"url")
			// 			// window.open(url);
			// 			var tempwindow = window.open('_blank');
			// 			tempwindow.location.href = url;
			// 		} else {
			// 			window.open(url);
			// 		}
			// 	}
			// };
			// stack.serve();
		},
		/**提示弹窗中的按钮触发事件*/
		clickTip() {
			if (this.eventType == 'login') {
				this.$loginOut();
				// this.$router.push('/login');
			}
		},
		//获取推荐技能列表数据
		async getServeList() {
			this.serveLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.getOutLink();
				if (rCode == 0) {
					this.serveList = results;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				this.serveList = [];
				console.error('>>>', error);
			} finally {
				this.serveLoading = false;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.main {
	width: 100%;
	height: 100%;
	font-family: Microsoft YaHei;
	// background: #1da3fca1;
	min-height: calc(100vh - 350px);
	.pic-box {
		width: 100%;
		min-height: 800px;
		background: url('~@/assets/alumni-association-images/zjdt-bg.png') center no-repeat;
		// background-color: #ffffff;
		background-position: top;
		background-size: 100% auto;
		// border-top: 1px solid transparent;
		padding: 1px 0 0 0;
		position: relative;
	}
	.title-box {
		width: 100%;
		height: 350px;
		position: absolute;
		top: 0;
		left: 0;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.title {
			font-size: 60px;
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 60px;
		}
		.desc {
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #fff600;
			line-height: 30px;
			margin-top: 20px;
		}
	}
	article {
		width: 1260px;
		margin: 350px auto 50px;
		background: #fff;
		padding: 30px;
		border-radius: 10px;
		section {
			position: relative;
			h1 {
				position: relative;
				font-size: 20px;
				padding: 0 0 20px 0;
				margin-bottom: 20px;
				font-weight: bold;
				color: #000000;
				line-height: 20px;
				border-bottom: 2px solid #e8eaf0;
				&::after {
					position: absolute;
					bottom: -2px;
					display: block;
					content: '';
					width: 96px;
					height: 4px;
					background: var(--brand-6, #0076e8);
				}
			}
			ul {
				@include flexBox(space-between);
				width: 420px;
				position: absolute;
				right: 0;
				top: 20px;
				li {
					width: 90px;
					border-radius: 18px;
					font-size: 14px;
					padding: 12px 0;
					text-align: center;
					color: #747d85;
					background: #ffffff;
					box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
					cursor: pointer;
				}
				.tab-actice {
					color: var(--brand-6, #0076e8);
				}
			}
			.tags-box {
				@include flexBox(flex-start);
				margin: 28px 0 22px 0;
				span {
					font-size: 14px;
					margin-right: 20px;
					cursor: pointer;
				}
				.tag-actice {
					color: var(--brand-6, #0076e8);
				}
			}
			.serve-card-box {
				@include flexBox(flex-start);
				flex-wrap: wrap;
				min-height: 54px;
				.card {
					// margin-right: 20px;
				}
				.mg {
					margin-right: 0;
				}
			}
		}
	}
	.guidance-box-pic {
		width: 100%;
	}
}

.iframe-dialog ::v-deep .el-dialog {
	height: 90vh;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	margin-top: 0 !important;
	overflow: hidden;
}
.iframe-dialog ::v-deep .el-dialog__body {
	height: calc(100% - 30px);
}
</style>
