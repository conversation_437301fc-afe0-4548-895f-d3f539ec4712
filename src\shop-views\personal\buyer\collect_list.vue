<template>
	<div class="collect_list" :style="isMain ? 'width:1200px;' : 'width: 100%'">
		<div class="head">
			<div class="tab-box">
				<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">
					收藏商品 {{ goodsList.length }}
				</div>
				<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">
					收藏店铺 {{ shopList.length }}
				</div>
				<div class="line" :style="{ left: lineLeft(tabIndex) }"></div>
			</div>
			<div class="lf">
				<el-checkbox v-show="multiple" v-model="checked" @change="changeAll">全选</el-checkbox>
				<div v-show="multiple" class="btn" @click="delList">
					<i class="el-icon-delete"></i>
					<span>删除</span>
				</div>
				<el-button v-show="!multiple" @click="openMultiple">批量管理</el-button>
				<el-button v-show="multiple" @click="closeMultiple">取消管理</el-button>
				<el-input v-model="search" placeholder="请输入内容" class="input-with-select">
					<el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
				</el-input>
			</div>
		</div>
		<div class="box">
			<div v-show="tabIndex == 0" v-loading="loading" class="goods">
				<CollectGoods
					ref="CollectGoods"
					:list="goodsList"
					:multiple="multiple"
					@handleDelete="goodsDel"
					@handleList="hendleGoods"
					@goShop="goShop"
				></CollectGoods>
				<Empty v-if="goodsList.length == 0" tips="暂无收藏商品"></Empty>
			</div>
			<div v-show="tabIndex == 1">
				<CollectShop
					ref="CollectShop"
					:list="shopList"
					:multiple="multiple"
					@goShop="goShop"
					@handleDelete="shopDel"
				></CollectShop>
				<Empty v-if="shopList.length == 0" tips="暂无收藏店铺"></Empty>
			</div>
		</div>
	</div>
</template>

<script>
import CollectGoods from './../components/collect_goods.vue';
import CollectShop from './../components/collect_shop.vue';
import Empty from '@/components/empty';
export default {
	name: 'CollectList',
	components: {
		CollectGoods,
		CollectShop,
		Empty
	},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		}
	},
	data() {
		return {
			tabIndex: 0,
			search: '',
			goodsList: [],
			shopList: [],
			multiple: false,
			checked: false,
			loading: false,
			userId: '',
			main: window.__POWERED_BY_WUJIE__
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (this.userId) {
			this.favoriteProduct();
			this.getFollowShopList();
		}
	},
	methods: {
		// 取消关注店铺
		shopDel(t) {
			this.loading = true;
			let data = {
				userId: this.userId,
				shopId: t.ID
			};
			this.$api.shop_api.setCancelFollowShop(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message({
						message: res.msg || '取消收藏成功',
						type: 'success'
					});
					this.getFollowShopList();
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 搜索
		handleSearch() {
			if (this.tabIndex == 0) {
				this.favoriteProduct();
			} else {
				this.getFollowShopList();
			}
		},
		// 进入店铺详情
		goShop(t) {
			this.$router.push({
				path: `/shopHomePage?id=${t.shopId || t.ID}`
			});
		},
		// 进入商品详情
		hendleGoods(t) {
			let url = `/shopDetail?id=${t.productId}&type=${t.regCode}`;
			if (t.pointExchangeProduct == 1) {
				url += '&goodsType=point';
			}
			this.$router.push({
				path: url
			});
		},
		// 商品删除
		goodsDel(t) {
			this.loading = true;
			let data = {
				siteId: this.getSiteId(),
				userId: this.userId, // 用户id 后期修改
				id: t.productId
			};
			this.$api.shop_api.cancelFollowShop(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message({
						message: res.msg || '取消收藏成功',
						type: 'success'
					});
					this.favoriteProduct();
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
			});
		},
		// 获取店铺
		getFollowShopList() {
			let data = {
				keyword: this.search,
				siteId: this.getSiteId(),
				offset: 0,
				psize: 1000,
				userId: this.userId
			};
			this.$api.shop_api.getFollowShopList(data).then(res => {
				if (res.state) {
					this.shopList = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
			});
		},
		// 获取商品收藏列表
		favoriteProduct() {
			this.loading = true;
			let data = {
				keywords: this.search,
				siteId: this.getSiteId(),
				offset: 0,
				psize: 1000,
				userId: this.userId,
				queryType: 'favorite'
			};
			this.$api.shop_api.favoriteProduct(data).then(res => {
				if (res.state) {
					this.goodsList = res.result;
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 全选
		changeAll(v) {
			if (v) {
				if (this.tabIndex == 0) {
					this.$refs.CollectGoods.checkList = this.goodsList;
				} else {
					this.$refs.CollectShop.checkList = this.shopList;
				}
			} else {
				this.$refs.CollectGoods.checkList = [];
				this.$refs.CollectShop.checkList = [];
			}
		},
		// 取消批量
		closeMultiple() {
			this.$refs.CollectGoods.checkList = [];
			this.$refs.CollectShop.checkList = [];
			this.multiple = false;
		},
		// 批量删除
		delList() {
			if (this.tabIndex == 0 && this.$refs.CollectGoods.checkList.length === 0) {
				this.$message.close();
				this.$message({
					type: 'info',
					message: '请选择需要删除的商品!'
				});
				return;
			}
			if (this.tabIndex == 1 && this.$refs.CollectShop.checkList.length === 0) {
				this.$message.close();
				this.$message({
					type: 'info',
					message: '请选择需要删除的店铺!'
				});
				return;
			}
			this.$confirm(`确定要取消${this.tabIndex == 0 ? '商品' : '店铺'}的关注吗？`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.loading = true;
					if (this.tabIndex == 0) {
						let ids = this.$refs.CollectGoods.checkList.map(i => {
							return i.productId;
						});
						ids = ids.toString(',');

						let data = {
							siteId: this.getSiteId(),
							userId: this.userId, // 用户id 后期修改
							ids
						};
						this.$api.shop_api.batchDelCollection(data).then(res => {
							if (res.state) {
								this.$message.close();
								this.$message({
									message: res.msg || '取消收藏成功',
									type: 'success'
								});
								this.closeMultiple();
								this.favoriteProduct();
							} else {
								this.$message.close();
								this.$message.error(res.msg || '网络错误，请稍候再试！');
							}
						});
					} else {
						let ids = this.$refs.CollectShop.checkList.map(i => {
							return i.ID;
						});
						ids = ids.toString(',');
						let data = {
							userId: this.userId,
							shopIds: ids
						};
						this.$api.shop_api.batchDelCollection(data).then(res => {
							if (res.state) {
								this.$message.close();
								this.$message({
									message: res.msg || '取消收藏成功',
									type: 'success'
								});
								this.closeMultiple();
								this.getFollowShopList();
							} else {
								this.$message.close();
								this.$message.error(res.msg || '网络错误，请稍候再试！');
							}
							this.loading = false;
						});
					}
				})
				.catch(() => {});
		},
		// 打开多选
		openMultiple() {
			this.$refs.CollectGoods.checkList = [];
			this.$refs.CollectShop.checkList = [];
			this.multiple = true;
		},
		// 点击导航栏
		handleTab(i) {
			if (i !== this.tabIndex) {
				this.option = 0;
				this.tabIndex = i;
				this.page = 1;
				this.closeMultiple();
			}
		},
		// 左侧高度
		lineLeft(i) {
			return 110 * i + 'px';
		}
	}
};
</script>

<style lang="scss" scoped>
.collect_list {
	margin: 0 auto 60px;
	background: #ffffff;
	border-radius: 4px;
	padding: 28px 16px 36px;
	min-height: 100%;
	.head {
		border-bottom: 1px solid #d9d9d9;
		display: flex;
		align-items: center;
		.tab-box {
			width: 250px;
			height: 40px;
			display: flex;
			align-items: center;
			position: relative;
			.tab {
				font-size: 14px;
				font-family: Noto Sans SC-Medium, Noto Sans SC;
				font-weight: 500;
				color: #404040;
				margin-right: 40px;
				cursor: pointer;
				text-align: center;
			}
			.act {
				color: var(--brand-6, '#ca3f3b');
			}
			.line {
				width: 70px;
				height: 1px;
				background: var(--brand-6, '#ca3f3b');
				position: absolute;
				left: 0;
				bottom: -1px;
				transition: all 0.3s;
			}
		}
		.lf {
			height: 32px;
			display: flex;
			margin-left: auto;
			.btn {
				display: flex;
				width: 100px;
				align-items: center;
				cursor: pointer;
				margin: 0 25px 0 15px;
				i {
					font-size: 14px;
					color: #262626;
					margin-right: 5px;
				}
				span {
					font-size: 14px;
					font-weight: 400;
					color: #262626;
				}
			}
			::v-deep.el-checkbox {
				line-height: 30px;
				margin-right: 20px;
			}
			::v-deep.el-input {
				margin-left: 5px;
			}
			::v-deep.el-input__inner {
				height: 32px;
			}
		}
	}
	.box {
		.goods {
			padding-right: 18px;
		}
	}
}
</style>
