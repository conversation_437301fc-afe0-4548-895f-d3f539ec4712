<template>
	<el-dialog title="提示" :visible.sync="visible" @close="visible = false">
		<div class="content">
			<img class="content-img" src="@/assets/images/layout/waring-tip.png" alt="" />
			<div class="content-tip">{{ tipText }}</div>
			<div v-if="button" class="content-button" @click="handleEvent">{{ button }} >>></div>
		</div>
	</el-dialog>
</template>

<script>
export default {
	name: 'Index',
	props: {
		tipText: {
			type: String,
			default: () => {
				return '';
			}
		},
		button: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			visible: false
		};
	},
	methods: {
		/**处理事件*/
		handleEvent() {
			this.$emit('onClick');
			this.visible = false;
		}
	}
};
</script>

<style scoped lang="scss">
.content {
	padding-top: 29px;
	text-align: center;

	&-img {
		width: 60px;
		height: 60px;
	}
	&-tip {
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #8390a3;
		margin: 19px auto 0;
	}
	&-button {
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #0076e8;
		margin-top: 16px;
		cursor: pointer;
	}
}
::v-deep .el-dialog {
	width: 400px;
	height: 250px;
	background: #ffffff;
	border: 1px solid #ebeef5;
	border-radius: 6px;
	.el-dialog__header {
		background: #f8f8f8;
		border-radius: 6px;
		padding: 12px 16px 12px 13px;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.el-dialog__headerbtn {
			position: static !important;
		}
	}
	.el-dialog__body {
		padding: 0 !important;
	}
}
</style>
