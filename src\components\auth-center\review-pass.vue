<template>
	<div>
		<div class="steps">
			<Steps :status="1"></Steps>
		</div>
		<div class="ir-content">
			<div class="ir-img"><i class="iconfont icon-check-circle-filled"></i></div>
			<div class="ir-status">认证通过</div>
			<div class="ir-info">尊敬的用户，您已认证成功</div>
		</div>
	</div>
</template>

<script>
import Steps from '@/components/auth-center/steps';
export default {
	name: 'ReviewPass',
	components: {
		Steps
	},
	data() {
		return {};
	},
	computed: {
		stepIndex() {
			const num = this.steps.index / this.steps.data.length;
			return `${num * 100}%`;
		}
	},
	methods: {}
};
</script>
<style lang="scss" scoped>
.steps {
	margin: 44px auto 77px auto;
	width: 438px;
	::v-deep.el-step__head {
		.el-step__icon {
			color: var(--gray-6, #bfbfbf);
			border-color: var(--gray-6, #bfbfbf);
		}
		&.is-finish {
			.el-step__icon {
				background-color: var(--brand-6, #0076e8);
				color: #fff;
				border-color: var(--brand-6, #0076e8);
			}
		}
	}
	::v-deep.el-step__title {
		color: var(--gray-6, #bfbfbf);
		&.is-finish {
			font-weight: 500;
			color: var(--brand-6, #0076e8);
		}
	}
	// ::v-deep.el-step__line-inner {
	// 	border-width: 1px!important;
	// 	width: 50%;
	// }
	::v-deep .el-steps .el-step {
		&:nth-child(2) {
			.el-step__icon {
				display: none;
			}
		}
	}
}
.ir {
	&-content {
		width: 633px;
		padding-top: 85px;
		padding-bottom: 111px;
		text-align: center;
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
		margin: 0 auto;
	}
	&-img i {
		font-size: 63px;
		color: #76bf6a;
	}
	&-status {
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 44px;
		margin: 13px 0 9px 0;
	}
	&-info {
		font-size: 16px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 24px;
	}
	&-im a {
		color: var(--brand-6, #0076e8);
	}
}
</style>
