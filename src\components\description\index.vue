<!--
 * @Description: 自定义的公共描述组件，处理了描述组件列文字超长省略带tooltip
 * @Version: 1.0
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-16 15:47:30
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-10-04 09:53:30
-->

<script>
import BaseToolTip from '../tooltip';

export default {
	name: 'BaseDescription',
	components: {
		BaseToolTip,
		RenderHtmlComponent: {
			props: {
				htmlString: String
			},
			computed: {
				renderedHtml() {
					const div = document.createElement('div');
					div.innerHTML = this.htmlString;
					const vNodes = Array.from(div.childNodes).map(node => this.$createElement('div', [node]));
					return vNodes;
				}
			},
			render(h) {
				return h('div', {
					domProps: {
						innerHTML: this.htmlString
					}
				});
			}
		}
	},
	props: {
		title: {
			type: String,
			default: ''
		},
		// 渲染描述列所需数据源，包含label和value值
		dataSource: {
			type: Array,
			default: () => {
				return [];
			},
			valueDemo: [
				{
					label: '列表名',
					value: '列表值',
					mapObject: { 0: '默认', 1: '开始' }, // 可选 枚举类型
					tagClass: {
						0: 'red',
						1: 'blue'
					}, // 可选 标签类型 样式枚举
					images: ['http://xxx.jpg'], // 可选  图片地址数组
					path: 'http://xxx' //可选 链接地址 内部路由'/路由名' 或者 具体链接地址
				}
			]
		},
		// 描述每行显示多少列
		column: {
			type: [Number],
			default: 3
		},
		// 是否展示边框
		border: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			imageDialog: {
				visible: false,
				images: [],
				image: {}
			}
		};
	},
	methods: {
		// 渲染插槽
		renderSlots() {
			const slots = Object.entries(this.$attrs?.slots).map(([key, value]) => {
				const obj = {};
				obj[key] = value;
				return obj;
			});
			return slots.map(slot => {
				const slotName = Object.keys(slot)[0];
				return <template slot={slotName}>{slot[slotName]()}</template>;
			});
		},
		// 根据优先级渲染内容
		judgmentRender(item) {
			if (item.path) {
				return this.renderLink(item);
			} else if (item.images) {
				return this.renderImage(item);
			} else if (item.mapObject) {
				return this.renderMap(item);
			} else if (item.tagClass) {
				return this.renderTag(item);
			} else if (item.htmlString) {
				return this.renderHtml(item);
			} else if (item.table) {
				return this.renderTable(item);
			} else {
				return this.renderText(item);
			}
		},
		// 渲染列表项
		renderDescriptionsItem(item) {
			return (
				<el-descriptions-item
					key={item.label}
					label={item.label}
					contentClassName={
						item.images && item.showImageName ? 'base-descriptions-image__text' : ''
					}
					{...{
						props: item.$attrs,
						on: item.$listeners
					}}
				>
					{this.judgmentRender(item)}
				</el-descriptions-item>
			);
		},
		// 渲染链接
		renderLink(item) {
			//链接类型 分为外部链接和内部路由path
			const isRoute = item.path && item.path.includes('http');
			return isRoute ? (
				<el-link href={item.path} target="_blank" type="primary">
					<BaseToolTip content={item.value} />
				</el-link>
			) : (
				<router-link tag="a" class="el-link el-link--primary" to={{ path: item.path }}>
					<BaseToolTip content={item.value} />
				</router-link>
			);
		},
		// 渲染枚举类型
		renderMap(item) {
			const value = item.mapObject[item.value];
			return item.tagClass ? (
				<el-tag size="small" type={item.tagClass[item.value]}>
					{value}
				</el-tag>
			) : item.overflowHidden ? (
				<BaseToolTip content={value} />
			) : (
				value
			);
		},
		// 渲染标签
		renderTag(item) {
			return (
				<el-tag size="small" type={item.tagClass[item.value]}>
					{item.value}
				</el-tag>
			);
		},
		// 渲染html
		renderHtml(item) {
			return <RenderHtmlComponent htmlString={item.htmlString}></RenderHtmlComponent>;
		},
		// 渲染纯文本类型
		renderText(item) {
			return item.overflowHidden ? (
				<BaseToolTip content={item.value} />
			) : (
				<template>
					{item.value}
					{item.unit || ''}
				</template>
			);
		},
		// 渲染图片
		renderImage(item) {
			// 展示文件名
			if (item?.showImageName) {
				return item.images.map(image => {
					return (
						<div class="base-descriptions-item__image">
							<el-link
								type="primary"
								onClick={() => {
									this.handleShowImage(item.images, image);
								}}
							>
								<BaseToolTip content={image.name} />
							</el-link>
							{item?.allowDownload && (
								<i
									class="el-icon-download base-descriptions-item__download"
									onClick={() => {
										this.handleDownload(image);
									}}
								></i>
							)}
						</div>
					);
				});
			}
			// 展示图片
			const images = item.images.map(image => {
				return typeof image === 'string' ? image : image.url;
			});
			return images.map(image => (
				<el-image
					class="base-descriptions-image"
					preview-src-list={images}
					key={image}
					src={image}
					lazy
					alt="加载失败"
					{...{ props: item.imageAttrs }}
				/>
			));
		},
		// 渲染表格
		renderTable(item) {
			return (
				<el-table
					class="base-descriptions-table"
					{...{
						props: { data: item.table.data },
						on: item.$listeners
					}}
				>
					{item.table?.columns.map(column => (
						<el-table-column
							{...{
								props: column,
								on: column.$listeners
							}}
						></el-table-column>
					))}
				</el-table>
			);
		},
		// 暂不支持查看图片
		handleShowImage(files, file) {
			if (['.png', '.jpeg', '.jpg', '.gif', '.webp'].some(item => file.url.includes(item))) {
				this.imageDialog.visible = true;
				this.imageDialog.images = files;
				this.imageDialog.image = file;
			} else {
				this.$message.warning('当前文件格式不支持查看');
			}
		},
		// 下载文件
		async handleDownload(file) {
			const { url, name } = file;
			let blob = await this.getBlob(url);
			this.saveFile(blob, name);
		},
		// 获取文件流
		getBlob(fileUrl) {
			return new Promise(resolve => {
				const xhr = new XMLHttpRequest();
				xhr.open('GET', fileUrl, true);

				xhr.responseType = 'blob';
				xhr.onload = () => {
					if (xhr.status === 200) {
						resolve(xhr.response);
					}
				};
				xhr.send();
			});
		},
		// 下载
		saveFile(blob, fileName) {
			// ie的下载
			if (window.navigator.msSaveOrOpenBlob) {
				navigator.msSaveBlob(blob, fileName);
			} else {
				// 非ie的下载
				const link = document.createElement('a');
				const body = document.querySelector('body');

				link.href = window.URL.createObjectURL(blob);
				link.download = fileName;

				// fix Firefox
				link.style.display = 'none';
				body.appendChild(link);

				link.click();
				body.removeChild(link);

				window.URL.revokeObjectURL(link.href);
			}
		}
	},
	render() {
		return (
			<div class="base-descriptions">
				<el-descriptions
					column={this.column}
					title={this.title}
					border={this.border}
					{...{
						props: this.$attrs,
						on: this.$listeners
					}}
				>
					<template slot="title">{this.$slots?.title}</template>
					<template slot="extra">{this.$slots?.extra}</template>
					{this?.dataSource.map(item => this.renderDescriptionsItem(item))}
				</el-descriptions>
			</div>
		);
	}
};
</script>
<style lang="scss">
.base-descriptions {
	border-radius: 3px 3px 0 0;
	.el-descriptions-item__label {
		flex-shrink: 0;
	}
	.el-descriptions-item__content {
		overflow: hidden;
		vertical-align: baseline;
	}
	.item {
		padding: 20px 0;
		border-bottom: 1px solid #e4e4e4;
	}
	.el-tag + .el-tag {
		margin-left: 8px;
	}
	.el-link {
		font-size: inherit;
		max-width: 100%;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		.el-link--inner {
			width: 100%;
		}
	}
	.el-icon-circle-close {
		color: #fff;
	}
	&-item {
		&__image {
			display: flex;
			align-items: center;
			max-width: 100%;
			&__text {
				display: block;
			}
		}
		&__download {
			display: inline-block;
			margin-left: 5px;
			cursor: pointer;
			&:hover {
				color: var(--brand-6, #0076e8);
			}
		}
	}
	&-image {
		max-height: 150px;
		max-width: 200px;
		margin: 0 10px;
		& > img {
			width: 100%;
			max-height: inherit;
		}
	}
	span.base-descriptions-image__text {
		display: block;
	}
}
.base-descriptions-dialog__image {
	width: 100%;
	height: 100%;
	& > img {
		width: 100%;
		height: 100%;
	}
}
</style>
