<template>
	<div class="dashboard-container">
		<div class="lesson-recently">
			<div class="inner">
				<div class="stitle flex2">
					<span class="title">在线直播</span>
				</div>
				<div class="safeMain">
					<div
						v-for="(item, index) in superStarList"
						:key="index"
						class="temp"
						@click="openLiveBox(item)"
					>
						<div class="infoBox">
							<div class="name over2">
								{{ item.title }}
							</div>
							<div v-if="item.reserveStartTime" class="time">
								{{ item.reserveStartTime + ' 至 ' + item.reserveEndTime }}
							</div>
							<div v-else class="time"></div>
							<div class="desc over1">
								{{ item.description }}
							</div>
						</div>
						<span :class="['live-tag', checkInSuperLiveSpan(item) ? 'tag-active' : 'tag-gray']">
							{{ checkInSuperLiveSpan(item) }}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			superStarList: []
		};
	},
	mounted() {
		this.getSuperStarList();
	},
	methods: {
		async getSuperStarList() {
			const data = await this.$api.study_api.superStarList(this.superPage);
			this.superStarList = data.results.records || [];
			console.log(this.superStarList, 77);
		},
		//打开直播弹框
		async openLiveBox(data) {
			// 打开新页面
			window.open(data.joinUrl);
		},
		//检查是否在时段
		checkInSuperLiveSpan(data) {
			let sTime = data.reserveStartTime;
			let eTime = data.reserveEndTime;
			let curDate = new Date();

			// 检查开始时间是否有效且晚于当前时间
			if (sTime && new Date(sTime) > curDate) {
				return '未开始'; // 直播未开始
			}

			// 检查结束时间是否有效且早于当前时间
			if (eTime && new Date(eTime) < curDate) {
				return '已结束'; // 直播已结束
			}

			// 如果开始时间早于或等于当前时间，并且（如果提供）结束时间晚于或等于当前时间，则直播正在进行
			return '直播中';
		}
	}
};
</script>

<style lang="scss" scoped>
.dashboard-container {
	height: 100%;
	background: #f4f5f8;
	padding: 20px 50px;
	.stitle {
		.title {
			font-size: 20px;
			font-weight: bold;
			color: #222222;
			line-height: 30px;
		}
	}
}
.lesson-recently {
	background: #f6f7f9;
	box-sizing: border-box;
	padding: 40px 0 70px 0;
	.safeMain {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		.temp {
			margin: 28px 24px 0 0;
			padding: 28px 10px 14px 24px;
			position: relative;
			width: 280px;
			background: #ffffff;
			border-radius: 10px;
			overflow: hidden;
			cursor: pointer;
			display: flex;
			align-items: center;
			.insureImg {
				width: 54px;
				height: 54px;
				border-radius: 50%;
				margin-right: 16px;
			}
			.infoBox {
				box-sizing: border-box;
				width: 100%;
				overflow: hidden;
				.name {
					font-size: 14px;
					font-weight: 400;
					color: #222222;
					line-height: 18px;
					margin-bottom: 6px;
				}
				.time {
					font-size: 12px;
					color: #666;
					margin-bottom: 6px;
				}
				.desc {
					font-size: 12px;
					color: #8c8c8c;
					line-height: 18px;
				}
			}
			.live-tag {
				position: absolute;
				top: 0;
				left: 0;
				display: inline-block;
				width: 55px;
				height: 20px;
				font-size: 12px;
				color: #ffffff;
				border-radius: 9px 0px 8px 0px;
				text-align: center;
				line-height: 20px;
			}
			.tag-gray {
				background: #545454;
			}
			.tag-active {
				background: #ea9f12;
			}
		}
		.temp:nth-child(4n) {
			margin-right: 0;
		}
	}
}
</style>
