<!--
 @desc:个人中心 校友会 校友社区 我创建的社区 邀请分享弹窗
 @author: WH
 @date: 2023/9/13
 -->
<template>
	<el-dialog title="邀请成员" :visible.sync="visible" :before-close="beforeClose" @open="init">
		<p ref="shareUrl" class="share-url">{{ url }}</p>
		<ul>
			<li @click="copyUrl">
				<div class="img">
					<img class="is-img" src="@/assets/alumni-association-images/link.png" alt="" />
				</div>
				<p>复制链接</p>
			</li>
			<!-- <li>
				<div class="img">
					<img class="is-img" src="@/assets/alumni-association-images/QQ.png" alt="" />
				</div>
				<p>QQ好友</p>
			</li>
			<li>
				<div class="img">
					<img class="is-img" src="@/assets/alumni-association-images/WeChat.png" alt="" />
				</div>
				<p>微信好友</p>
			</li>
			
			<li>
				<div class="img">
					<img class="is-img" src="@/assets/alumni-association-images/img.png" alt="" />
				</div>
				<p>生成图片</p>
			</li>
			<li>
				<div class="img">
					<img class="is-img" src="@/assets/alumni-association-images/QR.png" alt="" />
				</div>
				<p>生成二维码</p>
			</li> -->
		</ul>
	</el-dialog>
</template>

<script>
export default {
	props: {
		visible: {
			type: Boolean,
			required: true
		},
		communityId: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			url: ''
		};
	},

	methods: {
		init() {
			this.url = `${window.location.href}&inviter=${this.$store.state.user.userInfo.id}&communityId=${this.communityId}`;
		},
		beforeClose(e) {
			this.$emit('update:visible', false);
		},
		copyUrl() {
			let textarea = document.createElement('textarea');
			textarea.value = this.url;
			document.body.appendChild(textarea);
			textarea.select();
			document.execCommand('copy');
			document.body.removeChild(textarea);
			this.$message.success('已复制到粘贴板');
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.share-url {
	margin: 10px 0 20px 0;
}
ul {
	@include flexBox(space-evenly);
	width: 100%;
}
li {
	text-align: center;
	.img {
		width: 48px;
		height: 48px;
		padding: 6px;
		margin-bottom: 10px;
		background: #f3f5f7;
		img {
			width: 100%;
			height: 100%;
		}
	}
	p {
		font-size: 14px;
		color: #7a8392;
	}
}
</style>
