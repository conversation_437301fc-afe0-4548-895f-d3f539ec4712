<template>
	<el-dialog
		ref="imageSliceDialog"
		custom-class="image-slice"
		:close-on-click-modal="false"
		:visible.sync="visible_"
		title="图片裁剪"
	>
		<div class="image-slice-wrapper">
			<div class="image-slice-body" ref="imageSliceBody">
				<VueCropper
					ref="cropper"
					:img="src"
					:outputSize="option.outputSize"
					:outputType="outputType"
					:info="true"
					:canScale="option.canScale"
					:autoCrop="option.autoCrop"
					:autoCropWidth="option.autoCropWidth"
					:autoCropHeight="option.autoCropHeight"
					:fixedBox="option.fixedBox"
					:fixed="option.fixed"
					:fixedNumber="fixedNumber"
					:canMove="option.canMove"
					:canMoveBox="option.canMoveBox"
					:original="option.original"
					:centerBox="option.centerBox"
					:infoTrue="option.infoTrue"
					:full="option.full"
					:enlarge="option.enlarge"
					:mode="option.mode"
				></VueCropper>
			</div>
		</div>
		<div slot="footer" class="cropper-dialog-footer">
			<el-form inline size="mini">
				<!--        <el-form-item label='固定比例:'>
          <el-switch v-model='option.fixed'/>
        </el-form-item>
        <el-form-item>
          宽:
          <NumberInput :disabled='!option.fixed' v-model='option.fixedNumberWidth' style='width: 80px'/>
          高:
          <NumberInput :disabled='!option.fixed' v-model='option.fixedNumberHeight' style='width: 80px'/>
        </el-form-item>-->
				<el-form-item>
					<el-button size="small" @click="rotateLeft">向左旋转90°</el-button>
					<el-button type="primary" size="small" @click="handleSlice">确认裁剪</el-button>
				</el-form-item>
			</el-form>
		</div>
	</el-dialog>
</template>

<script>
import { VueCropper } from 'vue-cropper';
import NumberInput from '@/components/public/NumberInput';
export default {
	name: 'ImageCropper',
	components: {
		VueCropper,
		NumberInput
	},
	props: {
		visible: {
			type: Boolean,
			required: true
		},
		src: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			// 裁剪组件的基础配置option
			option: {
				// 固定配置
				mode: 'contain', // 图片默认渲染方式
				outputSize: 1, // 裁剪生成图片的质量 0.1 - 1
				info: true, // 裁剪框的大小信息
				canScale: true, // 图片是否允许滚轮缩放
				autoCrop: true, // 是否默认生成截图框
				autoCropWidth: 300, // 默认生成截图框宽度
				autoCropHeight: 300, // 默认生成截图框高度
				fixedBox: false, // 固定截图框大小 不允许改变
				canMove: true, // 上传图片是否可以移动
				canMoveBox: true, // 截图框能否拖动
				centerBox: true, // 截图框是否被限制在图片里面
				infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
				original: false, // 上传图片按照原始比例渲染
				full: true, // 是否输出原图比例的截图
				enlarge: 1, // 图片根据截图框输出比例倍数
				// 表单配置
				fixed: false, // 是否开启截图框宽高固定比例
				fixedNumberWidth: 1, // 截图框的宽高比例-宽度
				fixedNumberHeight: 1 // 截图框的宽高比例-高度
			}
		};
	},
	computed: {
		visible_: {
			get() {
				return this.visible;
			},
			set(newVal) {
				this.$emit('update:visible', newVal);
				if (newVal) return this.$emit('close');
				this.$emit('open');
			}
		},
		outputType() {
			return this.src.split('.').pop();
		},
		fixedNumber() {
			return [this.option.fixedNumberWidth, this.option.fixedNumberHeight];
		}
	},
	methods: {
		handleSlice() {
			this.$refs.cropper.getCropBlob(async data => {
				let resultFile = new File([data], new Date().getTime() + '.png');
				this.$emit('success', resultFile);
				this.visible_ = false;
			});
		},
		rotateLeft() {
			this.$refs.cropper.rotateLeft();
		}
	}
};
</script>

<style lang="scss" scoped>
.image-slice {
	width: 820px;

	.image-slice-body {
		width: 100%;
		height: 400px;
	}
	.cropper-dialog-footer .el-form {
		display: inline-block;
	}
}
</style>
