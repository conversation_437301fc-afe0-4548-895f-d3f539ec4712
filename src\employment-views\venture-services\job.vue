<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<!-- 筛选区域 -->
			<filter-box
				:filter-list="filterList"
				:filter-form="filterForm"
				label="shortName"
				value="id"
				@search="search"
				@filterChange="filterChange"
			></filter-box>
			<!-- 列表数据区域 -->
			<ul v-if="list.length" v-loading="loading" class="list-box">
				<li v-for="(item, index) in list" :key="index" class="list-item">
					<div class="item-base" @click="jumpPage(`/job-detail?id=${item.id}`)">
						<div class="item-box">
							<div class="position-base">
								<p class="over1 flex1">
									<span class="name over1">{{ item.name }}</span>
									<span class="position over1">{{ item.areaName }}</span>
									<img
										v-if="item.isDualSelect"
										class="tag-img"
										:src="require('@/assets/employment-images/tag-samll.png')"
										alt=""
									/>
								</p>
								<p class="position flex1" style="margin: 10px 0">
									<el-tag v-if="item.lineType === 1" size="mini" type="success">线下</el-tag>
									<el-tag v-else size="mini" type="primary">线上</el-tag>
									<el-tooltip v-if="item.lineType === 1" effect="dark" placement="top">
										<div slot="content">
											进校时间：{{ item.enterSchoolTime || '-' }}
											<br />
											招聘地点：{{ item.offlinePlace || '-' }}
										</div>
										<span class="ellipsis">
											进校时间：{{ (item.enterSchoolTime || '-') + ' / ' }}招聘地点：{{
												item.offlinePlace || '-'
											}}
										</span>
									</el-tooltip>
								</p>

								<span class="price">
									<!-- {{ item.salaryStructure }} -->
									<span>{{ item.minMoney || 0 }}-{{ item.maxMoney || 0 }}k</span>
									<!-- <span v-if="item.payment">/{{ item.payment }}</span> -->
								</span>
							</div>
							<div class="company-base">
								<div class="com-box">
									<img
										v-if="item.enterprise && item.enterprise.logo"
										class="com-img"
										:src="getImgUrl(item.enterprise && item.enterprise.logo)"
										alt=""
									/>
									<img
										v-else
										class="com-img"
										:src="require('@/assets/employment-images/com-defalut-img.jpg')"
										alt=""
									/>
									<span class="com-name over1">
										{{ item.enterprise && item.enterprise.corpName }}
									</span>
								</div>
								<p class="com-desc over1">
									{{
										item.enterprise &&
										item.enterprise.industryField | enterpriseTypeStr(filterList.industryField)
									}}
									<span v-if="item.enterprise && item.enterprise.peopleNumCode">
										/{{
											item.enterprise &&
											item.enterprise.peopleNumCode | enterpriseTypeStr(filterList.peopleNumCode)
										}}
									</span>
								</p>
							</div>
						</div>
						<div class="labels-box over1">
							<span v-for="(labelItem, labelIndex) in item.tags" :key="labelIndex" class="label">
								{{ labelItem }}
							</span>
						</div>
					</div>
					<div v-if="item.lineType === 0" class="btns-box">
						<el-button
							type="info"
							plain
							round
							:class="['contact-btn', item.jobSend ? 'send-btn' : '']"
							@click="jobSend(item)"
						>
							{{ item.jobSend ? '简历已投递' : '投递简历' }}
						</el-button>
					</div>
				</li>
				<!-- 分页 -->
				<el-pagination
					class="pagination"
					background
					layout="prev, pager, next,jumper"
					:total="total"
					@current-change="handleCurrentChange"
				></el-pagination>
			</ul>
			<Empty v-else :tips="'暂无数据'" />
		</div>
	</div>
</template>

<script>
import PreviewAdjunctMixin from '../mixin/previewAdjunct';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import filterBox from '../components/filter-box.vue';
import { getDictionaryByCode } from '@/utils';

export default {
	components: {
		subBreadcrumb,
		filterBox
	},
	filters: {
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList.typeList) {
				if (item.cciValue == value && value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		}
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			regionOptions: '',
			filterList: {
				industryField: {
					label: '行业领域：',
					porp: 'industryField',
					typeList: [
						{
							shortName: '全部',
							id: ''
						}
					]
				},
				cityCode: {
					label: '城市选择：',
					porp: 'cityCode',
					optionLabel: 'label',
					typeList: [
						{
							label: '全部',
							value: ''
						}
					]
				},
				postType: {
					label: '岗位类型：',
					porp: 'postType',
					// needAllBtn: true,
					// allBtnText: '不限',
					typeList: [
						{
							shortName: '不限',
							code: ''
						}
					]
				},
				expectedSalary: {
					label: '期望薪资：',
					porp: 'expectedSalary',
					// needAllBtn: true,
					// allBtnText: '不限',
					typeList: [
						{
							shortName: '不限',
							code: ''
						}
					]
				},
				workExperience: {
					label: '工作经验：',
					porp: 'workExperience',
					// needAllBtn: true,
					// allBtnText: '不限',
					typeList: [
						{
							shortName: '不限',
							id: ''
						}
					]
				},
				education: {
					label: '学历要求：',
					porp: 'education',
					// needAllBtn: true,
					// allBtnText: '不限',
					typeList: [
						{
							shortName: '不限',
							id: ''
						}
					]
				},
				jobNature: {
					label: '工作性质：',
					porp: 'jobNature',
					typeList: [
						{
							shortName: '不限',
							id: ''
						}
					]
				},
				peopleNumCode: {
					label: '公司规模：',
					porp: 'peopleNumCode',
					typeList: [
						{
							shortName: '不限',
							id: ''
						}
					]
				}
			}, //筛选条件数组
			filterForm: {
				industryField: [], //行业
				cityCode: [],
				expectedSalary: [], //期望薪资
				education: [], //学历要求
				workExperience: [], //工作经验
				jobNature: [], //工作性质
				peopleNumCode: [], //规模
				postType: [] //岗位类型
			}, //筛选条件表单，用于接收筛选条件数组的选择结果
			keyword: '', //关键字
			pageNum: 1, //当前页码
			total: 0, //数据总量
			loading: false, //列表加载动画
			list: [] // 推荐就业岗位
		};
	},
	mounted() {
		this.getJobCoPostList();
		this.handlerGetRegion('cityCode'); //获取地区信息
		this.findSysCode();
		// this.findSysCode('post_job_nature', 'jobNature'); //工作性质
		// this.findSysCode('post_job_type', 'postType'); //岗位类型
		// this.findSysCode('post_work_experience', 'workExperience'); //工作经验
		// this.findSysCode('post_company_scale', 'peopleNumCode'); //人数规模
		// this.findSysCode('post_industry_area', 'industryField'); //行业
		// this.findSysCode('post_salary_expectation', 'expectedSalary'); //期望薪资
		// this.findSysCode('post_education', 'education'); //学历要求
	},
	methods: {
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode(code, codeValue) {
			// let param = {
			// 	sysAppCode: code
			// };
			// await this.$api.employment_api.findSysCode(param).then(res => {
			// 	this.filterList[codeValue].typeList = this.filterList[codeValue].typeList.concat(
			// 		res?.results || []
			// 	);
			// });
			const dicts = await getDictionaryByCode([
				'post_job_nature',
				'post_job_type',
				'post_work_experience',
				'post_company_scale',
				'post_industry_area',
				'post_salary_expectation',
				'post_education'
			]);
			this.filterList['jobNature'].typeList = this.filterList['jobNature'].typeList.concat(
				dicts.post_job_nature || []
			); //工作性质
			this.filterList['postType'].typeList = this.filterList['postType'].typeList.concat(
				dicts.post_job_type || []
			); //岗位类型
			this.filterList['workExperience'].typeList = this.filterList[
				'workExperience'
			].typeList.concat(dicts.post_work_experience || []); //工作经验
			this.filterList['peopleNumCode'].typeList = this.filterList['peopleNumCode'].typeList.concat(
				dicts.post_company_scale || []
			); //人数规模
			this.filterList['industryField'].typeList = this.filterList['industryField'].typeList.concat(
				dicts.post_industry_area || []
			); //行业
			this.filterList['expectedSalary'].typeList = this.filterList[
				'expectedSalary'
			].typeList.concat(dicts.post_salary_expectation || []); //期望薪资
			this.filterList['education'].typeList = this.filterList['education'].typeList.concat(
				dicts.post_education || []
			); //学历要求
		},
		/**
		 * @description 获取地区信息
		 */
		async handlerGetRegion(code) {
			await this.$api.personal_api.getRegion().then(res => {
				const { results } = res;
				// 获取格式化数据
				this.regionOptions = this.handleInitRegionData(results, 100000000000);
				this.filterList[code].typeList = this.filterList[code].typeList.concat(
					this.regionOptions || []
				);
			});
		},
		// 格式化行政区域数据格式
		handleInitRegionData(data, parentId) {
			const regTree = [];
			data.forEach(reg => {
				if (reg.parentId == parentId) {
					const regObject = {
						value: reg.id,
						label: reg.name
					};
					const childrenData = this.handleInitRegionData(data, reg.id);
					if (childrenData.length > 0) {
						regObject.children = childrenData;
					}
					regTree.push(regObject);
				}
			});
			return regTree;
		},
		/**
		 * @descrtiption 岗位分页接口
		 * */
		getJobCoPostList() {
			this.loading = true;

			let param = {
				pageNum: this.pageNum,
				pageSize: 10,
				keyword: this.keyword, // 关键字
				auditStatus: 1, //审核状态(0.待审核、1.审核通过、2.驳回)
				cityCode: this.filterForm?.cityCode?.value || '', //城市选择
				industryField: this.filterForm?.industryField?.cciValue || '', //行业领域
				education: this.filterForm?.education?.cciValue || '', //学历要求
				jobNature: this.filterForm?.jobNature?.cciValue || '', //工作性质
				expectedSalary: this.filterForm?.expectedSalary?.cciValue || '', //预期工资
				peopleNumCode: this.filterForm?.peopleNumCode?.cciValue || '', //公司规模
				workExperience: this.filterForm?.workExperience?.cciValue || '', //工作经验
				postType: this.filterForm?.postType?.cciValue || '' //岗位类型
			};
			this.$api.employment_api
				.jobCoPostList(param)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 简历投递
		 * */
		jobSend(item) {
			if (!this.isShopLogin()) {
				return;
			}
			// 是否已投递(true 已投递；false 未投递)
			if (item.jobSend) {
				return;
			}
			this.loading = true;
			// 岗位id
			const param = {
				postId: item.id
			};
			this.$api.employment_api
				.jobSend(param)
				.then(res => {
					if (res.success) {
						this.$message.success('提交成功');
						this.getJobCoPostList();
					} else {
						this.$message.error(res.msg);
					}
					this.loading = false;
				})
				.catch(res => {
					this.loading = false;
					this.$message.error('提交失败');
				});
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
			this.getJobCoPostList();
		},
		filterChange(form) {
			this.pageNum = 1;
			this.getJobCoPostList();
		},
		/**
		 * @description 类型点击事件
		 */
		typeClick(item, group) {
			if (group.indexOf(item) != '-1') return;
			group.push(item);
		},
		/**
		 * @description 类型删除事件
		 */
		deleteValue(item, group) {
			// this.form.courseClassId = '';
			let index;
			for (let i = 0; i < group.length; i++) {
				if (item == group[i]) {
					index = i;
					break;
				}
			}
			group.splice(index, 1);
			// this.lessonListFn();
		},
		/**
		 * @description 关键字搜索事件
		 */
		/**
		 * @description 关键字搜索事件
		 */
		search(name) {
			this.keyword = name;
			this.pageNum = 1;
			this.getJobCoPostList();
		},
		/**
		 * @description 关键字重置事件
		 */
		reset() {
			this.search('');
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		/**
		 * @description 岗位分页接口
		 * */
		jobCoPostList() {
			// 岗位id
			let param = { id: '' };
			this.$api.employment_api.jobCoPostList(param).then(res => {});
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;

// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;

	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}

.main {
	width: $max-width;
	margin: 20px auto 60px;
}

.list-box {
	width: 100%;
	background: #ffffff;
	padding: 20px;
	padding-bottom: 30px;
	margin-top: 20px;

	.list-item {
		width: 100%;
		border-bottom: 1px solid #e8eaf0;
		padding: 18px 10px;
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
		height: 140px;
		cursor: pointer;

		.item-base {
			width: calc(100% - 150px);
			flex-shrink: 0;

			.item-box {
				display: flex;
				// width: 50%;
			}

			.name {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
			}

			.position {
				display: inline-block;
				font-size: 12px;
				color: #999999;
				margin-left: 11px;
				vertical-align: bottom;

				.ellipsis {
					max-width: 450px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					display: inline-block;
					line-height: 1;
				}
			}

			.tag-img {
				height: 24px;
			}

			.price {
				display: block;
				font-size: 16px;
				line-height: 16px;
				color: #fe574a;
				// margin-top: 7px;
			}

			.company-base,
			.position-base {
				width: 50%;
				flex-shrink: 0;
				margin-right: 10px;
			}

			.company-base {
				font-size: 12px;
				line-height: 12px;
				color: #666666;
			}

			.com-box {
				display: flex;
				align-items: center;
			}

			.com-img {
				width: 30px;
				height: 30px;
				margin-right: 13px;
				flex-shrink: 0;
				object-fit: cover;
			}

			.com-desc {
				margin-top: 9px;
			}
		}

		.labels-box {
			.label {
				display: inline-block;
				height: 22px;
				padding: 0px 15px;
				background: #f5f5f5;
				border-radius: 4px;
				font-size: 12px;
				color: #666666;
				line-height: 22px;
				margin-right: 10px;
				margin-top: 10px;
			}
		}

		.btns-box {
			width: 100px;
			margin-left: 46px;
			flex-shrink: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.online-btn,
			.contact-btn {
				width: 100px;
				height: 30px;
				border-radius: 15px;
				font-size: 14px;
				margin: 10px 0;
				border: 1px solid #0076e8;
			}

			.send-btn {
				color: #999;
				border: 1px solid #999;
			}

			.online-btn {
				background: #0076e8;
				color: #ffffff;
			}

			.contact-btn {
				background: rgba(255, 255, 255, 0);

				color: #0076e8;
			}
		}
	}
}

.pagination {
	width: 100%;
	text-align: center;
	margin-top: 30px;

	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;

		> span {
			line-height: 40px;
		}
	}

	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}

	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
