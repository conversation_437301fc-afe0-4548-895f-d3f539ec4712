<template>
	<div class="collect_shop">
		<div v-for="(item, index) in list" :key="index" class="list" @click="handleList(item)">
			<div class="name nth1">{{ item.SHOP_NAME }}</div>
			<!-- <div class="tag nth1">主营类目：{{ item.tag }}</div> -->
			<div class="img-box">
				<div v-for="(t, i) in item.images" :key="i" class="img">
					<img :src="$judgeFile(t.URL)" alt="" />
					<div class="title">{{ t.NAME }}</div>
				</div>
			</div>
			<div class="adr">
				<i class="el-icon-location-outline"></i>
				{{ item.SHOP_DETAIL }}
			</div>
			<el-button type="primary" @click.stop="goShop(item)">进店逛逛</el-button>
			<div v-show="multiple" class="multiple" :class="getClass(item)">
				<span class="el-icon-check"></span>
			</div>
			<div v-show="!multiple" class="del el-icon-delete" @click.stop="handleDelete(item)"></div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CollectShop',
	props: {
		list: {
			type: Array,
			default: () => {
				return [];
			}
		},
		multiple: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			checkList: []
		};
	},
	methods: {
		// 删除
		handleDelete(i) {
			this.$confirm('确定要取消对此店铺的关注吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$emit('handleDelete', i);
				})
				.catch(() => {});
		},
		// 进入店铺
		goShop(i) {
			if (this.multiple) {
				return;
			}
			this.$emit('goShop', i);
		},
		// 点击详情
		handleList(i) {
			if (this.multiple) {
				for (let a = 0; a < this.checkList.length; a += 1) {
					if (i.ID === this.checkList[a].ID) {
						this.checkList.splice(a, 1);
						this.$forceUpdate();
						return;
					}
				}
				this.checkList.push(i);
				this.$forceUpdate();
			} else {
				this.$emti('handleList', i);
			}
		},
		// 判断是否选中
		getClass(i) {
			for (let a = 0; a < this.checkList.length; a += 1) {
				if (i.ID == this.checkList[a].ID) {
					return 'act';
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.nth1 {
	overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
	text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
	white-space: nowrap; /*文本不换行*/
}
.collect_shop {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	.list {
		margin: 24px 0 0 12px;
		width: 447px;
		height: 314px;
		border: 1px solid #eeeeee;
		box-sizing: border-box;
		padding: 15px 20px;
		position: relative;
		overflow: hidden;
		.name {
			padding-left: 2px;
			font-size: 16px;
			font-weight: 500;
			color: #262626;
			line-height: 24px;
		}
		.tag {
			padding-left: 2px;
			margin-top: 7px;
			font-size: 14px;
			font-weight: 400;
			color: #bfbfbf;
			line-height: 22px;
		}
		.img-box {
			display: flex;
			margin-top: 21px;
			.img {
				width: 118px;
				height: 118px;
				margin-right: 15px;
				position: relative;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
				.title {
					width: 100%;
					position: absolute;
					height: 28px;
					background: rgba(0, 0, 0, 0.5);
					font-size: 12px;
					font-weight: 400;
					color: #ffffff;
					line-height: 28px;
					text-align: center;
					bottom: 0;
					left: 0;
				}
			}
		}
		.adr {
			padding-left: 2px;
			margin-top: 7px;
			font-size: 12px;
			font-weight: 400;
			color: #8c8c8c;
		}
		::v-deep.el-button {
			margin: 19px 0 0 8px;
		}
		.del {
			position: absolute;
			width: 24px;
			height: 24px;
			background: rgba(0, 0, 0, 0.45);
			top: -24px;
			right: 0;
			text-align: center;
			line-height: 24px;
			color: #fff;
			transition: all 0.3s;
			cursor: pointer;
		}
		.multiple {
			width: 62px;
			height: 57px;
			margin: 0 auto;
			background: conic-gradient(from 135deg, rgba(0, 0, 0, 0.45) 90deg, transparent 90deg);
			top: -29px;
			right: -31px;
			position: absolute;
			transform: rotate(45deg);
			span {
				margin: 38px 0 0 24px;
				color: #fff;
				transform: rotate(-45deg);
				font-weight: 700;
			}
		}
		.act {
			background: conic-gradient(from 135deg, var(--brand-6, '#ca3f3b') 90deg, transparent 90deg);
		}
	}
	.list:hover {
		box-shadow: 0px 3px 16px 0px rgba(66, 89, 158, 0.15);
		opacity: 1;
		border: 1px solid #eeeeee;
		.del {
			top: 0;
		}
		.btm {
			bottom: 0;
		}
	}
}
</style>
