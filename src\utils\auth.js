import Cookies from 'js-cookie';

/**获取该项目的token*/
const TokenKey = 'ybzycode';

export function getToken() {
	return Cookies.get(TokenKey);
}

export function setToken(token) {
	return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
	return Cookies.remove(TokenKey);
}
/**设置有过期时间的cookie*/
export function setCookie1(key, value, expires) {
	return Cookies.set(`${key}`, value, expires);
}
/**获取cookie*/
export function getCookie(key) {
	return Cookies.get(`${key}`);
}
/**删除cookie*/
export function delCookie(key) {
	return Cookies.remove(`${key}`);
}
