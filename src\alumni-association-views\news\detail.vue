<template>
	<div class="detail">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="contentBox">
			<div class="content">
				<div class="left">
					<detailContent :detail-id="detailId"></detailContent>
				</div>
				<div class="right">
					<div class="topAboutNotice">
						<div class="titleNotice">通知公告</div>
						<div
							v-if="alumnaNoticeList && alumnaNoticeList.length"
							v-loading="alumnaNoticeListLoading"
							style="width: 100%; height: 100%"
						>
							<div
								v-for="(item, index) in alumnaNoticeList"
								:key="index"
								class="noticeContent"
								@click="toDetail('notice', item.id)"
							>
								<div class="leftAboutTime">
									<div class="top">{{ item.publishTime | dateHandel('day') }}</div>
									<div class="bottom">{{ item.publishTime | dateHandel }}</div>
								</div>
								<div class="rightAboutNoticeContent">
									<div class="top">{{ item.title }}</div>
									<div class="bottom">
										{{ item.abstract }}
									</div>
								</div>
							</div>
						</div>
						<Empty v-else :tips="'暂无数据'" />
					</div>
					<div v-loading="activeloading" class="bottomAboutActive">
						<div class="titleActive">校友活动</div>
						<div
							v-if="activeList && activeList.length"
							v-loading="activeloading"
							style="width: 100%; height: 100%"
						>
							<div
								v-for="(item, index) in activeList"
								:key="index"
								class="active"
								@click="toDetail('active', item.id)"
							>
								<div class="pic">
									<img :src="item.pictureurl" alt="" />
								</div>
								<div class="titleActiveContent">{{ item.activity_title }}</div>
								<div class="addirse">
									<i class="el-icon-location-outline icon"></i>
									{{ item.activity_address }}
								</div>
								<div class="time">
									<i class="el-icon-time icon"></i>
									{{ handleTimeChange(item.activity_start_time) }}
									{{ cutString(item.activity_time) }}~
									{{ cutStringEnd(item.activity_time) }}
									{{ handleTimeChange(item.activity_end_date) }}
								</div>
								<div :class="item.status == (1 | 4) ? 'btn ' : 'btn color-grey'">
									{{ statuStranslate(item.status) }}
									<i class="el-icon-arrow-right"></i>
									<i class="el-icon-arrow-right"></i>
								</div>
							</div>
						</div>
						<Empty v-else :tips="'暂无数据'" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import detailContent from './conponents/detail-content';
export default {
	components: {
		subBreadcrumb,
		detailContent
	},
	data() {
		return {
			detailId: '',
			alumnaNoticeList: [],
			alumnaNoticeListLoading: false,
			activeloading: false,
			activeList: []
		};
	},
	watch: {
		$route: {
			handler: function (route) {
				this.detailId = this.$route.query.id;
			},
			immediate: true
		}
	},
	mounted() {
		this.getInterfaceDataList(5);
		this.getInformation('alumnaNotice', 'alumnaNoticeList', 5);
	},
	methods: {
		/**
		 * @description 获取列表
		 * */
		getInformation(code, list, listSize) {
			this[list + `Loading`] = true;
			let data = {
				nodeCode: code,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				pageNum: 1,
				pageSize: listSize || 10
			};
			this.$api.information_api.paging(data).then(res => {
				if (list == 'contentList') {
					this.contentList = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				} else {
					this[list] = res?.results?.records || [];
				}

				this[list + `Loading`] = false;
			});
		},
		// 跳转详情页面
		toDetail(category, id) {
			// 显示类型(2:普通、1:外链)
			// if (item.infoType == 2) {
			// 	this.jumpPage(`/information-detail?id=${item.id}&code=${item.nodeCode}`);
			// } else {
			// 	window.open(item.linkUrl);
			// }
			switch (category) {
				case 'notice':
					this.jumpPage(`alumni-news-detail?updateTitle=通知公告详情页&id=${id}&code=alumnaNotice`);
					break;
				case 'active':
					this.jumpPage(`alumni-association-detail?id=${id}`);
					break;
				default:
					break;
			}
		},
		/**
		 * @description 获取列表
		 * */
		getInterfaceDataList(listSize) {
			this.activeloading = true;
			let newData = new FormData();
			newData.append('pageNumber', 1);
			let data = {
				'pager.pageNumber': 1,
				'pager.pageSize': listSize,
				'queryParams.status': '0,1,2',
				'queryParams.tenementId': this.$tenantId,
				'queryParams.plateCode': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
				// 'queryParams.activityType': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
			};

			this.$api.alumni_association_api.alumniActiveList(data).then(res => {
				this.activeList = res.data.list;
				this.activeloading = false;
			});
		},
		//把时间戳转换成时间格式
		handleTimeChange(time) {
			let timestamp = time;
			let date = new Date(timestamp);
			let Year = date.getFullYear();
			let Moth = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
			let Day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
			let GMT = Year + '-' + Moth + '-' + Day;
			return GMT;
		},
		//截取字符串
		cutString(string) {
			let str = string;
			let arr = str.split('-');
			return arr[0];
		},
		//截取字符串
		cutStringEnd(string) {
			let str = string;
			let arr = str.split('-');
			return arr[1];
		},
		jumpPage(url) {
			this.$router.push(url);
		},
		//状态
		statuStranslate(status) {
			switch (status) {
				case 0:
					return '待开放';
				case 1:
					return '预约中';
				case 2:
					return '已结束';
				case 3:
					return '已取消';
				case 4:
					return '已预约';
				default:
					break;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: var(--brand-6, #0076e8);
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #c4c4c6;
	border-radius: 50%;
}
::v-deep .el-carousel__indicators--outside {
	display: flex;
	justify-content: right;
	align-items: center;
	position: absolute;
	left: 0;
	bottom: 0;
	height: 40px;
	width: 100%;
	padding: 0 20px 0 0;
	background: rgb(31, 31, 31, 0.1);
}
.pageActive {
	background: #0076e8 !important;
	color: #ffffff !important;
}
input[type='number'] {
	-moz-appearance: textfield;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.contentBox {
	width: 100%;
	background: #f5f5f5;
	display: flex;
	justify-content: center;
	.content {
		width: $max-width !important;
		height: 100%;
		display: flex;
		padding: 20px 0 0 0;
		.left {
			width: 900px;
			height: 100%;
		}
		.right {
			width: 360px;
			padding: 0 0 0 20px;
			.topAboutNotice {
				width: 100%;
				padding: 20px 18px 30px 20px;
				background: #ffffff;
				border-top: 3px solid #0076e8;
				.titleNotice {
					font-size: 16px;
					font-weight: bold;
					color: #0076e8;
					line-height: 24px;
				}
				.noticeContent {
					cursor: pointer;
					margin: 20px 0 0 0;
					height: 80px;
					width: 100%;
					display: flex;
					.leftAboutTime {
						width: 80px;
						height: 80px;
						border: 1px solid #0076e8;
						border-radius: 8px;
						overflow: hidden;
						.top {
							width: 100%;
							height: 50%;
							font-size: 18px;
							font-weight: 400;
							color: #0076e8;
							text-align: center;
							line-height: 40px;
						}
						.bottom {
							width: 100%;
							height: 50%;
							font-size: 18px;
							font-weight: 400;
							color: #ffffff;
							text-align: center;
							background: #0076e8;
							line-height: 40px;
						}
					}
					.rightAboutNoticeContent {
						flex: 1;
						height: 100%;
						padding: 0 0 0 10px;
						.top {
							width: 200px;
							font-size: 14px;
							font-weight: bold;
							color: #333333;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
						.bottom {
							padding: 10px 0 0 0;
							font-size: 14px;
							font-weight: 400;
							color: #7a8392;
							line-height: 24px;
							white-space: normal;
							text-overflow: ellipsis;
							overflow: hidden;
							display: -webkit-box;
							/* 限制在一个块元素显示的文本的行数 */
							/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
							-webkit-line-clamp: 2;
							/* 设置或检索伸缩盒对象的子元素的排列方式 */
							-webkit-box-orient: vertical;
						}
					}
				}
			}
			.bottomAboutActive {
				margin: 20px 0 20px 0;
				width: 100%;
				padding: 20px 18px 30px 20px;
				background: #ffffff;
				border-top: 3px solid #0076e8;
				.titleActive {
					font-size: 16px;
					font-weight: bold;
					color: #0076e8;
					line-height: 24px;
				}
				.active {
					margin: 24px 0 0 0;
					width: 100%;
					.pic {
						width: 100%;
						height: 174px;
						overflow: hidden;
						border-radius: 8px;
						img {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}
					}
					.titleActiveContent {
						font-size: 18px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #333333;
						line-height: 40px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					.addirse {
						font-size: 14px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #7a8392;
						line-height: 28px;
					}
					.time {
						font-size: 14px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #7a8392;
						line-height: 28px;
					}
					.icon {
						font-size: 18px;
					}
					.btn {
						margin: 10px 0 0 0;
						display: inline-block;
						height: 30px;
						background: #0076e8;
						border-radius: 4px;
						font-size: 14px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #ffffff;
						line-height: 30px;
						padding: 0 6px;
						cursor: pointer;
					}
				}
			}
		}
	}
}
.pagination {
	text-align: center;
	margin: 60px 0 30px 0;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
.color-grey {
	background: #dcdcdc !important;
}
</style>
