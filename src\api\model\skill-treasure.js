import request from '@/utils/treasure_request';
// import xhr_request from '@/utils/xhr_request';
// import Qs from 'qs';
// 获取宝库的分类
export function memberStoryTypeList(data) {
	return request({
		url: '/api/activity-web/actStoryType/memberStoryTypeList',
		method: 'GET',
		params: data
	});
}
// 获取宝库的列表数据
export function memberStoryList(data) {
	return request({
		url: '/api/activity-web/actStory/memberStoryList',
		method: 'GET',
		params: data
	});
}
// 宝库详情接口
export function actStoryInfo(data) {
	return request({
		url: `/api/activity-web/actStory/info/${data}`,
		method: 'GET'
	});
}
// 宝库详情里面的关联商品列表数据
export function getProductsByIds(data) {
	return request({
		url: `/api/trip-api/api/product/getProductsByIds`,
		method: 'GET',
		params: data
	});
}
// 获取评论列表
export function interactFeedbackList(data) {
	return request({
		url: `/api/activity-web/interactFeedback/list`,
		method: 'GET',
		params: data
	});
}
// 发布评论
export function addFeedback(data) {
	// xhr_request({
	// 	type: 'post',
	// 	dataType: 'json',
	// 	data: JSON.stringify(data),
	// 	url: 'activity-web/interactFeedback/addFeedback',
	// }).then().catch()
	return request({
		url: `/api/activity-web/interactFeedback/addFeedback`,
		method: 'POST',
		data
	});
	// fetch(baseUrl+`activity-web/interactFeedback/addFeedback`, {
	//         method: 'POST',
	//         headers: {
	//             'Content-Type': 'application/json'
	//         },
	//         body: JSON.stringify(data),
	//         mode:'cors'
	//     })
	// 	.then(res => res.json())
	// 	.then(res => console.log(res));
}
// 游玩攻略、攻略评论，点赞/收藏
export function memberInteract(data) {
	return request({
		url: `/api/activity-web/interact/memberInteract`,
		method: 'POST',
		params: data
	});
}
// 游记攻略-收藏列表
export function getCollectStory(data) {
	return request({
		url: `/api/activity-web/actStory/getCollectStory`,
		method: 'GET',
		params: data
	});
}

// 获取用户信息
export function getUserInfo(data) {
	return request({
		url: `/api/trip-api/api/user/getUserInfo`,
		method: 'POST',
		params: data
	});
}
// 会员修改信息接口
export function updateUserInfo(data) {
	return request({
		url: `/api/trip-api/api/user/updateUserInfo`,
		method: 'POST',
		params: data
	});
}
// 游玩攻略-关注数量、粉丝数、点赞、收藏数量
export function getCountInteract(data) {
	return request({
		url: `/api/activity-web/interact/getCountInteract`,
		method: 'POST',
		params: data
	});
}
// 发布
export function actStoryPublish(data) {
	return request({
		url: `/api/activity-web/actStory/publish`,
		method: 'POST',
		data
	});
}
// 编辑
export function actStoryUpdate(data) {
	return request({
		url: `/api/activity-web/actStory/memberUpdate`,
		method: 'POST',
		data
	});
}
// 图片上传
export function singleFile(data) {
	return request({
		url: `/api/supply-web/fileUpload/singleFile`,
		method: 'post',
		type: 'form',
		data
	});
}
// 直播地址获取和检查
export function getLivePathAndCheck(data) {
	return request({
		url: `/api/supply-web/liveCtrl/getUrl`,
		method: 'post',
		data
	});
}
// 删除
export function actStoryDel(data) {
	return request({
		url: `/api/activity-web/actStory/del`,
		method: 'GET',
		params: data
	});
}
// 游玩攻略-关注列表
export function getInteractList(data) {
	return request({
		url: `/api/activity-web/interact/getInteractList`,
		method: 'POST',
		params: data
	});
}
// 游玩攻略-粉丝列表
export function getFollowerList(data) {
	return request({
		url: `/api/activity-web/interact/getFollowerList`,
		method: 'POST',
		params: data
	});
}
// 游玩攻略-点赞和收藏列表
export function getLaudCollectList(data) {
	return request({
		url: `/api/activity-web/interact/getLaudCollectList`,
		method: 'POST',
		params: data
	});
}
// 游玩攻略-通知-评论列表
export function getInteractFeedbackList(data) {
	return request({
		url: `/api/activity-web/interact/getInteractFeedbackList`,
		method: 'POST',
		params: data
	});
}
