<template>
	<div class="title">
		<h3>
			<img src="@/assets/img/Group_1221.png" alt="" />
			<span>{{ title }}</span>
			<img src="@/assets/img/Group_1221.png" alt="" />
		</h3>
	</div>
</template>

<script>
export default {
	name: 'Title',
	props: {
		title: {
			type: String,
			default: ''
		}
	}
};
</script>
<style lang="scss" scoped>
.title {
	padding: 56px 0 27px;
	text-align: center;
	font-size: 30px;
	font-family: Source Han Sans SC-Medium, Source Han Sans SC;
	font-weight: 500;
	color: #262626;
	img {
		margin: 0 27px;
	}
}
</style>
