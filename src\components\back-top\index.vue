<template>
	<div v-if="show" class="back" @click="top">
		<img
			:src="icon"
			alt=""
			class="icon"
			:style="{ width: iconWidth || '24px', height: iconHeight || '24px' }"
		/>
		<span class="name">{{ name }}</span>
	</div>
</template>

<script>
export default {
	name: 'BackTop',
	props: {
		name: {
			type: String,
			default: '返回顶部'
		},
		icon: {
			type: String,
			default: require('@/assets/alumni-association-images/back-icon.png')
		},
		iconWidth: {
			type: String,
			default: ''
		},
		iconHeight: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			show: false
		};
	},
	computed: {},
	created() {
		window.addEventListener('scroll', event => {
			//此处执行事件
			if (document.documentElement.scrollTop > 200) {
				this.show = true;
			} else {
				this.show = false;
			}
		});
	},
	methods: {
		/**处理按钮事件*/
		top() {
			window.scrollTo(0, 0);
		}
	}
};
</script>

<style scoped lang="scss">
.back {
	width: 68px;
	height: 78px;
	padding: 19px 0px 11px;
	background: #ffffff;
	box-shadow: 0px 0px 10px 0px rgba(209, 209, 209, 0.75);
	border-radius: 4px;
	position: fixed;
	right: 30px;
	top: 60%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	.icon {
		margin-bottom: 13px;
	}
	.name {
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #666666;
	}
}
</style>
