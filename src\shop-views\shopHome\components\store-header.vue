<template>
	<div class="StoreHeader">
		<div class="left">
			<div class="logo">
				<img v-if="info.shopLog" :src="$judgeFile(info.shopLog)" alt="" />
				<img v-else src="@/assets/shop-images/SHOP_LOG.png" alt="" />
			</div>
			<div>
				<div class="name">
					<div class="name-title">{{ info.shopName }}</div>
					<div class="button" @click="setFollowState(info.followState)">
						<AlIcon
							v-if="info.followState"
							size="16"
							name="el-icon-star-on"
							color="#0076e8"
						></AlIcon>
						<AlIcon v-else name="el-icon-star-off" size="16" color="#0076e8"></AlIcon>
						<div class="button-text">{{ info.followState ? '已收藏' : '收藏店铺' }}</div>
					</div>
				</div>
				<div class="tags">
					<span class="lable">主营品类：</span>
					<span>{{ info.businessType }}</span>
				</div>
			</div>
		</div>
		<div class="right">
			<el-button icon="el-icon-service" round @click="setDialog">联系客服</el-button>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="info"
			:dialog-form-visible="dialogMessageVisible"
		/>
		<el-dialog title="收货地址" :visible.sync="dialogFormVisible">
			<div class="dialog-content">
				<div class="left">
					<div class="left-content"></div>
					<div class="button">发送</div>
				</div>
				<div class="right">
					<div class="right-top">
						<div>
							<img src="" alt="" />
						</div>
						<div>
							<div>知洛洛旗舰店</div>
							<div>进入店铺</div>
						</div>
					</div>
					<div class="goods">
						<div>正在咨询的宝贝</div>
						<div></div>
					</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import contactMessage from '@/components/public/contactMessage.vue';
import { getCookie } from '@/utils/auth';

export default {
	name: 'StoreHeader',
	components: {
		contactMessage
	},
	props: {
		shopInfo: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			info: {},
			dialogFormVisible: false,
			dialogMessageVisible: false,
			form: {
				name: '',
				region: ''
			},
			userId: getCookie('user_id'),
			collection: false
		};
	},
	created() {
		this.info = this.shopInfo;
		this.info.SHOP_NAME = this.info.shopName;
		this.info.SHOP_LOG = this.info.shopLog;
		this.info.SELLER_ID = this.info.sellerId;
		this.info.isGoods = true;
		this.shopId = this.$route.query?.id || '';
		// this.userId = this.isShopLogin();
		let data = {
			siteId: this.getSiteId(),
			targetId: this.shopId,
			userId: this.userId
		};
		if (this.userId) {
			this.$api.shop_api.isCollection(data).then(res => {
				this.collection = res.result.isCollection;
			});
		}
	},
	methods: {
		setDialog() {
			this.userId = this.isShopLogin();
			if (this.userId) {
				this.dialogMessageVisible = true;
			}
		},
		setFollowState(item) {
			this.userId = this.isShopLogin();
			if (this.userId) {
				this.$emit('setFollowState', item);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.StoreHeader {
	width: 100%;
	height: 100px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	padding: 21px 0 21px 16px;
	display: flex;
	justify-content: space-between;
	.left {
		display: flex;
		align-items: center;
		.logo {
			width: 50px;
			height: 50px;
			opacity: 1;
			border: 1px solid #cfd8ec;
			border-radius: 50%;
			margin-right: 12px;
			img {
				width: 50px;
				height: 50px;
				opacity: 1;
				border: 1px solid #cfd8ec;
				border-radius: 50%;
			}
		}
		.name {
			align-items: center;
			line-height: 32px;
			font-size: 18px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #000000;
			padding-right: 35px;
			margin-bottom: 5px;
			display: flex;
			&-title {
				overflow: hidden; //超出的文本隐藏
				text-overflow: ellipsis; //溢出用省略号显示
				display: -webkit-box;
				-webkit-line-clamp: 1; // 超出多少行
				-webkit-box-orient: vertical;
				margin-right: 46px;
			}
			.button {
				height: 30px;
				display: flex;
				align-items: center;
				z-index: 99;
				line-height: 18px;
				background: #ffffff;
				border-radius: 19px 19px 19px 19px;
				opacity: 1;
				border: 1px solid var(--brand-6, '#0076e8');
				padding: 5px 15px;
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#0076e8');
				cursor: pointer;
				&-text {
					margin-left: 5px;
				}
			}
		}
		.tags {
			overflow: hidden; //超出的文本隐藏
			text-overflow: ellipsis; //溢出用省略号显示
			display: -webkit-box;
			-webkit-line-clamp: 1; // 超出多少行
			-webkit-box-orient: vertical;
			width: 348px;
			span {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
				line-height: 22px;
			}
			.lable {
				color: #8c8c8c;
				line-height: 22px;
			}
		}
	}
	.right {
		display: flex;
		align-items: center;
		z-index: 99;
	}
}
</style>
