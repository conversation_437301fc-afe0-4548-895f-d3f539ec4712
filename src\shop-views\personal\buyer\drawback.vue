<template>
	<div v-loading="loading" class="drawback">
		<div v-if="isHead()" class="head" :style="{ width: main ? '964px' : '1200px' }">
			<i class="el-icon-s-home icon" @click="$router.push({ path: '/' })"></i>
			<el-breadcrumb separator-class="el-icon-arrow-right">
				<el-breadcrumb-item :to="{ path: '/personal' }">买家中心</el-breadcrumb-item>
				<el-breadcrumb-item>
					<span class="cur" @click="$router.go(-1)">我的订单</span>
				</el-breadcrumb-item>
				<el-breadcrumb-item>
					<span class="red">申请退款</span>
				</el-breadcrumb-item>
			</el-breadcrumb>
		</div>
		<div class="main" :style="{ width: !main ? '1200px' : '100%' }">
			<div class="title">申请退款</div>
			<div class="step">
				<div class="step-item" :class="step >= 0 ? 'act' : ''">
					<p>1</p>
					买家申请仅退款
					<div class="line"></div>
				</div>
				<div class="step-item" :class="step >= 1 ? 'act' : ''">
					<p>2</p>
					卖家处理退款申请
					<div class="line"></div>
				</div>
				<div class="step-item" :class="step >= 2 ? 'act' : ''">
					<p>3</p>
					退款完毕
				</div>
			</div>
			<div class="content">
				<el-form
					v-show="step == 0"
					ref="form"
					:model="form"
					:rules="rules"
					label-width="95px"
					class="form"
				>
					<el-form-item
						v-for="(item, index) in details.ORDER_GOODS_DETAILS"
						:key="index"
						label="退款商品："
					>
						<div class="img-box">
							<img :src="$judgeFile(item.COVER_URL)" alt="" />
							<div class="rf">
								<div class="title nth2">{{ item.NAME }}</div>
								<div class="num">商品数量：{{ item.NUMBER }}</div>
								<div class="money">
									商品总价：
									<span>¥{{ item.PRICE.toFixed(2) }}</span>
								</div>
							</div>
						</div>
					</el-form-item>
					<el-form-item label="退款原因：" prop="reasonIds">
						<el-select
							v-model="form.reasonIds"
							placeholder="退款原因"
							multiple
							@change="changeSelect"
						>
							<el-option
								v-for="(item, index) in option"
								:key="index"
								:label="item.value"
								:value="item.key"
							></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="退款金额：">
						<div class="price">¥{{ details.ORDER_MONEY.toFixed(2) }}</div>
					</el-form-item>
					<el-form-item label="退款说明：" prop="remark">
						<el-input
							v-model="form.remark"
							type="textarea"
							:rows="4"
							placeholder="请输入退款说明"
							resize="none"
						></el-input>
					</el-form-item>
					<el-form-item label="上传图片：">
						<el-upload
							:action="updUrl"
							list-type="picture-card"
							:on-remove="handleRemove"
							:file-list="fileList"
							:on-success="success"
							:before-upload="
								() => {
									btnLoading = true;
								}
							"
							:multiple="false"
							accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PBG,.GIF,.BMP"
						>
							<i class="el-icon-plus"></i>
						</el-upload>
					</el-form-item>
					<div class="btn-box">
						<el-button size="mini" type="primary" :loading="btnLoading" @click="refund">
							提交
						</el-button>
						<el-button :loading="btnLoading" @click="$router.go(-1)">返回</el-button>
					</div>
				</el-form>
				<div v-show="step != 0" class="left">
					<div class="state">
						<div class="lf">
							<AlIcon :name="icon" size="40" color="#ca3f3b"></AlIcon>
						</div>
						<div class="rf">
							<div v-if="details.refunds && details.refunds[0].refundState == 0" class="name">
								申请中
							</div>
							<div v-if="details.refunds && details.refunds[0].refundState == 1" class="name">
								退款中
							</div>
							<div v-if="details.refunds && details.refunds[0].refundState == 2" class="name">
								退款成功
							</div>
							<div v-if="details.refunds && details.refunds[0].refundState == 3" class="name">
								退款失败
							</div>
							<div v-if="details.refunds && details.refunds[0].refundState == 9" class="name">
								不同意退款
							</div>
							<div class="btm">
								申请退款时间：
								<span>{{ details.refunds ? details.refunds[0].createTime : '-' }}</span>
							</div>
						</div>
					</div>
					<div class="money">
						退款金额：
						<span>
							¥
							{{ details.refunds ? details.refunds[0].refundMoney.toFixed(2) : '0.00' }}
						</span>
					</div>
					<div class="voucher">
						<div class="lf">退款凭证：</div>
						<div class="rf">
							<img
								v-for="(item, index) in imgList"
								:key="index"
								:src="$judgeFile(item)"
								alt=""
								class="list"
							/>
						</div>
					</div>
					<div class="notes">
						<div class="title">退款记录</div>
						<div class="list-box">
							<div class="top">
								<span v-if="details.refunds && details.refunds[0].refundType == 0" class="red">
									[整单退款]
								</span>
								<span v-else-if="details.refunds && details.refunds[0].refundType == 1" class="red">
									[部分退款]
								</span>
								<span v-else-if="details.refunds && details.refunds[0].refundType == 2" class="red">
									[重复付款退款]
								</span>
								<span v-else-if="details.refunds && details.refunds[0].refundType == 3" class="red">
									[强制退款]
								</span>
								<span v-if="details.refunds && details.refunds[0].isacc == 0" class="green">
									[申请中]
								</span>
								<span v-if="details.refunds && details.refunds[0].isacc == 1" class="green">
									[同意]
								</span>
								<span v-if="details.refunds && details.refunds[0].isacc == 2" class="green">
									[不同意]
								</span>
								<span class="tit">在线支付退款：</span>
								<span class="money">
									¥ {{ details.refunds ? details.refunds[0].refundMoney.toFixed(2) : '0.00' }}
								</span>
							</div>
							<div v-for="(item, index) in details.refunds || []" :key="index" class="list">
								<div v-if="item.refundres" class="box">
									<div class="lf">买家：</div>
									<div class="rf">{{ item.refundres }}</div>
								</div>
								<div v-if="item.sellerres" class="box">
									<div class="lf">卖家：</div>
									<div class="rf">{{ item.sellerres }}</div>
								</div>
							</div>
							<div class="time">
								<div class="cell">
									<div class="name">操作时间：</div>
									<div class="val">{{ details.refunds ? details.refunds[0].createTime : '-' }}</div>
								</div>
								<div class="cell">
									<div class="name">修改时间：</div>
									<div class="val">{{ details.refunds ? details.refunds[0].updateTime : '-' }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="title">订单详情</div>
					<div v-for="(item, index) in details.ORDER_GOODS_DETAILS" :key="index">
						<div class="img-box">
							<img :src="$judgeFile(item.COVER_URL)" alt="" />
							<div class="rf">
								<div class="name nth2">{{ item.NAME }}</div>
								<div class="tit">商品数量：{{ item.NUMBER }}</div>
								<div class="tit">
									商品总价：
									<span>¥{{ item.PRICE.toFixed(2) }}</span>
								</div>
							</div>
						</div>
					</div>
					<div class="data">
						<div class="cell">
							<div class="title">订单编号：</div>
							<el-tooltip
								class="item"
								effect="dark"
								:content="details.ORDER_ID || '-'"
								placement="top"
							>
								<div class="val">{{ details.ORDER_ID || '-' }}</div>
							</el-tooltip>
						</div>
						<div class="cell">
							<div class="title">订单总额：</div>
							<div class="val">{{ details.TOTAL_MONEY || '0.00' }}</div>
						</div>
						<div class="cell">
							<div class="title">实际支付：</div>
							<div class="val">{{ details.ACTUAL_MONEY || '0.00' }}</div>
						</div>
						<!-- <div class="cell">
							<div class="title">下单人：</div>
							<div class="val">{{ details.passengers ? details.passengers.name : '-' }}</div>
						</div> -->
						<div class="cell">
							<div class="title">下单时间：</div>
							<div class="val">{{ details.ORDER_TIME || '-' }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
export default {
	name: 'Drawback',
	data() {
		return {
			step: 0,
			form: {
				remark: ''
			},
			main: window.__POWERED_BY_WUJIE__,
			isLaw: this.$route.query.isLaw,
			loading: false,
			imgList: [],
			fileList: [],
			icon: 'icon-dengdai',
			option: [],
			btnLoading: false,
			rules: {
				reasonIds: [{ required: true, message: '请选择原因', trigger: 'change' }],
				remark: [{ required: true, message: '请输入退款说明', trigger: 'blur' }]
			},
			updUrl: baseUrl + '/api/supply-web/fileUpload/singleFile',
			details: {
				ORDER_GOODS_DETAILS: []
			},
			userId: ''
		};
	},
	created() {
		this.userId = this.isShopLogin();
		if (!this.$route.query.id) {
			this.$message.close();
			this.$message.error('参数错误！');
			// this.$router.go(-1);
		} else {
			this.id = this.$route.query.id;
			this.getOrderDetail();
			this.getRefundReasonOption();
		}
	},
	methods: {
		isHead() {
			if (this.main) {
				return false;
			} else if (this.isLaw == 1) {
				return false;
			} else {
				return true;
			}
		},
		// 提交申请
		refund() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					let reasonIds = this.form.reasonIds;
					reasonIds = reasonIds.toString(',');
					let remarkPic = [];
					if (this.fileList.length > 0) {
						remarkPic = this.fileList.map(i => {
							return i.response.results.fileId;
						});
					}
					remarkPic = remarkPic.toString(',');
					let data = {
						userId: this.userId,
						orderId: this.id,
						reasonIds,
						remark: this.form.remark,
						remarkPic
					};
					this.$api.shop_api.refund(data).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '申请成功',
								type: 'success'
							});
							this.getOrderDetail();
						} else {
							this.loading = false;
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
					});
				} else {
					return false;
				}
			});
		},
		// 上传成功回调
		success(response, file, fileList) {
			this.fileList = fileList;
			this.btnLoading = false;
		},
		// 移除图片
		handleRemove(file, fileList) {
			this.fileList = fileList;
		},
		// 拼接退款原因
		changeSelect(m) {
			let list = [];
			this.form.remark = '';
			if (m.length == 0) {
				return;
			}
			for (let i = 0; i < m.length; i += 1) {
				for (let a = 0; a < this.option.length; a += 1) {
					if (this.option[a].key == m[i]) {
						list.push(this.option[a].value);
					}
				}
			}
			list = list.toString(',');
			this.form.remark += list;
		},
		// 获取退款原因
		getRefundReasonOption() {
			this.$api.shop_api.getRefundReasonOption({ orderId: this.id }).then(res => {
				if (res.state) {
					this.option = res.result;
				}
			});
		},
		// 获取订单详情
		getOrderDetail() {
			this.loading = true;
			this.$api.shop_api.getOrderDetail({ orderId: this.id }).then(res => {
				if (res.state) {
					this.details = res.result;
					if (
						this.details.refunds &&
						(this.details.refunds[0].refundState == 0 ||
							this.details.refunds[0].refundState == 9 ||
							this.details.refunds[0].refundState == 1)
					) {
						this.step = 1;
						if (this.details.CAN_REFUND) {
							this.step = 0;
						}
					} else if (
						this.details.refunds &&
						(this.details.refunds[0].refundState == 2 || this.details.refunds[0].refundState == 3)
					) {
						this.step = 2;
						if (this.details.CAN_REFUND) {
							this.step = 0;
						}
						if (this.details.refunds[0].refundState == 2) {
							this.icon = 'icon-check-circle';
						} else {
							this.icon = 'icon-close-circle';
						}
					}
					if (this.details.refunds && this.details.refunds[0].refundrespic) {
						this.imgList = this.details.refunds[0].refundrespic.split(',');
					}
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.nth2 {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.drawback {
	width: 100%;
	.head {
		width: 1200px;
		display: flex;
		align-items: center;
		margin: 22px auto;
		.icon {
			color: #9aa3ba;
			font-size: 17px;
			margin-right: 8px;
			cursor: pointer;
		}
		.cur {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.45);
			transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		}
		.cur:hover {
			color: var(--brand-6, '#ca3f3b');
		}
		.red {
			color: var(--brand-6, '#ca3f3b');
		}
	}
	.main {
		width: 1200px;
		margin: 0 auto;
		background: #fff;
		padding: 28px 24px 42px 34px;
		box-sizing: border-box;
		.title {
			font-size: 24px;
			font-weight: 500;
			color: #404040;
			line-height: 28px;
		}
		.step {
			margin-top: 24px;
			background: #d9d9d9;
			display: flex;
			align-items: center;
			height: 40px;
			overflow: hidden;
			.step-item {
				width: 33.3%;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				font-size: 14px;
				font-weight: 400;
				color: #ffffff;
				height: 100%;
				p {
					margin: 0;
					width: 28px;
					height: 28px;
					background: #ffffff;
					opacity: 1;
					text-align: center;
					line-height: 28px;
					border-radius: 50%;
					font-size: 14px;
					font-weight: 400;
					color: #bfbfbf;
					margin-right: 10px;
				}
				.line {
					height: 40px;
					width: 40px;
					border: 1px solid #fff;
					position: absolute;
					top: 0px;
					right: -20px;
					transform: rotate(45deg);
					border-left: none;
					border-bottom: none;
					z-index: 5;
				}
			}
			.act {
				background: var(--brand-6, '#ca3f3b');
				p {
					color: var(--brand-6, '#ca3f3b');
				}
				.line {
					background: var(--brand-6, '#ca3f3b');
				}
			}
		}
	}
	.content {
		margin-top: 42px;
		display: flex;
		.form {
			width: 71%;
			::v-deep.el-form-item__label {
				font-size: 14px;
				font-weight: 400;
				color: #404040;
				line-height: 40px;
			}
			.img-box {
				display: flex;
				align-items: center;
				img {
					width: 106px;
					height: 106px;
					display: block;
				}
				.rf {
					margin-left: 11px;
					height: 106px;
					.title {
						font-size: 16px;
						font-weight: 400;
						color: #404040;
						line-height: 24px;
						height: 48px;
					}
					.num {
						margin-top: 10px;
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
					}
					.money {
						margin-top: 6px;
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
						span {
							color: #262626;
						}
					}
				}
			}
			::v-deep.el-select {
				width: 235px;
			}
			::v-deep.el-textarea {
				// width: 574px;
			}
			.price {
				font-size: 16px;
				font-weight: 500;
				color: var(--brand-6, '#ca3f3b');
				line-height: 40px;
			}
			.btn-box {
				display: flex;
				margin: 32px 0 0 97px;
				::v-deep .el-button {
					width: 140px;
				}
			}
		}
		.left {
			width: 71%;
			box-sizing: border-box;
			.state {
				margin-right: 16px;
				padding: 0 15px;
				height: 128px;
				opacity: 1;
				border-bottom: 1px solid #d9d9d9;
				display: flex;
				align-items: center;
				margin-left: 16px;
				.rf {
					margin-left: 16px;
					.name {
						font-size: 24px;
						font-weight: 500;
						color: #262626;
						line-height: 32px;
					}
					.btm {
						margin: 16px 0 0 0;
						font-size: 14px;
						font-weight: 400;
						color: #9da5b7;
						line-height: 22px;
						span {
							color: #404040;
						}
					}
				}
			}
			.money {
				padding: 0 34px;
				font-size: 16px;
				margin: 47px 0 0 0;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
				span {
					font-size: 20px;
					color: var(--brand-6, '#ca3f3b');
				}
			}
			.voucher {
				margin-top: 36px;
				display: flex;
				padding: 0 34px;
				.lf {
					font-size: 14px;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					width: 70px;
				}
				.rf {
					width: calc(100% - 70px);
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					img {
						width: 188px;
						height: 188px;
						border-radius: 8px 8px 8px 8px;
						margin: 0 5px 5px 0;
					}
				}
			}
			.notes {
				margin-top: 71px;
				padding: 0 34px;
				.title {
					font-size: 20px;
					font-weight: bold;
					color: #262626;
					line-height: 28px;
				}
				.voucher {
				}
				.list-box {
					margin-top: 21px;
					border: 1px solid #d9d9d9;
					padding: 21px 18px 30px 18px;
					font-size: 16px;
					font-weight: 500;
					color: var(--brand-6, '#ca3f3b');
					line-height: 24px;
					.top {
						margin-bottom: 5px;
						.green {
							margin: 0 8px 0 6px;
							color: #76bf6a;
						}
						.tit {
							color: #8c8c8c;
						}
						.money {
							padding: 0;
							color: var(--brand-6, '#ca3f3b');
						}
					}
					.list {
						.box {
							margin-top: 2px;
							display: flex;
							align-items: center;
							height: 48px;
							background: #f4f5f8;
							flex-wrap: wrap;
						}
						.lf {
							width: 81px;
							text-align: right;
							font-size: 14px;
							font-weight: 400;
							color: #9da5b7;
							line-height: 22px;
						}
						.rf {
							width: calc(100 - 87px);
							font-size: 14px;
							font-weight: 400;
							color: #404040;
							line-height: 22px;
						}
					}
					.time {
						margin-top: 20px;
						display: flex;
						align-items: center;
						.cell {
							display: flex;
							align-items: center;
							.name {
								font-size: 12px;
								font-weight: 400;
								color: #9da5b7;
								line-height: 20px;
							}
							.val {
								font-size: 12px;
								font-weight: 400;
								color: #404040;
								line-height: 20px;
							}
						}
						.cell:last-child {
							margin-left: 32px;
						}
					}
				}
			}
		}
		.right {
			width: 28%;
			margin-left: auto;
			height: 880px;
			border: 1px solid #d9d9d9;
			padding: 14px 16px;
			box-sizing: border-box;
			.title {
				font-size: 16px;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
			}
			.img-box {
				margin-top: 26px;
				display: flex;
				img {
					width: 72px;
					height: 70px;
				}
				.rf {
					width: 192px;
					margin-left: 6px;
					.name {
						font-size: 16px;
						font-weight: 400;
						color: #404040;
						line-height: 24px;
					}
					.tit {
						margin-top: 6px;
						font-size: 14px;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
						span {
							color: var(--brand-6, '#ca3f3b');
						}
					}
				}
			}
			.data {
				margin-top: 38px;
				padding: 7px 0;
				background: #f4f5f8;
				.cell {
					display: flex;
					align-items: center;
					.title {
						width: 80px;
						height: 36px;
						font-size: 14px;
						font-weight: 400;
						color: #9da5b7;
						line-height: 36px;
						text-align: right;
					}
					.val {
						margin-left: auto;
						width: calc(100% - 90px);
						font-size: 14px;
						font-weight: 400;
						color: #404040;
						line-height: 36px;
						overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
						text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
						white-space: nowrap; /*文本不换行*/
						padding-right: 5px;
						box-sizing: border-box;
					}
				}
			}
		}
	}
}
</style>
