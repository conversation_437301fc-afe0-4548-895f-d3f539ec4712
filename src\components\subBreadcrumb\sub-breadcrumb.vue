<template>
	<div
		class="sub-breadcrumb"
		:style="{
			width: isMain ? '1200px' : '100%',
			background,
			margin: isMain ? '16px auto' : '0 auto',
			color: color
		}"
	>
		<i :class="[icon, 'pages-icon']"></i>
		<span class="tips-text">{{ text }}</span>
		<el-breadcrumb separator-class="el-icon-arrow-right">
			<el-breadcrumb-item
				v-for="(item, index) of routePath"
				:key="index"
				:to="{ path: item.fullPath }"
				:class="[index == routePath.length - 1 ? 'item-last' : '']"
			>
				{{ item.title }}
			</el-breadcrumb-item>
		</el-breadcrumb>
	</div>
</template>

<script>
import { mapState } from 'vuex';
export default {
	name: 'SubBreadCrumb',
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		background: {
			type: String,
			default: () => {
				return '#ffffff';
			}
		},
		// 图标展示
		icon: {
			type: String,
			default: () => {
				return 'el-icon-s-home';
			}
		},
		// 提示文字展示
		text: {
			type: String,
			default: () => {
				return '';
			}
		},
		// 颜色设置
		color: {
			type: String,
			default: () => {
				return '#332c2b';
			}
		}
	},
	computed: {
		...mapState({
			routePath: state => state.app.routePath
		})
	}
};
</script>

<style lang="scss" scoped>
.sub-breadcrumb {
	height: 50px;
	display: flex;
	align-items: center;
	border-radius: 4px;
	padding: 0 19px;
	.pages-icon {
		width: 14px;
		height: 14px;
		margin-right: 6px;
		color: #9aa3ba;
	}
	.tips-text {
		font-size: 14px;
		color: #999999;
	}
	.item-last {
		color: #0076e8;
	}
}
::v-deep .el-breadcrumb__separator {
	margin: 0;
	// color: #332c2b;
	color: inherit;
}
.el-breadcrumb ::v-deep .el-breadcrumb__inner {
	color: inherit;
}
.el-breadcrumb ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner {
	color: inherit;
}
</style>
