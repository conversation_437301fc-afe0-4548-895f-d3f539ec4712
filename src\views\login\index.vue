<template>
	<!-- <div class="login"> -->
	<div class="login">
		<!-- :style="{ backgroundImage: `url(${logoBj.pcLoginBj || bg})` }" -->
		<video class="video" autoPlay loop muted :src="url"></video>
		<!-- // 视频静音 -->
		<!-- // 循环播放 -->
		<!-- // 自动播放 -->
		<login-header @openInfo="openInfo"></login-header>
		<resetForm v-if="isReset"></resetForm>
		<login-form v-else></login-form>
		<login-footer></login-footer>

		<el-dialog title="声明" :visible.sync="dialogVisible" width="30%" custom-class="Mydialog">
			<span>
				“云上职教”智能化校园项目包含的是“云上职教”智能化校园平台，现在变更为“技状元”智能化校园平台V1.0，变更仅涉及名称，平台功能、性能标准及服务承诺保持不变。
			</span>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="dialogVisible = false">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import loginHeader from '@/views/login/components/header';
import loginFooter from '@/views/login/components/footer';
import loginForm from '@/views/login/components/form';
import resetForm from '@/views/login/components/form-reset';
import { mapState } from 'vuex';
import { baseUrl } from '@/config';
export default {
	name: 'Login',
	components: {
		loginHeader,
		loginFooter,
		loginForm,
		resetForm
	},
	data() {
		return {
			isReset: false,
			url: `${baseUrl}/project-ybzy/picture/video/1.mp4`,
			dialogVisible: false
			// bg: require('@/assets/images/login/login-bg.png')
		};
	},
	computed: {
		...mapState('app', ['logoBj'])
	},
	watch: {
		$route: {
			handler(val) {
				if (val.path == '/loginReset') {
					this.isReset = true;
				} else {
					this.isReset = false;
				}
			}
		}
	},
	// computed: {
	// isReset() {
	// 	return window.location.href.includes('loginReset');
	// }
	// },
	mounted() {
		this.isReset = this.$route.path == '/loginReset' ? true : false;
	},
	methods: {
		openInfo() {
			this.dialogVisible = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.login {
	height: 100vh;
	width: 100%;
	background-size: 100% 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	background-size: cover;
}

.video {
	position: absolute;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

::v-deep .Mydialog .el-dialog__body {
	padding: 5px 20px !important;
}
</style>
