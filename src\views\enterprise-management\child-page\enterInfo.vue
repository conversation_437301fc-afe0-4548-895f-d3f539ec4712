<template>
	<div v-loading="pageLoading" class="main">
		<el-form ref="form" :model="form" :rules="rules" label-width="140px">
			<div class="business-info">
				<h3 class="form-title">企业信息</h3>
				<el-row :gutter="20">
					<el-col>
						<el-form-item label="企业名称">
							<span>{{ corpName }}</span>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="企业LOGO" prop="logo">
							<el-upload
								class="avatar-uploader"
								:action="updUrl"
								:limit="1"
								:accept="acceptImg"
								:data="{
									ownId: form.id,
									code: 'job_co_enterprise_logo'
								}"
								:show-file-list="false"
								:before-upload="beforeLogoUpload"
								:on-success="handleLogoSuccess"
								:on-remove="handleLogoRemove"
							>
								<div class="avatar-wrapper" v-if="form.logo">
									<img :src="logoUrl" class="avatar" />
									<i class="el-icon-delete avatar-delete" @click.stop="handleLogoRemove"></i>
								</div>
								<i v-else class="el-icon-plus avatar-uploader-icon"></i>
							</el-upload>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业视频" prop="logo">
							<el-upload
								ref="videoUpload"
								class="avatar-uploader"
								:action="updUrl"
								:limit="1"
								:accept="acceptVideo"
								:data="{
									ownId: form.id,
									code: 'job_co_enterprise_video'
								}"
								:file-list="videoList"
								:before-upload="beforeVideoUpload"
								:on-success="handleVideoSuccess"
								:on-remove="videoRemove"
								:on-exceed="onExceed"
							>
								<i class="el-icon-plus avatar-uploader-icon"></i>
							</el-upload>
							<div class="el-upload__tip">
								提示：最多上传一份视频文件，支持格式：mp4、mov，最大100M
							</div>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业规模" prop="peopleNumCode">
							<el-select v-model="form.peopleNumCode" placeholder="请选择企业人数规模">
								<el-option
									v-for="(item, index) in post_company_scale"
									:key="index"
									:label="item.shortName"
									:value="item.cciValue"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="行业领域" prop="industryField ">
							<el-select v-model="form.industryField" placeholder="请选择行业领域">
								<el-option
									v-for="(item, index) in post_industry_area"
									:key="index"
									:label="item.shortName"
									:value="item.cciValue"
								></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="公司简介" prop="introduction">
							<el-input
								v-model="form.introduction"
								class="business-scope"
								type="textarea"
								maxlength="1000"
								resize="none"
								:autosize="{ minRows: 6 }"
								show-word-limit
								placeholder="请填写公司简介"
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="企业标签" prop="tags">
							<el-input v-model="form.tags" placeholder="请填写企业标签" type="input"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<span style="line-height: 40px">
							提示：多个标签请使用英文逗号","隔开。（例如：五险一金,带薪年假）
						</span>
					</el-col>
					<el-col :span="24">
						<el-form-item label="通讯地址" prop="contactAddress">
							<el-input
								v-model="form.contactAddress"
								placeholder="请填写企业通讯地址"
								type="textarea"
								maxlength="200"
								show-word-limit
							></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="经纬度" prop="lngLat">
							<lng-lat v-model="form.lngLat"></lng-lat>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="企业照片" prop="coverImg">
							<div class="business-license">
								<div class="business-license-upload">
									<el-upload
										class="upload-drag"
										:action="updUrl"
										multiple
										:limit="8"
										:accept="acceptImg"
										list-type="picture-card"
										:data="{
											ownId: form.id,
											code: 'job_co_enterprise_photos'
										}"
										:file-list="imageList"
										:before-upload="beforeImagesUpload"
										:on-success="handleImagesSuccess"
										:on-exceed="onExceed"
										:on-remove="uploadOnRemove"
									>
										<i class="el-icon-picture-outline upload-icon" size="44"></i>
									</el-upload>
								</div>
								<p class="desc">
									提示：最多上传8张，上传图片建议为正方形，推荐尺寸800×800；支持格式：.jpg .jpeg
									.gif .bmp .png ，单个文件不能超过1MB
								</p>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
		</el-form>
		<div class="info-save">
			<el-button
				class="info-save-confirm"
				type="primary"
				size="large"
				:loading="submitLoading"
				@click="handlerSubmit"
			>
				保存
			</el-button>
			<el-button class="info-save-staging" size="large" @click="resetForm">重置</el-button>
		</div>
	</div>
</template>

<script>
import { getDictionaryByCode } from '@/utils';
import { baseUrl } from '@/config';
import LngLat from '@/components/lnglat';

export default {
	name: 'EnterInfo',
	components: { LngLat },
	data() {
		return {
			dataCode: 'plat_enterprise_validity',
			updUrl: baseUrl + '/ybzy/mecpfileManagement/front/upload', // 文件上传地址
			acceptImg: '.jpg,.gif,.jpeg,.png,.JPG,.GIF,.JPEG,.PNG', //上传图片格式
			acceptVideo: '.mp4,.mov,.MP4,.MOV', //上传视频格式
			imgListSize: 1,
			imageList: [],
			videoList: [], //视频集合
			logoUrl: '',
			corpName: '', //企业名称
			form: {
				id: '',
				peopleNumCode: '', //
				industryField: '', //行业领域
				coverImg: '', //
				tags: '', //
				introduction: '', //
				lngLat: [],
				contactAddress: ''
			},
			rules: {
				logo: [{ required: true, message: '请上传企业logo', trigger: 'change' }], //企业规模
				peopleNumCode: [{ required: true, message: '请选择企业规模', trigger: 'change' }], //企业规模
				industryField: [{ required: true, message: '请选择行业领域', trigger: 'change' }], //企业规模
				coverImg: [{ required: true, message: '请上传企业照片', trigger: 'blur' }], //企业照片
				tags: [
					{ required: true, message: '请输入企业标签', trigger: 'blur' },
					{
						trigger: 'blur',
						validator: (rule, value, callback) => {
							if (/[\uFF0C]/.test(value)) {
								return callback(new Error('对不起您输入的是中文“，”，请使用英文逗号“,”'));
							} else {
								callback();
							}
						}
					}
				], // 企业标签
				introduction: [{ required: true, message: '请输入公司简介', trigger: 'blur' }], //经营范围
				contactAddress: [{ required: true, message: '请输入公司通讯地址', trigger: 'blur' }],
				lngLat: [{ required: true, message: '请选择公司定位', trigger: 'change' }]
			},
			post_company_scale: [], //行业人数规模
			post_industry_area: [], //行业领域
			pageLoading: true,
			submitLoading: false
		};
	},
	created() {
		this.getMyEnterpriseInfo();
		this.handlerQueryDict('');
	},
	methods: {
		// 获取企业信息
		async getMyEnterpriseInfo() {
			try {
				this.pageLoading = true;
				const { results } = await this.$api.enterprise_center.getMyEnterpriseInfo();
				const {
					peopleNumCode,
					industryField,
					introduction,
					coverImg,
					tags,
					id,
					contactAddress,
					lon,
					lat,
					logo,
					corpName
				} = results;
				this.corpName = corpName;
				this.form = {
					peopleNumCode,
					industryField,
					introduction,
					coverImg,
					tags,
					id,
					lngLat: lon && lat ? [lon, lat] : [],
					contactAddress,
					logo
				};
				this.logoUrl = `${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${logo}`;
				this.imageList = coverImg.split(',').map(adjunctId => ({
					name: adjunctId,
					url: `${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${adjunctId}`
				}));
				this.getFiles('job_co_enterprise_video'); //视频数据
			} finally {
				this.pageLoading = false;
			}
		},
		// 数据字典查询
		async handlerQueryDict() {
			const dicts = await getDictionaryByCode(['post_company_scale', 'post_industry_area']);
			this.post_company_scale = dicts.post_company_scale;
			this.post_industry_area = dicts.post_industry_area;
		},
		// 获取企业video视频
		getFiles(code) {
			this.$api.personal_api.getFileInfo({ code: code, ownId: this.form.id }).then(async res => {
				this.videoList = res.results.splice(res.results.length - 1, 1);
			});
		},
		// 删除视频，防止视频记录残留，影响视频展示逻辑
		deleteFile(id) {
			this.$api.personal_api.deleteFile({ id: id }).then(async res => {});
		},
		/**
		 * @description 上传图片集合文件
		 */
		beforeImagesUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < this.imgListSize;
			if (!isImgSize) {
				this.$message.error(`上传图片大小不能超过 ${this.imgListSize}MB!`);
			}
			return isImgSize;
		},
		/**
		 * @description 上传图片集合文件
		 */
		beforeLogoUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < this.imgListSize;
			if (!isImgSize) {
				this.$message.error(`上传图片大小不能超过 ${this.imgListSize}MB!`);
			}
			return isImgSize;
		},
		/**
		 * @description 上传视频文件
		 */
		beforeVideoUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < 100;
			if (!isImgSize) {
				this.$message.error(`上传视频大小不能超过100MB!`);
			}
			return isImgSize;
		},
		handleVideoSuccess(res, file, fileList) {
			this.videoList = fileList;
		},
		videoRemove(file, fileList) {
			this.videoList = fileList;
			this.$refs.videoUpload.clearFiles();
			this.deleteFile(file.adjunctId);
		},
		/**
		 * @description 上传图片集合文件
		 */
		handleImagesSuccess(res, file, fileList) {
			this.imageList = fileList;

			this.form.coverImg = fileList
				.map(file => {
					if (file.response) {
						return file.response.results[0].adjunctId;
					}
					return file.name;
				})
				.join(',');
		},
		/**
		 * @description 上传图片集合文件
		 */
		handleLogoSuccess(res, file, fileList) {
			if (res.success) {
				this.form.logo = fileList
					.map(file => {
						if (file.response) {
							return file.response.results[0].adjunctId;
						}
						return file.name;
					})
					.join(',');
				this.logoUrl = URL.createObjectURL(file.raw);
			} else {
				this.$message.warning('上传失败!');
			}
		},
		// 监听文件删除
		uploadOnRemove(file, fileList) {
			this.imageList = fileList;
			this.form.coverImg = fileList
				.map(file => {
					if (file.response) {
						return file.response.results[0].adjunctId;
					}
					return file.name;
				})
				.join(',');
		},
		// 监听文件删除
		handleLogoRemove(file, fileList) {
			this.form.logo = '';
		},
		onExceed(file, fileList) {
			this.$message.error(`超过最多上传数量!`);
		},
		resetForm() {
			this.$refs.form.resetFields();
			this.imageList = [];
			this.videoList[0]?.adjunctId && this.deleteFile(this.videoList[0]?.adjunctId);
			this.videoList = [];
		},
		/**
		 * @description 表单提交
		 * */
		handlerSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.submitLoading = true;
					this.$api.enterprise_center
						.saveMyEnterpriseInfo({
							...this.form,
							lon: this.form.lngLat[0],
							lat: this.form.lngLat[1]
						})
						.then(res => {
							if (res.success) {
								this.$message.success('保存成功！');
							} else {
								this.$message.warning(res.msg || '保存失败！');
							}
						})
						.finally(() => {
							this.submitLoading = false;
						});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	// border: 1px solid red;
	background: #fff;
}
.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.info-save {
	width: 100%;
	padding-top: 40px;
	border-top: 1px solid #d9d9d9;
	text-align: center;
	&-staging {
		margin-right: 30px;
		padding-left: 56px;
		padding-right: 56px;
	}
	&-confirm {
		padding-left: 56px;
		padding-right: 56px;
	}
}
.avatar-uploader ::v-deep {
	.el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
	.el-upload:hover {
		border-color: #1890ff;
	}
}
.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 148px;
	height: 148px;
	line-height: 148px;
	text-align: center;
}
.avatar {
	width: 148px;
	height: 148px;
	display: block;
}
.avatar-wrapper {
	position: relative;
	width: 148px;
	height: 148px;

	.avatar-delete {
		position: absolute;
		top: 0;
		right: 0;
		padding: 4px;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.6);
		cursor: pointer;

		&:hover {
			background-color: rgba(0, 0, 0, 0.8);
		}
	}
}
</style>
