<template>
	<div class="category">
		<!--S 表单-->
		<Search :search-data="searchData" :form-bottom="formBottom" :form-data="formData" />
		<!--E 表单-->
		<!--S 内容-->
		<div class="category__con my-border-table">
			<el-container>
				<el-aside v-show="!pageType" width="240px">
					<Tree
						ref="treeNode"
						:page-type="pageType"
						:name="formData.name"
						:type="types"
						@updateLevel="updateLevel"
						@updateCategoryList="updateCategoryList"
					/>
				</el-aside>
				<el-main>
					<el-table
						v-loading="loading"
						:data="categoryListInfo && categoryListInfo.rows ? categoryListInfo.rows : []"
						style="width: 100%"
					>
						<el-table-column
							type="index"
							label="编号"
							width="80"
							:index="indexMethod"
						></el-table-column>
						<el-table-column prop="code" label="分类code"></el-table-column>
						<el-table-column prop="name" label="分类名称" show-overflow-tooltip></el-table-column>
						<!--<el-table-column prop="description" label="分类描述" show-overflow-tooltip>-->
						<!--</el-table-column>-->
						<!--<el-table-column label="分类图片" v-if="this.pageType == 2">
              <template slot-scope="scope">
                <ImgEnlarge
                  :src="scope.row.picUrl || 'https://img10.dgg.cn/sp/cms/cjci3nswfts0000.png'"
                ></ImgEnlarge>
              </template>
            </el-table-column>-->
						<el-table-column prop="sort" label="排序"></el-table-column>
						<el-table-column v-if="this.pageType == 2" label="审核">
							<template slot-scope="scope">
								<el-switch
									v-if="categoryListInfo && categoryListInfo.rows"
									v-model="scope.row.examineType"
									disabled
									@click.native="changeExamineSwitch(scope.row)"
								/>
							</template>
						</el-table-column>
						<el-table-column label="状态">
							<template slot-scope="scope">
								<el-switch
									v-if="categoryListInfo && categoryListInfo.rows"
									v-model="scope.row.statusType"
									disabled
									@click.native="changeSwitch(scope.row)"
								></el-switch>
							</template>
						</el-table-column>
						<el-table-column fixed="right" label="操作" width="200">
							<template v-if="categoryListInfo && categoryListInfo.rows" slot-scope="scope">
								<el-link type="primary" @click="lookCategory(scope.row)">查看</el-link>
								<el-link type="primary" @click="editCategory(scope.row)">编辑</el-link>
								<el-link
									type="primary"
									:disabled="scope.row.status === 1"
									@click="delCategory(scope.row)"
								>
									删除
								</el-link>
							</template>
						</el-table-column>
					</el-table>
					<div class="pagination">
						<Pagination
							:current-page="page"
							:page-size="limit"
							:total="total"
							@paginationChange="paginationChange"
						></Pagination>
					</div>
				</el-main>
			</el-container>
		</div>
		<!--E 内容-->
	</div>
</template>

<script>
import Tree from '@/views/category/components/Tree';
import Pagination from '_c/public/Pagination';
import Search from '@/components/public/Search';
import { menuCategory } from '@/api';
import ImgEnlarge from '@/components/public/ImgEnlarge';
export default {
	name: 'Index',
	components: { Tree, Pagination, Search, ImgEnlarge },
	props: {
		types: {
			type: String,
			default: ''
		},
		pageType: {
			type: String,
			default: () => {
				return '';
			}
		},
		categoryCode: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			formData: {
				name: ''
			},
			page: 1, // 当前页
			limit: 10, // 每页显示的条数
			total: 0, // 总条数
			level: null, // 被选中到分类层级
			categoryListInfo: null, // 分类列表信息
			treeNode: null,
			loading: false,
			searchData: [
				// 组件表单数据
				{
					model: 'name',
					type: 'text',
					label: '分类名称：',
					placeholder: '',
					width: 228
				}
			],
			formBottom: [
				{
					type: 'button',
					icon: '',
					btnType: 'primary',
					text: '搜索',
					handle: this.search
				},
				{
					type: 'button',
					icon: 'el-icon-plus',
					btnType: 'primary',
					text: '添加分类',
					handle: this.newCategory
				}
			],
			treeCode: '', // 每个分类对应的code
			baseForm: {}
		};
	},
	watch: {
		types(newVal) {
			this.types = newVal;
		}
	},
	mounted() {
		this.getParams();
	},
	activated() {
		this.getParams();
	},
	methods: {
		// pagetype > 0 时没有Tree，根据code查询对应参数
		getParams() {
			if (this.pageType > 0) {
				menuCategory.categoryTree({ type: this.types }).then(res => {
					if (res.code === 200) {
						let arr = res.data[0];
						this.baseForm = {
							pid: arr.id,
							level: arr.level,
							levelIds: arr.levelIds,
							pcode: arr.code
						};
					}
				});
			} else {
				return;
			}
		},
		// 显示编号
		indexMethod(index) {
			const num = index + 1;
			return num === 100 ? num : num >= 10 ? '0' + num : '00' + (index + 1);
		},
		updateLevel(data) {
			// 接收子组件触发的改变level方法然后赋值level
			this.level = data.node ? data.node.level : null;
			this.treeNode = data.val || {};
			this.treeCode = data.val.code;
		},
		newCategory() {
			// 添加分类，跳转到添加分类页面
			if (!this.treeNode && !this.pageType) {
				this.$message.close();
				this.$message({
					message: '请选择父级分类',
					type: 'warning'
				});
				return;
			}
			this.$router.push({
				path: `/category/add/1/${this.pageType ? this.baseForm.pid : this.treeNode.id}/${
					this.pageType ? this.pageType : '0'
				}/${this.pageType ? this.types : this.treeCode}/${
					this.pageType ? (this.pageType == 1 ? '菜单管理' : '内容管理') : this.treeNode.name
				}`,
				query: { tagName: '添加分类' }
			});
		},
		search() {
			this.page = 1;
			this.$refs.treeNode.getCategoryList(
				this.pageType ? this.baseForm.pid : this.treeNode.id,
				this.page,
				this.limit
			);
		},
		editCategory(row) {
			// 编辑分类，跳转到编辑分类页面
			this.$router.push({
				path: `/category/add/2/${row.id}/${this.pageType ? this.pageType : '0'}/${
					this.types ? this.types : '0'
				}/${this.pageType ? (this.pageType == 1 ? '菜单管理' : '内容管理') : this.treeNode.name}`,
				query: { tagName: '编辑分类' }
			});
		},
		lookCategory(row) {
			// 查看分类
			this.$router.push({
				path: `/category/add/3/${row.id}/${this.pageType ? this.pageType : '0'}/${
					this.types ? this.types : '0'
				}/${this.pageType ? (this.pageType == 1 ? '菜单管理' : '内容管理') : this.treeNode.name}`,
				query: { tagName: '查看分类' }
			});
		},
		updateCategoryList(data) {
			// 接收子组件触发的改变分类列表数据事件并赋值
			this.categoryListInfo = data;
			this.total = data.total;
		},
		handleSizeChange(val) {
			// 改变每页条数
			this.$refs.treeNode.getCategoryList(
				this.pageType ? this.baseForm.pid : this.treeNode.id,
				this.page,
				val
			);
		},
		handleCurrentChange(val) {
			// 改变当前页
			this.$refs.treeNode.getCategoryList(
				this.pageType ? this.baseForm.pid : this.treeNode.id,
				val,
				this.limit
			);
		},
		paginationChange(val) {
			// 分页组件暴露的事件
			if (val.type == 1) {
				this.limit = val.value;
				this.handleSizeChange(val.value);
			} else {
				this.page = val.value;
				this.handleCurrentChange(val.value);
			}
		},
		delCategory(row) {
			this.$confirm('确定删除此分类?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				closeOnPressEscape: false,
				closeOnClickModal: false,
				type: 'warning'
			})
				.then(() => {
					let data = {
						id: row.id,
						type: this.categoryCode
					};
					menuCategory.isDelete(data).then(res => {
						if (res.code === 200) {
							this.$message.close();
							this.$message({
								message: '删除成功',
								type: 'success'
							});
							if (this.page > 1 && this.categoryListInfo.rows.length === 1) {
								this.page -= 1;
							}
							this.$refs.treeNode.getCategoryList(
								this.pageType ? this.baseForm.pid : this.treeNode.id,
								this.page,
								this.limit
							);
							this.$refs.treeNode.getCategoryTree(this.page, this.limit);
						} else {
							this.$message.close();
							this.$message({
								message: res.message || '操作失败',
								type: 'warning'
							});
						}
					});
				})
				.catch(() => {});
		},
		changeExamineSwitch(row) {
			// 开启/禁用审核
			this.loading = true;
			let data = {
				id: row.id,
				status: row.isExamine == 1 ? 0 : 1
			};
			menuCategory.categortExamine(data).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.$message.close();
					this.$message({
						message: row.isExamine ? '关闭成功' : '启用成功',
						type: 'success'
					});
					this.$refs.treeNode.getCategoryList(
						this.pageType ? this.baseForm.pid : this.treeNode.id,
						this.page,
						this.limit
					);
				} else {
					this.$message.close();
					this.$message({
						message: res.message || '操作失败',
						type: 'warning'
					});
				}
			});
		},
		changeSwitch(row) {
			// 启用/禁用分类
			this.loading = true;
			let data = {
				id: row.id,
				status: row.status ? 0 : 1
			};
			menuCategory.categortStatus(data).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.$message.close();
					this.$message({
						message: row.status ? '禁用成功' : '启用成功',
						type: 'success'
					});
					this.$refs.treeNode.getCategoryList(
						this.pageType ? this.baseForm.pid : this.treeNode.id,
						this.page,
						this.limit
					);
				} else {
					this.$message.close();
					this.$message({
						message: res.message || '操作失败',
						type: 'warning'
					});
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
// @import '@/assets/scss/table-style.scss';
// @import '@/assets/scss/switch.scss';
.category {
	margin-top: 16px;
	&__form {
		background-color: #fff;
		padding: 16px 20px;
		box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.1);
		.el-form-item {
			margin-bottom: 0;
		}
		.el-button--medium {
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #ffffff;
			border-radius: 2px;
		}
		.FormItem__label {
			font-size: 14px;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #222222;
		}
		&_middleInput {
			width: 228px;
		}
	}
	&__con {
		padding: 16px 20px;
		background-color: #fff;
		margin-top: 16px;
		.el-aside {
			border: 1px solid #e5e5e5;
			padding: 16px;
		}
		.el-main {
			padding: 0 0 0 16px;
			img {
				width: 60px;
				height: 60px;
			}
			.pagination {
				text-align: center;
				margin-top: 16px;
			}
		}
		::v-deep .el-switch.is-disabled .el-switch__core,
		::v-deep .el-switch.is-disabled .el-switch__label {
			cursor: pointer;
		}
		::v-deep .el-switch.is-disabled {
			opacity: 1;
		}
	}
}
::v-deep .el-tree-node__content {
	border-color: #4974f5;
	padding: 7px 0;
}
::v-deep .el-tree-node__label {
	line-height: 18px;
}
</style>
<style lang="css">
.el-tooltip__popper {
	max-width: 300px;
}
</style>
