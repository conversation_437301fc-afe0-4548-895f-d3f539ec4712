<template>
	<div class="type-box">
		<!-- 分类栏目 -->
		<span
			v-for="(item, index) in typeList"
			:key="index"
			:class="['type-box-item', navCode == item.code ? 'active-item' : '']"
			@click="typeClick(item)"
		>
			{{ item.name }}
		</span>
	</div>
</template>

<script>
export default {
	name: 'TabsNav',
	props: {
		// 类型列表
		typeList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		//选中的类型
		activeCode: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			navCode: '' //选中的类型
		};
	},
	watch: {
		activeCode(value) {
			this.navCode = this.activeCode;
		}
	},
	methods: {
		/**
		 * @description 类型点击事件，用于切换内容
		 * @params {item} 点击的类型数据
		 */
		typeClick(item) {
			this.navCode = item.code;
			this.$emit('click', item);
		}
	}
};
</script>

<style lang="scss" scoped>
.type-box {
	width: 100%;
	height: 72px;
	padding: 20px;
	background: #ffffff;
	&-item {
		font-size: 16px;
		font-weight: 400;
		color: #404040;
		line-height: 24px;
		padding: 4px 16px;
		margin-right: 8px;
		cursor: pointer;
		display: inline-block;
	}
	.active-item {
		background: var(--brand-6, #0076e8);
		border-radius: 21px;
		color: #ffffff;
		line-height: 24px;
	}
}
</style>
