<template>
	<div class="usullayApp">
		<el-carousel
			arrow="never"
			:interval="5000"
			:autoplay="false"
			indicator-position="outside"
			height="340px"
		>
			<el-carousel-item v-for="(outeritem, outerindex) in myUsullyApp" :key="outerindex">
				<div v-for="(senitem, senindex) in outeritem" :key="senindex" class="rowUsullay">
					<div v-for="(item, index) in senitem" :key="index" class="rowUsullayItem">
						<div v-if="item.name !== 'emptyText'" class="itemUsullay" @click="toOpenWindow(item)">
							<div class="topPicUsullay">
								<img :src="handelPicUrl(item.icons)" alt="" srcset="" width="40px" />
							</div>
							<div class="textUsullay">{{ item.text }}</div>
						</div>
					</div>
				</div>
			</el-carousel-item>
		</el-carousel>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
export default {
	data() {
		return {
			loading: false,
			myUsullyApp: []
		};
	},
	mounted() {
		this.getListData();
		// this.getListSort();
	},
	methods: {
		toOpenWindow(item) {
			if (item.id == '11111111') {
				return;
			}
			// window.open(`${picUrl}${item.newDataUrl}${item.code}`);
			window.open(`${alumniUrl}${item.url}`);
		},
		//处理图片路径
		handelPicUrl(url) {
			return `${alumniUrl}/static/andyui2.0/css/picIcon/img/${url}`;
		},
		//获取接口数据
		getListData() {
			this.$api.vocational_education_hall_api
				.usuSet()
				.then(res => {
					let arr = [];
					res.results.forEach(i => {
						i.children.forEach(item => {
							if (item.url == '') {
								console.log(item, '');
							} else {
								arr.push(item);
							}
						});
					});
					let list = arr;
					for (let i = 0; i < Math.ceil(list.length / 15); i++) {
						let arr = []; //页
						if (i < Math.floor(list.length / 15)) {
							for (let k = 0; k < 3; k++) {
								let itemArr = []; //行
								for (let j = 0; j < 5; j++) {
									itemArr.push(list[i * 15 + k * 5 + j]);
								}
								arr.push(itemArr);
							}
						} else {
							let remain = list.length % 15;
							let remainRow = Math.ceil(remain / 5);
							for (let i = 0; i < remainRow; i++) {
								let itemArr = [];
								for (let k = 0; k < 5; k++) {
									if (list[list.length - remain + i * 5 + k]) {
										itemArr.push(list[list.length - remain + i * 5 + k]);
									} else {
										itemArr.push({
											icons: '',
											text: '',
											id: '11111111'
										});
									}
								}
								arr.push(itemArr);
							}
						}
						this.myUsullyApp.push(arr);
					}
				})
				.catch(error => {});
		},
		//获取排序接口
		getListSort() {
			this.$request({
				url: '/gwapi/ybzy/platUserCommfunc/front/dragSort',
				data: {
					sortNum: '0'
				},
				method: 'POST'
			})
				.then(res => {})
				.catch(error => {});
		},
		//
		getAddListSort() {
			let data = {
				commId: 'fb88eea4ad734ee2a8d04e1221a14d56'
			};
			this.$api.vocational_education_hall_api
				.usuAddSet(data)
				.then(res => {})
				.catch(error => {});
		}
	}
};
</script>

<style lang="scss" scoped>
.usullayApp {
	width: 100%;
	height: 380px;
	padding: 10px 30px 0 30px;
	background: #fff;
	// overflow: hidden;
	.rowUsullay {
		width: 100%;
		height: 114px;
		display: flex;
		.rowUsullayItem {
			flex: 1;
			height: 100%;
			display: flex;
			justify-content: center;
			.itemUsullay {
				cursor: pointer;
				width: 70px;
				height: 100%;

				.topPicUsullay {
					width: 100%;
					height: 70px;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					// background: #4545ef;
				}
				.textUsullay {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 100%;
					text-align: center;
					height: 36px;
					line-height: 36px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #333333;
				}
			}
		}
	}
}
::v-deep .is-active .el-carousel__button {
	// 指示器激活按钮
	background: var(--brand-6, #0076e8);
	height: 10px;
	width: 22px;
	border-radius: 5px;
}
::v-deep .el-carousel__button {
	// 指示器按钮
	width: 10px;
	height: 10px;
	background: #c4c4c6;
	border-radius: 50%;
}
</style>
