<template>
	<div v-loading="loading" :class="{ 'upload-item': true, tips: tips }">
		<input
			ref="inputFile1"
			type="file"
			style="display: none"
			:accept="accept"
			multiple
			@change="inputChange"
		/>
		<div class="left">
			<div v-for="(item, index) in files" :key="index" class="file-item">
				<div v-if="item.imgUrl" class="img-box">
					<el-image class="img" :src="item.imgUrl" :preview-src-list="srcList" lazy></el-image>
					<div v-if="!onlyRead" class="op-btn">
						<el-button
							type="danger"
							icon="el-icon-delete"
							circle
							size="mini"
							title="删除"
							@click="dltFile(item)"
						></el-button>
						<el-button
							type="primary"
							icon="el-icon-refresh-right"
							circle
							size="mini"
							title="重选"
							@click="addFile(item)"
						></el-button>

						<!-- <i class="iconfont el-icon-delete" title="删除" @click="dltFile(item)"></i>
						<i class="iconfont el-icon-refresh" title="重选" @click="addFile(item)"></i> -->
					</div>
				</div>
				<div v-else class="file">
					<!-- 文件名显示方案 超过11个字中间。。。替代 -->
					<p class="file-name">
						<!-- {{
							item.originalName.length > 11
								? item.originalName.substring(0, 4) +
								  '....' +
								  item.originalName.substring(item.originalName.length - 7)
								: item.originalName
						}} -->
						{{ item.originalName }}
					</p>
					<div class="download">
						<i v-if="!onlyRead" class="el-icon-delete" @click="dltFile(item)"></i>
						<i class="el-icon-download" @click="download(item.adjunctId)"></i>
					</div>
				</div>
			</div>
			<div v-if="files.length < fileNumber && !onlyRead" class="img add-btn" @click="addFile">
				<i class="el-icon-plus avatar-uploader-icon"></i>
			</div>
		</div>
		<div v-if="tips" class="el-upload__tip">只能上传jpg/jpeg/png文件，且不超过{{ filesize }}kb</div>
	</div>
</template>

<script>
// import { alumniUrl } from '@/config';

export default {
	name: 'UploadItem',
	props: {
		accept: {
			type: String,
			default: 'image/jpeg,image/png,image/jpg'
		},
		code: {
			type: String,
			default: ''
		},
		ownId: {
			type: String,
			default: ''
		},
		onlyRead: {
			type: Boolean,
			default: false
		},
		fileNumber: {
			type: Number,
			default: 1
		},
		// kb为单位
		filesize: {
			type: Number,
			default: 5 * 1024
		},
		tips: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: false,
			srcList: [],
			selectedFile: {}, // 需要删除的文件
			files: []
		};
	},
	computed: {
		userInfo() {
			return this.$store.state.user.userInfo;
		}
	},
	watch: {
		ownId(val) {
			if (val) {
				this.getFiles(val);
			}
		}
	},
	mounted() {
		this.getFiles();
		// this.$api.personal_api.previewFile({ id: 'f87cc977-3422-4967-af44-35b5d8e9f74a' }).then(res => {
		// 	console.log('res', res);
		// });
	},
	methods: {
		download(id) {
			window.open(
				// `${alumniUrl}/ybzy/mecpfileManagement/front/downloadByAdjunctId.json?adjunctId=${id}`
				this.$judgeFile(`/ybzy/mecpfileManagement/front/downloadByAdjunctId.json?adjunctId=${id}`)
			);
		},
		seeFile() {},
		addFile(item) {
			this.selectedFile = item;
			this.$refs.inputFile1.click();
		},
		dltFile(item) {
			this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				this.loading = true;
				this.$api.alumni_api
					.deleteFile({ id: item.adjunctId, userName: this.userInfo.username })
					.then(res => {
						if (res.rCode === 0) {
							this.getFiles();
						}
					});
			});
		},
		getFiles(ownId) {
			this.loading = true;
			this.$api.alumni_api
				.getFileInfo({ code: this.code, ownId: ownId || this.ownId, userId: this.userInfo.id })
				.then(async res => {
					if (res.rCode === 0) {
						const imgReqAll = [];
						for (const item of res.results) {
							this.srcList.push('');
							if (
								['bmp', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'apng'].indexOf(item.suffix) >
								-1
							) {
								// 获取到blob数据
								const req = await this.$api.alumni_api.previewFile({ adjunctId: item.adjunctId });
								imgReqAll.push(req);
							} else {
								imgReqAll.push(null);
							}
						}
						const resAll = await Promise.all(imgReqAll);
						for (const item of resAll) {
							const index = resAll.indexOf(item);
							if (item) {
								this.changeImgToBase64(item, index);
							}
						}
						this.files = res.results;
						this.$emit('fileChange', res.results);
						this.loading = false;
					}
				});
		},
		changeImgToBase64(item, index) {
			// console.log('>>>item, index', item, index);
			const that = this;
			let reader = new FileReader();
			reader.onload = function (e) {
				const oItem = that.files[index];
				oItem.imgUrl = e.target.result;
				that.files.splice(index, 1, oItem);
				that.srcList.splice(index, 1, e.target.result);
			};
			reader.readAsDataURL(item);
			// reader.readAsDataURL(item.data);
		},
		// 上传文件
		async inputChange(e) {
			if (!e.target.files.length) {
				return;
			}
			if (this.selectedFile && this.selectedFile.adjunctId) {
				// 重选文件先删除该文件
				this.$api.alumni_api
					.deleteFile({ id: this.selectedFile.adjunctId, userName: this.userInfo.username })
					.then(res => {
						if (res.rCode === 0) {
							this.selectedFile = {};
						}
					});
			}
			let file = e.target.files;
			let filesize = null;

			if (this.fileNumber > 1) {
				const requests = [];
				for (let i = 0; i < file.length; i++) {
					let item = file[i];
					filesize = item.size;
					if (filesize > this.filesize * 1024) {
						this.$Message.warning('文件过大，请重新选择！');
						return;
					}
					requests.push(this.uploadFn(item));
				}
				try {
					// 在这里处理所有请求的响应

					const responses = await Promise.all(requests);
					for (let j = 0; j < responses.length; j++) {
						if (responses[j].rCode != 0) {
							this.$message.error(responses[j].msg);
							return;
						}
					}
					this.uploadCallback();
				} catch (error) {
					// 在这里处理错误情况
					this.$message.error('请求出错');
					console.error('请求出错：', error);
				}
			} else {
				// 利用fileReader对象获取file
				// this.fileUrl = file;
				filesize = file[0].size;
				if (filesize > this.filesize * 1024) {
					this.$Message.warning('文件过大，请重新选择！');
					return;
				}
				let { rCode, msg } = await this.uploadFn(file[0]);
				if (rCode === 0) {
					this.uploadCallback();
				} else {
					this.$message.error(msg);
				}
			}
		},

		async uploadFn(file) {
			this.loading = true;
			const formData = new FormData();
			formData.append('file', file);
			formData.append('code', this.code);
			formData.append('ownId', this.ownId);
			formData.append('userId', this.userInfo.id);
			let url = this.fileNumber > 1 ? 'uploadMoreFile' : 'uploadFile';
			let res = await this.$api.alumni_api[url](formData);
			this.loading = false;
			return res;
		},
		uploadCallback() {
			this.getFiles();
			this.$refs.inputFile1.value = '';
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.upload-item {
	position: relative;
	&.tips {
		padding-bottom: 20px;
	}
	.el-upload__tip {
		position: absolute;
		bottom: 0px;
		left: 0px;
		width: 300px;
	}
	.left {
		width: auto;
		overflow: hidden;
		.file-item {
			width: 106px;
			min-height: 106px;
			border: 1px dashed #bfbfbf;
			position: relative;
			float: left;
			margin-right: 4px;
			margin-bottom: 6px;
			border-radius: 6px;
			overflow: hidden;

			.img-box {
				width: 100%;
				height: 104px;
				.img {
					width: 100%;
					height: 100%;
				}
			}
			.op-btn {
				position: absolute;
				right: 4px;
				top: 6px;
				i {
					cursor: pointer;
					margin-left: 6px;
					color: #666;
				}
			}
		}
		.add-btn {
			cursor: pointer;
			font-size: 48px;
			line-height: 106px;
			width: 106px;
			height: 106px;
			text-align: center;
			color: #999;
			border: 1px dashed #bfbfbf;
			border-radius: 6px;
			float: left;
			display: flex;
			align-items: center;
			justify-content: center;
			.avatar-uploader-icon {
				font-size: 28px;
				color: #8c939d;
				line-height: 100%;
				text-align: center;
			}
		}
		.title {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.85);
			padding-left: 40px;
			position: relative;
			&::before {
				position: absolute;
				content: '*';
				left: 12px;
				top: 6px;
			}
		}
		.file-name {
			padding: 0 10px;
			line-height: 20px;
		}
		.download {
			@include flexBox(space-evenly);
			width: 100%;
			text-align: center;
			margin: 0 auto;
			margin-top: 10px;
		}
	}
	.right {
		padding: 24px 0;
		flex: 1;
		font-size: 12px;
		color: #8c8c8c;
		line-height: 26px;
	}
}
</style>
