<!--
 @desc:个人中心 校友会 校友通讯录
 @author: WH
 @date: 2023/9/11
 -->
<template>
	<div class="main">
		<div v-loading="loading" class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无校友信息',
					detail: '你还没有校友信息',
					clickText: ''
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<div class="search-box">
					<div class="search">
						<el-input
							v-model="searchData"
							placeholder="请输入姓名/手机号"
							type="input"
							suffix-icon="el-icon-search"
						></el-input>
						<el-button type="primary">搜索</el-button>
					</div>
					<div class="show-list-type">
						<p :class="{ 'p-actice': pActive === 'list' }" @click="changeShow('list')">
							<i class="el-icon-s-fold"></i>
							列表
						</p>
						<p :class="{ 'p-actice': pActive === 'card' }" @click="changeShow('card')">
							<i class="el-icon-menu"></i>
							卡片
						</p>
					</div>
				</div>
				<div class="list-card">
					<transition name="fade">
						<el-table v-if="pActive === 'list'" :data="list" border style="width: 100%">
							<el-table-column prop="userName" label="姓名">
								<template slot-scope="scope">
									<div class="row-name">
										<!-- <img
											class="is-img"
											:src="
												getImgUrl(scope.row.headImg) ||
												require('@/assets/shop-images/default-avatar.png')
											"
											alt=""
										/> -->
										<headAvator :own-id="scope.row.userId" class="is-img" />
										<span>{{ scope.row.memberName }}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="phoneNum" label="手机号" />
							<el-table-column prop="email" label="邮箱" />
							<el-table-column prop="currentCompany" label="公司" />
							<el-table-column prop="currentPosition" label="职位" />
						</el-table>
					</transition>
					<transition name="fade">
						<div v-if="pActive === 'card'" class="card-box">
							<ab-card
								v-for="(item, index) in list"
								:key="index"
								:card-data="item"
								:class="{ 'mgr-0': [3, 7, 11].includes(index) }"
							/>
						</div>
					</transition>
				</div>

				<!-- <address-book-card v-for="(item, index) in list" :key="index" /> -->
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import AbCard from './address-book/ab-card.vue';
import NoData from './no-data.vue';
import { alumniUrl } from '@/config';

export default {
	components: { AbCard, NoData },
	props: {
		params: {
			type: Object,
			required: false,
			default: () => {}
		}
	},
	data() {
		return {
			pActive: 'list',
			searchData: '',
			loading: true,
			list: [],
			page: 1, // 页数
			size: 10, // 条数
			total: 0
		};
	},
	watch: {
		params() {
			this.getMember();
		}
	},
	mounted() {
		this.getMember();
	},

	methods: {
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		//分页查询社区成员—————————————————
		async getMember() {
			this.loading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getMember({
					pageNum: this.page,
					pageSize: this.size,
					communityId: this.params.communityId
				});
				if (rCode == 0) {
					this.list = results?.records;
					this.total = results.total;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},

		changeShow(type) {
			if (this.pActive == type) return;
			this.pActive = type;
		},
		noDataFn() {},
		// 条数
		handleSizeChange(i) {
			this.pageSize = i;
			this.pageNum = 1;
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getMember();
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.main {
	padding: 20px;
	background: #fff;

	.list-box {
		width: 100%;
		// height: 680px;
		padding: 20px 0px;
		border-radius: 4px;
		// overflow: auto;
		.search-box {
			@include flexBox(space-between);
			margin-bottom: 20px;
			.search {
				@include flexBox();
				.el-button {
					margin-left: 16px;
				}
			}
			.show-list-type {
				@include flexBox();
				p {
					@include flexBox();
					width: 70px;
					height: 28px;
					border-radius: 4px;
					font-size: 14px;
					color: #7a8392;
					background: #fff;
					border: 1px solid #dcdfe6;
					margin-left: 10px;
					cursor: pointer;
				}
				.p-actice {
					color: #ffffff;
					background: #0076e8;
				}
			}
		}
		.list-card {
			width: 100%;
			height: 900px;
			overflow: auto;
		}
		.card-box {
			@include flexBox(flex-start);
			width: 980px;
			flex-wrap: wrap;
			.mgr-0 {
				margin-right: 0;
			}
		}
	}
	.row-name {
		@include flexBox(flex-start);
		.is-img {
			width: 40px;
			height: 40px;
			border-radius: 50%;
			margin-right: 14px;
		}
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
	opacity: 0;
}
</style>
