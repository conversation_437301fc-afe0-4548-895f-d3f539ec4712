<template>
	<div class="person">
		<div class="person-left">
			<div class="person-left-con">
				<el-menu
					v-loading="menuLoading"
					:default-active="activeMenu"
					class="el-menu-vertical-demo"
					active-text-color="#3274E0"
					text-color="#262626"
					@select="select"
					@open="handleOpen"
					@close="handleClose"
				>
					<template v-for="menu of menus">
						<el-submenu v-if="menu.children" :key="menu.index" :index="menu.index">
							<template slot="title">{{ menu.name }}</template>
							<el-menu-item
								v-for="menuSon of menu.children"
								:key="menuSon.index"
								:index="menuSon.index"
							>
								<span slot="title" class="menu-children-title u-line-1">{{ menuSon.name }}</span>
							</el-menu-item>
						</el-submenu>
						<el-menu-item v-else :key="menu.index" :index="menu.index">
							<span slot="title">{{ menu.name }}</span>
						</el-menu-item>
					</template>
				</el-menu>
			</div>
		</div>
		<div class="person-right">
			<subBreadcrumb :is-main="false" background="transparent"></subBreadcrumb>
			<personHeader :title="activeName"></personHeader>
			<component
				:is="activeComponent"
				:key="params.key || ''"
				:is-main="false"
				:params="params"
			></component>
		</div>
		<invite-join-dialog
			v-loading="loading"
			:visible="inviteJoinVisible"
			:info="communityData"
			@handle="handle"
		/>
	</div>
</template>

<script>
import Authentication from './components/authentication.vue';
import SetUpCommunity from './components/community/set-up.vue';
import JoinCommunity from './components/community/join.vue';
import InviteJoinDialog from './components/community/invite-join-dialog.vue';
import Activity from './components/activity.vue';
import AddressBook from './components/address-book.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import personHeader from '@/components/person-header';
import { getParams } from '@/utils/url_name';

export default {
	components: {
		Authentication,
		SetUpCommunity,
		JoinCommunity,
		Activity,
		subBreadcrumb,
		personHeader,
		AddressBook,
		InviteJoinDialog
	},
	data() {
		return {
			loading: false,
			menuLoading: false,
			inviteJoinVisible: false,
			communityData: {},
			activeMenu: '1-1',
			activeComponent: 'Authentication',
			activeName: '校友认证',
			menus: [
				{
					name: '校友认证',
					index: '1-1',
					path: 'Authentication',
					params: { key: 3, showActiveCode: 'works' }
				},
				{
					name: '校友通讯录',
					index: '2-1',
					path: 'AddressBook'
				},
				{
					name: '校友社区',
					index: '3-1',
					children: [
						{
							name: '我创建的社区',
							index: '3-1-1',
							path: 'SetUpCommunity'
						},
						{
							name: '我加入的社区',
							index: '3-1-2',
							path: 'JoinCommunity'
						}
					]
				},
				{
					name: '我报名的校友活动',
					index: '4-1',
					path: 'Activity'
				}
			],
			params: { key: 3, showActiveCode: 'works' }
		};
	},
	mounted() {
		this.getAddressBook();
		//检测到路径中有邀请码就弹出邀请弹窗
		let inviter = getParams('inviter');
		let communityId = getParams('communityId');
		if (inviter) {
			this.inviteInfo(inviter, communityId);
			this.inviteJoinVisible = true;
		}
	},
	methods: {
		handle() {
			this.inviteJoinVisible = false;
			this.activeMenu = '3-1-2';
			this.activeComponent = 'JoinCommunity';
			this.activeName = '我加入的社区';
		},
		findMenu(array, target) {
			for (let i = 0; i < array.length; i++) {
				const element = array[i];
				if (element?.children) {
					let result = this.findMenu(element.children, target);
					if (result !== undefined) {
						return result;
					}
				} else if (element.index === target) {
					return element;
				}
			}
			return undefined;
		},
		handleOpen(key, keyPath) {
			if (key == '2-1') {
				this.getAddressBook();
			}
		},
		handleClose(key, keyPath) {},
		/**选中菜单项*/
		select(index, path) {
			let currentObj = this.findMenu(this.menus, index);
			if (currentObj.params) {
				this.params = currentObj.params;
			} else {
				this.params = {};
			}
			// console.log('>>>currentObj', index, path, currentObj);
			this.activeMenu = currentObj.index;
			this.activeName = currentObj.name;
			this.activeComponent = currentObj.path;
		},
		async inviteInfo(inviter, id) {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.alumni_api.inviteInfo({ inviter, id });
				if (rCode == 0) {
					this.communityData = results;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		async getAddressBook() {
			try {
				this.menuLoading = true;
				let { rCode, msg, results } = await this.$api.alumni_api.getAddressBook();
				if (rCode == 0) {
					let list = results.map((item, index) => {
						return {
							name: item.communityName,
							index: '2-1-' + (index + 1),
							path: 'AddressBook',
							params: { communityId: item.id }
						};
					});

					if (list?.length > 0) {
						this.$set(this.menus, 1, {
							name: '校友通讯录',
							index: '2-1',
							children: list
						});
					}
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.menuLoading = false;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.person {
	width: 1260px;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	&-left {
		width: 220px;
		margin-right: 16px;
		flex-shrink: 0;
		background: #ffffff;
	}
	&-right {
		width: calc(100% - 240px);
	}
}
.menu-children-title {
	display: inline-block;
	width: 100%;
}
// .is-active {
// 	position: relative;
// 	&::before {
// 		content: '';
// 		display: inline-block;
// 		position: absolute;
// 		left: 20px;
// 		top: calc(50% - 7px);
// 		width: 16px;
// 		height: 16px;
// 		border-radius: 50%;
// 		background: #3274e0;
// 	}
// }
</style>
