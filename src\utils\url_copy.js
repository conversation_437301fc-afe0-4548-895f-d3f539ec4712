// 页面打印
import ElementUI from 'element-eoss';
function urlCopy() {
	let url = window.location.href; //拿到想要复制的值
	let copyInput = document.createElement('input'); //创建input元素
	document.body.appendChild(copyInput); //向页面底部追加输入框
	copyInput.setAttribute('value', url); //添加属性，将url赋值给input元素的value属性
	copyInput.select(); //选择input元素
	document.execCommand('Copy'); //执行复制命令
	ElementUI.Message.success('复制成功！'); //弹出提示信息，不同组件可能存在写法不同
	//复制之后再删除元素，否则无法成功赋值
	copyInput.remove(); //删除动态创建的节点
	// this.$message.close();
	// this.$message.success('复制成功');
}
export default urlCopy;
