<template>
	<div class="home">
		<div class="top">
			<div class="avatar">
				<img src="" alt="" />
			</div>
			<div class="mid">
				<div class="name nth1">西部物流大数据中心用户2938</div>
				<div class="btn">修改个人信息 ></div>
			</div>
			<div class="rf">
				<div class="cell">
					<div class="title">账户安全：</div>
					<div class="val org">普通</div>
				</div>
				<div class="cell">
					<div class="title">绑定手机：</div>
					<div class="val">13888888888</div>
				</div>
				<div class="cell">
					<div class="title">绑定邮箱：</div>
					<div class="val">普通</div>
				</div>
			</div>
		</div>
		<div class="btm">
			<div class="item">
				<div class="img org">
					<AlIcon name="icon-daifukuanb" color="#fff" size="60"></AlIcon>
				</div>
				<div class="rf">
					<div class="num">
						待支付的订单：
						<span>1</span>
					</div>
					<div class="btn">查看待支付订单 ></div>
				</div>
			</div>
			<div class="item">
				<div class="img green">
					<AlIcon name="icon-daishouhuo" color="#fff" size="60"></AlIcon>
				</div>
				<div class="rf">
					<div class="num">
						待收货的订单：
						<span>1</span>
					</div>
					<div class="btn">查看待收货订单 ></div>
				</div>
			</div>
			<div class="item">
				<div class="img blue">
					<AlIcon name="icon-daipingjia" color="#fff" size="60"></AlIcon>
				</div>
				<div class="rf">
					<div class="num">
						待评价商品数：
						<span>1</span>
					</div>
					<div class="btn">查看待评价商品 ></div>
				</div>
			</div>
			<div class="item">
				<div class="img yellow">
					<AlIcon name="icon-tuikuanzhong" color="#fff" size="60"></AlIcon>
				</div>
				<div class="rf">
					<div class="num">
						退款中的订单：
						<span>1</span>
					</div>
					<div class="btn">查看退款订单 ></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Home'
};
</script>

<style lang="scss" scoped>
.nth1 {
	overflow: hidden; /*内容会被修剪，并且其余内容是不可见的*/
	text-overflow: ellipsis; /*显示省略符号来代表被修剪的文本。*/
	white-space: nowrap; /*文本不换行*/
}
.home {
	padding: 32px 42px 154px 34px;
	.top {
		display: flex;
		align-items: center;
		.avatar {
			border-radius: 50%;
			width: 164px;
			height: 164px;
			background: #ffffff;
			opacity: 1;
			border: 1px solid #d9d9d9;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			img {
				width: 150px;
				height: 150px;
				background: red;
				border-radius: 50%;
			}
		}
		.mid {
			margin-left: 16px;
			width: calc(100% - 400px);
			.name {
				font-size: 20px;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
			}
			.btn {
				margin-top: 37px;
				font-size: 12px;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 20px;
			}
		}
		.rf {
			margin-left: auto;
			.cell {
				display: flex;
				align-items: center;
				margin-bottom: 14px;
				.title {
					font-size: 14px;
					font-weight: 400;
					color: #9da5b7;
					line-height: 22px;
				}
				.val {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
				}
				.org {
					color: #f2a665;
				}
			}
		}
	}
	.btm {
		margin-top: 16px;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 36px 32px 0 32px;
		.item {
			width: 49%;
			display: flex;
			margin-bottom: 64px;
			align-items: center;
			.img {
				width: 102px;
				height: 102px;
				background-color: #f2a665;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.org {
				background: linear-gradient(133deg, #f1ab78 0%, #ff652f 100%);
			}
			.green {
				background: linear-gradient(137deg, #a5ea9a 0%, #3fa449 100%);
			}
			.blue {
				background: linear-gradient(133deg, #8bd8ff 0%, #2b99fe 100%);
			}
			.yellow {
				background: linear-gradient(128deg, #ffa22b 0%, #ffc42b 100%);
			}
			.rf {
				margin-left: 13px;
				.num {
					font-size: 16px;
					font-weight: 500;
					color: #404040;
					line-height: 24px;
					span {
						font-size: 16px;
						font-weight: 500;
						color: var(--brand-6, '#ca3f3b');
					}
				}
				.btn {
					margin-top: 19px;
					font-size: 12px;
					font-weight: 400;
					color: #404040;
					line-height: 20px;
				}
			}
		}
	}
}
</style>
