<!--
 * @Description: 高德地图
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-03-30 15:53:35
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-03-31 14:55:23
-->
<template>
	<div v-if="firstArr && firstArr.length > 0" id="container" class="container">
		<el-amap vid="amap" :zoom="zoom" :center="firstArr">
			<!-- 绘制点 -->
			<el-amap-marker
				:offset="offset"
				:position="firstArr"
				animation="AMAP_ANIMATION_DROP"
				:icon="imgUrl"
				:auto-rotation="true"
				:angle="angle"
			/>
			<!-- 绘制完整的路线 -->
			<el-amap-polyline
				:path="lineArr"
				:show-dir="true"
				stroke-color="#28F"
				:stroke-opacity="1"
				:stroke-weight="3"
				stroke-style="solid"
				line-join="round"
			/>
			<!-- 绘制已经过路线 -->
			<el-amap-polyline
				:path="line"
				:show-dir="true"
				stroke-color="#ca3f3b"
				:stroke-opacity="1"
				:stroke-weight="3"
				stroke-style="solid"
			/>
		</el-amap>
	</div>
</template>

<script>
import icon from '@/assets/shop-images/police-badge.png';
export default {
	name: 'Amap',
	props: {
		imgUrl: {
			type: String,
			default: 'https://webapi.amap.com/images/car.png'
		},
		logistics: {
			type: Array,
			default: () => {
				return [];
			}
		},
		newest: {
			type: Object,
			default: () => {
				return {};
			}
		},
		angle: {
			type: Number,
			default: -90
		},
		offset: {
			type: Array,
			default: () => {
				return [-26, -13];
			}
		},
		zoom: {
			type: Number,
			default: 4
		}
	},
	data: () => {
		return {
			icon,
			map: null,
			polyline: null,
			// 绘制线路需要的坐标
			lineArr: [
				// [116.478935, 39.997761],
				// [108.983569, 34.285675],
				// [103.85094, 35.987496],
				// [106.205794, 38.458831],
				// [111.761777, 40.875595]
			],
			line: [
				// [116.478935, 39.997761],
				// [108.983569, 34.285675]
			],
			firstArr: [108.983569, 34.285675] // 中心点/初始坐标
		};
	},
	watch: {
		logistics: {
			handler(newName, oldName) {
				this.firstArr = [
					this.logistics[this.logistics.length - 1].longitude,
					this.logistics[this.logistics.length - 1].latitude
				];
				// this.lineArr = [
				// 	[parseFloat(this.logistics[0].longitude), parseFloat(this.logistics[0].latitude)],
				// 	[parseFloat(this.newest.longitude), parseFloat(this.newest.latitude)]
				// ];
			},
			immediate: true
		}
	},
	created() {},
	mounted() {
		setTimeout(() => {
			// this.initMap(); // 异步加载（否则报错initMap is not defined）
			// this.initroad()
		}, 1000);
	},
	methods: {
		// 初始化地图
		// initMap() {
		// 	var that = this;
		// 	this.map = new VueAMap.Map('container', {
		// 		resizeEnable: true, // 窗口大小调整
		// 		center: this.firstArr, // 中心 firstArr: [116.478935, 39.997761],
		// 		zoom: 5
		// 	});
		// 	// 添加maker
		// 	this.marker = new VueAMap.Marker({
		// 		map: this.map,
		// 		position: this.firstArr,
		// 		icon: 'https://webapi.amap.com/images/car.png',
		// 		offset: new VueAMap.Pixel(-26, -13), // 调整图片偏移
		// 		autoRotation: true, // 自动旋转
		// 		angle: -90 // 图片旋转角度
		// 	});
		// 	that.initroad();
		// },
		// 初始化轨迹
		// initroad() {
		// 	// 绘制还未经过的路线
		// 	this.polyline = new VueAMap.Polyline({
		// 		map: this.map,
		// 		path: this.lineArr,
		// 		showDir: true,
		// 		strokeColor: '#77DDFF', // 线颜色--浅蓝色
		// 		// strokeOpacity: 1,     //线透明度
		// 		strokeWeight: 6, // 线宽
		// 		// strokeStyle: "solid"  //线样式
		// 		lineJoin: 'round' // 折线拐点的绘制样式
		// 	});
		// 	// 绘制路过了的轨迹
		// 	var passedPolyline = new VueAMap.Polyline({
		// 		map: this.map,
		// 		strokeColor: '#00BBFF', // 线颜色-深蓝色
		// 		path: [
		// 			[116.478935, 39.997761],
		// 			[108.983569, 34.285675]
		// 		],
		// 		// strokeOpacity: 1,     //线透明度
		// 		strokeWeight: 6 // 线宽
		// 		// strokeStyle: "solid"  //线样式
		// 	});
		// 	this.map.setFitView(); // 合适的视口
		// }
	}
};
</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 100%;
}
</style>
