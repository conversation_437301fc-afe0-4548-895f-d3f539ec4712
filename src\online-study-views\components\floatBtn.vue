<template>
	<div class="floatBtn">
		<div class="item" @click="goShoppingCar">
			<span class="num">{{ carNum }}</span>
			<i class="iconfont icon-gouwuche1-copy-copy"></i>
			<div class="word">购物车</div>
		</div>
		<div class="item" @click="goOrder">
			<i class="iconfont icon-dingdan"></i>
			<div class="word">订单</div>
		</div>
		<div class="item" @click="goCollet">
			<i class="iconfont icon-shoucang1"></i>
			<div class="word">收藏</div>
		</div>
	</div>
</template>

<script>
import { getToken } from '@/utils/auth';
export default {
	name: 'FloatBtn',
	data() {
		return {
			carNum: 0
		};
	},
	computed: {},
	mounted() {
		if (getToken()) {
			this.getCarLength();
		}
	},
	methods: {
		async getCarLength() {
			this.$api.study_api.getCartGoods().then(res => {
				console.log('购物车更新');
				this.carNum = res.data?.length || 0;
			});
		},
		goShoppingCar() {
			this.$router.push({ path: '/study-car' });
		},
		goOrder() {
			this.$router.push({ path: '/study-order-list' });
			// let url = config.url.replace(/\./g, '/');
			// window.open(
			// 	`${config.domainName}/?scwl_main_view=%2Fzzfw%2F%23%2FlawLectureHall%2ForderList#/${url}/zzfw/lawLectureHall/orderList`
			// );
		},
		goCollet() {
			this.$router.push({ path: '/collet-list' });
			// let url = config.url.replace(/\./g, '/');
			// window.open(
			// 	`${config.domainName}/?scwl_main_view=%2Fzzfw%2F%23%2FlawLectureHall%2FcolletList#/${url}/zzfw/lawLectureHall/colletList`
			// );
		}
	}
	// created() {
	// 	this.getAssessmentTagListFn();
	// },
	// async mounted() {
	// 	if (!this.userInfo.id) {
	// 		let queryString = '?';
	// 		queryString += ['redirect', location.href].join('=');
	// 		localStorage.setItem('scwl_homepage', 'http://scswl.eimm.wisesoft.net.cn:8099/zzfw/#/');
	// 		window.location.href = `${config.domainUrl}scwl_user_main/#/login${queryString}`;
	// 		return;
	// 	} else {
	// 		if (!getLessonToken()) {
	// 			await this.loginFrontFn();
	// 		}
	// 	}
	// }
};
</script>

<style lang="scss" scoped>
.floatBtn {
	position: fixed;
	z-index: 500;
	right: 10px;
	background: #fff;
	top: 50%;
	transform: translateY(-50%);
	width: 70px;
	box-sizing: border-box;
	padding: 5px 10px;
	box-shadow: 0px 0px 10px 0px #e2dede;
	border-radius: 10px 10px 10px 10px;
	.item {
		position: relative;
		cursor: pointer;
		flex-direction: column;
		align-items: center;
		// height: 50px;
		display: flex;
		border-bottom: 1px solid #e2dede;
		padding: 5px 0;
		.num {
			position: absolute;
			right: 0;
			top: 0;
			font-weight: bold;
			color: #fff;
			background: #ff0000;
			display: inline-block;
			text-align: center;
			width: 20px;
			height: 20px;
			border-radius: 50%;
			line-height: 20px;
		}
		.iconfont {
			margin-bottom: 5px;
			color: var(--brand-6, #0076e8);
			font-size: 24px;
		}
		.word {
			font-size: 14px;
			color: #666;
		}
	}
}
</style>
