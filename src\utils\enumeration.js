/**后端返回数据枚举匹配*/
const enumeration = {
	// 身份相关
	identity: {
		student: '学生',
		teacher: '教师',
		enterprise: '企业',
		business: '商家'
	},
	// 权限相关
	authority: {
		student: 1,
		teacher: 2,
		enterprise: 3,
		business: 4
	}
};
/**
 * @Methods 获取相应的枚举
 * @Params key 哪方面的枚举
 * @Params cede 枚举对应的值
 * */
function getEnumeration(key, code) {
	if (enumeration[key] && enumeration[key][code]) {
		return enumeration[key][code];
	} else {
		return '';
	}
}
export default getEnumeration;
