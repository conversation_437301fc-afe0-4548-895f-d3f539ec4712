<template>
	<section class="app-main">
		<transition name="fade-transform" mode="out-in">
			<router-view :key="key" />
		</transition>
	</section>
</template>

<script>
export default {
	name: 'AppMain',
	computed: {
		key() {
			return this.$route.path;
		}
	}
};
</script>

<style scoped>
.app-main {
	/* navbar 350px  */
	min-height: calc(100vh - 350px);
	position: relative;
	overflow: hidden;
	margin: 0 auto;
}
.fixed-header + .app-main {
	padding-top: 0px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
	.fixed-header {
		padding-right: 15px;
	}
}
</style>
