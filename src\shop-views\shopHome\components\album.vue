<template>
	<div class="album">
		<div class="title">店铺相册</div>
		<div class="list">
			<img
				v-for="(item, index) of images"
				:key="index"
				class="list-img"
				:src="$judgeFile(item.URL)"
				alt=""
			/>
		</div>
		<!-- <div class="pagination">
			<pagination
				:fixed="'left'"
				:current-page="page"
				:page-size="limit"
				:total="total"
				:layout="'total,sizes,->,prev,pager,next,jumper'"
				@paginationChange="paginationChange"
			/>
		</div> -->
	</div>
</template>

<script>
// import Pagination from '@/components/public/Pagination.vue';
export default {
	name: 'Album',
	// components: { Pagination },
	props: {
		images: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			page: 1, // 当前页
			limit: 10, // 每页显示的条数
			total: 0 // 总条数
		};
	},
	methods: {
		paginationChange() {}
	}
};
</script>

<style lang="scss" scoped>
.album {
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	padding: 32px 30px;
	.title {
		font-size: 30px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #000000;
		line-height: 44px;
	}
	.list {
		display: flex;
		flex-wrap: wrap;
		&-img {
			width: 368px;
			height: 368px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			margin-right: 12px;
			margin-top: 32px;
		}
	}
	.pagination {
		padding-top: 64px;
	}
}
</style>
