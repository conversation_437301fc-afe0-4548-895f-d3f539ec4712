<!--
 @desc:在线学习卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<el-image class="head-img" :src="cardData.coverImg" lazy fit="cover"></el-image>

		<div class="title over1">{{ cardData.name }}</div>
		<div class="content">{{ cardData.content }}</div>
		<div class="star-box">
			<el-rate v-model="cardData.score" disabled></el-rate>
			<div class="score">
				{{ Number(cardData.score).toFixed(2) }}&nbsp;|&nbsp;{{ cardData.learnPersonNum || 0 }}人学习
			</div>

			<!-- <div class="star">
				<img
					v-for="(item, index) in Math.floor(starNum)"
					:key="index + 'solod'"
					:src="starSolid"
					alt=""
				/>
				<img v-if="starHalfNum" :src="starHalf" alt="" />
				<img v-for="(item, index) in starEmptyNum" :key="index + 'empty'" :src="starEmpty" alt="" />
			</div>
			{{ starNum }}&nbsp;|&nbsp;{{ cardData.scale }}人学习 -->
		</div>
		<!-- <footer>
			<p class="now-money-box">
				¥
				<span>{{ cardData.nowMoney }}</span>
			</p>
			<p class="old-money-box">
				¥
				{{ cardData.oldMoney }}
			</p>
		</footer> -->
	</div>
</template>

<script>
export default {
	props: {
		starNum: {
			type: [String, Number],
			default: () => {
				return 3;
			}
		},
		cardData: {
			type: Object,
			default: () => {
				return {
					imgUrl: '',
					title: '民法典时代的典型合同与生活',
					content:
						'所有研究生必须培养其科研与学术的“底线意识”，即遵守科研伦理与学术规范。本课程有助于学生规避学术风险，提高科研规范性，为严谨扎实从事科研工作打好基础。',
					scale: '50~200',
					nowMoney: '199.00',
					oldMoney: '1572.00'
				};
			}
		}
	},
	data() {
		return {
			starSolid: require('@imgs/home/<USER>'),
			starHalf: require('@imgs/home/<USER>'),
			starEmpty: require('@imgs/home/<USER>')
		};
	},
	computed: {
		starHalfNum() {
			//任何整数都会被1整除
			return this.starNum % 1 !== 0 ? true : false;
		},
		starEmptyNum() {
			//向下取整
			return Math.floor(5 - this.starNum);
		}
	},
	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'study', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 236px;
	height: 210px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	margin-right: 20px;
	&:nth-child(5n) {
		margin-right: 0;
	}
	// background: #ffffff;
	.head-img {
		width: 236px;
		height: 140px;
		border-radius: 8px;
		background: #0fafcf;
		object-fit: cover;
		// border: 1px solid red;
	}
	// padding: 22px 20px 0 22px;
	.title {
		font-size: 14px;
		color: #333333;
		margin: 6px 0 10px 0;
	}
	.content {
		width: 100%;
		font-size: 12px;
		color: #7a8392;
		// line-height: 18px;
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
	}
	.star-box {
		@include flexBox(flex-start);
		font-size: 12px;
		margin: 8px 0 6px 0;
		color: #7a8392;
		.star {
			margin-right: 6px;
			img {
				width: 16px;
				height: 16px;
			}
		}
	}
	footer {
		@include flexBox(flex-start);
		width: 100%;
		.now-money-box {
			font-size: 12px;
			font-weight: 400;
			margin-right: 12px;
			color: #ff0000;
			span {
				font-size: 14px;
			}
		}
		.old-money-box {
			font-size: 12px;
			text-decoration: line-through;
			color: #7a8392;
		}
	}
}
</style>
