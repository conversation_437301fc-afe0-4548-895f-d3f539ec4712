<!--
 * @Description: 描述列表
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-03-30 15:53:35
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-03-30 17:22:33
-->
<template>
	<div class="descriptions">
		<div class="descriptions_title">{{ data.title }}</div>
		<!-- eslint-disable-next-line vue/no-v-html -->
		<div v-if="data.html" v-html="data.htmlData"></div>
		<div v-else class="descriptions_body">
			<div v-for="(item, index) in data.data" :key="index" class="descriptions_item">
				<span class="descriptions_key">{{ item.key }}：</span>
				<span class="descriptions_val">{{ item.val }}</span>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Product',
	props: {
		data: {
			type: Object,
			default: () => {}
		}
	},
	mounted() {}
};
</script>

<style lang="scss" scoped>
.descriptions {
	margin-top: 29px;
	padding: 0 23px;
	margin-bottom: 35px;
	&_title {
		font-size: 16px;
		font-family: Source <PERSON> Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 24px;
		padding-left: 16px;
		position: relative;
		margin-bottom: 15px;
		&::after {
			content: ' ';
			display: inline-block;
			width: 6px;
			height: 20px;
			border-radius: 4px;
			background: var(--brand-6, '#ca3f3b');
			position: absolute;
			left: 0px;
			top: 50%;
			margin-top: -10px;
		}
	}
	&_body {
		display: flex;
		flex-wrap: wrap;
	}
	&_item {
		width: 33%;
		margin-bottom: 16px;
	}
	&_key {
		display: inline-block;
		width: 113px;
		text-align: right;
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #9da5b7;
		line-height: 22px;
		margin-right: 5px;
	}
	&_val {
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #404040;
		line-height: 22px;
	}
}
</style>
