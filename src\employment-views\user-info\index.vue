<template>
	<div>
		<PersonHeader @changeName="changeName"></PersonHeader>
		<div class="personal">
			<div class="personal-top" :style="{ backgroundImage: `url(${bg})` }">
				<div class="personal-top-title">技状元数字化校园个人中心</div>
				<div class="personal-top-subTitle">高效 · 触手可及 智能 · 超乎想象</div>
				<div class="personal-top-header">
					<div class="personal-top-header-con" :style="{ backgroundImage: `url(${headerBg})` }">
						<div class="text">
							<div class="line"></div>
							<div>{{ componentName }}</div>
						</div>
						<img src="@/assets/shop-images/personal-left-icon.png" class="img" alt="" />
					</div>
				</div>
			</div>
			<div class="component-page">
				<component :is="activeComponent" :sub-menu="subMenu"></component>
			</div>
		</div>
	</div>
</template>

<script>
import { PersonHeader } from '@/layout/components';
import skillPersonal from '@/skill-treasure-views/personal';
import onlineStudyPersonal from '@/online-study-views/personal';
import shopPersonalIndex from '@/shop-views/personal';
import personHome from '@/views/personal/components/personHome';
export default {
	name: 'Personal',
	components: {
		PersonHeader,
		skillPersonal,
		onlineStudyPersonal,
		shopPersonalIndex,
		personHome
	},
	data() {
		return {
			activeComponent: 'personHome',
			componentName: '个人中心',
			bg: require('@/assets/shop-images/personal-bg.png'),
			headerBg: require('@/assets/shop-images/personal-left-bg.png'),
			subMenu: ''
		};
	},
	created() {
		this.getData();
	},
	methods: {
		getData() {},
		/**打开子系统个人中心*/
		changeName(tab, subMenu) {
			let { componentName = 'personHome', title = '个人信息' } = tab;
			this.activeComponent = componentName;
			this.subMenu = subMenu;
			this.componentName = title;
		}
	}
};
</script>
<style lang="scss" scoped>
.personal {
	background: #f9f9f9;
	&-top {
		width: 100%;
		height: 210px;
		background-size: 100% 100%;
		padding: 78px 0 0 626px;
		position: relative;
		&-title {
			font-size: 30px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: var(--brand-6, #0076e8);
			margin-bottom: 14px;
		}
		&-subTitle {
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: var(--brand-6, #0076e8);
			line-height: 46px;
		}
		&-header {
			width: 1200px;
			position: absolute;
			padding-left: -626px;
			bottom: 0;
			left: calc(50% - 600px);
			&-con {
				width: 220px;
				height: 100px;
				border-radius: 8px 8px 0 0;
				background-size: 100% 100%;
				padding: 26px 29px 25px 22px;
				display: flex;
				justify-content: space-between;
				.text {
					font-size: 20px;
					font-family: Microsoft YaHei;
					font-weight: bold;
					color: #ffffff;
					line-height: 24px;
					.line {
						height: 3px;
						width: 23px;
						border-radius: 8px;
						background: #b2d8f4;
						margin-bottom: 15px;
					}
				}
				.img {
					width: 50px;
					height: 50px;
				}
			}
		}
	}
	.component-page {
		//min-height: calc(100vh - 270px);
		width: 1200px;
		margin: 0 auto;
		padding-bottom: 24px;
	}
}
</style>
