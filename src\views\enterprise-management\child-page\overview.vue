<template>
	<div class="main">
		<!-- 统计数量 -->
		<div class="">
			<el-row :gutter="20">
				<el-col :span="6">
					<div class="data-card card-one" @click="toRoute('biographical', '收到的简历')">
						<p>简历投递数</p>
						<p>
							<span class="nums">500</span>
							<span>份</span>
						</p>
						<p>更多简历投递，提升效果</p>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="data-card card-two">
						<p>求职/创业者消息</p>
						<p>
							<span class="nums">715,400</span>
							<span>人</span>
						</p>
						<p>聊天沟通，方便省心</p>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="data-card card-three" @click="toRoute('interest', '对我感兴趣')">
						<p>对我感兴趣</p>
						<p>
							<span class="nums">500</span>
							<span>人</span>
						</p>
						<p>高意向人才，等你来聊</p>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="data-card card-four">
						<p>昨日浏览</p>
						<p>
							<span class="nums">1500</span>
							<span>人</span>
						</p>
						<p>优质人才，立即沟通</p>
					</div>
				</el-col>
			</el-row>
		</div>
		<!-- 待办事项 -->
		<div class="table">
			<h3 class="title">待办事项</h3>
			<el-table
				v-loading="tableLoading"
				:data="list"
				style="width: 100%"
				align="center"
				header-align="center"
				stripe
			>
				<el-table-column type="index" label="序号" width="50"></el-table-column>
				<el-table-column prop="name" label="待办事务名称"></el-table-column>
				<el-table-column prop="time" label="时间" width="280"></el-table-column>
				<el-table-column fixed="right" label="操作" width="80">
					<template slot-scope="scope">
						<el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
					</template>
				</el-table-column>
			</el-table>
			<!-- 分页 -->
			<el-pagination
				class="pagination"
				background
				layout="prev, pager, next,jumper"
				:total="paginationConfig.total"
				:current-page.sync="paginationConfig.pageNum"
				:page-size.sync="paginationConfig.pageSize"
				@current-change="getList"
			/>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Overview',
	data() {
		return {
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [
				{
					name: '《张三》向你的企业《销售经理》职位投递了简历',
					time: '2022-08-01  15:28:32'
				},
				{
					name: '《张三》向你发起了聊天咨询',
					time: '2022-08-01  15:28:32'
				},
				{
					name: '《张三》向你的企业《销售经理》职位投递了简历',
					time: '2022-08-01  15:28:32'
				}
			]
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getFollowPostList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		handleView() {},
		/**跳转*/
		toRoute(path, title) {
			this.$router.push(`/independentPersonal/enterprise?type=${path}`);
			this.addToBreadCrumb({ path, title });
		},
		/**添加面包屑*/
		addToBreadCrumb(item) {
			let obj = {
				path: `/independentPersonal/enterprise?type=${item.path}`,
				name: 'Enterprise',
				meta: {
					title: item.title,
					type: 'enterprise'
				}
			};
			this.$addToBreadCrumb(obj);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
}
.table {
	background: #fff;
	padding: 10px;
	border-radius: 10px;
}
.title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	// margin-top: 0;
	// margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.data-card {
	width: 100%;
	height: 120px;
	margin-bottom: 20px;
	border-radius: 10px;
	padding: 20px;
	color: #fff;
	cursor: pointer;
	.nums {
		font-size: 30px;
		margin-right: 10px;
	}
}
.card-one {
	background: rgba(27, 201, 142, 1);
}
.card-two {
	background: rgba(230, 71, 88, 1);
}
.card-three {
	background: rgba(178, 159, 255, 1);
}
.card-four {
	background: rgba(106, 90, 205, 1);
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
</style>
