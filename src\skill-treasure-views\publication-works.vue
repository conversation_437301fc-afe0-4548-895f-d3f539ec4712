<template>
	<div class="publication-works">
		<div v-loading="loading" class="content-box">
			<el-tabs v-model="activeName" @tab-click="handleClick">
				<el-tab-pane label="上传视频" name="video" :disabled="!!id"></el-tab-pane>
				<el-tab-pane label="上传图文" name="imageText" :disabled="!!id"></el-tab-pane>
				<el-tab-pane label="上传内容" name="content" :disabled="!!id"></el-tab-pane>
			</el-tabs>
			<!-- 上传内容区域 需要 activeName 区分三种不同类型的上传-->
			<!-- 上传视频 -->
			<div v-show="activeName == 'video'" class="video upload-box">
				<div class="step step-active">
					<span class="icon"></span>
					<span class="title">上传视频</span>
				</div>
				<el-upload
					class="upload-drag"
					drag
					:action="updUrl"
					:limit="1"
					:accept="acceptVideo"
					:on-success="handleVideoSuccess"
					:before-upload="beforeVideoUpload"
					:file-list="videoList"
					:on-exceed="onExceed"
				>
					<!-- 拖拽图片到此或点击上传 -->
					<i class="el-icon-video-camera upload-icon" size="44"></i>
					<div class="el-upload__text">拖拽视频到此或点击上传</div>
					<!-- slot="tip" -->
					<div class="el-upload__tip">
						支持格式：mp4、mov、avi、wmv、mpg、mpeg、rm、ram、flv最大1GB视频
					</div>
					<span class="upload-btn">上传视频</span>
				</el-upload>
				<div>
					<!-- 发布图文 -->
					<div class="step step-grey">
						<span class="icon"></span>
						<span class="title">发布视频</span>
					</div>
					<!-- 图片列表 -->
					<div class="update-title">上传封面图</div>
					<div class="uploader-box">
						<el-upload
							class="avatar-uploader"
							:action="updUrl"
							:show-file-list="false"
							:accept="acceptImg"
							:on-success="handleAvatarSuccess"
							:before-upload="beforeAvatarUpload"
							:limit="1"
						>
							<img v-if="imageUrl" :src="imageUrl" class="avatar" />
							<i v-else class="el-icon-plus avatar-uploader-icon"></i>
						</el-upload>
						<div class="uploader-tips">
							<span class="tips-span">支持格式：jpg、gif、jpeg、png</span>
							<span class="tips-span">尺寸800*800px，大小不超过10M的图片</span>
						</div>
					</div>
				</div>
			</div>
			<!-- 上传图文 -->
			<div v-show="activeName == 'imageText'" class="image-text upload-box">
				<div class="step step-active">
					<span class="icon"></span>
					<span class="title">上传图片</span>
				</div>
				<el-upload
					class="upload-drag"
					drag
					:action="updUrl"
					multiple
					:limit="18"
					:accept="acceptImg"
					:show-file-list="false"
					list-type="picture-card"
					:file-list="imageList"
					:on-success="handleImagesSuccess"
					:before-upload="beforeImagesUpload"
					:on-exceed="onExceed"
				>
					<!-- 拖拽图片到此或点击上传 -->
					<i class="el-icon-picture-outline upload-icon" size="44"></i>
					<div class="el-upload__text">将文件拖到此处，或点击上传</div>
					<div class="el-upload__tip">
						支持格式：jpg、gif、jpeg、png，最大20MB图片文件，最多上传18张
					</div>
					<span class="upload-btn">上传图片</span>
				</el-upload>
				<div>
					<!-- 发布图文 -->
					<div class="step step-grey">
						<span class="icon"></span>
						<span class="title">发布图文</span>
					</div>
					<!-- 图片列表 -->
					<div v-if="imageList.length" class="pic-list">
						<div class="list-item">
							<div class="list-item-left swiper-button-prev">
								<div class="left">
									<i class="el-icon-arrow-left"></i>
								</div>
							</div>
							<swiper :options="swiperOption" @swiper="onSwiper" @slideChange="onSlideChange">
								<swiper-slide v-for="(item, index) in imageList" :key="index">
									<div
										class="list-img-box"
										@mouseenter="hoverImg(item, index)"
										@mouseleave="hoverImg(item, index, 'leave')"
									>
										<img :class="['list-img']" :src="$judgeFile(item.url)" alt="" />
									</div>
									<div v-if="activeCoverImg == index" class="img-btn">已设为封面图</div>
									<template v-if="hoverImgIndex == index">
										<!-- 右上角关闭 -->
										<img
											src="@/assets/skill-treasure/img-colse.png"
											class="close-icon"
											@mouseenter="hoverImg(item, index)"
											@mouseleave="hoverImg(item, index, 'leave')"
											@click="deleteImg(item, index)"
										/>
										<!-- 底部的设置为封面按钮 -->
										<div
											v-show="activeCoverImg != index"
											class="img-btn"
											@mouseenter="hoverImg(item, index)"
											@mouseleave="hoverImg(item, index, 'leave')"
											@click="setCoverImg(item, index)"
										>
											设为封面图
										</div>
									</template>
								</swiper-slide>
							</swiper>
							<div class="list-item-right swiper-button-next">
								<div class="right">
									<i class="el-icon-arrow-right"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 上传内容 -->
			<div v-show="activeName == 'content'" class="content upload-box">
				<div class="step step-active">
					<span class="icon"></span>
					<span class="title">发布内容</span>
				</div>
				<div class="update-title">上传封面图</div>
				<div class="uploader-box">
					<el-upload
						class="avatar-uploader"
						:action="updUrl"
						:show-file-list="false"
						:accept="acceptImg"
						:on-success="handleAvatarSuccess"
						:before-upload="beforeAvatarUpload"
						:limit="1"
					>
						<img v-if="imageUrl" :src="imageUrl" class="avatar" />
						<i v-else class="el-icon-plus avatar-uploader-icon"></i>
					</el-upload>
					<div class="uploader-tips">
						<span class="tips-span">支持格式：jpg、gif、jpeg、png</span>
						<span class="tips-span">尺寸800*800px，大小不超过10M的图片</span>
					</div>
				</div>
			</div>
			<el-form ref="ruleForm" class="form-box upload-box" :model="form" :rules="rules">
				<el-form-item label="" prop="title">
					<el-input
						v-model.trim="form.title"
						maxlength="20"
						show-word-limit
						placeholder="填写标题，可能会哟更多赞哦~"
					></el-input>
				</el-form-item>
				<el-form-item label="" prop="content">
					<el-input
						v-model.trim="form.content"
						type="textarea"
						placeholder="填写更全面的内容，让更多人看到哦~"
						maxlength="1000"
						rows="10"
						show-word-limit
						resize="none"
					></el-input>
				</el-form-item>
				<el-form-item class="form-btns">
					<el-button class="form-btn" @click="closeBtn">取消</el-button>
					<el-button class="form-btn form-btn-submit" @click="onSubmit">发布</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
export default {
	data() {
		return {
			// 页签数据，用于结合页签通过接口获取对应的typeid，后续上传的时候使用
			typeIds: {
				video: '',
				imageText: '',
				content: ''
			},
			activeName: 'video', //选中的类型
			acceptImg: '.jpg,.gif,.jpeg,.png,.JPG,.GIF,.JPEG,.PNG', //上传图片格式
			acceptVideo: '.mp4,.mov,.MP4,.MOV,.avi,.wmv,.mpg,.mpeg,.rm,.ram,.flv', //上传视频格式
			imageUrl: '', //封面图上传之后的展示
			coverImg: '', //接口上传时的图片fileId值，不展示
			imgSize: 10, // 图片上传大小限制在10M
			imgListSize: 20, // 图片集合里面的图片上传大小限制在20M
			videoSize: 1, //视频上传大小限制在1G
			// 表单部分数据
			form: {
				title: '',
				content: ''
			},
			// 表单校验
			rules: {
				title: [{ required: true, message: '请填写标题', trigger: 'blur' }],
				content: [{ required: true, message: '请填写内容', trigger: 'blur' }]
			},
			updUrl: baseUrl + '/api/supply-web/fileUpload/singleFile', // 文件上传地址
			videoList: [], //上传视频文件列表
			video: '', //上传的视频文件
			imageList: [], //上传的图片文件集合
			// seiper参数设置
			swiperOption: {
				slidesPerView: 6,
				// spaceBetween: 30,
				// initialSlide: 1,
				// centeredSlides: true,
				// 设置分页器
				// pagination: {
				// 	el: '.swiper-pagination',
				// 	// 设置点击可切换
				// 	clickable: false
				// },
				// 设置前进后退按钮
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev'
				},
				// 设置自动轮播
				// autoplay: {
				// 	delay: 500000000 // 5秒切换一次
				// },
				// 设置轮播可循环
				loop: false
			},
			activeCoverImg: -1, //用于图文格式的时候设置为封面的图片
			hoverImgIndex: -1, //用于图文格式的时候鼠标悬浮的时候动态事件判断下标
			id: '', // 当前文章id
			loading: false //加载动画
		};
	},
	mounted() {
		this.id = this.$route.query.id || '';
		this.memberStoryTypeList();
	},
	methods: {
		/**
		 * @description 获取分类,用于上传的时候接口的传参，这方法应该有优化空间
		 * */
		memberStoryTypeList() {
			let params = {
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.memberStoryTypeList(params).then(res => {
				if (res.code == 200) {
					this.typeList = res.results || [];
					this.typeList.map(item => {
						if (item.name == '图文') {
							this.typeIds.imageText = item;
						} else if (item.name == '内容') {
							this.typeIds.content = item;
						} else if (item.name == '视频') {
							this.typeIds.video = item;
						}
					});
					this.id && this.getDetail();
				}
			});
		},
		/**
		 * @description 获取详情数据
		 * */
		getDetail() {
			this.loading = true;
			this.$api.treasure_api
				.actStoryInfo(this.id)
				.then(res => {
					if (res.code == 200) {
						this.detail = res.results || {};
						// 判断当前用户是否作者
						// this.userType = this.detail.memberId == this.dydUserinfo.id ? 'user' : '';
						// 判断当前文章类型 视频，图文，内容
						for (let key in this.typeIds) {
							if (this.typeIds[key].id == this.detail.typeId) {
								this.activeName = key;
								// this.notDisabledType = this.activeName;
							}
						}
						// 开始赋值
						// 表单赋值
						this.form = {
							title: this.detail.title,
							content: this.detail.content
						};
						// 封面赋值
						this.coverImg = this.detail.coverImg;
						this.imageUrl = this.$judgeFile(this.detail.coverImg);
						// 视频赋值
						this.video = this.detail.video;
						let videoObj = {
							name: this.detail.video,
							url: this.$judgeFile(this.detail.video)
						};
						this.videoList.push(videoObj);
						// 图片集赋值
						this.detail.imagesList.map(item => {
							let obj = {
								name: item,
								url: item,
								response: {
									results: {
										fileId: item
									}
								}
							};
							this.imageList.push(obj);
						});
						// 图片集封面图默认设置
						if (this.coverImg) {
							this.activeCoverImg = 0;
						}
					}
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 页签切换事件
		 * @params {item} 点击内容数据
		 * */
		handleClick(item) {
			// 清空内容
			this.clearForm();
			this.clearImgsVideosList();
			this.clearCoverImg();
		},
		/**
		 * @description 清空表单
		 * */
		clearForm() {
			this.$refs.ruleForm.resetFields();
		},
		/**
		 * @description 清空视频，图片列表
		 * */
		clearImgsVideosList() {
			this.videoList = [];
		},
		/**
		 * @description 清空封面图
		 * */
		clearCoverImg() {
			this.imageUrl = '';
		},
		/**
		 * @description 上传视频文件
		 */
		handleVideoSuccess(res, file, fileList) {
			this.videoList = fileList;
			this.video = file.response.results.fileId;
		},
		/**
		 * @description 上传视频文件
		 */
		beforeVideoUpload(file) {
			const isImgSize = file.size / 1024 / 1024 / 1024 < this.videoSize;
			if (!isImgSize) {
				this.$message.error(`上传视频大小不能超过 ${this.videoSize}G!`);
			}
			return isImgSize;
		},
		/**
		 * @description 上传图片集合文件
		 */
		beforeImagesUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < this.imgListSize;
			if (!isImgSize) {
				this.$message.error(`上传图片大小不能超过 ${this.imgListSize}MB!`);
			}
			return isImgSize;
		},
		/**
		 * @description 上传图片集合文件
		 */
		handleImagesSuccess(res, file, fileList) {
			this.imageList = fileList;
		},
		onExceed(file, fileList) {
			this.$message.error(`超过最多上传数量!`);
		},
		/**
		 * @description 文件上传成功时的钩子
		 * */
		handleAvatarSuccess(res, file) {
			if (file.response.code != 200) {
				return this.$message.error(file.response.msg);
			}
			this.imageUrl = file.response.results.url;
			this.coverImg = file.response.results.fileId;
		},
		/**
		 * @description 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
		 * */
		beforeAvatarUpload(file) {
			const isImgSize = file.size / 1024 / 1024 < this.imgSize;
			if (!isImgSize) {
				this.$message.error(`上传头像图片大小不能超过 ${this.imgSize}MB!`);
			}
			return isImgSize;
		},
		onSlideChange() {},
		onSwiper() {},
		// 设置封面
		setCoverImg(item, index) {
			// 设置成功之后需要将图片挪动到第一张，并且需要赋值给封面变量
			if (index !== 0) {
				this.imageList.splice(index, 1);
				this.imageList.unshift(item);
				this.coverImg = item.response.results.fileId;
			} else {
				this.coverImg = item.response.results.fileId;
			}
			this.activeCoverImg = 0;
		},
		// 删除图片
		deleteImg(item, index) {
			this.imageList.splice(index, 1);
			if (index == 0) {
				this.coverImg = '';
				this.activeCoverImg = -1;
			}
		},
		hoverImg(item, index, type) {
			this.hoverImgIndex = index;
			if (type) {
				this.hoverImgIndex = -1;
			}
		},
		closeBtn() {
			this.$confirm('确认取消当前编辑内容，并返回上一页?')
				.then(_ => {
					window.history.go(-1);
				})
				.catch(_ => {});
		},
		/**
		 * @descripotion 表单提交事件
		 * */
		onSubmit() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					// 各类型校验
					// 图文 imageText  需要判断imageList不为空，并且需要选中一个封面即coverImg不为空，优先级先判断imageList
					// 内容 content 需要判断coverImg不为空
					// 视频 video  需要判断videoList不为空,coverImg不为空
					if (this.activeName != 'imageText') {
						if (!this.coverImg) {
							return this.$message({
								type: 'success',
								message: `请上传封面`
							});
						}
					}
					if (this.activeName == 'video') {
						if (!this.videoList.length) {
							return this.$message({
								type: 'success',
								message: `请上传视频`
							});
						}
					}
					if (this.activeName == 'imageText') {
						if (!this.imageList.length) {
							return this.$message({
								type: 'success',
								message: `请上传图片`
							});
						}
						if (!this.coverImg) {
							return this.$message({
								type: 'success',
								message: `请选择封面`
							});
						}
					}
					// 组合参数集合
					let param = {
						title: this.form.title, //标题
						content: this.form.content, // 内容
						coverImg: this.coverImg, // 封面图
						typeId: this.typeIds[this.activeName].id,
						// imageList: '', // 图片集
						rentId: this.getSiteId() // 租户id
						// video: '' //视频
					};
					if (this.activeName == 'video') {
						param['video'] = this.video;
					}
					if (this.activeName == 'imageText') {
						let images = [];
						if (this.imageList.length > 0) {
							images = this.imageList.map(i => {
								return i.response.results.fileId;
							});
						}
						// images = images.toString(',');
						param['imageList'] = images;
					}
					// 需要用this.id区分是编辑还是新发布
					let apiUrl = 'actStoryPublish';
					if (this.id) {
						param['id'] = this.id;
						apiUrl = 'actStoryUpdate';
					}
					this.loading = true;
					this.$api.treasure_api[apiUrl](param)
						.then(res => {
							if (res.code == 200) {
								this.$message({
									type: 'success',
									message: `发布成功`
								});
								setTimeout(() => {
									this.jumpPage(`/personal-homepage`);
								}, 500);
							} else {
								this.$message({
									type: 'error',
									message: res.msg
								});
							}
							this.loading = false;
						})
						.catch(() => {
							this.loading = false;
						});
				}
			});
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1200px;
.publication-works {
	width: $max-width;
	margin: 0 auto;
	padding: 0 0 16px;
	font-family: PingFang SC-Regular, PingFang SC;
	.content-box {
		padding: 0 20px 30px;
		background: #fff;
	}
	.upload-box {
		padding: 0 28px;
	}
	.image-text,
	.video {
		// border-left: 1px solid red;
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 20px;
			left: 12px;
			width: 1px;
			height: 480px;
			border: 1px dashed #f0f0f0;
		}
	}
	//
	.step {
		display: flex;
		align-items: center;
		margin-left: -24px;

		.title {
			font-size: 16px;
			color: #262626;
			line-height: 32px;
		}
	}
	.step-active {
		.icon {
			width: 8px;
			height: 8px;
			background: var(--brand-6, #0076e8);
			border-radius: 50%;
			display: inline-block;
			position: relative;
			margin-right: 16px;
			margin-left: 4px;
			&::after {
				content: '';
				width: 16px;
				height: 16px;
				border-radius: 50%;
				background: rgba(0, 118, 232, 0.1);
				position: absolute;
				top: -4px;
				left: -4px;
			}
		}
	}
	.step-grey {
		.icon {
			width: 16px;
			height: 16px;
			border: 2px solid #d9d9d9;
			border-radius: 50%;
			display: inline-block;
			position: relative;
			margin-right: 12px;
			background: #fff;
		}
	}
	.update-title {
		font-size: 14px;
		color: #262626;
		line-height: 24px;
		margin-top: 16px;
	}
	// 上传封面样式
	.uploader-box {
		margin-top: 16px;
	}
	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}
	.avatar-uploader .el-upload:hover {
		border-color: #1890ff;
	}
	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 160px;
		height: 213px;
		line-height: 213px;
		text-align: center;
	}
	.avatar {
		width: 160px;
		height: 213px;
		display: inline-block;
	}

	.avatar-uploader {
		width: 160px;
		height: 213px;
		background: #f9f9f9;
		display: inline-block;
	}
	.uploader-tips {
		display: inline-block;
		font-size: 12px;
		color: #8c8c8c;
		line-height: 24px;
		margin-left: 20px;
		vertical-align: top;
		.tips-span {
			margin-bottom: 8px;
			display: block;
		}
	}
	.form-box {
		margin-top: 20px;
	}
	.form-btns {
		text-align: center;
	}
	.form-btn {
		width: 168px;
		height: 38px;
		background: #ffffff;
		border-radius: 3px;
		font-size: 14px;
		color: #404040;
		line-height: 22px;
	}
	.form-btn-submit {
		background: var(--brand-6, #0076e8);
		color: #ffffff;
	}
	// 图文上传图片展示
	.upload-drag {
		width: 1104px;
		height: 450px;
		.upload-icon {
			font-size: 44px;
			color: #8c8c8c;
		}
		.el-upload__text {
			margin-top: 20px;
			font-size: 14px;
			color: #262626;
			line-height: 24px;
		}
		.el-upload__tip {
			font-size: 12px;
			color: #8c8c8c;
			line-height: 24px;
			margin-top: 4px;
		}
		.upload-btn {
			display: inline-block;
			width: 168px;
			height: 38px;
			background: var(--brand-6, #0076e8);
			border-radius: 3px;
			font-size: 14px;
			color: #ffffff;
			line-height: 38px;
			text-align: center;
			margin-top: 20px;
		}
		::v-deep .el-upload {
			width: 100%;
			height: 400px;
			background: #f9f9f9;
		}
	}
	::v-deep .el-upload-dragger {
		width: 100%;
		height: 100%;
		background: #f9f9f9;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	::v-deep .el-upload--picture-card {
		border: none;
	}
	.pic-list {
		width: 100%;
		height: 220px;
	}
	.list-item {
		position: relative;
	}
	.list-img-box {
		width: 160px;
		height: 213px;
		background: #f9f9f9;
		border-radius: 10px;
		margin-right: 16px;
		position: relative;
		display: inline-block;
	}
	.list-img {
		width: 160px;
		height: 213px;
		background: #f9f9f9;
		border-radius: 10px;
	}
	// .swiper-container {
	// 	margin: 0px 0 0 4px;
	// 	width: 100%;
	// }
	.swiper-button-prev,
	.swiper-button-next {
		// width: 36px;
		position: absolute;
		top: 77px;
		width: 32px;
		height: 60px;
		background: rgba(38, 38, 38, 0.8);
		border-radius: 6px 6px 6px 6px;
	}
	.swiper-button-prev:after,
	.swiper-button-next:after {
		display: none;
	}
	.list-img_active {
		border: 1px solid var(--brand-6, '#ca3f3b');
	}
	.close-icon {
		position: absolute;
		width: 24px;
		height: 24px;
		top: 8px;
		right: 34px;
		cursor: pointer;
	}
	.img-btn {
		position: absolute;
		width: 160px;
		height: 38px;
		background: rgba(38, 38, 38, 0.8);
		border-radius: 0px 0px 10px 10px;
		font-size: 12px;
		color: #ffffff;
		line-height: 38px;
		text-align: center;
		left: 0;
		bottom: 0;
		cursor: pointer;
	}
}
</style>
