<template>
	<div class="main">
		<!-- 收到的简历 -->
		<h3 class="title">收到的简历</h3>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="post.name" label="职位名称"></el-table-column>
			<!-- <el-table-column prop="name" label="简历名称"></el-table-column> -->
			<el-table-column prop="user.username" label="姓名"></el-table-column>
			<el-table-column prop="createTime" label="投递时间" width="170"></el-table-column>
			<el-table-column label="是否查看">
				<template #default="{ row }">
					<el-tag :type="{ 0: 'info', 1: 'success' }[row.status]">
						{{ ['待查看', '已查看'][row.status] }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="是否面邀">
				<template #default="{ row }">
					<el-tag :type="{ 0: 'info', 1: 'success' }[row.isInvite]">
						{{ ['未邀请', '已邀请'][row.isInvite] }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="220">
				<template slot-scope="scope">
					<el-button type="text" size="small" class="del-btn" @click="delClick(scope.row)">
						删除
					</el-button>
					<!-- <el-button type="text" size="small" @click="handleOpenMsg(scope.row)">沟通</el-button> -->
					<el-button type="text" size="small" @click="biographicalView(scope.row)">
						查看简历
					</el-button>
					<!-- <el-button
						v-if="scope.row.isInvite == 0"
						type="text"
						size="small"
						@click="handleInterviewInvitation(scope.row)"
					>
						面试邀请
					</el-button> -->
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList()"
		/>
		<el-dialog
			title="发起面试邀请"
			:visible.sync="interviewInvitationDialog.visible"
			@close="handleCloseDialog"
		>
			<div class="content">
				<el-form
					ref="form"
					:model="interviewInvitationDialog.form"
					:rules="interviewInvitationDialog.rules"
					label-width="120px"
				>
					<div class="business-info">
						<el-row :gutter="20">
							<el-col :span="12">
								<el-form-item label="职位名称" prop="name">
									<!-- <el-input
										v-model=""
										placeholder="请填写职位名称"
										disabled
										type="input"
									></el-input> -->
									{{ interviewInvitationDialog.form.postName || '-' }}
									<!-- <el-select
										v-model="interviewInvitationDialog.form.name"
										placeholder="请选择职位名称"
									>
										<el-option
											v-for="(item, index) in options"
											:key="index"
											:label="item.name"
											:value="item.value"
										></el-option>
									</el-select> -->
								</el-form-item>
							</el-col>
							<!-- <el-col :span="12">
								<el-form-item label="公司名称" prop="comName">
									<el-input
										v-model="interviewInvitationDialog.form.comName"
										placeholder="请填写公司名称"
										type="input"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="联系人姓名" prop="contactName">
									<el-input
										v-model="interviewInvitationDialog.form.contactName"
										placeholder="请填写联系人姓名"
										type="input"
									></el-input>
								</el-form-item>
							</el-col> -->
							<el-col :span="12">
								<el-form-item label="联系人电话" prop="phone">
									<!-- <el-input
										v-model="interviewInvitationDialog.form.phone"
										placeholder="请填写联系人电话"
										type="input"
									></el-input> -->
									{{ interviewInvitationDialog.form.phone }}
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="选择面试时间" prop="interviewDate">
									<el-date-picker
										v-model="interviewInvitationDialog.form.interviewDate"
										type="date"
										value-format="yyyy-MM-dd"
										placeholder="选择日期"
										:picker-options="pickerOptions"
									/>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item label="预计时间段" prop="interviewTime">
									<el-time-select
										v-model="interviewInvitationDialog.form.startTime"
										value-format="HH:mm"
										placeholder="请选择预计开始时间"
										:picker-options="{
											start: '08:30',
											step: '00:30',
											end: '18:30'
										}"
									/>
									至
									<el-time-select
										v-model="interviewInvitationDialog.form.endTime"
										value-format="HH:mm"
										placeholder="请选择预计结束时间"
										:picker-options="{
											start: '08:30',
											step: '00:30',
											end: '18:30',
											minTime: interviewInvitationDialog.form.startTime
										}"
									/>
								</el-form-item>
							</el-col>

							<el-col :span="24">
								<el-form-item label="面试地点" prop="interviewAddress">
									<el-input
										v-model="interviewInvitationDialog.form.interviewAddress"
										placeholder="请填写面试地点"
										type="input"
									></el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</div>
				</el-form>
				<div class="info-save">
					<el-button class="info-save-confirm" type="primary" size="large" @click="handlerSubmit">
						保存
					</el-button>
					<el-button class="info-save-staging" size="large" @click="resetForm">重置</el-button>
				</div>
			</div>
		</el-dialog>
		<!-- 查看简历弹窗 -->
		<biographical-dialog ref="biographicalDialog" />
		<!--沟通-->
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="contactDialogInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import biographicalDialog from './components/biographical.vue';
import contactMessage from '@/components/public/contactMessageEnterprise.vue';

export default {
	name: 'Biographical',
	components: {
		contactMessage,
		biographicalDialog // 简历弹窗内容
	},
	data() {
		return {
			contactDialogInfo: {},
			dialogMessageVisible: false,
			tableLoading: true,
			interviewInvitationDialog: {
				visible: false,
				submitLoading: false,
				form: {
					postName: '',
					phone: '',
					interviewDate: '',
					interviewTime: '',
					startTime: '',
					endTime: '',
					interviewAddress: ''
				}, //表单数据
				rules: {
					contactName: [{ required: true, message: '请填写联系人', trigger: 'blur' }],
					interviewDate: [{ required: true, message: '请选择面试日期', trigger: 'change' }],
					interviewTime: [
						{
							required: true,
							validator: (rule, value, callback) => {
								if (
									!this.interviewInvitationDialog.form.startTime &&
									!this.interviewInvitationDialog.form.endTime
								) {
									callback('请选择面试时间');
								} else if (
									!this.interviewInvitationDialog.form.startTime &&
									this.interviewInvitationDialog.form.endTime
								) {
									callback('请选择预计面试开始时间');
								} else if (
									this.interviewInvitationDialog.form.startTime &&
									!this.interviewInvitationDialog.form.endTime
								) {
									callback('请选择预计面试结束时间');
								}

								callback();
							},
							trigger: 'change'
						}
					],
					interviewAddress: [{ required: true, message: '请填写面试地点', trigger: 'blur' }]
				} //表单校验规则
			},
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [],
			pickerOptions: {
				disabledDate: time => {
					return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
				}
			}
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getPostResumeList({
					isView: 1,
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		/**
		 * @description 删除操作
		 * */
		delClick(row) {
			this.$confirm('确认删除简历？删除之后简历将保存在回收站')
				.then(_ => {
					this.$api.enterprise_center.removePostResume({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		/**
		 * @description 沟通
		 * */
		handleOpenMsg(row) {
			this.contactDialogInfo = {
				SHOP_LOG: row.user.photoUrl,
				SHOP_NAME: row.user.username,
				SELLER_ID: row.user.id,
				isGoods: true
			};
			this.dialogMessageVisible = true;
		},
		/**
		 * @description 发起面试邀约
		 * */
		handleInterviewInvitation(row) {
			this.interviewInvitationDialog.visible = true;
			this.interviewInvitationDialog.form = {
				...this.interviewInvitationDialog.form,
				postResumeId: row.id,
				postName: row.post?.name,
				phone: row.user?.phone,
				postId: row.post?.id,
				enterpriseId: row.enterpriseId,
				userId: row.user?.id,
				userName: row.user?.username,
				resumeId: row.resumeId
			};
		},
		/**
		 * @description 关闭弹窗
		 */
		handleCloseDialog() {
			this.interviewInvitationDialog.visible = false;
			this.resetForm();
		},
		/**
		 * @description 表单数据提交
		 */
		handlerSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.interviewInvitationDialog.submitLoading = true;
					const { startTime, endTime, ...otherParams } = this.interviewInvitationDialog.form;
					this.$api.enterprise_center
						.addInterview({
							...otherParams,
							interviewTime: `${startTime}-${endTime}`
						})
						.then(res => {
							if (res.success) {
								this.$message.success('操作成功');
								this.interviewInvitationDialog.visible = false;
								this.getList();
							} else {
								this.$message.error(res.msg);
							}
						})
						.finally(() => {
							this.interviewInvitationDialog.submitLoading = false;
						});
				}
			});
		},
		/**
		 * @description 表单重置
		 */
		resetForm() {
			this.$nextTick(() => {
				this.interviewInvitationDialog.form = {
					...this.interviewInvitationDialog.form,
					...{
						postName: '',
						phone: '',
						interviewDate: '',
						interviewTime: '',
						startTime: '',
						endTime: '',
						interviewAddress: ''
					}
				}; //表单数据
				this.$refs.form.resetFields();
			});
		},
		// 查看简历
		biographicalView(row) {
			this.$refs.biographicalDialog.show(row);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
.info-save {
	text-align: center;
}
</style>
