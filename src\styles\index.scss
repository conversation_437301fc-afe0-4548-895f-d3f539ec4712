@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
	height: 100%;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial,
		sans-serif;
}

label {
	font-weight: 700;
}

html {
	height: 100%;
	box-sizing: border-box;
}

#app {
	height: 100%;
}

*,
*:before,
*:after {
	box-sizing: inherit;
}

a:focus,
a:active {
	outline: none;
}

a,
a:focus,
a:hover {
	cursor: pointer;
	color: inherit;
	text-decoration: none;
}

div:focus {
	outline: none;
}

.clearfix {
	&:after {
		visibility: hidden;
		display: block;
		font-size: 0;
		content: ' ';
		clear: both;
		height: 0;
	}
}

// main-container global css
.app-container {
	padding: 20px;
}
.u-line-1 {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.u-line-2 {
	text-overflow: ellipsis;
	white-space: break-spaces;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}
.u-line-3 {
	text-overflow: ellipsis;
	white-space: break-spaces;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
}
.u-line-4 {
	text-overflow: ellipsis;
	white-space: break-spaces;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 4;
}
.flex0 {
	display: flex;
	flex-direction: row;
}

.flex1 {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.flex2 {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.flex3 {
	display: flex;
	flex-direction: column;

	align-items: center;
}

.flex4 {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	flex-wrap: wrap;
}

.flex5 {
	display: flex;
	flex-direction: row;
	align-items: flex-end;
}

.flex6 {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.fs14 {
	font-size: 14px;
	font-weight: 400;
}

.fs12 {
	font-size: 12px;
	font-weight: 400;
}

.fs16 {
	font-size: 16px;
	font-weight: 400;
}

.fs18 {
	font-size: 18px;
	font-weight: 400;
}

.mcolor {
	color: #7a8392;
}

.c3 {
	color: #333;
}

.c6 {
	color: #666;
}

.c9 {
	color: #999;
}

.c74 {
	color: #747d85;
}
.over1 {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.over2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

.over3 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
}

.bold {
	font-weight: bold;
}
