<!-- eslint-disable vue/no-v-html -->
<!--
 * @Description: 公共详情
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-04-27 10:57:01
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-05-05 13:35:15
-->
<template>
	<div class="content-detail" :style="{ width: isMain ? '1200px' : '100%' }">
		<div class="content-detail-header">
			<h1 class="content-detail-header_title">{{ detailInfo.title }}</h1>
			<p class="content-detail-header_date">发布时间：{{ detailInfo.publishTime }}</p>
		</div>
		<div class="content-detail-body" v-html="detailInfo.detail"></div>
	</div>
</template>

<script>
export default {
	name: 'NoticeDetail',
	components: {},
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		params: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			detailInfo: {}
		};
	},
	computed: {
		userInfo() {
			return this.$store.state.user.userInfo;
		}
	},
	mounted() {
		this.handleGetMyMessageInfo();
	},
	methods: {
		/**获取详情*/
		handleGetMyMessageInfo() {
			// 主应用在路由取参，子应用就取组件传参
			const detailId = this.isMain ? this.$route.query.id : this.params.id;
			let params = this.isMain ? this.$route.query : this.params;
			if (detailId) {
				this.$api.personal_api.findCmsDetail(params).then(res => {
					this.detailInfo = res.results;
					this.handleAddViews(detailId, res.results.infoType);
				});
			}
		},
		/**增加阅读量*/
		handleAddViews(id, type) {
			this.$api.personal_api
				.addViews({
					objectType: 'info',
					objectId: id,
					tenantId: this.$tenantId
				})
				.then(res => {});
		}
	}
};
</script>
<style lang="scss" scoped>
.content-detail {
	padding: 20px;
	margin-top: 20px;
	border: 1px solid #e8eaec;
	border-radius: 4px;
	background-color: #fff;
	&-header {
		&_title {
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: var(--brand-6, #0076e8);
			font-size: 24px;
			margin-top: 27px;
			margin-bottom: 28px;
			text-align: center;
		}
		&_date {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			text-align: center;
			border-bottom: 1px solid #d9d9d9;
			padding-bottom: 21px;
		}
	}
	&-body {
		padding: 42px 0 104px 0;
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #404040;
		line-height: 22px;
		min-height: 300px;
	}
}
</style>
