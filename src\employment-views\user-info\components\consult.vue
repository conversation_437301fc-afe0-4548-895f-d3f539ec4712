<!--
 @desc:个人中心 我的咨询
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="main">
		<div class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无咨询信息',
					detail: '你还没有资讯记录，或者前往',
					clickText: '招聘信息'
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<consult-card
					v-for="(item, index) in list"
					:key="index"
					:card-data="item"
					@handle="handle"
				/>
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="messageInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import ConsultCard from './consult/consult-card.vue';
import NoData from './no-data.vue';
import contactMessage from '@/components/public/contactMessageEnterprise.vue';

export default {
	components: { ConsultCard, NoData, contactMessage },
	data() {
		return {
			dialogMessageVisible: false,
			messageInfo: {},

			list: [],
			// list: [1, 1, 1, 1, 1],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 0
		};
	},

	mounted() {
		this.getConsult();
	},
	methods: {
		//在线咨询
		handle(item) {
			this.setMessageInfo(item);
			this.dialogMessageVisible = true;
		},
		setMessageInfo(cardData) {
			// 判断用户是发方还是收方
			let obj = {
				headImg: '',
				userName: '',
				userId: ''
			};
			if (cardData.send_id == this._userinfo.id) {
				obj.headImg = cardData.recipientPhoto;
				obj.userName = cardData.recipientName;
				obj.userId = cardData.recipient_id;
			} else {
				obj.headImg = cardData.sendPhoto;
				obj.userName = cardData.sendName;
				obj.userId = cardData.send_id;
			}
			this.messageInfo = {
				SHOP_LOG: obj.headImg,
				SHOP_NAME: obj.userName,
				SELLER_ID: obj.userId,
				isGoods: true
			};
			// let obj = {
			// 	SHOP_NAME: cardData.userName,
			// 	SHOP_LOG: cardData.headImg || '',
			// 	SELLER_ID: cardData.userId,
			// 	isGoods: true
			// };
			// this.messageInfo = this.contactDialogInfo;
		},
		async getConsult() {
			try {
				this.loading = true;
				// let { rCode, msg, results } = await this.$api.employment_api.getConsult({
				// 	pageNum: this.page,
				// 	pageSize: this.size
				// });
				const { rCode, msg, results } = await this.$api.employment_api.getConsult({
					pageNum: this.page,
					pageSize: this.size
				});
				// if (res.results) {
				// 	this.list = results?.records;
				// 	this.paginationConfig.total = res.results?.total || 0;
				// } else {
				// 	this.$message.error(res.msg);
				// }
				if (rCode == 0) {
					this.list = results.records;
					this.total = results.total || 0;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		noDataFn() {
			this.$router.push('/job-list');
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.getPostResumeList();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getPostResumeList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
