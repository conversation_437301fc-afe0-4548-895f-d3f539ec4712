<!--
 @desc:个人中心 就业创业 我的评价
 @author: WH
 @date: 2023/9/7
 -->
<template>
	<div class="main">
		<div class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无评价信息',
					detail: '你还没有公司无评价信息，或者前往',
					clickText: '招聘信息'
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<EvaluateCard v-for="(item, index) in list" :key="index" />
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import EvaluateCard from './evaluate/evaluate-card.vue';
import NoData from './no-data.vue';

export default {
	components: { EvaluateCard, NoData },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			activeName: 'No1',
			list: [1, 1, 1, 1, 1],
			conpanyList: [1, 1, 1, 1, 1],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10
		};
	},

	mounted() {
		// this.handlerGetRegion();
		// this.handlerQueryDict();
	},
	methods: {
		noDataFn() {
			this.$router.push('/job-list');
		},
		handleClick(tab, event) {
			console.log(tab, event);
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.$emit('sizeChange', this.size);
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.$emit('pageChange', this.page);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
