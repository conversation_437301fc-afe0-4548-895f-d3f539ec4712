<script>
/**
 * slot  default 不能用v-if会出现弹窗已经加载出来了 default还在加载  $scopedSlots.default
 *
 */
export default {
	model: {
		prop: 'dialogVisible'
	},
	props: {
		// 弹框标题
		title: {
			type: String,
			default: '提示'
		},
		// 是否显示关闭按钮
		showClose: {
			type: Boolean,
			default: true
		},
		// 弹框宽度
		width: {
			type: String,
			default: '360px'
		},
		// 弹框显示/隐藏
		dialogVisible: {
			type: Boolean,
			default: false
		},
		// 是否可以通过点击 modal 关闭 Dialog
		closeOnClickModal: {
			type: Boolean,
			default: false
		},
		// 是否需要取消按钮
		closeBtn: {
			type: Boolean,
			default: true
		},
		// 是否需要确定按钮
		confirmBtn: {
			type: Boolean,
			default: true
		},
		// 关闭时销毁 Dialog 中的元素
		destroyOnClose: {
			type: Boolean,
			default: true
		}
	},
	methods: {
		btnHandle(item) {
			if (typeof item === 'function') {
				this.$emit('dialogHandle', {
					code: 0,
					description: '点击了关闭按钮'
				});
				return;
			}
			this.$emit('dialogHandle', item);
		}
	},
	render() {
		let footer;
		let cancel;
		let confirm;
		if (this.confirmBtn) {
			confirm = (
				<el-button type="primary" onClick={() => this.btnHandle(2)}>
					确定
				</el-button>
			);
		}
		if (!this.closeBtn) {
			cancel = <el-button onClick={() => this.btnHandle(1)}>取消</el-button>;
		}
		if (this.$scopedSlots.footer) {
			footer = this.$scopedSlots.footer();
			confirm = '';
			cancel = '';
		}

		return (
			<div class="my-message-box">
				<el-dialog
					title={this.title}
					visible={this.dialogVisible}
					width={this.width}
					close-on-click-modal={this.closeOnClickModal}
					show-close={this.showClose}
					before-close={this.btnHandle}
					destroy-on-close={this.destroyOnClose}
				>
					{this.$scopedSlots.default({
						info: this.info
					})}
					<span slot="footer" class="dialog-footer">
						{footer ? footer : ''}
						{cancel ? cancel : ''}
						{confirm ? confirm : ''}
					</span>
				</el-dialog>
			</div>
		);
	}
};
</script>
<style lang="scss" scoped>
.my-message-box {
	::v-deep .el-dialog {
		display: flex;
		display: -ms-flex; /* 兼容IE */
		flex-direction: column;
		-ms-flex-direction: column; /* 兼容IE */
		margin: 0 !important;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-height: calc(100% - 30px);
		max-width: calc(100% - 30px);
		border-radius: 0;
	}
	::v-deep .el-dialog__header {
		padding: 13px 16px !important;
		box-sizing: border-box;
		margin-bottom: 16px;
		height: 48px;
	}
	::v-deep .el-dialog .el-dialog__body {
		padding: 0 20px !important;
		min-height: 40px;
		max-height: 75vh;
		flex: 1;
		-ms-flex: 1 1 auto; /* 兼容IE */
		overflow-y: auto;
		overflow-x: hidden;
		max-height: 480px;
	}
	::v-deep .el-dialog__footer {
		padding: 28px 20px 16px 20px; /*这个不重要*/
		.el-button {
			padding: 9px 10px;
			border-radius: 2px;
			box-sizing: border-box;
			font-size: 12px;
			// font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
		}
	}

	::v-deep .el-dialog__wrapper {
		overflow: hidden; /*隐藏ie和edge中遮罩的滚动条*/
	}
}
</style>
