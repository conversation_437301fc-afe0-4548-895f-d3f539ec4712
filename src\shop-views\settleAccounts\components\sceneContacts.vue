<template>
	<div class="scene">
		<div class="scene-top">
			<div class="line"></div>
			<div class="title">{{ title }}</div>
			<div v-if="titleTip" class="tip">{{ titleTip }}</div>
		</div>
		<div class="scene-con">
			<div
				v-for="item of list"
				v-show="item.active"
				:key="item.id"
				class="scene-con-item"
				@click="activeSite(item)"
			>
				<div class="scene-con-item-left select">
					<span v-for="info of needShowInfo" :key="info.key" class="title">
						<span v-if="info.key === 'phoneNum'" class="phone">
							{{ item.telno }}
						</span>
						<span v-else-if="info.key === 'userName'">{{ item.contact }}</span>

						<span v-else-if="item[info.key]">{{ item[info.key] }}</span>
						<span v-else style="color: red">{{ info.text }}空缺</span>
					</span>
				</div>
				<div class="edit" @click="toEdit(item, true)">编辑</div>
			</div>
			<div v-if="selectType === 'multiple' && multipleNum > showList.length" class="err-tip">
				*还需填写{{ multipleNum - showList.length }}人信息
			</div>
			<div class="scene-con-button" @click="dialog">
				{{ selectType === 'radios' ? '修改' : '+新增' }}{{ title }}
			</div>
		</div>
		<addContactsDialog
			v-if="dialogUserVisible"
			ref="PickUpDialog"
			:need-show-info="needShowInfo"
			:multiple-num="multipleNum"
			:select-type="selectType"
			:title="title"
			:dialog-user-visible="dialogUserVisible"
			:arr-list="list"
			@selectContacts="selectContacts"
		/>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';
import addContactsDialog from './addContactsDialog.vue';

export default {
	name: 'SceneContacts',
	components: {
		addContactsDialog
	},
	props: {
		/**标题提示*/
		titleTip: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**标题*/
		title: {
			type: String,
			default: () => {
				return '联系人';
			}
		},
		/**存储数据key*/
		formKey: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**组件key*/
		dataKey: {
			type: String,
			default: () => {
				return '';
			}
		},
		/**单选还是多选*/
		selectType: {
			type: String,
			default: () => {
				return 'multiple';
			}
		},
		/**selectType为multiple时有效，多选数量*/
		multipleNum: {
			type: [String, Number],
			default: () => {
				return 2;
			}
		},
		/**需要展示的信息*/
		needShowInfo: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			dialogUserVisible: false,
			list: [],
			catchArr: [] // 缓存被选中的联系人
		};
	},
	computed: {
		showList() {
			return this.list.filter(item => {
				return item.active;
			});
		}
	},
	created() {
		this.getUserAddrList();
	},
	methods: {
		/**编辑单个联系人*/
		toEdit(item, isClose) {
			this.dialogUserVisible = true;
			this.$nextTick(() => {
				this.$refs.PickUpDialog.updateAddSite(item, isClose);
			});
		},
		/**通知外层联系人选择发生变化*/
		selectContacts() {
			/**通知上级联系人选中信息*/
			this.$emit('selectContactsInfo', this.formKey, this.list);
		},
		/**选中某个联系人*/
		activeSite() {},
		/**收货地址列表*/
		getUserAddrList(arr) {
			if (arr && arr.length) {
				this.catchArr = [...arr];
			}
			let data = {
				offset: 0,
				psize: 10,
				userId: getCookie('user_id')
			};
			this.$api.shop_api.getUserAddrList(data).then(res => {
				if (res.state) {
					this.list = res.result;
					if (this.list && this.list.length) {
						this.list = this.list.map(item => {
							let obj = {
								...item,
								active: false
							};
							if (this.catchArr.length > 0) {
								/**之前选中过的，复刻选中的状态*/
								if (this.catchArr.includes(obj.id)) obj.active = true;
							} else {
								obj.active = item.type == 1;
							}
							return obj;
						});
					}
					// 通知上级联系人信息被修改删除或者新增，要求同样的组件重新加载
					if (arr) {
						this.$emit('updateComponents', this.dataKey);
					}
					// 通知联系人选择发生变化
					this.selectContacts();
					this.$nextTick(() => {
						if (this.$refs.PickUpDialog) {
							this.$refs.PickUpDialog.list = JSON.parse(JSON.stringify(this.list));
						}
					});
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
			});
		},
		/**打开弹窗*/
		dialog(type) {
			this.dialogUserVisible = true;
		}
	}
};
</script>

<style scoped lang="scss">
.scene {
	width: 100%;
	background: #ffffff;
	border-radius: 4px;
	margin: 16px 0;
	padding: 16px 0 20px 20px;
	&-top {
		margin-bottom: 15px;
		display: flex;
		align-items: center;
		.line {
			margin-right: 7px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, #0076e8);
		}
		.title {
			height: 24px;
			font-size: 16px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
		.tip {
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #bfbfbf;
			line-height: 22px;
		}
	}
	&-con {
		width: 1160px;
		background: #ffffff;
		border: 1px solid #d9d9d9;
		padding: 20px 0 20px 20px;
		.select {
			color: var(--brand-6, #0076e8);
			border: 1px solid var(--brand-6, #0076e8);
		}
		&-item {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
			&-left {
				display: inline-block;
				height: 30px;
				background: #ffffff;
				border: 1px solid #eeeeee;
				padding: 5px 8px;
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
				.title {
					margin-right: 16px;
				}
			}
			.edit {
				color: var(--brand-6, #0076e8);
				font-size: 12px;
				margin-left: 12px;
				cursor: pointer;
			}
		}
		.err-tip {
			margin-bottom: 8px;
			color: red;
			font-size: 14px;
		}
		&-button {
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: var(--brand-6, #0076e8);
			line-height: 20px;
			cursor: pointer;
		}
	}
}
</style>
