<template>
	<div class="personal-homepage" :style="{ width: isMain ? '1200px' : '100%' }">
		<!-- 个人主页 -->
		<div v-if="!params.showActiveCode" class="personal-data">
			<!-- 个人数据 -->
			<div class="data-box">
				<img
					:src="personalData.headImg ? $judgeFile(personalData.headImg) : defaulAvatar"
					class="personal-img"
				/>
				<!-- <headAvator class="personal-img" :own-id="getOwnId(personalData.headImg)" /> -->
				<div class="info-box">
					<span class="name">{{ personalData.nickname }}</span>
					<span class="sign">
						<div v-if="editprofile" class="edit-profile-box">
							<el-input
								v-model.trim="profileInput"
								maxlength="20"
								show-word-limit
								placeholder="请输入内容"
								@blur="changeProfile"
							>
								<template slot="append">
									<span class="edit-btn" @click="changeProfile">提交</span>
								</template>
							</el-input>
							<!-- <span class="edit-btn" @click="changeProfile">提交</span> -->
						</div>
						<template v-else>
							<span class="singn-text">{{ personalData.userSignature }}</span>
							<img
								v-if="userType == 'user'"
								src="@/assets/skill-treasure/edit.png"
								class="sign-icon"
								@click="editprofileFun"
							/>
						</template>
					</span>
					<div class="bottom">
						<div
							class="bottom-item"
							@click="jumpPage(`/interest-list?memberId=${memberId}`, userType == 'user')"
						>
							<span class="number">{{ countInteract.interestCount || 0 }}</span>
							关注
						</div>
						<span class="item-space"></span>
						<div
							class="bottom-item"
							@click="jumpPage(`/interest-list?memberId=${memberId}&code=fans`, userType == 'user')"
						>
							<span class="number">{{ countInteract.followerCount || 0 }}</span>
							粉丝
						</div>
						<span class="item-space"></span>
						<div
							class="bottom-item"
							@click="jumpPage('/notice-list?code=digs', userType == 'user')"
						>
							<span class="number">
								{{ countInteract.likeCount + countInteract.collectCount || 0 }}
							</span>
							获赞与收藏
						</div>
					</div>
				</div>
				<!-- 右侧悬浮操作按钮 -->
				<div v-if="userType == 'user'" class="person-btns">
					<div class="btn-item" @click="jumpPage('./notice-list', userType == 'user')">
						<img src="@/assets/skill-treasure/tips.png" class="item-icon" />
						<span>通知</span>
					</div>
				</div>
				<div v-else class="person-btns">
					<span v-if="countInteract.isInterest" class="interest-active" @click="memberInteract(6)">
						取消关注
					</span>
					<span v-else class="interest-btn" @click="memberInteract(6)">关注</span>
				</div>
			</div>
			<!-- 个人数据分类 -->
			<tabs-nav
				:type-list="userType == 'user' ? typeList : userTypeList"
				:active-code="activeCode"
				@click="typeClick"
			/>
		</div>
		<!-- 内容区域 -->
		<div
			v-loading="loading"
			class="content-box"
			:style="{ paddingTop: params.showActiveCode ? '20px' : '' }"
		>
			<Empty v-if="list.length == 0" :tips="'暂无数据'" />
			<template v-else>
				<div
					v-for="(item, index) in list"
					:key="index"
					class="content-box-item"
					:style="{ height: isMain ? '480px' : '360px' }"
					@click="contentClick(item)"
				>
					<div class="item-box">
						<!-- 文章图片 -->
						<img
							:src="$judgeFile(item.coverImg)"
							class="item-img"
							:style="{ height: isMain ? '380px' : '266px' }"
						/>
						<div class="item-content">
							<!-- 文章详情部分 标题，作者信息 -->
							<span class="content-title u-line-2">{{ item.title }}</span>
							<div class="content-bottom">
								<div class="bottom-left">
									<!-- <headAvator class="author-img" :own-id="getOwnId(item.memberId)" /> -->
									<img :src="getImgUrl(item.coverImg)" class="author-img" />
									<span class="author u-line-1">{{ item.memberName }}</span>
								</div>
								<div class="bottom-right">
									<img
										v-if="item.isDiggs"
										src="@/assets/skill-treasure/heart-small-active.png"
										class="item-icon"
									/>
									<img v-else src="@/assets/skill-treasure/heart-small.png" class="item-icon" />
									<span>{{ item.diggs || 0 }}</span>
								</div>
							</div>
							<div v-if="userType == 'user'" class="view-box">
								<img src="@/assets/skill-treasure/views.png" class="view-icon" />
								<span>{{ item.views || 0 }}</span>
							</div>
						</div>
						<!-- 左上角审核展示 -->
						<span
							v-if="userType == 'user' && item.auditStatus != 1 && activeCode == 'works'"
							class="status-top-box"
						>
							{{ getState(item.auditStatus) }}
						</span>
					</div>
				</div>
			</template>
		</div>
		<el-pagination
			v-if="total"
			background
			layout="prev, pager, next"
			class="pagination"
			:page-size="pageSize"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
import tabsNav from './components/tabs.vue';
import { getCookie } from '@/utils/auth';
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	components: { tabsNav },
	mixins: [PreviewAdjunctMixin],
	props: {
		isMain: {
			type: Boolean,
			default: () => {
				return true;
			}
		},
		/**
		 * 个人中心传过来的参数集合
		 * @Params showActiveCode 只展示哪个分类
		 * */
		params: {
			type: Object,
			default: () => {
				return { showActiveCode: '' };
			}
		}
	},
	data() {
		return {
			defaulAvatar: require('@/assets/shop-images/default-avatar.png'),
			// 类型列表
			typeList: [
				{
					name: '作品',
					id: '',
					code: 'works'
				},
				{
					name: '喜欢',
					id: '',
					code: 'like',
					type: '1'
				},
				{
					name: '收藏',
					id: '',
					code: 'collect',
					type: '2'
				}
			],
			userTypeList: [
				{
					name: '作品',
					id: '',
					code: 'works'
				},
				{
					name: '喜欢',
					id: '',
					code: 'like',
					type: '1'
				}
			],
			activeCode: '', //选中的类型
			personalData: {}, // 用户数据
			countInteract: {}, // 用户数据
			loading: false, //加载动画
			// 内容部分数据
			list: [],
			pageNum: 1,
			pageSize: 8,
			total: 0,
			memberId: '', //用户id
			editprofile: false, // 是否修改签名
			profileInput: '', // 个人签名输入框的值
			userId: getCookie('user_id'), //获取登录信息的用户id
			userType: '' //用户类型，用于区分是否作者本人， ''为非本人，user为本人
		};
	},
	mounted() {
		if (this.params.showActiveCode) {
			this.activeCode = this.params.showActiveCode; //选中的类型
		} else {
			this.activeCode = 'works'; //默认选中的类型
		}
		let dydUserinfo = this.$getDydUserinfo();
		this.memberId = this.$route.query.memberId || dydUserinfo.id || '';
		this.userType = this.memberId == dydUserinfo.id ? 'user' : '';
		if (this.activeCode === 'works') {
			this.getList();
		} else {
			this.getCollectStory(this.params.type);
		}
		this.getUserInfo();
		this.getCountInteract();
	},
	methods: {
		/**
		 * @description 类型点击事件，用于切换内容
		 * @params {item} 点击的类型数据
		 */
		typeClick(item) {
			this.activeCode = item.code;
			this.pageNum = 1;
			this.list = [];
			if (item.type) {
				this.getCollectStory(item.type);
			} else {
				this.getList();
			}
		},
		/**
		 * @description 内容点击事件
		 * @params {item} 点击内容数据
		 * */
		contentClick(item) {
			this.jumpPage(`/skill-treasure-details?id=${item.id}`, true);
		},
		// 获取作者的个人信息
		getUserInfo() {
			let params = {
				userId: this.memberId
			};
			this.$api.treasure_api.getUserInfo(params).then(res => {
				if (res.state) {
					this.personalData = res.result || {};
				}
			});
		},
		// 修改用户个人签名触发事件
		editprofileFun() {
			this.editprofile = true;
			this.profileInput = this.personalData.userSignature || '';
		},
		// 修改用户个人签名
		changeProfile() {
			let params = {
				id: this.memberId,
				userSignature: this.profileInput
			};
			this.$api.treasure_api.updateUserInfo(params).then(res => {
				if (res.state) {
					// 成功之后重新请求
					this.editprofile = false;
					this.getUserInfo();
				}
			});
		},
		/**
		 * @description 获取列表数据
		 * */
		getList() {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				rentId: this.getSiteId(), // 租户id
				queryOwn: this.userType == 'user' ? true : false,
				memberId: this.memberId
			};
			this.$api.treasure_api.memberStoryList(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		},
		// 获取审核状态
		// (0:待审核,1:审核通过,2:审核未通过)   auditStatus
		getState(auditStatus) {
			let text = '';
			switch (auditStatus) {
				case 0:
					text = '待审核';
					break;
				case 1:
					text = '审核通过';
					break;
				case 2:
					text = '审核未通过';
					break;
				default:
					text = '审核中';
			}
			return text;
		},
		/**
		 * @description 获取点赞收藏列表数据
		 * @param {type} 点赞列表1  收藏列表2  不传默认为2
		 * */
		getCollectStory(type) {
			this.loading = true;
			let params = {
				pageNum: this.pageNum, // 页数
				pageSize: this.pageSize, // 页面大小
				actType: type, //点赞列表1  收藏列表2  不传默认为2
				memberId: this.memberId,
				rentId: this.getSiteId() // 租户id
			};
			this.$api.treasure_api.getCollectStory(params).then(res => {
				if (res.code == 200) {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
				}
				this.loading = false;
			});
		},
		/**
		 * @description 1点赞攻略    2收藏攻略  6关注用户   11点赞评论 操作
		 * @param {code} 1点赞攻略    2收藏攻略  6关注用户   11点赞评论
		 * */
		memberInteract(code) {
			// 未登录不继续执行
			if (!this.isShopLogin()) {
				return;
			}
			let param = {
				rentId: this.getSiteId(), // 租户id
				objId: this.memberId,
				actType: code
			};
			this.$api.treasure_api.memberInteract(param).then(res => {
				if (res.code == 200) {
					this.getCountInteract();
				}
			});
		},

		// 获取关注，点赞，粉丝数量
		getCountInteract() {
			let params = {
				rentId: this.getSiteId(), // 租户id
				memberId: this.memberId
			};
			this.$api.treasure_api.getCountInteract(params).then(res => {
				if (res.code == 200) {
					this.countInteract = JSON.parse(res.results) || {};
				}
			});
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			this.getList();
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url, type) {
			if (!type) {
				return false;
			}
			this.$router.push(url);
		}
	}
};
</script>

<style lang="scss" scoped>
.personal-homepage {
	margin: 0 auto;
	padding: 0 0 16px;
	font-family: PingFang SC-Regular, PingFang SC;
	// 个人数据区域
	.personal-data {
		width: 100%;
		min-height: 316px;
		background: #ffffff;
		border-radius: 4px;
		position: relative;
		margin-bottom: 16px;
		.data-box {
			max-width: 80%;
			min-height: 244px;
			display: flex;
			justify-content: center;
			padding: 40px 0;
			margin: 0 auto;
			.personal-img {
				width: 164px;
				height: 164px;
				overflow: hidden;
				border-radius: 50%;
				flex-shrink: 0;
			}
			.info-box {
				display: flex;
				flex-direction: column;
				margin-left: 40px;
				padding-top: 12px;
				position: relative;
				.name {
					font-size: 24px;
					font-weight: 500;
					color: #404040;
					line-height: 32px;
					word-break: break-all;
				}
				.sign {
					font-size: 18px;
					color: #404040;
					line-height: 32px;
					min-height: 78px;
					margin-top: 8px;
					display: flex;
					word-break: break-all;
				}
				.edit-profile-box {
					display: flex;
					align-items: center;
				}
				.edit-btn {
					width: 60px;
					height: 32px;
					display: inline-block;
					background: var(--brand-6, #0076e8);
					border-radius: 4px;
					color: #ffffff;
					line-height: 32px;
					font-size: 13px;
					font-weight: 400;
					// margin-right: 8px;
					text-align: center;
					cursor: pointer;
				}
				.sign-icon {
					width: 24px;
					height: 24px;
					margin-left: 8px;
					flex-shrink: 0;
					margin-top: 4px;
				}
				.bottom {
					display: flex;
					align-items: center;
				}
				.bottom-item {
					font-size: 16px;
					color: #8c8c8c;
					line-height: 32px;
					cursor: pointer;
					.number {
						color: #404040;
					}
				}
				.item-space {
					height: 20px;
					width: 0px;
					border: 1px solid #f0f0f0;
					display: inline-block;
					margin: 0 16px;
				}
			}
		}
		::v-deep .el-input-group__append {
			padding: 0;
		}
		::v-deep .el-input__inner {
			width: 250px;
			padding-right: 50px;
		}
		.type-box {
			max-width: 100%;
			text-align: center;
			border-top: 1px solid #f0f0f0;
		}
		// 右侧操作按钮
		.person-btns {
			position: absolute;
			top: 40px;
			right: 40px;
			display: flex;
			.btn-item {
				display: flex;
				margin-left: 30px;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				.item-icon {
					height: 20px;
					margin-bottom: 4px;
				}
			}
			.interest-btn {
				width: 64px;
				height: 32px;
				background: var(--brand-6, #0076e8);
				border-radius: 21px;
				font-size: 16px;
				color: #ffffff;
				line-height: 32px;
				text-align: center;
				cursor: pointer;
			}
			.interest-active {
				width: 96px;
				padding: 4px 0;
				border-radius: 21px;
				border: 1px solid #d9d9d9;
				font-size: 16px;
				color: #8c8c8c;
				line-height: 24px;
				text-align: center;
				cursor: pointer;
			}
		}
	}
	.content-box {
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 24%;
			margin: 0 1% 16px 0;
			cursor: pointer;
			.item-box {
				width: 100%;
				height: 100%;
				border-radius: 10px;
				overflow: hidden;
				background: #ffffff;
				position: relative;
			}
			.item-img {
				width: 100%;
				object-fit: cover;
			}
			.item-content {
				flex-shrink: 0;
				padding: 12px 16px;
				position: relative;
			}
			.content-title {
				height: 40px;
				font-size: 14px;
				color: #404040;
				line-height: 22px;
			}
			.content-bottom {
				display: flex;
				justify-content: space-between;
				margin-top: 8px;
			}
			.bottom-left,
			.bottom-right {
				display: flex;
				align-items: center;
				flex-shrink: 0;
			}
			.author-img {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				margin-right: 8px;
				flex-shrink: 0;
			}
			.author {
				font-size: 12px;
				color: #404040;
				line-height: 22px;
			}
			.item-icon {
				width: 20px;
				height: 20px;
				margin-right: 8px;
			}
			.status-top-box {
				position: absolute;
				font-size: 14px;
				font-weight: 500;
				color: #ffffff;
				background: rgba(0, 0, 0, 0.3);
				border-radius: 28px;
				top: 12px;
				left: 12px;
				padding: 6px 8px;
				display: inline-block;
			}
			.view-box {
				height: 20px;
				background: rgba(0, 0, 0, 0.3);
				border-radius: 24px;
				padding: 5px 7px;
				position: absolute;
				top: -35px;
				right: 5px;
				font-size: 14px;
				font-weight: 500;
				color: #ffffff;
				display: flex;
				align-items: center;
				.view-icon {
					width: 14px;
					margin-right: 2px;
				}
			}
		}
	}
	.pagination {
		margin-top: 8px;
		text-align: right;
	}
}
</style>
