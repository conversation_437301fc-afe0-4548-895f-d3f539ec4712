<template>
	<div v-loading="loading" class="history-box">
		<el-table
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
		>
			<el-table-column type="index" label="序号" width="150"></el-table-column>
			<el-table-column prop="name" label="课程信息">
				<template slot-scope="scope">
					<div class="detail-box">
						<img class="course-img" :src="scope.row.coverImg" alt="" />
						<div class="course-info">
							<span class="name">{{ scope.row.name }}</span>
							<span class="desc">{{ scope.row.desc }}</span>
						</div>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="logTime" label="浏览时间"></el-table-column>
			<el-table-column fixed="right" label="操作" width="130">
				<template slot-scope="scope">
					<el-button type="text" size="small" class="del-btn" @click="delClick(scope.row)">
						删除
					</el-button>
					<span class="space"></span>
					<el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			:current-page="pageNum"
			:page-size="pageSize"
			layout="prev, pager, next, jumper"
			:total="total"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>
<script>
export default {
	data() {
		return {
			loading: false,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			list: [], //浏览记录数据
			ids: [] //当前选中数据id
		};
	},
	mounted() {
		this.readLogList();
	},
	methods: {
		/**
		 * @description 获取浏览记录
		 */
		readLogList() {
			this.loading = true;
			let param = {
				pageSize: this.pageSize,
				pageNum: this.pageNum
			};
			this.$api.study_api
				.readLogList(param)
				.then(({ code, data }) => {
					this.loading = false;
					if (code == 200) {
						this.list = data?.items || [];
						this.total = data?.total || 0;
					}
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 删除操作
		 * */
		delClick(item) {
			// let ids = [];
			// ids.push(item.id)
			// console.log(item.id,"item",ids)
			this.$confirm('确认删除该记录？')
				.then(_ => {
					const param = {
						ids: item.id
					};
					this.$api.study_api
						.readLogDelete(param)
						.then(({ code, data }) => {
							if (code == 200) {
								this.$message.success('操作成功');
								this.readLogList();
							}
						})
						.catch(({ msg }) => {
							this.$message.errorr(msg);
						});
				})
				.catch(_ => {});
		},
		/**
		 * @description 查看操作
		 * */
		handleClick(item) {
			this.$router.push({ path: '/freecourses', query: { id: item.docId } });
		},
		handleCurrentChange(value) {
			this.pageNum = value;
			this.readLogList();
		}
	}
};
</script>

<style lang="scss" scoped>
.history-box {
	background: #ffffff;
	border-radius: 2px;
	margin-top: 20px;
	padding: 20px;
	.del-btn {
		color: #de2d2d;
	}
	.space {
		display: inline-block;
		width: 1px;
		height: 10px;
		background: #dfdfdf;
		margin: 0 20px;
	}
}
.el-table {
	.history-table {
		background: #f8f7f7;
		th {
			background: #f8f7f7;
		}
	}
}
.detail-box {
	display: flex;
	align-items: center;
	.course-img {
		width: 84px;
		height: 56px;
		margin-right: 12px;
		object-fit: cover;
	}
	.course-info {
		.name {
			font-size: 14px;
			color: #28292d;
			display: block;
		}
		.desc {
			margin-top: 10px;
			font-size: 12px;
			color: #93969c;
			display: block;
		}
	}
}
.pagination {
	margin-top: 20px;
	text-align: center;
}
</style>
