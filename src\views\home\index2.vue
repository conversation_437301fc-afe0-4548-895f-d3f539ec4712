<template>
	<div class="employment">
		<el-button v-focus type="primary">主要按钮</el-button>
	</div>
</template>

<script>
import PreviewAdjunctMixin from './mixin/previewAdjunct';
import { baseUrl } from '@/config';
export default {
	directives: {
		focus: {
			bind: function (el) {
				console.log('>>>bind', el);
			},
			// 指令的定义
			inserted: function (el) {
				console.log('>>>el', el);
			}
		}
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			carouselList: [], //轮播
			toolList: [
				{
					img: require('@/assets/employment-images/tool-enterprise.png'),
					name: '入驻企业',
					nums: '0',
					unit: '家'
				},
				{
					img: require('@/assets/employment-images/tool-interview.png'),
					name: '在线面试',
					nums: '0',
					unit: 'W+'
				},
				{
					img: require('@/assets/employment-images/tool-fund.png'),
					name: '创业基金',
					nums: '0',
					unit: 'W+'
				},
				{
					img: require('@/assets/employment-images/tool-master.png'),
					name: '创业导师',
					nums: '0',
					unit: '+'
				},
				{
					img: require('@/assets/employment-images/tool-positions.png'),
					name: '招聘岗位',
					nums: '0',
					unit: '个'
				}
			], // 分类数据集合数据
			servicesLeftList: [], // 创业服务
			employmentServiceList: [], //就业服务
			servicesRightList: [], // 名师指导
			positionList: [], // 推荐就业岗位
			tabList: [
				{
					shortName: '推荐',
					cciValue: ''
				}
			], //推荐就业类型选择数据
			tabActive: '',
			e_guide: '', //就业指导的typeId
			e_example: '', //就业案例的typeId
			employment: [
				{
					img: require('@/assets/employment-images/employment1.png'),
					name: '就业培训',
					url: '/course-list',
					desc: '专业导师培训，就业快人一步'
				},
				{
					img: require('@/assets/employment-images/employment2.png'),
					name: '就业指导',
					code: 'e_guide',
					url: `/services-type-list?typeNum=1&typeId=`,
					desc: '全方位就业指导，了解市场行业需求'
				},
				{
					img: require('@/assets/employment-images/employment3.png'),
					name: '就业案例解析',
					code: 'e_example',
					url: `/services-type-list?typeNum=1&typeId=`,
					desc: '多个就业案例，体会就业求职技巧'
				},
				{
					img: require('@/assets/employment-images/employment4.png'),
					name: '实习网点查询',
					url: '/internship-network',
					desc: '分析城市企业，了解各行业文化'
				}
			], //就业服务
			projectList: [] // 创业项目库
		};
	},

	methods: {
		jump() {
			// /information-detail?id=${item.id}&code=${item.nodeCode}
		},
		/**
		 * @description 顶部轮播
		 * */
		async getAdvertsByCode() {
			const { result } = await this.$api.shop_api.getAdvertsByCode({
				sysCode: 'pc_commodity_top_top',
				siteId: this.getSiteId() // 租户id
			});
			this.carouselList = result.adData || [];
		},
		// 获取顶部统计数据展示
		getHomeStatistics() {
			this.$api.employment_api.getHomeStatistics().then(res => {
				let results = res.results;
				let listStr = ['enterpriseNum', 'interviewNum', 'moneyCount', 'mentorNum', 'postNum'];
				if (res.rCode == 0) {
					this.toolList.forEach((item, index) => {
						item.nums = results[listStr[index]] || 0;
					});
				}
			});
		},
		/**
		 * @description 获取创业基金数据
		 * */
		getFundInfo() {
			// 主应用在路由取参，子应用就取组件传参
			let params = {
				id: '7119576191144562688',
				tenantId: this._userinfo.tenantId || this.$tenantId,
				code: 'fundManage'
			};
			this.$api.information_api.detail(params).then(res => {
				this.toolList[2].nums = res.results?.amount || 0;
			});
		},
		/**
		 * @description 获取创业类型分页接口 0是创业 1是就业
		 * */
		getServiceTypeList() {
			let param = {
				pageNum: 1,
				pageSize: 7,
				type: 0
			};
			this.$api.employment_api.serviceTypeList(param).then(res => {
				this.servicesLeftList = res?.results?.records || [];
			});
		},
		/**
		 * @description 名师指导获取
		 * */
		getInformation() {
			let data = {
				nodeCode: 'pioneerMentorTeacher',
				tenantId: this._userinfo.tenantId || this.$tenantId,
				pageNum: 1,
				pageSize: 4
			};
			this.$api.information_api.paging(data).then(res => {
				this.servicesRightList = res?.results?.records || [];
			});
		},
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		/**
		 * @description 获取就业类型分页接口 0是创业 1是就业
		 * */
		getEmploymentServiceList() {
			let param = {
				pageNum: 1,
				pageSize: 8,
				type: 1
			};
			this.$api.employment_api.serviceTypeList(param).then(res => {
				// this.employmentServiceList = res?.results?.records || [];
				let list = res?.results?.records || [];
				console.log(list, 'list');
				list.map((item, index) => {
					console.log(item, 'item');
					if (item.code == 'e_guide' || item.code == 'e_example') {
						this[item.code] = item.id;
					}
				});
			});
		},
		/*
		 * @description 创业导师分页接口
		 * */
		getMentorList() {
			let param = {
				pageNum: 1,
				pageSize: 20
			};
			this.$api.employment_api.jobCoPioneerMentor(param).then(res => {});
		},
		/*
		 * @description 创业项目表分页接口
		 * */
		// getProjectList() {
		// 	let param = {
		// 		pageNum: 1,
		// 		pageSize: 8
		// 	};
		// 	this.$api.employment_api.jobStudentProjectList(param).then(res => {
		// 		this.projectList = res?.results?.records || [];
		// 	});
		// },
		/**
		 * @description 查询数据字典
		 * */
		findSysCode(code) {
			let param = {
				sysAppCode: code
			};
			this.$api.employment_api.findSysCode(param).then(res => {
				this.tabList = this.tabList.concat(res?.results || []);
			});
		},
		/**
		 * @descrtiption 岗位分页接口
		 * */
		getJobCoPostList() {
			let param = {
				pageNum: 1,
				pageSize: 9,
				postType: this.tabActive,
				auditStatus: 1 //审核状态(0.待审核、1.审核通过、2.驳回)
			};
			this.$api.employment_api.jobCoPostList(param).then(res => {
				this.positionList = res?.results?.records || [];
			});
		},
		/*
		 * @description 创业项目表分页接口
		 * */
		getProjectList() {
			let param = {
				pageNum: 1,
				pageSize: 8,
				auditStatus: 1
			};
			this.$api.employment_api.jobStudentProject(param).then(res => {
				this.projectList = res?.results?.records || [];
			});
		},
		/**
		 * @description 推荐就业类型点击切换
		 * */
		tabClick(item) {
			this.tabActive = item.cciValue;
			this.getJobCoPostList();
		},
		// 就业服务专项跳转
		employmentJumpPage(item) {
			let url = item.url;
			if (item.code) {
				url += this[item.code];
			}
			this.jumpPage(url);
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>

<style lang="scss" scoped>
.employment {
	background: #f9f9f9;
}
.carousel-box {
	width: 100%;
	height: 400px;
	.carousel-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}
::v-deep .el-carousel__button {
	width: 10px;
	height: 10px;
	background: #ffffff;
	border-radius: 50%;
}
::v-deep .el-carousel__indicators .is-active {
	.el-carousel__button {
		width: 22px;
		height: 10px;
		background: #0076e8;
		border-radius: 5px;
	}
}
.main {
	width: 1260px;
	margin: 0 auto;
	padding: 30px 0;
}
.title-box {
	display: flex;
	justify-content: space-between;
	.title-name {
		font-size: 24px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #000000;
	}
	.sub-title {
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #999999;
		margin-left: 9px;
	}
	.box-right {
		display: flex;
		align-items: center;
	}
	.tabs-list {
		// border: 1px solid red;
		display: flex;
		.tabs-item {
			display: inline-block;
			padding: 12px 20px;
			background: #ffffff;
			border-radius: 18px;
			font-size: 14px;
			color: #747d85;
			margin-left: 10px;
			cursor: pointer;
		}
		.item-active {
			color: #0076e8;
		}
	}
	.pagination-box {
		display: flex;
		.prev-btn,
		.next-btn {
			display: flex;
			width: 36px;
			height: 36px;
			background: #ffffff;
			border-radius: 50%;
			color: #747d85;
			justify-content: center;
			align-items: center;
			margin-left: 10px;
			cursor: pointer;
			&:hover {
				color: #0076e8;
			}
		}
		.space-btn {
			width: 70px;
			height: 36px;
			background: #ffffff;
			border-radius: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #747d85;
			margin-left: 10px;
		}
	}
	.more-btn {
		display: inline-block;
		width: 70px;
		height: 36px;
		font-size: 14px;
		line-height: 36px;
		text-align: center;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #747d85;
		background: #ffffff;
		border-radius: 18px;
		cursor: pointer;
		&:hover {
			color: #0076e8;
		}
	}
}
.tool-box {
	width: 100%;
	height: 48px;
	display: flex;
	padding: 0 30px;
	justify-content: space-between;
	.tool-item {
		display: flex;
		flex-direction: row;
		font-size: 14px;
		font-weight: 400;
		color: #7a8392;
	}
	.item-img {
		width: 48px;
		height: 48px;
		object-fit: contain;
		margin-right: 10px;
	}
	.item-right {
		.item-nums {
			font-size: 24px;
			font-family: DINCond-Bold;
			font-weight: 400;
			color: #0076e8;
			margin-right: 6px;
		}
	}
}
.services-box {
	margin-top: 50px;
	display: flex;
	justify-content: space-between;
	.services-left,
	.services-right {
		width: 620px;
	}
	.left-list-box,
	.right-list-box {
		margin-top: 32px;
		display: flex;
		flex-wrap: wrap;
	}
	.list-item {
		width: 300px;
		height: 130px;
		background: #ffffff;
		border-radius: 5px;
		padding: 30px;
		margin-bottom: 20px;
		display: flex;
		align-items: center;
		cursor: pointer;
		.item-img {
			width: 69px;
			height: 69px;
			object-fit: contain;
			margin-right: 19px;
		}
		.list-right {
			&-name {
				font-size: 18px;
				color: #333333;
			}
			&-sub {
				font-size: 14px;
				color: #999999;
			}
		}
		&:nth-child(2n) {
			margin-left: 18px;
		}
	}
	.right-list-item {
		width: 300px;
		height: 280px;
		background: #ffffff;
		border-radius: 5px;
		padding: 30px 15px;
		margin-bottom: 20px;
		display: flex;
		flex-direction: column;
		align-items: center;
		cursor: pointer;
		.item-img {
			width: 150px;
			height: 150px;
			border-radius: 50%;
			overflow: hidden;
		}
		.list-right-name {
			font-size: 18px;
			color: #333333;
			margin-top: 17px;
		}
		.list-right-desc {
			font-size: 14px;
			color: #999999;
			margin-top: 18px;
		}
		&:nth-child(2n) {
			margin-left: 18px;
		}
	}
}
.positions-box {
	margin-top: 36px;
	.positions-banner {
		height: 86px;
		margin-top: 24px;
	}
	.position-list {
		display: flex;
		flex-wrap: wrap;
	}
	.list-item {
		width: 406px;
		height: 186px;
		background: #ffffff;
		border-radius: 8px;
		margin-right: 19px;
		margin-top: 20px;
		padding: 19px;
		position: relative;
		cursor: pointer;
		.item-title {
			display: flex;
			justify-content: space-between;
			.name {
				font-size: 18px;
				color: #333333;
			}
			.price {
				font-size: 16px;
				color: #fe574a;
				flex-shrink: 0;
			}
		}
		.item-position {
			font-size: 12px;
			color: #999999;
			margin-top: 12px;
			display: inline-block;
			width: 100%;
		}
		.item-labels {
			display: inline-block;
			background: #f5f5f5;
			padding: 6px 15px;
			border-radius: 4px;
			margin-right: 10px;
			font-size: 12px;
			color: #666666;
			margin-top: 14px;
		}
		.com-box {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 60px;
			// background: #f9fcfb;
			background: url('~@/assets/employment-images/com-bg.png') center;
			background-size: cover;
			padding: 15px 20px;
			font-size: 12px;
			color: #666666;
			display: flex;
			align-items: center;
			.com-img {
				width: 30px;
				height: 30px;
				background: #f5f5f5;
				border-radius: 4px;
				margin-right: 13px;
				flex-shrink: 0;
			}
			.com-name {
				display: inline-block;
				width: 100%;
			}
			.com-base {
				flex-shrink: 0;
				margin-left: 5px;
			}
		}
		&:nth-child(3n) {
			margin-right: 0;
		}
	}
}
.employment-box {
	margin-top: 56px;
	.employment-content-box {
		margin-top: 32px;
		display: flex;
		justify-content: space-between;
	}
	.left-box,
	.right-box {
		width: 620px;
		height: 430px;
		display: flex;
		justify-content: space-between;
	}
	.item-box {
		position: relative;
	}
	.left-box {
		.item-box {
			width: 300px;
			height: 100%;
		}
	}
	.right-box {
		flex-direction: column;
		.item-box {
			width: 100%;
			height: 205px;
		}
	}
	.item-img {
		width: 100%;
		height: 100%;
	}
	.masker-box {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 80px;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 0px 0px 5px 5px;
		padding: 15px;
		font-size: 18px;
		font-weight: bold;
		color: #ffffff;
		cursor: pointer;
		.item-name {
			line-height: 18px;
		}
		.item-desc {
			font-size: 14px;
			margin-top: 20px;
			display: flex;
			justify-content: space-between;
		}
	}
}
.project-box {
	margin-top: 56px;
	.project-list {
		margin-top: 30px;
		display: flex;
		flex-wrap: wrap;
	}
	.project-item {
		width: 300px;
		height: 280px;
		background: #ffffff;
		border-radius: 5px;
		margin-right: 19px;
		margin-bottom: 20px;
		&:nth-child(4n) {
			margin-right: 0;
		}
		.item-img {
			width: 100%;
			height: 178px;
			background: #ffffff;
		}
		.item-base {
			padding: 17px 19px;
			.name {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
			}
			.desc {
				font-size: 14px;
				line-height: 18px;
				color: #999999;
				margin-top: 12px;
			}
		}
	}
}
</style>
