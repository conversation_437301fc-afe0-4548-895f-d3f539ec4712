<!-- eslint-disable vue/no-v-html -->
<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<!-- 筛选区域 -->
			<filter-box
				:filter-list="filterList"
				:filter-form="filterForm"
				:need-del="false"
				@filterChange="filterChange"
				@search="search"
			></filter-box>
			<!-- 列表数据区域 -->
			<div v-loading="loading">
				<ul class="list-box">
					<li
						v-for="(item, index) in list"
						:key="index"
						class="list-item"
						@click="jumpPage(`/services-detail?type=mentor&id=${item.id}`)"
					>
						<img class="item-img" :src="getYbzyImg(item.coverImg)" alt="" />
						<div class="item-base">
							<p class="name over1">
								<span>{{ item.title }}</span>
								<!-- <span class="sub-name">{{ item.subName }}</span> -->
							</p>
							<span class="desc over1">{{ item.abstract }}</span>
						</div>
						<div class="masker-box">
							<span class="bar"></span>
							<p class="masker-desc" v-html="item.detail"></p>
						</div>
					</li>
					<!-- 分页 -->
					<el-pagination
						v-if="total"
						class="pagination"
						background
						layout="prev, pager, next,jumper"
						:total="total"
						@current-change="handleCurrentChange"
					></el-pagination>
				</ul>
				<Empty v-if="!list.length" :tips="'暂无数据'" />
			</div>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import filterBox from '../components/filter-box.vue';
import { baseUrl } from '@/config';
export default {
	components: {
		subBreadcrumb,
		filterBox
	},
	data() {
		return {
			filterList: {
				typeValue: {
					label: '导师分类：',
					porp: 'typeValue',
					typeList: [
						{
							name: '就业导师',
							id: 'jobMentorTeacher'
						},
						{
							name: '创业导师',
							id: 'pioneerMentorTeacher'
						}
					]
				}
			}, //
			// filterList: [
			// 	{
			// 		label: '导师分类：',
			// 		porp: 'typeValue',
			// 		typeList: [
			// 			{
			// 				name: '就业导师',
			// 				code: 'jobMentorTeacher'
			// 			},
			// 			{
			// 				name: '创业导师',
			// 				code: 'pioneerMentorTeacher'
			// 			}
			// 		]
			// 	}
			// ], //
			filterForm: {
				typeValue: ''
			},
			loading: false,
			pageNum: 1, //当前页码
			total: 0, //总条数
			keywords: '',
			nodeCode: '',
			list: [] //列表数据
		};
	},
	mounted() {
		this.nodeCode = this.$route.query.nodeCode || ''; //获取类型
		this.filterForm.typeValue = this.nodeCode;
		this.getMentorList();
	},
	methods: {
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		/*
		 * @description 创业导师分页接口
		 * */
		getMentorList() {
			this.loading = true;
			let data = {
				nodeCode: this.nodeCode,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				keywords: this.keywords,
				pageNum: this.pageNum,
				pageSize: 8
			};
			this.$api.information_api
				.paging(data)
				.then(res => {
					this.list = res?.results?.records || [];
					this.total = res?.results?.total || 0;
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		// 筛选事件
		filterChange(form) {
			this.nodeCode = form.typeValue.id;
			this.pageNum = 1;
			this.getMentorList();
		},
		/**
		 * @description 关键字搜索事件
		 */
		search(name) {
			this.keywords = name;
			this.pageNum = 1;
			this.getMentorList();
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.main {
	width: $max-width;
	margin: 20px auto 60px;
}
.filter-box {
	width: 100%;
	height: 102px;
	padding: 0 20px;
	background: #ffffff;
	.type {
		height: 50px;
		box-sizing: border-box;
		border-bottom: 1px solid #e8eaf0;
		.all {
			padding: 2px 16px;
			color: #333333;
			font-size: 16px;
			cursor: pointer;
			margin: 0 10px;
		}
		.all-active {
			background: #4f85ff;
			border-radius: 14px;
			color: #ffffff;
			box-sizing: content-box;
			border: 1px solid #4f85ff;
		}
		ul {
			li {
				font-weight: 400;
				cursor: pointer;
				color: #333333;
				font-size: 16px;
				margin: 0 10px;
				padding: 0 15px;
				height: 30px;
				line-height: 30px;
				border-radius: 4px;
				position: relative;
				overflow: hidden;
				position: relative;
			}
			.liActive {
				color: #0076e8;
				border: 1px solid #0076e8;
				border-radius: 4px;
				// width: 20px;
				// height: 20px;
			}
			.item-icon {
				position: absolute;
				top: -6px;
				right: -6px;
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #0076e8;
				display: inline-block;
				&::before {
					content: 'X';
					position: absolute;
					z-index: 3;
					color: #fff;
					left: 4px;
					top: -3px;
					font-size: 10px;
				}
			}
		}
	}
	.label {
		font-size: 14px;
		font-weight: bold;
		color: #666666;
	}
	.input {
		height: 50px;
		.el-input {
			margin: 0 10px 0 30px;
			width: 220px;
		}
		.search-btn {
			background: #0076e8;
			border-radius: 4px;
		}
		.reset-btn {
			background: #ffffff;
			color: #666666;
			border-radius: 4px;
		}
	}
}
.list-box {
	width: $max-width;
	margin: 20px auto 60px;
	// margin-top: 30px;
	display: flex;
	flex-wrap: wrap;
	.list-item {
		width: 300px;
		height: 390px;
		background: #ffffff;
		border-radius: 5px;
		margin-right: 20px;
		margin-bottom: 20px;
		border-radius: 5px;
		position: relative;
		&:nth-child(4n) {
			margin-right: 0;
		}
		.item-img {
			width: 100%;
			height: 300px;
			background: #ffffff;
			object-fit: contain;
		}
		.item-base {
			padding: 19px 15px;
			.name {
				font-size: 18px;
				line-height: 18px;
				font-weight: bold;
				color: #333333;
			}
			.sub-name {
				font-size: 14px;
				font-weight: 400;
				color: #999999;
				margin-left: 10px;
			}
			.desc {
				width: 100%;
				display: inline-block;
				font-size: 14px;
				line-height: 14px;
				color: #999999;
				margin-top: 23px;
			}
		}
		.masker-box {
			position: absolute;
			display: none;
			width: 100%;
			height: 180px;
			padding: 20px 15px 40px;
			background: #0076e8;
			border-radius: 0px 0px 5px 5px;
			left: 0;
			bottom: 0;
			font-size: 14px;
			color: #ffffff;
			.masker-desc {
				height: 100%;
				overflow: auto;
			}
			.bar {
				display: inline-block;
				width: 26px;
				height: 4px;
				background: #ffffff;
				margin-bottom: 20px;
			}
		}
		&:hover {
			.masker-box {
				display: block;
			}
		}
	}
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
