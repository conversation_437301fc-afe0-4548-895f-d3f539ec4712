<template>
	<div class="tinymce-editor">
		<Editor :id="tinymceId" v-model="myValue" :init="init" :disabled="disabled"></Editor>
	</div>
</template>
<script>
import tinymce from 'tinymce/tinymce'; //tinymce默认hidden，不引入不显示
import Editor from '@tinymce/tinymce-vue'; //编辑器引入
import 'tinymce/themes/silver/theme'; //编辑器主题

import 'tinymce/plugins/image'; // 插入上传图片插件
import 'tinymce/plugins/media'; // 插入视频插件
import 'tinymce/plugins/textcolor'; //颜色
import 'tinymce/plugins/advlist'; //高级列表
import 'tinymce/plugins/autolink'; //自动链接
import 'tinymce/plugins/link'; //超链接
import 'tinymce/plugins/image'; //插入编辑图片
import 'tinymce/plugins/lists'; //列表插件
import 'tinymce/plugins/charmap'; //特殊字符
import 'tinymce/plugins/media'; //插入编辑媒体
import 'tinymce/plugins/wordcount'; // 字数统计
import 'tinymce/plugins/table';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/imagetools';
import 'tinymce/plugins/print';
import 'tinymce/plugins/importcss';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/save';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/template';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/charmap';
import 'tinymce/plugins/hr';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/anchor';
import 'tinymce/plugins/toc';

import 'tinymce/plugins/textpattern';
import 'tinymce/plugins/noneditable';
import 'tinymce/plugins/help';
import 'tinymce/plugins/charmap';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/code';
import 'tinymce/skins/ui/oxide/skin.css';

// xhr封装
function ajax({ methods, url, data, success, error, headers }) {
	let xhr = new XMLHttpRequest();
	xhr.withCredentials = false;
	xhr.open(methods, url, true);
	// 遍历设置Header
	Object.keys(headers).forEach(key => {
		xhr.setRequestHeader(key, headers[key]);
	});
	xhr.send(data);
	xhr.onload = () => {
		var res = JSON.parse(xhr.responseText);
		success(res);
	};
	/****************错误监听*****************/
	xhr.onerror = function () {
		error('上传失败！');
	};
}
export default {
	components: {
		Editor
	},
	props: {
		//内容
		value: {
			type: String,
			default: ''
		},
		//是否禁用
		disabled: {
			type: Boolean,
			default: false
		},
		triggerChange: {
			type: Boolean,
			default: false,
			required: false
		},
		//插件
		plugins: {
			type: [String, Array],
			default:
				'print preview importcss  searchreplace autolink autosave save directionality  visualblocks visualchars fullscreen image link media template codesample table charmap hr pagebreak nonbreaking anchor toc advlist lists  wordcount imagetools textpattern noneditable help charmap  quickbars code'
		},
		toolbar: {
			type: [String, Array],
			default() {
				return [
					'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist  | forecolor backcolor casechange   removeformat | pagebreak | charmap emoticons | fullscreen code preview save print | insertfile image media  template link anchor codesample | a11ycheck ltr rtl | showcomments addcomment '
				];
			}
		},
		// 上传接口
		imageUploadUrl: {
			type: String,
			default: ''
		},
		// 自定义图片上传接口的Header参数
		setHeaders: {
			type: Object,
			default: () => {
				return {};
			}
		},
		// 自定义图片上传接口formData参数
		formData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			//初始化配置
			tinymceHtml: '',
			tinymceId: 'tinymce',
			mytinymce: tinymce,
			init: {
				images_upload_url: this.imageUploadUrl, //这两行是更改只能上传图片路径的方法，变成可以拖拉上传（此处路径为后端需要上传图片的路径）
				images_upload_base_path: '/demo', //这两行是更改只能上传图片路径的方法，变成可以拖拉上传
				images_upload_credentials: true,
				images_upload_handler: (blobInfo, success, failure) => {
					if (this.imageUploadUrl && this.imageUploadUrl != '') {
						this.uploadImg(blobInfo, success, failure); // 自定义图片上传地址
					} else {
						console.log('未配置关键参数');
					}
				},
				selector: '#tinymce',
				skin_url: 'tinymce/skins/ui/oxide', //自己的static中路径
				content_css: 'tinymce/skins/content/default/content.css', //自己的static中路径
				content_style: 'img {max-width:100%;}', //限制图片大小
				language_url: 'tinymce/langs/zh_CN.js', //自己的static路径，路径错误会汉化失败
				language: 'zh_CN', //注意大小写
				fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px', //字体大小
				menubar: false, //此处设置为false为默认不显示菜单栏，如果需要展示出来可以将此行注释
				plugins: this.plugins, //插件
				//工具栏
				toolbar: this.toolbar,
				height: 500, //高度
				branding: false, //隐藏右下角技术支持
				paste_preprocess: function (plugin, args) {
					console.log(args.content);
				},
				paste_as_text: true,
				//init_instance_callback为回调配置项
				init_instance_callback: editor => {
					editor.on('input', e => {
						this.$emit('input', e.target.innerHTML);
					});
					editor.on('change', e => {
						this.$emit('change', e.level.content);
					});
				}
			}
		};
	},
	computed: {
		myValue: {
			get() {
				return this.value;
			},
			set(value) {
				this.$emit('change', value);
			}
		}
	},
	// watch: {
	// 	myValue: {
	// 		handler() {
	// 			this.$emit('change', this.myValue);
	// 		}
	// 	}
	// },
	mounted() {
		tinymce.init({}); //初始化编辑器
	},
	methods: {
		getContent() {
			return window.tinymce.get(this.tinymceId).getContent();
		},
		// 自定义图片上传地址
		uploadImg(blobInfo, success, failure) {
			try {
				let formData = new FormData();
				// 遍历设置formData
				Object.keys(this.formData).forEach(key => {
					formData.append(key, this.formData[key]);
				});
				formData.append('file', blobInfo.blob(), blobInfo.filename());
				// ownId
				formData.append('ownId', '123321');
				// 调用上传
				ajax({
					methods: 'POST',
					headers: this.setHeaders,
					url: this.imageUploadUrl,
					data: formData,
					success: res => {
						this.$emit('imageUploadSuccess', res, success, failure);
					},
					error: err => {
						failure(err);
					}
				});
			} catch (e) {
				failure(e);
			}
		}
	}
};
</script>
<style>
.tox-tinymce-aux {
	z-index: 10000 !important;
}
.tox-dialog--width-lg {
	z-index: 10000;
}
</style>
