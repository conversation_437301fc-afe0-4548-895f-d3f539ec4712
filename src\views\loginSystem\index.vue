<template>
	<div class="login">
		<!-- :style="{ backgroundImage: `url(${bg})` }" -->
		<video class="video" autoPlay loop muted :src="url"></video>
		<!-- // 视频静音 -->
		<!-- // 循环播放 -->
		<!-- // 自动播放 -->
		<login-header></login-header>

		<login-form></login-form>
		<login-footer></login-footer>
	</div>
</template>

<script>
import loginHeader from '@/views/loginSystem/components/header';
import loginFooter from '@/views/loginSystem/components/footer';
import loginForm from '@/views/loginSystem/components/form';
import { baseUrl } from '@/config';
export default {
	name: 'Login',
	components: {
		loginHeader,
		loginFooter,
		loginForm
	},
	data() {
		return {
			url: `${baseUrl}/project-ybzy/picture/video/1.mp4`
			// bg: require('@/assets/images/login/login-bg.png')
		};
	},
	watch: {},
	methods: {}
};
</script>

<style lang="scss" scoped>
.login {
	height: 100vh;
	width: 100%;
	background-size: 100% 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.video {
	position: absolute;
	width: 100%;
	height: 100%;
	object-fit: cover;
}
</style>
