<!--
 @desc:个人中心 就业创业 我的简历 实习经历
 @author: WH
 @date: 2023/9/5
 -->
<template>
	<div>
		<el-table :data="list" border style="width: 100%">
			<el-table-column fixed prop="companyName" label="公司"></el-table-column>
			<el-table-column prop="post" label="职务"></el-table-column>
			<el-table-column prop="time" label="工作时间" width="160"></el-table-column>
			<el-table-column
				prop="work"
				label="工作经历"
				width="300"
				:show-overflow-tooltip="true"
			></el-table-column>
			<el-table-column fixed="right" label="操作" width="100">
				<template slot-scope="scope">
					<el-button
						icon="el-icon-edit"
						type="text"
						size="small"
						@click="educationFn('edit', scope.$index, scope.row)"
					></el-button>
					<el-popconfirm title="确定删除吗？" @confirm="delRow(scope.$index)">
						<el-button slot="reference" icon="el-icon-delete" type="text" size="small"></el-button>
					</el-popconfirm>
				</template>
			</el-table-column>
		</el-table>
		<div class="add-btn-box">
			<el-button type="primary" icon="el-icon-plus" @click="educationFn('add')">
				新增工作经历
			</el-button>
		</div>
		<el-dialog title="实习经历" :visible.sync="visible">
			<el-form ref="rorm" :rules="rules" :model="form" label-width="140px">
				<el-form-item label="公司名称" prop="companyName">
					<el-input v-model="form.companyName" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="职务" prop="post">
					<el-input v-model="form.post" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="工作时间" prop="time">
					<!-- year/month/date/dates/week/ datetime/datetimerange/daterange/ monthrange/quarter/halfyear -->
					<el-date-picker
						v-model="form.time"
						type="monthrange"
						align="right"
						unlink-panels
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					></el-date-picker>
				</el-form-item>

				<el-form-item label="工作经历" prop="work">
					<el-input v-model="form.work" autocomplete="off" type="textarea" :rows="2"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button type="primary" @click="educationSubmit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { parseTime } from '@/utils';
export default {
	props: {
		tableData: {
			type: [Array, null],
			default: () => []
		}
	},
	data() {
		return {
			visible: false,
			list: [
				// {
				// 	companyName: '五粮液',
				// 	endTime: '2024-02',
				// 	post: '调酒师',
				// 	startTime: '2023-02',
				// 	time: '2023-02~2024-02',
				// 	work: '五粮液任职调酒师'
				// }
			],
			editIdx: null,
			form: { companyName: '', post: '', time: '', work: '' },
			rules: {
				companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
				post: [{ required: true, message: '请输入职务', trigger: 'blur' }],
				time: [{ required: true, message: '请选择工作时间', trigger: 'change' }],
				work: [{ required: true, message: '请输入工作经历', trigger: 'blur' }]
			}
		};
	},
	watch: {
		tableData(newVal) {
			this.list = [...newVal];
		}
	},
	mounted() {
		// this.handlerGetRegion();
		// this.handlerQueryDict();
	},
	methods: {
		educationSubmit() {
			this.$refs.rorm.validate(valid => {
				if (valid) {
					this.visible = false;
					const { time } = this.form;
					let startTime = parseTime(time[0], '{y}-{m}');
					let endTime = parseTime(time[1], '{y}-{m}');
					let timeStr = startTime + '~' + endTime;
					let row = { ...this.form, time: timeStr, startTime, endTime };
					//编辑
					if (this.editIdx !== null) {
						this.$set(this.list, this.editIdx, row);
					} else {
						//新增
						this.list.push(row);
					}
					this.form = {};
				}
			});
		},
		educationFn(type, index = null, row = {}) {
			this.visible = true;
			this.editIdx = index;
			if (type == 'edit') {
				this.form = {
					...row,
					time: [row.startTime, row.endTime]
				};
			}
		},
		delRow(index) {
			this.$delete(this.list, index);
		}
	}
};
</script>

<style lang="scss" scoped>
.add-btn-box {
	margin-top: 16px !important;
	text-align: center;
}
.el-input,
.el-select {
	width: 300px;
}
</style>
