<template>
	<div class="home">
		<!-- 顶部 -->
		<div class="top">
			<!-- 左侧 -->
			<div class="top-left">
				<img class="logo" :src="require('@/assets/images/homeVersion2/logo.png')" alt="" />
				<span class="login">统一身份认证</span>
				<span
					class="login"
					@click="goPages('http://***********/project-ybzy/ybzy_zhxy/index.html#/home')"
				>
					统一信息门户
				</span>
			</div>
			<!-- 右侧 -->
			<div class="top-right">
				<input class="search-input" placeholder="请输入学校或学院名称" type="text" />
				<div class="button">
					<img class="button-img" src="@/assets/images/home/<USER>" alt="" />
				</div>
			</div>
		</div>
		<!-- 内容区域 -->
		<div class="content">
			<!-- 标题 -->
			<h1 class="title">职业教育前途广阔、大有可为</h1>
			<div class="sub-title">面向高校各类场景的智能化服务</div>
			<div class="desc">
				建设“基于学校、立足宜宾、服务全国”的职教联盟统一服务平台，以“1+2+4+N”为主线的“云上职教”整体架构平台，集成校园基础设施建设管理系统及现有的业务系统，通过大数据管理平台、支撑服务平台打造属于职业技术学院的“智慧大脑”，实现整个校园“一网统管”“一网通办”的管理服务新格局，并为全省乃至全国的职业教育资源共享打造新引擎。
			</div>
			<!-- 学校轮选区 -->
			<!-- @swiper="onSwiper" @slideChange="onSlideChange" -->
			<div class="list">
				<div class="list-item-left swiper-button-prev">
					<img class="icon" :src="require('@/assets/images/homeVersion2/left-icon.png')" alt="" />
				</div>
				<swiper :options="swiperOption">
					<swiper-slide v-for="(item, index) in schoolList" :key="index">
						<div
							:class="['list-item', hoverImgIndex == index && 'item-active']"
							@mouseenter="hoverImg(item, index)"
							@mouseleave="hoverImg(item, index, 'leave')"
							@click="goPages(item.loginUrl)"
						>
							<img
								:class="['item-img']"
								:src="hoverImgIndex == index ? item.activeImg : item.img"
								alt=""
							/>
							<span class="item-name">
								{{ item.name }}
							</span>
						</div>
					</swiper-slide>
				</swiper>
				<div class="list-item-right swiper-button-next">
					<img class="icon" :src="require('@/assets/images/homeVersion2/right-icon.png')" alt="" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Home',
	data: () => {
		return {
			swiperOption: {
				slidesPerView: 4,
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev'
				}
			},
			hoverImgIndex: -1, //鼠标悬浮的时候动态事件判断下标
			schoolList: [
				{
					name: '宜宾职业技术学院',
					code: '',
					img: require('@/assets/images/homeVersion2/ybzyjsxy-logo.png'),
					activeImg: require('@/assets/images/homeVersion2/ybzyjsxy-logo-active.png'),
					loginUrl: 'http://***********/project-ybzy/ybzy_zhxy/index.html#/login'
				},
				{
					name: '南溪职业技术学校',
					code: '',
					img: require('@/assets/images/homeVersion2/nxzyjsxy-logo.png'),
					activeImg: require('@/assets/images/homeVersion2/nxzyjsxy-logo.png'),
					loginUrl: 'http://***********:9802/project-ybzy/ybzy_zhxy_standard/index.html#/login'
				},
				{
					name: '高县职业技术学校',
					code: '',
					img: require('@/assets/images/homeVersion2/gxzyjsxy-logo.png'),
					activeImg: require('@/assets/images/homeVersion2/gxzyjsxy-logo.png'),
					loginUrl: 'http://***********:9801/project-ybzy/ybzy_zhxy_standard/index.html#/login'
				},
				{
					name: '宜宾市职业技术学校',
					code: '',
					img: require('@/assets/images/homeVersion2/ybszyjsxy-logo.png'),
					activeImg: require('@/assets/images/homeVersion2/ybszyjsxy-logo.png'),
					loginUrl: ''
				}
			]
		};
	},
	methods: {
		// 悬浮效果展示
		hoverImg(item, index, type) {
			this.hoverImgIndex = index;
			if (type) {
				this.hoverImgIndex = -1;
			}
		},
		// 页面跳转
		goPages(loginUrl) {
			window.open(loginUrl);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
$min-width: 1200px;
.home {
	background: url('~@/assets/images/homeVersion2/bg.png') center;
	background-size: cover;
	width: 100%;
	height: 100%;
	min-width: $min-width;
	overflow: auto;
}
.top {
	width: 100%;
	padding: 37px 60px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	&-left {
		display: flex;
		align-items: center;
		.logo {
			height: 61px;
		}
		.login {
			display: inline-block;
			margin-left: 21px;
			padding-left: 27px;
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: bold;
			font-size: 24px;
			color: #ffffff;
			line-height: 44px;
			border-left: 1px solid rgba(206, 255, 255, 0.4);
			cursor: pointer;
		}
	}
	&-right {
		width: 353px;
		height: 48px;
		background: rgba(214, 232, 254, 0);
		overflow: hidden;
		border-radius: 24px;
		border: 1px solid #768dac;
		display: flex;
		justify-content: space-between;
		.search-input {
			width: 280px;
			height: 100%;
			padding: 0 20px;
			background: transparent;
			border: none;
			color: #fff;
			&::placeholder {
				font-size: 16px;
				color: #b6d8ff;
			}
		}
		.button {
			width: 72px;
			height: 100%;
			background: #0175e8;
			border-radius: 0px 24px 24px 0px;
			text-align: center;
			cursor: pointer;
			&-img {
				width: 20px;
				height: 20px;
				margin-top: 14px;
			}
		}
	}
}
// 内容区域
.content {
	width: $min-width;
	margin: 142px auto 0;
	.title {
		width: 100%;
		height: 95px;
		font-family: SourceHanSerifSC, SourceHanSerifSC;
		font-weight: 800;
		font-size: 66px;
		text-align: center;
		color: #fff;
		line-height: 95px;
		font-style: normal;
		background-image: -webkit-linear-gradient(270deg, #ffffff 0%, #6effff 100%);
		background-clip: text;
		-webkit-text-fill-color: transparent;
	}
	.sub-title {
		width: 1045px;
		height: 24px;
		margin: 30px auto 0;
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: bold;
		font-size: 18px;
		color: #e7faff;
		line-height: 24px;
		text-align: center;
		font-style: normal;
		position: relative;
		&::before {
			content: '';
			display: inline-block;
			width: 378px;
			height: 1px;
			border: 1px solid;
			opacity: 0.6;
			border-image: linear-gradient(270deg, rgba(195, 252, 255, 1), rgba(255, 255, 255, 0.18)) 1 1;
			position: absolute;
			top: 50%;
			left: 0;
		}
		&::after {
			content: '';
			display: inline-block;
			width: 378px;
			height: 1px;
			border: 1px solid;
			opacity: 0.6;
			border-image: linear-gradient(90deg, rgba(195, 252, 255, 1), rgba(255, 255, 255, 0.18)) 1 1;
			position: absolute;
			top: 50%;
			right: 0;
		}
	}
	.desc {
		width: 100%;
		height: 54px;
		margin-top: 16px;
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #b1e2ff;
		line-height: 27px;
		text-align: center;
		font-style: normal;
	}
	.list {
		margin-top: 26px;
		width: 100%;
		padding: 0 34px;
		position: relative;
		// padding: 8px 10px;
	}
	.list-item {
		width: 288px;
		height: 78px;
		background: url('~@/assets/images/homeVersion2/school-bg.png') center;
		background-size: cover;
		padding: 0 0 0 30px;
		display: flex;
		align-items: center;
		cursor: pointer;
		color: #ffffff;
		.item-img {
			width: 22px;
			height: 22px;
			margin-right: 3px;
			vertical-align: middle;
		}
		.item-name {
			font-family: MicrosoftYaHei, MicrosoftYaHei;
			font-weight: bold;
			font-size: 22px;

			line-height: 29px;
			text-align: right;
			font-style: normal;
		}
	}
	.item-active {
		background: url('~@/assets/images/homeVersion2/school-bg-active.png') center;
		background-size: cover;
		color: #45fffa;
	}
	.swiper-button-prev,
	.swiper-button-next {
		position: absolute;
		bottom: 18px;
		width: 30px;
		height: 46px;
		cursor: pointer;
		opacity: 1;
		.icon {
			width: 100%;
			height: 100%;
		}
	}
	.swiper-button-prev {
		left: 0;
	}
	.swiper-button-next {
		right: 0;
	}
	.swiper-button-prev:after,
	.swiper-button-next:after {
		display: none;
	}
}
</style>
