<template>
	<div class="logo-box">
		<div class="container">
			<!-- <router-link :to="{ path: pathUrl, query: $route.query }"> -->
			<component :is="type" v-bind="linkProps()">
				<div class="logo">
					<img src="@/assets/images/layout/header-logo.png" alt="" />
					<span class="logo-title">{{ title }}</span>
				</div>
			</component>
			<!-- </router-link> -->
			<div v-if="showTel" class="tel400">
				<i class="iconfont icon-dianhua"></i>
				{{ tal400 }}
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Header',
	components: {},
	props: {
		title: {
			type: String,
			default: '欢迎登录'
		},
		showTel: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			tal400: '6666'
		};
	},
	computed: {
		type() {
			if (this.$route.path != '/login') {
				return 'router-link';
			}
			return 'a';
		}
	},
	methods: {
		linkProps() {
			if (this.$route.path != '/login') {
				return {
					to: { path: '/login', query: this.$route.query }
				};
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.logo-box {
	background: #ffffff;
	width: 100%;
	.container {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 46px;
		padding: 41px 0 40px 0;
	}
}
.logo {
	display: flex;
	align-items: center;
	img {
		width: 150px;
	}
	&-title {
		height: 32px;
		font-size: 24px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #262626;
		line-height: 32px;
		margin-left: 14px;
		position: relative;
		&::after {
			content: '';
			display: inline-block;
			height: 18px;
			width: 1px;
			background-color: #d9d9d9;
			position: absolute;
			left: -10px;
			top: 50%;
			margin-top: -9px;
		}
	}
}
.tel400 {
	font-size: 20px;
	font-family: Rany-Medium, Rany;
	font-weight: 500;
	color: #0076e8;
	line-height: 32px;
	display: flex;
	align-items: center;
	i {
		font-size: 31px;
		margin-right: 9px;
	}
}
</style>
