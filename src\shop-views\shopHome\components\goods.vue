<template>
	<div class="goods">
		<div class="goods-title">{{ titleText }}</div>
		<div class="item">
			<a v-for="item of list" :key="item.id" href="javascript:void(0)" @click="onLink(item)">
				<div class="item-nav">
					<img :src="$judgeFile(item.coverUrl)" alt="" />
					<div class="name">
						<span class="title">
							<span v-show="item.recommendNames" class="tag">{{ item.recommendNames }}</span>
							{{ item.productName }}
						</span>
					</div>
					<div class="price">
						<span>¥</span>
						{{ item.oriPrice.toFixed(2) }}
					</div>
					<div class="store-name">
						<img v-if="item.shopLogo" :src="$judgeFile(item.shopLogo)" alt="" />
						<img v-else src="@/assets/shop-images/Group-1205.png" alt="" />
						{{ item.shopName }}
					</div>
				</div>
			</a>
		</div>
		<div v-if="isPagination" class="pagination">
			<pagination
				:key="pageKey"
				:fixed="'left'"
				:current-page="page"
				:page-size="limit"
				:total="total"
				:layout="'total,->,prev,pager,next,jumper'"
				@paginationChange="paginationChange"
			/>
		</div>
	</div>
</template>

<script>
import Pagination from '@/components/public/Pagination.vue';
export default {
	components: { Pagination },
	props: {
		titleText: {
			type: String,
			default: ''
		},
		isPagination: {
			type: Boolean,
			default: true
		},
		list: {
			type: Array,
			default: () => []
		},
		total: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			page: 1, // 当前页
			pageKey: 0,
			limit: 8 // 每页显示的条数
		};
	},
	created() {},
	methods: {
		paginationChange(item) {
			this.$emit('paginationChange', item);
		},
		onLink(t) {
			this.$router.push({
				path: '/shopDetail',
				query: {
					id: t.productId,
					type: t.regCode
				}
			});
		},
		reset() {
			this.page = 1;
			this.limit = 8;
			this.pageKey += 1;
		}
	}
};
</script>

<style lang="scss" scoped>
.goods {
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	margin-top: 16px;
	padding: 20px;
	margin-bottom: 60px;
	&-title {
		font-size: 30px;
		padding-left: 15px;
		padding-bottom: 20px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #000000;
		line-height: 44px;
	}
	.item {
		display: flex;
		flex-wrap: wrap;
		&-nav {
			margin: 8px 12px 0;
			width: 263px;
			height: 367px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			padding: 8px 6px;
			&:hover {
				.title {
					color: var(--brand-6, '#ca3f3b') !important;
				}
			}
			img {
				width: 250px;
				height: 250px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				object-fit: cover;
			}
			.name {
				margin-top: 7px;
				.tag {
					padding: 2px 8px;
					background: #ffeeee;
					border-radius: 2px 2px 2px 2px;
					opacity: 1;
					font-size: 12px;
					font-family: Source Han Sans SC-Normal, Source Han Sans SC;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
					line-height: 20px;
					width: 40px;
					margin-right: 5px;
				}
				.title {
					font-size: 16px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #404040;
					height: 48px;
					line-height: 24px;
					overflow: hidden; //超出的文本隐藏
					text-overflow: ellipsis; //溢出用省略号显示
					display: -webkit-box;
					-webkit-line-clamp: 2; // 超出多少行
					-webkit-box-orient: vertical;
				}
			}
			.price {
				width: 79px;
				height: 26px;
				font-size: 15px;
				font-family: Rany-Medium, Rany;
				font-weight: 500;
				color: #f95f55;
				line-height: 20px;
				span {
					font-size: 11px;
				}
			}
			.store-name {
				font-size: 12px;
				font-family: Source Han Sans SC-Normal, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
				display: flex;
				align-items: center;
				img {
					width: 12px;
					height: 12px;
					margin-right: 5px;
					object-fit: cover;
				}
			}
		}
	}
	.pagination {
		margin-top: 73px;
	}
}
</style>
