<!--
 @desc:个人中心 就业创业 我的投递
 @author: WH
 @date: 2023/9/5
 -->
<template>
	<div class="main">
		<div v-loading="loading" class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无投递记录',
					detail: '你还没有提交职位投递简历，或者前往',
					clickText: '招聘信息'
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<DeliverCard v-for="(item, index) in list" :key="index" :card-data="item" />
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import DeliverCard from './deliver/deliver-card.vue';
import NoData from './no-data.vue';

export default {
	components: { DeliverCard, NoData },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			loading: false,
			list: [],
			page: 1, // 页数
			size: 10, // 条数
			total: 10
		};
	},

	mounted() {
		this.getPostResumeList();
		// this.handlerQueryDict();
	},
	methods: {
		async getPostResumeList() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.getPostResumeList({
					pageNum: this.page,
					pageSize: this.size
				});
				if (rCode == 0) {
					this.list = results.records;
					this.total = results.total;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		noDataFn() {
			this.$router.push('/job-list');
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.getPostResumeList();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getPostResumeList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		padding: 20px;
		border-radius: 4px;
		border: 1px solid #e8eaec;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
