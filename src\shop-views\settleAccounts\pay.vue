<template>
	<div class="pay">
		<div class="pay-header">
			<div class="pay-header-title">订单提交成功，请尽快付款！订单号：{{ orderInfo.orderId }}</div>
			<div class="pay-header-dec">
				<div>
					商品信息：
					<span>{{ orderInfo.orderName }}</span>
					<span>25kg/袋*20000</span>
				</div>
				<div style="display: flex; align-items: center">
					订单金额：
					<span class="pay-price">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
				</div>
			</div>
		</div>
		<div class="pay-type">
			<div class="pay-type-title">选择支付方式</div>
			<div class="pay-type-content">
				<div class="pay-type-content-radio">
					<div class="pay-type-content-radio-item">
						<div class="pay-type-content-radio-item-nav">
							<div class="radio-img">
								<img class="img" src="@/assets/shop-images/check-circle-filled.png" alt="" />
							</div>
							<AlIcon name="icon-zhifubao" color="#06b4fd" size="22px"></AlIcon>
							<div>支付宝</div>
						</div>
						<div class="price">
							<span>支付：</span>
							<span class="price-item">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
						</div>
					</div>
					<div class="pay-type-content-radio-item">
						<div class="pay-type-content-radio-item-nav">
							<div class="radio-img">
								<img class="img" src="@/assets/shop-images/check-circle-filled.png" alt="" />
							</div>
							<AlIcon name="icon-weixin" color="#28c445" size="22px"></AlIcon>
							<div>微信</div>
						</div>
						<div class="price">
							<span>支付：</span>
							<span class="price-item">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
						</div>
					</div>
				</div>
				<div class="divider"></div>
				<div class="qrcode"><img src="" alt="" /></div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Pay',
	data() {
		return {
			orderInfo: {
				orderId: '',
				orderName: '',
				otherParams: { sumPrice: '' }
			}
		};
	}
};
</script>

<style scoped lang="scss">
.pay {
	width: 1200px;
	margin: 0 auto;
	&-header {
		margin-top: 26px;
		height: 98px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		padding: 23px 24px;
		&-title {
			font-size: 20px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 24px;
		}
		&-dec {
			display: flex;
			justify-content: space-between;
			align-items: center;
			div {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
				line-height: 22px;
				span {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 22px;
				}
				.pay-price {
					font-size: 24px;
					font-family: Rany-Medium, Rany;
					font-weight: 500;
					color: var(--brand-6, '#0076e8');
					line-height: 32px;
				}
			}
		}
	}
	&-type {
		margin-top: 16px;
		height: 564px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		padding: 32px 16px;
		&-title {
			font-size: 24px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 32px;
		}
		&-content {
			display: flex;
			&-radio {
				padding: 20px 30px;
				height: 355px;
				&-item {
					width: 712px;
					height: 64px;
					padding: 16px 20px;
					background: #ffffff;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
					border: 1px solid #d9d9d9;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 15px;
					.price {
						display: flex;
						align-items: center;
						span {
							font-size: 14px;
							font-family: Source Han Sans SC-Regular, Source Han Sans SC;
							font-weight: 400;
							color: #404040;
							line-height: 22px;
						}
						&-item {
							font-size: 24px !important;
							font-family: Rany-Medium, Rany;
							font-weight: 500;
							color: var(--brand-6, '#0076e8') !important;
							line-height: 32px;
						}
					}
					&-nav {
						display: flex;
						align-items: center;
						div {
							font-size: 16px;
							font-family: Source Han Sans SC-Medium, Source Han Sans SC;
							font-weight: 500;
							color: #404040;
							line-height: 24px;
							margin-left: 10px;
						}
						img {
							width: 22px;
							height: 22px;
							opacity: 1;
						}
						.radio-img {
							width: 28px;
							height: 28px;
							background: #ffffff;
							opacity: 1;
							border-radius: 50%;
							border: 1px solid #d9d9d9;
							margin-right: 18px;
							position: relative;
							.img {
								position: absolute;
								left: -1px;
								bottom: -1px;
								width: 28px;
								height: 28px;
							}
						}
					}
				}
			}
			.divider {
				width: 1px;
				height: 355px;
				opacity: 1;
				border: 1px solid;
				border-image: linear-gradient(
						90deg,
						rgba(222, 225, 234, 0),
						rgba(222, 225, 234, 1),
						rgba(222, 225, 234, 0)
					)
					1 1;
			}
			.qrcode {
				margin-left: 63px;
				img {
					width: 212px;
					height: 248px;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
			}
		}
	}
}
</style>
