<template>
	<div class="page">
		<div class="list">
			<div v-for="(item, index) of list" :key="index" class="list-item" @click="toDetail(item)">
				<img class="list-item-img" :src="$judgeFile(item.coverUrl)" alt="" />
				<div class="list-item-title">{{ item.name }}</div>
				<div class="list-item-oldPrice">￥{{ item.oriPrice }}</div>
				<div class="list-item-sellPrice">
					<div class="price">{{ item.sellPoint }}积分</div>
					<div class="desc">已兑换{{ item.sellNum || 0 }}</div>
				</div>
			</div>
		</div>
		<Empty v-if="list.length === 0" :tips="'暂无商品信息'" />
		<el-pagination
			v-else
			:key="pageKey"
			class="page-bottom"
			:current-page="pageNum"
			:page-sizes="[8, 16, 32, 64]"
			:page-size="pageSize"
			layout="total, sizes, prev, pager, next, jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
export default {
	name: 'List',
	props: {
		list: {
			type: [Object, Array],
			default: () => {
				return [];
			}
		},
		total: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			pageNum: 1,
			pageSize: 8,
			pageKey: 0
		};
	},
	methods: {
		/**跳转详情*/
		toDetail(t) {
			this.$router.push({
				path: '/shopDetail',
				query: {
					id: t.itemId || '',
					type: t.regCode,
					goodsType: 'point'
				}
			});
		},
		/**改变分页大小*/
		handleSizeChange(size) {
			this.$emit('changePage', this.pageNum, size);
		},
		/**改变分页页码*/
		handleCurrentChange(page) {
			this.$emit('changePage', page, this.pageSize);
		},
		reset() {
			this.pageNum = 1;
			this.pageSize = 8;
			this.pageKey += 1;
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	.list {
		min-height: 555px;
		background: #ffffff;
		border-radius: 4px;
		margin: 16px 0;
		padding: 8px 10px;
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 263px;
			height: 378px;
			background: #ffffff;
			padding: 8px 6px;
			margin: 16px;
			cursor: pointer;
			&-img {
				width: 250px;
				height: 250px;
				object-fit: cover;
			}
			&-title {
				font-size: 16px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
				margin-top: 7px;
				overflow: hidden; //超出的文本隐藏
				text-overflow: ellipsis; //溢出用省略号显示
				display: -webkit-box;
				-webkit-line-clamp: 2; // 超出多少行
				-webkit-box-orient: vertical;
			}
			&-oldPrice {
				font-size: 11px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 20px;
				margin-bottom: 3px;
				text-decoration: line-through;
			}
			&-sellPrice {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.price {
					font-size: 18px;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #f95f55;
					line-height: 20px;
				}
				.desc {
					font-size: 11px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 20px;
				}
			}
		}
	}
	&-bottom {
		margin: 20px 0;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
