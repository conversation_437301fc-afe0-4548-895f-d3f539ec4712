<template>
	<div v-loading="loading" class="detail-content">
		<div class="header">
			<div class="header-title">
				{{ detail.title }}
			</div>
			<div class="article-description">
				<div class="article-info">
					<span v-if="detail.origin">来源：{{ detail.origin }}</span>
					发布时间：{{ detail.publishTime }} 浏览量：{{ detail.viewNum || 0 }}次
				</div>
				<div class="article-handle">
					【字体：
					<span
						:class="[activeSize == 'big' ? 'activeFontSize' : '', '']"
						@click="changeSize('big')"
					>
						大
					</span>
					<span
						:class="[activeSize == 'middle' ? 'activeFontSize' : '', '']"
						@click="changeSize('middle')"
					>
						中
					</span>
					<span
						:class="[activeSize == 'small' ? 'activeFontSize' : '', '']"
						@click="changeSize('small')"
					>
						小
					</span>
					】
					<span @click="preview">打印</span>
					<span @click="urlCopy">分享到</span>
				</div>
			</div>
		</div>
		<!-- eslint-disable-next-line vue/no-v-html -->
		<div ref="content" class="article-content" v-html="detail.detail"></div>
	</div>
</template>

<script>
import urlCopy from '@/utils/url_copy';

export default {
	props: {
		detailId: {
			type: String,
			default: () => {
				return '';
			}
		}
	},
	data() {
		return {
			activeSize: 'middle',
			detail: {}, //详情数据
			loading: false //加载动画
		};
	},
	watch: {
		detailId: {
			handler() {
				this.getDetail();
				this.view();
			},
			immediate: true
		}
	},
	mounted() {},
	methods: {
		/**
		 * @description 添加浏览量
		 * */
		view() {
			let data = {
				objectId: this.detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				objectType: 'info'
			};
			this.$api.information_api
				.view(data)
				.then(res => {})
				.catch(() => {});
		},
		/**
		 * @description 获取列表
		 * */
		getDetail() {
			let code = this.$route.query.code || '';
			let detailId = this.$route.query.id || '';
			this.loading = true;
			let data = {
				id: detailId,
				tenantId: this._userinfo.tenantId || this.$tenantId,
				code: code
			};
			this.$api.information_api
				.detail(data)
				.then(res => {
					this.detail = res?.results || {};
					this.loading = false;
				})
				.catch(() => {
					this.loading = false;
				});
		},
		/**
		 * @description 改变字号
		 * */
		changeSize(type) {
			this.activeSize = type;
			let size = {
				big: '22',
				middle: '16',
				small: '12'
			};
			this.$refs.content.style.fontSize = `${size[type] || 16}px`;
		},
		preview() {
			window.print();
		},
		/**
		 * @description 分享
		 * */
		urlCopy() {
			urlCopy();
		}
	}
};
</script>

<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
.detail-content {
	width: 100%;
	background: #ffffff;
	padding: 0 20px;
	margin-bottom: 30px;
	.header {
		width: 100%;
		.header-title {
			padding: 49px 27px 0px;
			width: 100%;
			text-align: center;
			font-size: 24px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #333333;
		}
		.article-description {
			border-bottom: 1px solid #e9e9e9;
			padding: 39px 0 37px 0;
			text-align: center;
			.article-info {
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #999999;
			}
			.article-handle {
				margin-top: 17px;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #666666;
				span {
					padding: 0 10px;
					cursor: pointer;
				}
			}
		}
	}
	.article-content {
		width: 100%;
		padding: 39px 21px;
		overflow: hidden;
		img {
			max-width: 100%;
		}
	}
	.activeFontSize {
		color: #0076e8;
	}
}
</style>
