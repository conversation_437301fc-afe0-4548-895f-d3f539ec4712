<template>
	<div v-loading="loading" class="estimate">
		<div class="estimate-top">
			<div
				:class="['estimate-top-tab', estimateTopActive === 1 ? 'estimate-top-active' : '']"
				@click="estimateTop(1)"
			>
				<a href="javascript:void(0)">全部</a>
			</div>
			<div
				:class="['estimate-top-tab', estimateTopActive === 2 ? 'estimate-top-active' : '']"
				@click="estimateTop(2)"
			>
				<a href="javascript:void(0)">有图</a>
			</div>
		</div>
		<div class="list">
			<div v-for="(item, index) of list" :key="index" class="list-item">
				<div class="top">
					<div class="avatar">
						<img :src="$judgeFile(item.userPicture)" alt="" />
					</div>
					<div class="top-item">
						<div class="top-item-name">{{ item.userName }}</div>
						<div class="top-item-dec">
							<span>{{ item.commentTime }}</span>
							<!-- <span>马卡龙+莫兰迪色系【10支礼盒装】</span> -->
						</div>
					</div>
				</div>
				<div class="conten">
					<div class="conten-item">
						<div class="text">
							{{ item.content }}
						</div>
						<div class="imgs">
							<a
								v-for="(nav, i) of item.pictures"
								:key="i"
								href="javascript:void(0)"
								@click="clickMask(i, nav, item)"
							>
								<div v-show="i <= maskIndex" class="imgs-none">
									<img
										:class="['img', imgActive === nav ? 'img-active' : '']"
										:src="$judgeFile(nav)"
										alt=""
									/>
									<div v-show="nav == maskIndex" class="mask">+5</div>
								</div>
							</a>
						</div>
						<div v-show="imgActive === item.imgs" class="preview">
							<img class="img" :src="$judgeFile(item.imgs)" alt="" />
						</div>
					</div>
				</div>
			</div>
		</div>
		<Empty v-show="list.length == 0" :tips="'暂无评价信息'" />
	</div>
</template>

<script>
export default {
	name: 'Estimate',
	props: {
		info: {
			type: Object,
			default: () => {}
		}
	},
	data() {
		return {
			estimateTopActive: 1,
			maskIndex: 4,
			imgActive: null,
			list: [],
			imgs: '',
			loading: false
		};
	},
	created() {
		this.getList();
	},
	methods: {
		getList() {
			this.loading = true;
			let data = {
				productId: this.info.baseInfo?.PRODUCT_ID || '',
				type: this.estimateTopActive == 1 ? 'all' : 'havePicture',
				offset: 0,
				orderRule: 'composite',
				psize: 100
			};
			this.$api.shop_api
				.getCommentListNew(data)
				.then(res => {
					this.list = res?.result;
					this.loading = false;
				})
				.catch(err => {
					this.loading = false;
				});
		},
		estimateTop(item) {
			this.estimateTopActive = item;
			this.getList();
		},
		clickMask(index, nav, item) {
			item.imgs = nav;
			this.imgActive = item.imgs;
			if (index + 1 == 4) {
				this.maskIndex = 6 + 1;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.estimate {
	background: #ffffff;
	padding: 29px 23px;
	margin-bottom: 60px;
	&-top {
		display: flex;
		width: 845px;
		border-bottom: 1px solid #eeeeee;
		padding-bottom: 24px;
		&-tab {
			padding: 8px 20px;
			background: #ffffff;
			border-radius: 36px 36px 36px 36px;
			opacity: 1;
			border: 1px solid #dcdcdc;
			font-size: 14px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #262626;
			line-height: 22px;
			margin-right: 11px;
			a {
				color: #262626;
			}
		}
		&-active {
			padding: 8px 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 36px 36px 36px 36px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			font-size: 14px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			line-height: 22px;
			a {
				color: #ffffff;
			}
		}
	}

	.list {
		margin-top: 28px;
		&-item {
			margin-bottom: 52px;
			.top {
				display: flex;
				align-items: center;
				.avatar {
					width: 60px;
					height: 60px;
					opacity: 1;
					margin-right: 18px;
					img {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}
				&-item {
					&-name {
						height: 24px;
						font-size: 16px;
						font-family: Source Han Sans SC-Medium, Source Han Sans SC;
						font-weight: 500;
						color: #262626;
						line-height: 24px;
					}
					&-dec {
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
						span {
							margin-right: 16px;
						}
					}
				}
			}
			.conten {
				padding-left: 78px;
				&-item {
					padding-bottom: 52px;
					border-bottom: 1px solid #eee;
					.text {
						padding: 14px 0;
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #404040;
						line-height: 22px;
					}
					.imgs {
						// width: 440px;
						display: flex;
						flex-wrap: wrap;
						&-none {
							position: relative;
						}
						.mask {
							position: absolute;
							width: 98px;
							height: 98px;
							background: rgba(0, 0, 0, 0.5);
							border-radius: 0px 0px 0px 0px;
							opacity: 1;
							top: 12px;
							left: 0;
							font-size: 14px;
							font-family: Source Han Sans SC-Regular, Source Han Sans SC;
							font-weight: 400;
							color: #ffffff;
							line-height: 22px;
							display: flex;
							justify-content: center;
							align-items: center;
						}
						.img {
							width: 98px;
							height: 98px;
							border-radius: 0px 0px 0px 0px;
							opacity: 1;
							border: 1px solid #d9d9d9;
							margin: 12px 12px 0 0;
						}
						.img-active {
							width: 98px;
							height: 98px;
							border-radius: 0px 0px 0px 0px;
							opacity: 1;
							border: 2px solid #ca3f3b;
						}
					}
					.preview {
						width: 428px;
						height: 428px;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
						border: 1px solid #d9d9d9;
						img {
							width: 100%;
							height: 100%;
						}
					}
				}
			}
		}
	}
}
.empty {
	margin-top: 100px;
}
</style>
