<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div v-loading="loading" class="list-box">
			<!-- 列表内容区域 -->
			<div v-if="list && list.length" class="list-card">
				<!-- <Empty v-if="list.length == 0" :tips="'暂无数据'" /> -->
				<homeAlumniActive :number="number" :alumni-active-list="list"></homeAlumniActive>
			</div>
			<Empty v-else :tips="'暂无数据'" />
			<!-- 分页区域 -->
			<el-pagination
				v-if="totalNum"
				class="pagination"
				background
				prev-text="上一页"
				next-text="下一页"
				layout="prev, pager, next, jumper"
				:total="totalNum"
				:page-size="psize"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import homeAlumniActive from '../components/home-alumni-active.vue';

export default {
	components: {
		subBreadcrumb,
		homeAlumniActive
	},
	data() {
		return {
			list: [], //列表数据
			number: 15, //一页展示多少个活动
			totalNum: 0, //总条数
			psize: 15, ///每页条数
			pageNum: 1, //当前页码
			loading: false
		};
	},
	mounted() {
		this.getInterfaceDataList('alumniActive', 'alumniActive', 15);
	},
	methods: {
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.list = [];
			this.pageNum = val;
			// this.getInformation();
		},
		/**
		 * @description 获取列表
		 * */
		getInterfaceDataList(code, list, listSize) {
			this.loading = true;
			let newData = new FormData();
			newData.append('pageNumber', 1);
			let data = {
				'pager.pageNumber': 1,
				'pager.pageSize': listSize,
				'queryParams.status': '0,1,2',
				'queryParams.tenementId': this.$tenantId,
				'queryParams.plateCode': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
				// 'queryParams.activityType': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
			};

			this.$api.alumni_association_api.alumniActiveList(data).then(res => {
				this.list = res.data.list;
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
* {
	padding: 0;
	margin: 0;
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.list-box {
	font-family: Microsoft YaHei;
	width: $max-width;
	margin: 20px auto 60px;
	.item-card {
	}
}
.pagination {
	text-align: center;
	margin-top: 80px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
