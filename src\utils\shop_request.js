/*
 * @Description: 请求拦截
 * @Version: 1.0
 * @Autor: zhaodongming
 * @Date: 2023-03-30 10:27:48
 * @LastEditors: zhaodongming
 * @LastEditTime: 2023-05-29 16:34:52
 */
import axios from 'axios';
import { getCookie } from '@/utils/auth';
import config from '@/config';
// import { baseUrl } from '@/config';
import { error, response } from '@/utils/response-handle';

const getSiteId = () => {
	let siteId = '';
	siteId = getCookie('siteId');
	return siteId || config.siteId;
};
const baseURL = origin.includes('localhost') || origin.includes('10.40') ? '/dev-api' : '';
const service = axios.create({
	baseURL: baseURL,
	timeout: 30000,
	withCredentials: true
});
// 请求对象
service.interceptors.request.use(
	config => {
		if (getSiteId()) {
			config.headers['siteId'] = getSiteId();
		}
		// 设置token
		config.headers['token'] = getCookie('token');

		// 如果post请求使用FormData格式传参，处理参数和头部
		if (config.type === 'form') {
			config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
			let data = config.data;
			let newData = new FormData();
			Object.keys(data).forEach(key => {
				newData.append(key, data[key]);
			});
			config.data = newData;
		}
		return config;
	},
	error => {
		console.log(error);
		return Promise.reject(error);
	}
);
// 响应对象
service.interceptors.response.use(response, error);

export default service;
