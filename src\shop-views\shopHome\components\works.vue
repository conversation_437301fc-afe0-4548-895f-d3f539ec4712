<template>
	<div class="page">
		<div v-loading="loading" class="works">
			<div
				v-for="(item, index) of list"
				:key="index"
				class="works-item"
				:style="(index + 1) % 4 === 0 ? 'margin-right:0' : ''"
				@click="toDetail(item.id)"
			>
				<img class="works-item-img" :src="$judgeFile(item.coverImg)" alt="" />
				<div class="works-item-text">
					<div class="title">{{ item.title }}</div>
					<div class="bottom">
						<div class="flex">
							<img
								class="avatar"
								:src="item.coverImg ? $judgeFile(item.coverImg) : defaultAvatar"
								alt=""
							/>
							<div class="nickname">{{ item.memberName }}</div>
						</div>
						<div class="flex" @click.stop="handleLove(item.id)">
							<img class="icon" :src="item.isDiggs ? heartActive : heart" alt="" />
							<div class="dgg">{{ item.diggs }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<Empty v-if="list.length === 0"></Empty>
		<el-pagination
			v-else
			class="page-bottom"
			:current-page="page"
			:page-sizes="[10, 20, 50, 100]"
			:page-size="size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="total"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
		></el-pagination>
	</div>
</template>

<script>
export default {
	name: 'Works',
	props: {
		list: {
			type: [Array, Object],
			default: () => {
				return [];
			}
		},
		total: {
			type: [String, Number],
			default: () => {
				return 0;
			}
		}
	},
	data() {
		return {
			heart: require('@/assets/shop-images/heart.png'),
			heartActive: require('@/assets/shop-images/heart-active.png'),
			defaultAvatar: require('@/assets/shop-images/default-avatar.png'),
			loading: false,
			page: 1,
			size: 10
		};
	},
	methods: {
		/**每页数据改变*/
		handleSizeChange(size) {
			this.$emit('getWorkList', this.page, size);
		},
		/**页码改变*/
		handleCurrentChange(page) {
			this.$emit('getWorkList', page, this.size);
		},
		/**操作点赞*/
		handleLove(id) {
			let param = {
				rentId: this.getSiteId(), // 租户id
				objId: id,
				actType: 1
			};
			this.loading = true;
			this.$api.shop_api.memberInteract(param).then(res => {
				this.loading = false;
				if (res.code === 200) {
					this.$message.success('操作成功');
					this.list.forEach(item => {
						/**不用请求接口直接修改数据*/
						if (item.id === id) {
							if (item.isDiggs === 1) {
								item.diggs -= 1;
								item.isDiggs = 0;
							} else {
								item.diggs += 1;
								item.isDiggs = 1;
							}
						}
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**跳转详情*/
		toDetail(id) {
			this.$router.push(`/skill-treasure-details?id=${id}&type=user`);
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	width: 1200px;
	margin: 20px auto 0;
	.works {
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 288px;
			border-radius: 12px;
			margin: 0 16px 16px 0;
			cursor: pointer;
			&-img {
				width: 288px;
				height: 384px;
				border-radius: 12px 12px 0 0;
			}
			&-text {
				background: #ffffff;
				border-radius: 0 0 12px 12px;
				padding: 12px 16px;
				.title {
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					margin-bottom: 9px;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
				}
				.bottom {
					display: flex;
					align-items: center;
					justify-content: space-between;
					.flex {
						display: flex;
						align-items: center;
						.avatar {
							width: 20px;
							height: 20px;
							margin-right: 8px;
							border-radius: 50%;
						}
						.nickname {
							font-size: 12px;
							font-family: PingFang SC-Regular, PingFang SC;
							font-weight: 400;
							color: #404040;
							line-height: 22px;
						}
						.icon {
							width: 17px;
							height: 16px;
							margin-right: 10px;
						}
						.dgg {
							font-size: 12px;
							font-family: PingFang SC-Regular, PingFang SC;
							font-weight: 400;
							color: #8c8c8c;
							line-height: 22px;
						}
					}
				}
			}
		}
	}
	&-bottom {
		margin: 20px 0;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
