<template>
	<div class="management">
		<div class="management-left">
			<el-menu
				:default-active="activeMenu"
				class="el-menu-vertical-demo"
				active-text-color="#3274E0"
				text-color="#262626"
				@select="select"
			>
				<el-submenu v-for="subMenu of menus" :key="subMenu.index" :index="subMenu.index">
					<template slot="title">
						<img :src="subMenu.icon" class="img" alt="" />
						<span>{{ subMenu.title }}</span>
					</template>
					<el-menu-item-group>
						<el-menu-item
							v-for="menuItem of subMenu.list"
							:key="menuItem.index"
							:index="menuItem.index"
							@click="toRoute(menuItem)"
						>
							{{ menuItem.title }}
						</el-menu-item>
					</el-menu-item-group>
				</el-submenu>
			</el-menu>
		</div>
		<div class="management-right">
			<subBreadcrumb class="subBreadcrumb" :is-main="false" background="#ffffff"></subBreadcrumb>
			<component :is="componentName"></component>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import overview from './child-page/overview';
import biographical from './child-page/biographical';
import enterInfo from './child-page/enterInfo';
import interest from './child-page/interest';
import interviewRecord from './child-page/interviewRecord';
import position from './child-page/position';
import recycle from './child-page/recycle';
import serveList from './child-page/serveList';
import message from './child-page/message';
import jobChoiceMeeting from './child-page/jobChoiceMeeting';
import liveList from './child-page/liveList';
import Postposts from './child-page/Postposts';

export default {
	name: 'Management',
	components: {
		subBreadcrumb, // 面包屑
		overview, //概览
		biographical, //收到的简历
		Postposts, //发布岗位
		enterInfo, //企业信息
		interest, //对我感兴趣
		interviewRecord, //面邀记录
		position, //职位列表
		recycle, //简历回收站
		serveList, //服务列表
		message, // 留言
		jobChoiceMeeting, //双选会,
		liveList // 最近直播
	},
	data() {
		return {
			activeMenu: '2-1',
			componentName: 'enterInfo',
			menus: [
				// {
				// 	title: '工作台',
				// 	index: '1',
				// 	icon: require('@/assets/images/person/work.png'),
				// 	list: [
				// 		{
				// 			title: '概览',
				// 			index: '1-1',
				// 			path: 'overview'
				// 		}
				// 	]
				// },
				{
					title: '企业信息',
					index: '2',
					icon: require('@/assets/images/person/enter-info.png'),
					list: [
						{
							title: '企业信息',
							index: '2-1',
							path: 'enterInfo'
						}
					]
				},

				{
					title: '发布岗位',
					index: '9',
					icon: require('@/assets/images/person/enter-info.png'),
					list: [
						{
							title: '发布岗位',
							index: '9-1',
							path: 'Postposts'
						}
					]
				},

				{
					title: '职位管理',
					index: '3',
					icon: require('@/assets/images/person/position.png'),
					list: [
						{
							title: '职位列表',
							index: '3-1',
							path: 'position'
						}
					]
				},
				{
					title: '简历管理',
					index: '4',
					icon: require('@/assets/images/person/biographical.png'),
					list: [
						// {
						// 	title: '对我感兴趣',
						// 	index: '4-1',
						// 	path: 'interest'
						// },
						// {
						// 	title: '在线咨询',
						// 	index: '4-4',
						// 	path: 'message'
						// },
						{
							title: '收到的简历',
							index: '4-2',
							path: 'biographical'
						}
						// {
						// 	title: '简历回收站',
						// 	index: '4-3',
						// 	path: 'recycle'
						// }
					]
				},
				// {
				// 	title: '面试管理',
				// 	index: '5',
				// 	icon: require('@/assets/images/person/interview.png'),
				// 	list: [
				// 		{
				// 			title: '面邀记录',
				// 			index: '5-1',
				// 			path: 'interviewRecord'
				// 		}
				// 	]
				// },
				// {
				// 	title: '创业/就业服务',
				// 	index: '6',
				// 	icon: require('@/assets/images/person/Entrepreneurship.png'),
				// 	list: [
				// 		{
				// 			title: '服务列表',
				// 			index: '6-1',
				// 			path: 'serveList'
				// 		}
				// 	]
				// },
				{
					title: '双选会邀请',
					index: '7',
					icon: require('@/assets/images/person/Entrepreneurship.png'),
					list: [
						{
							title: '双选会邀请',
							index: '7-1',
							path: 'jobChoiceMeeting'
						}
					]
				},
				{
					title: '职业技能培训',
					index: '8',
					icon: require('@/assets/images/person/Entrepreneurship.png'),
					list: [
						{
							title: '最近直播',
							index: '8-1',
							path: 'liveList'
						}
					]
				}
			]
		};
	},
	watch: {
		'$route.query': {
			deep: true,
			handler(newVal) {
				this.init();
			}
		}
	},
	created() {
		this.init();
	},
	methods: {
		/**初始化界面*/
		init() {
			let type = this.$route.query.type || 'enterInfo';
			let arr = [];
			this.menus.forEach(item => {
				if (item.list && item.list.length > 0) {
					arr.push(...item.list);
				} else {
					arr.push(item);
				}
			});
			const obj = arr.find(item => {
				return item.path === type;
			});
			if (obj) {
				this.activeMenu = obj.index;
				this.componentName = obj.path;
				this.addToBreadCrumb(obj);
			}
		},
		/**跳转*/
		toRoute(item) {
			this.$router.push(`/independentPersonal/enterprise?type=${item.path}`);
			this.addToBreadCrumb(item);
		},
		/**添加面包屑*/
		addToBreadCrumb(item) {
			let obj = {
				path: `/independentPersonal/enterprise?type=${item.path}`,
				name: 'Enterprise',
				meta: {
					title: item.title,
					type: 'enterprise'
				}
			};
			this.$addToBreadCrumb(obj);
		},
		/**选中菜单项*/
		select(index, path) {}
	}
};
</script>
<style lang="scss" scoped>
.management {
	width: 100%;
	margin: 0 auto;
	display: flex;
	min-height: calc(100vh - 270px);
	display: flex;
	background: #fcfcfc;
	&-left {
		width: 220px;
		flex-shrink: 0;
		background: #ffffff;
		box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.2);
		.img {
			width: 20px;
			height: 20px;
			margin-right: 5px;
		}
	}
	&-right {
		flex: 1;
		overflow: hidden;
		.subBreadcrumb {
			box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.2);
		}
	}
}
::v-deep .el-menu-item {
	padding-left: 45px !important;
}
::v-deep .el-menu-item.is-active {
	background: #fcfcfc;
	position: relative;
	padding-left: 45px !important;
	&::before {
		content: '';
		display: inline-block;
		position: absolute;
		left: 26px;
		top: calc(50% - 5.5px);
		width: 12px;
		height: 12px;
		border-radius: 50%;
		background: #3274e0;
	}
}
::v-deep .el-menu-item-group__title {
	display: none;
}
</style>
