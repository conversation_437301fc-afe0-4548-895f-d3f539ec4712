import { getToken } from '@/utils/auth';

const getDefaultState = () => {
	return {
		token: getToken(),
		name: '',
		avatar: '',
		userInfo: {}, // 用户信息
		identity: [], // 用户所有的身份信息
		roles: [] // 用户所有的身份权限
	};
};

const state = getDefaultState();

const mutations = {
	RESET_STATE: state => {
		Object.assign(state, getDefaultState());
	},
	SET_TOKEN: (state, token) => {
		state.token = token;
	},
	SET_NAME: (state, name) => {
		state.name = name;
	},
	SET_AVATAR: (state, avatar) => {
		state.avatar = avatar;
	},
	/**存储用户信息*/
	SET_USER_INFO(state, info) {
		state.userInfo = info;
		localStorage.setItem('userInfo', JSON.stringify(info));
	},
	/**存储用户权限*/
	SET_ROLES(state, roles) {
		state.roles = roles;
		localStorage.setItem('roles', JSON.stringify(roles));
	},
	/**存储用户身份*/
	SET_IDENTITY(state, identity) {
		state.identity = identity;
		localStorage.setItem('identity', JSON.stringify(identity));
	}
};

const actions = {};

export default {
	namespaced: true,
	state,
	mutations,
	actions
};
