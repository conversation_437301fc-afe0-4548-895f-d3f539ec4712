import Vue from 'vue';
import Router from 'vue-router';
import Layout from '@/layout';
/**子系统的路由*/
import shopRouter from '@/router/modules/shop';
import skillTreasureRouter from '@/router/modules/skill-treasure';
import onlineStudyRouter from '@/router/modules/online-study';
import informationRouter from '@/router/modules/information';
import alumniAssociation from '@/router/modules/alumni-association';
import bottomInformation from '@/router/modules/bottom-information';
import employmentRouter from '@/router/modules/employment';
import personalRouter from '@/router/modules/personal';

Vue.use(Router);
/**
 * @DESC
 * 外层存放整个系统的路由，
 * 其余子系统的路由单独存放，文件形式引入
 * */
export const constantRoutes = [
	{
		path: '/login',
		name: 'Login',
		component: () => import('@/views/login/index'),
		meta: { title: '登录' }
	},
	{
		path: '/loginServer',
		name: 'LoginServer',
		component: () => import('@/views/login/index'),
		meta: { title: '登录' }
	},
	{
		path: '/loginSystem',
		name: 'LoginSystem',
		component: () => import('@/views/loginSystem/index'),
		meta: { title: '登录' }
	},
	{
		path: '/loginReset',
		name: 'LoginReset',
		component: () => import('@/views/login/index'),
		meta: { title: '忘记密码' }
	},
	{
		path: '/register',
		name: 'Register',
		component: () => import('@/views/register/index'),
		meta: { title: '注册' }
	},
	{
		path: '/personal',
		name: 'Personal',
		component: () => import('@/views/personal/index'),
		meta: { title: '个人中心', needLogin: true }
	},
	{
		path: '/homepage',
		name: 'home',
		component: () => import('@/views/home/<USER>'),
		meta: { title: '首页' }
	},
	{
		path: '/download-app',
		name: 'download-app',
		component: () => import('@/views/download-app.vue'),
		meta: { title: '首页' }
	},
	{
		path: '/',
		component: Layout,
		redirect: '/home',
		children: [
			{
				path: 'home',
				name: 'Home',
				// component: () => import('@/views/home/<USER>'),
				component: () => import('@/views/home/<USER>'),
				meta: { title: '首页', noShowBreadCrumb: true }
			},
			// {
			// 	path: 'alumniAssociation',
			// 	name: 'AlumniAssociation',
			// 	component: () => import('@/views/alumni-association'),
			// 	meta: { title: '校友会', noShowBreadCrumb: true }
			// },
			{
				path: 'VEhall',
				name: 'VEhall',
				component: () => import('@/views/vocational-education/index'),
				meta: { title: '职教大厅', noShowBreadCrumb: true }
			},
			...shopRouter.subViews, // 技能商城
			...skillTreasureRouter.subViews, // 技能宝库
			...onlineStudyRouter.subViews, // 在线学习
			...informationRouter.subViews, // 教育资讯
			...alumniAssociation.subViews, //校友会
			...employmentRouter.subViews, // 就业创业
			...bottomInformation // 底部信息
		]
	},
	...shopRouter.views, // 技能商城
	...personalRouter.views, // 单独打开的个人中心
	{
		path: '/403',
		component: () => import('@/views/403')
	},
	// 404 页面必须放在末尾
	{
		path: '/404',
		component: () => import('@/views/404')
	},
	{ path: '*', redirect: '/404', hidden: true }
];

const createRouter = () =>
	new Router({
		mode: 'hash',
		scrollBehavior: () => ({ y: 0 }),
		routes: constantRoutes
	});

const router = createRouter();
// 详情文档 参考: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
	const newRouter = createRouter();
	router.matcher = newRouter.matcher;
}

export default router;
