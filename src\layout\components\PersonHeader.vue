<template>
	<div class="navbar">
		<div class="navbar-con">
			<div class="navbar-left">
				<img src="@/assets/images/home/<USER>" class="navbar-left-icon" @click="goHome" />
				<div class="navbar-left-tabs">
					<div
						v-for="(tab, index) of tabs"
						:key="index"
						:class="current === index ? 'select' : ''"
						class="tab"
						@click="toRoute(tab)"
					>
						{{ tab.title }}
						<div v-show="current === index" class="tab-line"></div>
					</div>
				</div>
			</div>
			<div class="navbar-right">
				<headAvator
					v-if="showPage"
					:own-id="_userinfo.id"
					class="navbar-right-avator"
					@click="handleButton('draw')"
				/>

				<div class="navbar-right-name" @click="handleButton('draw')">
					<div>{{ _userinfo.nickname }}</div>
					<div>{{ _userinfo.phone }}</div>
				</div>
				<div v-for="(item, index) of list" :key="index" class="navbar-right-list">
					<img
						class="img"
						:class="showMore && item.type === 'draw' ? 'rotate' : ''"
						:style="index === 0 ? 'width:12px;height: 12px;' : ''"
						:src="item.img"
						alt=""
						@click="handleButton(item.type)"
					/>
					<div v-if="index !== list.length - 1" class="line"></div>
				</div>
				<HeaderDraw v-model="showMore" @jumpLink="toRoute"></HeaderDraw>
			</div>
			<el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
				<span>确认退出系统吗？</span>
				<span slot="footer" class="dialog-footer">
					<el-button @click="dialogVisible = false">取 消</el-button>
					<el-button type="primary" @click="sure">确 定</el-button>
				</span>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import HeaderDraw from '@/components/header-draw';

export default {
	components: {
		HeaderDraw
	},
	data() {
		return {
			showMore: false,
			dialogVisible: false,
			list: [
				{ img: require('@/assets/images/home/<USER>'), type: 'draw' },
				{ img: require('@/assets/images/home/<USER>') },
				{ img: require('@/assets/images/home/<USER>'), type: 'open' }
			],
			tabs: [
				{
					title: '宝库',
					componentName: 'skillPersonal',
					path: '/personal?type=skillPersonal',
					type: 'personChild'
				},
				{
					title: '商城',
					componentName: 'shopPersonalIndex',
					path: '/personal?type=shopPersonalIndex',
					type: 'personChild'
				},
				{
					title: '就业创业',
					componentName: 'employment',
					path: '/personal?type=employment',
					type: 'personChild'
				},
				{
					title: '在线学习',
					componentName: 'onlineStudyPersonal',
					path: '/personal?type=onlineStudyPersonal',
					type: 'personChild'
				},
				{
					title: '校友会',
					componentName: 'alumniAssociation',
					path: '/personal?type=alumniAssociation',
					type: 'personChild'
				}
			],
			current: '',
			type: '', // 直接定位到什么类型
			personObj: {
				title: '个人信息',
				componentName: 'personHome',
				path: '/personal',
				type: 'personSelf'
			},
			showPage: false,
			subMenu: ''
		};
	},
	computed: {
		...mapState({
			routePath: state => state.app.routePath
		})
	},
	watch: {
		$route: {
			deep: true,
			handler(newVal) {
				// 因为组件keep-alive，所以要判断当前页面是否显示
				if (this.showPage) {
					this.toModule();
				}
			}
		}
	},
	activated() {
		this.showPage = true;
		this.toModule();
	},
	deactivated() {
		this.showPage = false;
	},
	created() {
		// 如果是直接进入这个页面指定模块，就要初始化赋值
		this.toModule();
	},
	methods: {
		...mapMutations('app', ['UPDATE_ROUTE_PATH']),
		/**处理进入页面带参数跳转到指定模块*/
		toModule() {
			this.type = this.$route.query.type || '';
			this.subMenu = this.$route.query.subMenu || '';
			if (this.type) {
				this.current = this.tabs.findIndex(item => {
					return item.componentName === this.type;
				});
				this.$emit('changeName', this.tabs[this.current], this.subMenu); // 父组件改变到对应的模块组件
				this.$addToBreadCrumb(this.tabs[this.current]); // 往路由历史添加信息
			} else {
				this.current = '';
				this.$emit('changeName', this.personObj, this.subMenu); // 父组件改变到对应的模块组件
				this.$addToBreadCrumb(this.personObj); // 往路由历史添加信息
			}
		},
		/**返回首页*/
		goHome() {
			this.$router.push('/home');
		},
		/**选择更多的按钮*/
		toRoute(tab, i) {
			// 如果更新地址，就往路由历史加一个对象
			if (tab.path) {
				this.$router.push(tab.path);
			} else {
				this.$message.warning('开发中，敬请期待...');
			}
		},
		/**退出系统*/
		sure() {
			this.dialogVisible = false;
			this.$loginOut();
			// this.$router.push('/login');
		},
		/**处理导航栏按钮点击*/
		handleButton(type) {
			if (type === 'open') {
				this.dialogVisible = true;
			} else if (type === 'draw') {
				this.showMore = !this.showMore;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.navbar {
	height: 60px;
	background: var(--brand-6, #0076e8);
	display: flex;
	align-items: center;
	justify-content: center;
	&-con {
		width: 1200px;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	&-left {
		display: flex;
		align-items: center;
		&-icon {
			// width: 93px;
			height: 34px;
			margin-right: 58px;
			cursor: pointer;
		}
		&-tabs {
			display: flex;
			align-items: center;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			.tab {
				margin-right: 58px;
				position: relative;
				cursor: pointer;
				&-line {
					width: 20px;
					height: 3px;
					background: #eaff00;
					border-radius: 2px;
					position: absolute;
					bottom: -8px;
					left: calc(50% - 10px);
				}
			}
			.select {
				color: #eaff00;
			}
		}
	}
	&-right {
		display: flex;
		align-items: center;
		position: relative;
		&-avator {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			margin-right: 10px;
			cursor: pointer;
			background: transparent;
		}
		&-name {
			margin-right: 12px;
			font-size: 12px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #ffffff;
			line-height: 20px;
			cursor: pointer;
		}
		&-list {
			display: flex;
			align-items: center;
			.rotate {
				transform: rotateZ(180deg);
				transition: all 0.5s;
			}
			.line {
				width: 1px;
				height: 16px;
				background: #000000;
				opacity: 0.2;
				margin: 0 22px;
			}
			.img {
				width: 21px;
				height: 21px;
				cursor: pointer;
			}
		}
	}
}
</style>
