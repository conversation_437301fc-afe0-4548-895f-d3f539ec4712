<template>
	<div class="Recommendation">
		<Title :title="'为您推荐'" />
		<div class="list">
			<!-- left -->
			<div class="left-item">
				<img src="@/assets/img/Group_1199.png" alt="" />
				<div class="conten">
					<div class="title">农副产品</div>
					<div class="dec">精选产品满减活动</div>
					<a href="">
						<div class="link">查看更多</div>
					</a>
				</div>
			</div>
			<!-- right -->
			<div class="right">
				<div class="right-item">
					<img class="img" src="@/assets/img/Group_1199.png" alt="" />
					<div class="title">潘婷氨基酸乳液修护洗发水1KG强韧秀发韧秀发韧秀发韧秀发深层滋...</div>
					<div class="price">
						<span>¥</span>
						92.98
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import Title from './Title.vue';
export default {
	name: 'Recommendation',
	components: { Title }
};
</script>

<style lang="scss" scoped>
.Recommendation {
	.list {
		padding-bottom: 48px;
		display: flex;
		.left-item {
			width: 198px;
			height: 507px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			position: relative;
			.conten {
				position: absolute;
				top: 0;
				left: 0;
				width: 198px;
				height: 507px;
				padding: 25px 20px;
				.title {
					font-size: 30px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 44px;
				}
				.dec {
					font-size: 18px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 32px;
				}
				.link {
					margin-top: 13px;
					width: 96px;
					height: 34px;
					border-radius: 6px 6px 6px 6px;
					opacity: 1;
					border: 1px solid #ffffff;
					text-align: center;
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					line-height: 31px;
				}
			}
		}
		.right {
			display: flex;
			flex-wrap: wrap;
			height: 509px;
			width: 992px;
			background: #ffffff;
			.right-item {
				width: 200px;
				height: 254px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				border: 1px solid #eeeeee;
				padding: 11px 20px 5px;
				.img {
					width: 160px;
					height: 160px;
				}
				.title {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					overflow: hidden; //超出的文本隐藏

					text-overflow: ellipsis; //溢出用省略号显示

					display: -webkit-box;

					-webkit-line-clamp: 2; // 超出多少行

					-webkit-box-orient: vertical;
				}
				.price {
					font-size: 16px;
					font-family: Rany-Medium, Rany;
					font-weight: 500;
					color: #ca3f3b;
					line-height: 20px;
					span {
						font-size: 12px;
					}
				}
			}
		}
	}
}
</style>
