<template>
	<div class="home">
		<div class="top-SwiperBox">
			<el-carousel height="550px" trigger="click" :interval="30000">
				<el-carousel-item v-for="(item, index) in banner" :key="index">
					<div class="banner-box">
						<video
							v-if="item.video"
							:src="item.video"
							class="banner-video"
							loop
							muted
							autoplay
						></video>
						<img v-else class="banner-img" :src="$judgeFile(item.url)" alt="" />
					</div>
				</el-carousel-item>
			</el-carousel>
			<div class="SearchBox">
				<div class="downList flexCenter" @click="toggleList">
					<div class="textBox">{{ searchType }}</div>
					<div class="donwIcon"></div>
					<div class="visableList" :class="{ show: isVisible }">
						<div
							v-for="(item, index) in searchTypeList"
							:key="index"
							class="textBox"
							@click="selectSearchType(item)"
						>
							{{ item }}
						</div>
					</div>
				</div>
				<input type="text" class="searchInput" placeholder="请输入关键字" />
				<div class="searchBtn flexCenter">
					<img src="@/assets/images/homeVersion4/search.png" alt="" />
				</div>
			</div>
		</div>

		<!-- 技能宝库 -->
		<div class="mainModule Skill-treasury">
			<div class="TitleBox flexColumn">
				<h1>技能宝库</h1>
				<p>技能分享，越来越多的同学与宝库一起成长</p>
			</div>
			<div class="Skill-treasury-body">
				<div class="ClassfyTab">
					<div
						v-for="(item, index) in skill.tabs"
						:key="index"
						class="items"
						:class="item.id === skill.tabActive ? 'select' : ''"
						@click="TabClick(item)"
					>
						{{ item.name }}
					</div>
				</div>

				<div class="MainContent">
					<div
						v-for="(item, index) in skill.list"
						:key="index"
						class="MainItem"
						@click="clickCard('skill', item)"
					>
						<div class="imgContainer">
							<img :src="$judgeFile(item.coverImg)" alt="" />
						</div>
						<div class="content">
							<div class="TitleBox">
								{{ item.title }}
							</div>
							<div class="infoBox">
								<div class="UserInfo flexCenter">
									<img :src="$judgeFile(item.coverImg)" alt="" class="userIcon" />
									<div class="UserName">{{ item.name }}</div>
								</div>
								<div class="heartInfo">
									<img
										v-if="item.isDiggs"
										src="@/assets/images/homeVersion4/heart-active.png"
										alt=""
									/>
									<img v-else src="@/assets/images/homeVersion4/heart.png" class="nums-icon" />
									{{ item.diggs }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="BtnBox">
					<div class="NavsItem flexCenter" @click="jumpPage('/skill-treasure')">
						<img src="@/assets/images/homeVersion4/find.png" alt="" />
						发现更多
					</div>
					<!-- <div class="NavsItem flexCenter">
						<img src="@/assets/images/homeVersion4/release.png" alt="">
						发布作品
					</div> -->
				</div>
			</div>
		</div>

		<!-- 技能商城
		<div class="mainModule Skill-shop">
			<div class="TitleBox flexColumn">
				<h1>技能商城</h1>
				<p>优秀品牌营销，助力好产品生长</p>
			</div>


			<div class="Skill-shop-body flexCenter">
				<div class="type-wrapper">
					<div class="type-title">分类</div>
					<div class="type-item bg1" @click="jumpPage('/shopHome')">特产</div>
					<div class="type-item bg2" @click="jumpPage('/shopHome')">非遗</div>
					<div class="type-item bg3" @click="jumpPage('/shopHome')">文创</div>
					<div class="type-item bg4" @click="jumpPage('/online-study')">技术技能培训</div>
					<div class="type-item bg4" @click="jumpPage('/onlineLive')">在线直播</div>
				</div>

			</div>
		</div> -->

		<!-- 就业创业 -->
		<div class="mainModule Employment">
			<div class="TitleBox flexColumn">
				<h1>技能商城</h1>
				<p>优秀品牌营销，助力好产品生长</p>
			</div>

			<div class="Employment-body">
				<div class="leftContent">
					<div class="TopNav flexCenter">创业服务</div>
					<div
						v-for="(item, index) in servicesLeftList"
						:key="index"
						class="NavItem"
						:class="index === EmploymentNavSelect ? 'NavItem-select' : ''"
						@click="SelectEmploymentNav(index, item)"
					>
						<!-- <img  :src="getImgUrl(item.icon)" alt="" /> -->
						<img
							class="iconImg"
							:src="require(`@/assets/images/homeVersion4/${item.icon}`)"
							alt=""
						/>

						<p>{{ item.name }}</p>
						<img src="@/assets/images/homeVersion4/arrow.png" alt="" class="Arrow" />
						<div class="BgImg"></div>
					</div>
				</div>
				<div
					v-for="(item, index) in shop.list"
					:key="index"
					class="Mainitem"
					@click="clickCard('shop', item)"
				>
					<div class="imgContainer">
						<img :src="$judgeFile(item.coverUrl)" alt="" />
					</div>
					<div class="content">
						<p class="titel">{{ item.productName }}</p>
						<div class="PriceBox">
							<span v-if="item.sellPrice" class="Pic">{{ item.sellPrice.toFixed(2) }}</span>
							<span v-else class="Pic">0.00</span>
							<!-- <span class="DiscountPic">￥34.90</span> -->
						</div>
						<div class="shopName">{{ item.shopName }}</div>
					</div>
				</div>
			</div>

			<div class="moreBtn flexCenter" @click="jumpPage('/shopHome')">more</div>
		</div>

		<!-- 热招职位 -->
		<div class="mainModule Recruit">
			<div class="TitleBox flexCenter">
				<img src="@/assets/images/homeVersion4/hot.png" alt="" />
				<h1>热招职位</h1>
				<img src="@/assets/images/homeVersion4/hot.png" alt="" />
			</div>
			<div class="Recruit-body">
				<div class="TabBody">
					<div class="TabFlexBox">
						<div
							v-for="(item, index) in RecruitTabList"
							:key="index"
							class="TabItem"
							:class="RecruitTabSelect === index ? 'TabItemSelect' : ''"
							@click="SelectTab(index, item)"
						>
							{{ item.shortName }}
						</div>
					</div>
					<div class="moreInfo" @click="jumpPage('/job-list')">more ></div>
				</div>
				<div class="contentBody">
					<div class="MianPic">
						<img src="@/assets/images/homeVersion4/rzzw.png" alt="" />
					</div>
					<div class="contentList">
						<div
							v-for="(item, index) in jobList"
							:key="index"
							class="RecruitItems"
							@click="jumpPage(`/job-detail?id=${item.id}`)"
						>
							<div class="introduce">
								<div class="JobTitle">
									<div class="jobPost">{{ item.name }}</div>
									<div class="salary">{{ item.minMoney || 0 }}-{{ item.maxMoney || 0 }}k</div>
								</div>
								<div class="jobInfo">
									<!-- <div class="requirementBox">
										<div class="requirementItem">经验不限</div>
										<div class="requirementItem">学历不行</div>
									</div> -->
									<div class="addres">
										<img src="@/assets/images/homeVersion4/addres.png" alt="" />
										{{ item.areaName }}
									</div>
								</div>
								<div class="jobTag">
									<div
										v-for="(labelItem, labelIndex) in item.tags"
										:key="labelIndex"
										class="TagItem"
									>
										{{ labelItem }}
									</div>
								</div>
							</div>
							<div class="companyBox">
								<img v-if="item.enterprise.logo" :src="getImgUrl(item.enterprise.logo)" alt="" />
								<img
									v-else
									:src="require('@/assets/employment-images/com-defalut-img.jpg')"
									alt=""
								/>
								<div class="textBox">
									<div class="name">{{ item.enterprise.corpName }}</div>
									<div class="scale">
										{{ item.enterprise.peopleNumCode | enterpriseTypeStr(peopleNumCodeList) }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 职教视界 -->
		<div class="mainModule Journalism">
			<div class="Journalism-body">
				<div class="MainImg">
					<img src="@/assets/images/homeVersion4/xyzx.png" alt="" />
				</div>
				<div class="JournalismNavBox">
					<div class="MainBox">
						<div class="title">校园资讯</div>

						<div class="TabFlexBox">
							<div
								v-for="(item, index) in NewsType"
								:key="index"
								class="TabItem"
								:class="JournalismSelect === index ? 'TabItemSelect' : ''"
								@click="SelectJournalismTab(index, item)"
							>
								{{ item.name }}
							</div>
						</div>
					</div>
					<div class="moreInfo" @click="jumpPage('/information-list?code=educationView')">
						more >
					</div>
				</div>

				<div class="NewsList">
					<div
						v-for="(item, index) in News.list"
						:key="index"
						class="NewsItem"
						@click="clickCard('information', item)"
					>
						<div class="imgContainer">
							<img :src="item.coverImg" alt="" />
						</div>
						<div class="infoBox">
							<h1 class="title">{{ item.title }}</h1>
							<p class="infos">{{ item.abstract }}</p>
							<p class="time">{{ item.publishTime }}</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<SuspendedWindow></SuspendedWindow>
		<!-- 强制修改密码 -->
		<EditProfile ref="refEditProfile" />
	</div>
</template>

<script>
import SuspendedWindow from './components/SuspendedWindow.vue';
import EditProfile from '@/components/EditProfile/index.vue';
import { alumniUrl, baseUrl } from '@/config';
import { getDictionaryByCode } from '@/utils';

export default {
	name: 'Home4',
	components: {
		SuspendedWindow,
		EditProfile
	},
	filters: {
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value && value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		}
	},
	data() {
		return {
			banner: [],
			isVisible: false,
			searchType: '',
			searchTypeList: ['找技能', '找商品', '找工作', '找项目', '找课程'],

			// 技能宝库
			skill: {
				loading: false,
				list: [],
				tabs: [],
				tabActive: 0,
				page: {
					num: 1, // 页数
					size: 4,
					total: 0
				}
			},

			// 技能商城
			shop: {
				// loading: true,
				list: [1, 1, 1, 1],
				page: {
					num: 1, // 页数
					size: 4,
					total: 0
				}
			},

			EmploymentNavSelect: 0,
			servicesLeftList: [
				{ name: '特产', icon: 'tc.png', url: '/shopHome' },
				{ name: '非遗', icon: 'fy.png', url: '/shopHome' },
				{ name: '文创', icon: 'wc.png', url: '/shopHome' },
				{ name: '技能技术培训', icon: 'jnpx.png', url: '/online-study' },
				{ name: '在线直播', icon: 'zb.png', url: '/onlineLive' }
			], // 创业服务

			RecruitTabSelect: 0,
			RecruitTabActive: '',
			RecruitTabList: [],
			peopleNumCodeList: [],
			jobList: [],

			newsList: [2, 2, 3, 4],
			JournalismSelect: 0,
			/*  
				校园资讯
				*/
			NewsType: [
				{
					name: '最新动态',
					key: 'information',
					type: 'dynamicNews'
				},
				{
					name: '职教视野',
					key: 'vocational',
					type: 'educationView'
				}
			],

			News: {
				//最新动态
				loading: false,
				list: [],
				type: '',
				page: {
					num: 1, // 页数
					size: 4,
					total: 0
				}
			}
		};
	},
	computed: {},
	created() {
		this.getAdvert();
		this.selectSearchType('找技能');
		this.getSkillTabs(); // ;获取技能宝库tab
		this.getShopList(); //获取技能商城列表
		this.findSysCode(); //岗位类型 //人数规模 //行业
		this.SelectJournalismTab(0, this.NewsType[0]); //初始化校园资讯
	},
	methods: {
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode() {
			// let param = {
			// 	sysAppCode: code
			// };
			// this.$api.employment_api.findSysCode(param).then(res => {
			// 	this[tabList] = this[tabList].concat(res?.results || []);
			// });
			const dicts = await getDictionaryByCode([
				'post_job_type',
				'post_company_scale',
				'post_industry_area'
			]);
			// 岗位类型
			this.RecruitTabList = dicts.post_job_type || [];
			this.SelectTab(0, this.RecruitTabList[0]);
			// 人数规模
			this.peopleNumCodeList = dicts.post_company_scale || [];
			// this.corpIndustryList = dicts.post_industry_area || [];
		},

		newsweekImg(url) {
			return `${baseUrl}/ybzyfile${url}`;
		},

		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},

		jumpPage(url) {
			this.$router.push(url);
		},

		toggleList() {
			this.isVisible = !this.isVisible;
		},

		// 选择搜索类型
		selectSearchType(content) {
			this.searchType = content;
		},

		// 选择创业服务类型
		SelectEmploymentNav(index, item) {
			this.EmploymentNavSelect = index;
			this.$router.push(item.url);
		},

		// 选择岗位类型
		SelectTab(index, item) {
			this.RecruitTabSelect = index;
			this.RecruitTabActive = item.cciValue;
			this.getJobCoPostList();
		},

		// 选择职教视界类型
		SelectJournalismTab(index, item) {
			this.JournalismSelect = index;
			this.News.num = 0;
			this.News.type = item.type;
			this.getNews();
		},

		/**获取广告*/
		async getAdvert() {
			let { result } = await this.$api.shop_api.getAdvertsByCode({
				siteId: this.getSiteId(),
				sysCode: 'pc_index_top_top'
			});
			let arr = result?.adData || [];
			this.banner = arr && arr.length && arr;
			let ele = {
				url: '',
				video: 'http://yszj.ybzy.cn/project-ybzy/picture/video/lv_0_20241201153730.mp4'
			};
			this.banner.unshift(ele);
		},

		/**
		 * @descrtiption 岗位分页接口
		 * */
		getJobCoPostList() {
			let param = {
				pageNum: 1,
				pageSize: 4,
				postType: this.RecruitTabActive,
				auditStatus: 1 //审核状态(0.待审核、1.审核通过、2.驳回)
			};
			this.$api.employment_api.jobCoPostList(param).then(res => {
				this.jobList = res?.results?.records || [];
			});
		},

		//获取技能商城列表——————————————————————————————
		async getShopList() {
			// this.shop.loading = true;
			try {
				const { num, size } = this.shop.page;
				let { state, result, totalNum } = await this.$api.shop_api.getAllProduct({
					offset: (num - 1) * size, //offset是从第几条开始
					psize: size,
					noType: 1,
					type1: 1,
					siteId: this.getSiteId()
					// categoryId:
				});
				if (state) {
					this.shop.list = result;
					this.shop.page.total = totalNum;
				}
			} catch (error) {
				console.error('>>>error', error);
				this.shop.list = [];
			} finally {
				// this.shop.loading = false;
			}
		},

		// 选择商品
		clickCard(cardName, cardData) {
			// console.log('>>>cardData', cardData);
			let id = cardData?.id;
			let productId = cardData?.productId;
			const stack = {
				skill: () => {
					let url = `/skill-treasure-details?id=${id}&type=user`;
					this.$router.push(url);
				},
				shop: () => {
					this.$router.push({
						path: '/shopDetail',
						query: {
							id: productId || id,
							type: cardData.regCode
						}
					});
				},
				integral: () => {
					this.$router.push({
						path: '/shopDetail',
						query: {
							id: cardData.itemId || '',
							type: cardData.regCode,
							goodsType: 'point'
						}
					});
				},
				recruit: () => {
					this.$router.push({
						path: '/job-detail',
						query: {
							id: cardData.id || ''
						}
					});
				},
				study: () => {
					this.$router.push({
						path: '/freecourses',
						query: {
							id: cardData.id || ''
						}
					});
				},
				information: () => {
					this.$router.push({
						path: '/information-detail',
						query: {
							id: cardData.id || '',
							code: cardData.nodeCode || ''
						}
					});
				}
			};
			stack[cardName]();
		},

		// 推荐技能tabsd点击
		TabClick(data) {
			this.skill.page.num = 1;
			this.skill.tabActive = data.id;
			this.getSkillList();
		},

		//获取推荐技能tabs分类
		async getSkillTabs() {
			try {
				let { code, results } = await this.$api.treasure_api.memberStoryTypeList({
					rentId: this.getSiteId() // 租户id
				});
				if (code == 200) {
					this.skill.tabs = [...results];

					this.skill.tabActive = results[2]?.id;
				}
				this.getSkillList();
			} catch (error) {
				this.typeList = [];
				console.error('>>>', error);
			}
		},

		//获取推荐技能列表数据
		async getSkillList() {
			this.skill.loading = true;
			try {
				const { page, tabActive } = this.skill;
				let { code, results } = await this.$api.treasure_api.memberStoryList({
					pageNum: page.num, // 页数
					pageSize: page.size, // 页面大小
					typeId: tabActive, //类型id
					rentId: this.getSiteId() // 租户id
				});
				if (code == 200) {
					let list = results.records;
					this.skill.list = list.map(item => {
						return {
							...item,
							name: item.memberName,
							time: item.createTime.substring(0, 10)
						};
					});
				}
			} catch (error) {
				this.skill.list = [];
				console.error('>>>', error);
			} finally {
				this.skill.loading = false;
			}
		},

		//获取最新动态列表——————————————————————————————
		async getNews() {
			try {
				const { num, size } = this.News.page;
				//  rCode, results, msg
				let { rCode, results } = await this.$api.information_api.paging({
					pageNum: num,
					pageSize: size,
					nodeCode: this.News.type,
					tenantId: this.$tenantId
				});
				if (rCode == 0) {
					// this.News.list = results.records;
					this.News.list = results.records.map(item => {
						return {
							...item,
							coverImg: this.newsweekImg(item.coverImg)
						};
					});
				} else {
					// this.$message.error(msg);
					this.News.list = [];
				}
			} catch (error) {
				this.News.list = [];
				console.error('>>>error', error);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.flexCenter {
	display: flex;
	align-items: center;
	justify-content: center;
}

.flexColumn {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.home {
	width: 100%;
	font-family: Microsoft YaHei;

	.top-SwiperBox {
		position: relative;

		.banner-img {
			width: 100%;
			height: 550px;
			object-fit: cover;
		}
		.banner-video {
			width: 100%;
			height: 550px;
			object-fit: cover;
		}

		.SearchBox {
			position: absolute;
			z-index: 99;
			bottom: 30px;
			width: 500px;
			height: 48px;
			left: 50%;
			display: flex;
			transform: translateX(-50%);
			background: #fff;
			border-radius: 20px;

			.downList {
				width: 106px;
				height: 100%;
				cursor: pointer;
				border-right: 1px solid #e4eaf0;
				position: relative;
				font-size: 16px;
				color: #000000;

				.donwIcon {
					margin-left: 10px;
					border-left: 5px solid transparent;
					border-right: 5px solid transparent;
					border-top: 5px solid #000;
					border-radius: 3px;
				}

				.visableList {
					position: absolute;
					top: 100%;
					width: 100%;
					background: #fff;
					display: flex;
					border-radius: 30px;

					flex-direction: column;
					justify-content: center;
					overflow: hidden;
					max-height: 0;
					transition: max-height 0.3s ease-in-out;

					&.show {
						border: 1px solid #e4eaf0;
						border-top: 1px solid #e4eaf0;
						max-height: 500px;
					}

					.textBox {
						text-align: center;
						padding: 10px 0;
						border-bottom: 1px solid #e4eaf0;
					}
				}
			}

			.searchInput {
				padding-left: 10px;
				flex: 1;
				height: 100%;
				border: none !important;
			}

			.searchBtn {
				width: 72px;
				height: 100%;
				background: #0175e8;
				border-top-right-radius: 20px;
				border-bottom-right-radius: 20px;

				cursor: pointer;

				img {
					width: 25px;
					height: 25px;
				}
			}
		}
	}

	.mainModule {
		padding: 50px 0;

		.TitleBox {
			h1 {
				font-size: 24px;
				color: #000000;
				margin-bottom: 10px;
				text-align: center;
			}

			p {
				font-size: 16px;
				text-align: center;
				color: #a0a4ab;
				margin-bottom: 20px;
			}
		}
	}

	.Skill-treasury {
		.Skill-treasury-body {
			display: flex;
			justify-content: center;

			.ClassfyTab {
				margin-right: 15px;

				.items {
					padding: 5px 30px;
					border-radius: 30px;
					cursor: pointer;
					border: 1px solid #e3e2e2;
					margin-bottom: 10px;
				}

				.select {
					color: #fff;
					background: #0072e1;
				}
			}

			.BtnBox {
				width: 180px;

				.NavsItem {
					cursor: pointer;

					height: 48px;
					margin-bottom: 20px;
					border-radius: 30px;
					border: 1px solid #e3e2e2;
					color: #000000;
					font-weight: bold;

					img {
						width: 24px;
						height: 24px;
						margin-right: 8px;
					}
				}
			}

			.MainContent {
				display: flex;
				margin-right: 35px;

				.MainItem {
					width: 216px;
					margin-right: 30px;
					border-radius: 8px;
					overflow: hidden;
					cursor: pointer;

					.imgContainer {
						height: 288px;

						img {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}
					}

					.content {
						margin-top: 10px;
						padding: 0 8px;

						.TitleBox {
							font-size: 15px;
							height: 40px;
							color: #000000;
							text-align: left;
							overflow: hidden;
							font-size: 15px;
							color: #000000;
							font-weight: bold;
							margin-bottom: 10px;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
							text-overflow: ellipsis;
						}

						.infoBox {
							margin-top: 12px;
							display: flex;
							justify-content: space-between;

							.UserInfo {
								.userIcon {
									width: 24px;
									height: 24px;
									background: yellow;
									border-radius: 50%;
									margin-right: 8px;
								}

								.UserName {
									font-size: 12px;
									color: #64666d;
								}
							}

							.heartInfo {
								display: flex;
								align-items: center;

								img {
									width: 14px;
									height: 14px;
									margin-right: 3px;
								}

								font-size: 12px;
								color: #a0a4ab;
							}
						}
					}

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	.Skill-shop {
		background: #eef4f9;

		.Skill-shop-body {
			.type-wrapper {
				flex: 0 0 220px;
				padding: 20px 15px;
				background-color: #fff;
				margin-right: 40px;

				.type-title {
					font-size: 20px;
					text-align: center;
					margin-bottom: 20px;
					background: url(./../../assets/shop-images/title-icon.png) no-repeat left 15px center,
						url(./../../assets/shop-images/title-icon.png) no-repeat right 15px center;
					background-size: 20px;
				}

				.type-item {
					font-size: 18px;
					height: 70px;
					line-height: 70px;
					margin-bottom: 15px;
					padding-left: 25px;
					font-weight: bold;
					cursor: pointer;

					&:hover {
						opacity: 0.9;
					}
				}

				.bg1 {
					// background-color: #fdf1f1;
					background: #fdf1f1 url(./../../assets/shop-images/culture.png) no-repeat right 15px
						center;
					background-size: 50px auto;
				}

				.bg2 {
					// background-color: #fffcdf;
					background: #fffcdf url(./../../assets/shop-images/fy.png) no-repeat right 15px center;
					background-size: 50px auto;
				}

				.bg3 {
					// background-color: #e0f2f8;
					background: #e0f2f8 url(./../../assets/shop-images/study.png) no-repeat right 15px center;
					background-size: 50px auto;
				}

				.bg4 {
					// background-color: #ffefde;
					background: #ffefde url(./../../assets/shop-images/scenic.png) no-repeat right 15px center;
					background-size: 50px auto;
				}
			}
		}
	}

	.Employment {
		height: 564px;
		background: url('./../../assets/images/homeVersion4/BG1.png');

		.TitleBox {
			h1 {
				color: white;
			}
		}

		.Employment-body {
			display: flex;
			justify-content: center;

			.leftContent {
				width: 220px;
				background: #36486d;
				margin-right: 110px;

				.TopNav {
					height: 48px;
					font-weight: bold;
					font-size: 18px;
					color: #ffffff;
					margin-bottom: 8px;
				}

				.NavItem {
					height: 42px;
					font-size: 15px;
					color: #ffffff;
					display: flex;
					align-items: center;
					cursor: pointer;
					transition: width 0.3s ease;
					width: 220px;
					position: relative;
					overflow: hidden;
					/* 隐藏超出部分 */

					.BgImg {
						position: absolute;
						top: 0;
						left: 0;
						width: 0;
						height: 100%;
						transition: width 0.3s ease;
						background: url('./../../assets/images/homeVersion4/jycyItme.png') no-repeat center;
						background-size: cover;
						z-index: 1;
					}

					.iconImg {
						width: 22px;
						height: 22px;
						margin: 0 20px;
						z-index: 2;
						position: relative;
					}

					p {
						position: relative;
						z-index: 2;
					}

					.Arrow {
						position: absolute;
						right: 20px;
						width: 20px;
						height: 20px;
						opacity: 0;
						z-index: 2;
						transition: opacity 0.3s ease;
					}
				}

				.NavItem:last-child {
					margin-bottom: 8px;
				}

				.NavItem:hover {
					width: 250px;
				}

				.NavItem:hover .Arrow {
					opacity: 1;
				}

				.NavItem:hover .BgImg {
					width: 250px;
				}

				.NavItem-select {
					width: 250px;

					.BgImg {
						width: 250px;
					}

					.Arrow {
						opacity: 1;
					}
				}
			}

			.Mainitem {
				margin: 0 10px;
				background: white;
				border-radius: 8px;
				overflow: hidden;
				width: 285px;

				.imgContainer {
					height: 214px;

					img {
						width: 100%;
						height: 214px;
						object-fit: cover;
					}
				}

				.content {
					padding: 10px 12px;

					p {
						overflow: hidden;
						font-size: 15px;
						color: #000000;
						font-weight: bold;
						height: 40px;
						margin-bottom: 10px;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						text-overflow: ellipsis;
					}

					.PriceBox {
						.Pic {
							font-size: 18px;
							color: #ff5200;
							font-weight: bold;
						}

						.DiscountPic {
							font-size: 14px;
							color: #8b8b8b;
							text-decoration: line-through;
						}
					}

					.shopName {
						margin-top: 10px;
						font-size: 14px;
						color: #a0a4ab;
					}
				}
			}
		}

		.moreBtn {
			margin: 0 auto;
			margin-top: 30px;
			width: 96px;
			height: 38px;
			background: #ffffff;
			border-radius: 4px;
			border: 1px solid #e3e2e2;
			font-size: 18px;
			color: #9f9f9f;
			cursor: pointer;
		}
	}

	.Recruit {
		background: #eef4f9;
		.TitleBox {
			h1 {
				margin-bottom: 0;
				margin: 0 8px;
				font-size: 20px;
				color: #464646;
			}
		}
		.Recruit-body {
			width: 1210px;
			margin: 0 auto;
			.TabBody {
				padding: 30px 0;
				padding-bottom: 10px;
				display: flex;
				justify-content: space-between;

				.TabFlexBox {
					width: 92%;
					display: flex;
					overflow-x: scroll;
					&::-webkit-scrollbar {
						display: none;
					}
					.TabItem {
						flex-shrink: 0;
						padding: 0px 25px;
						font-weight: bold;
						height: 30px;

						cursor: pointer;
						position: relative;
						/* 确保伪元素相对定位 */
						transition: all 0.3s ease;

						/* 添加淡入淡出效果 */
						&::after {
							content: '';
							position: absolute;
							bottom: 0px;
							left: 50%;
							width: 0;
							/* 初始宽度为 0 */
							height: 3px;
							background-color: #0175e8;
							/* 边框颜色 */
							transform: translateX(-50%);
							/* 居中对齐 */
							transition: all 0.3s ease;
							/* 添加淡入淡出效果 */
						}

						&:hover {
							color: #0175e8 !important;
						}

						&:hover::after {
							width: 30%;
							/* 鼠标移入时宽度变为 30% */
						}
					}

					.TabItemSelect {
						color: #0175e8 !important;

						&::after {
							content: '';
							position: absolute;
							bottom: 0px;
							left: 50%;
							width: 30%;
							/* 初始宽度为 0 */
							height: 3px;
							background-color: #0175e8;
							/* 边框颜色 */
							transform: translateX(-50%);
							/* 居中对齐 */
							transition: all 0.3s ease;
							/* 添加淡入淡出效果 */
						}
					}
				}

				.moreInfo {
					font-size: 18px;
					color: #9f9f9f;
					cursor: pointer;
					margin-right: 1%;
				}
			}

			.contentBody {
				display: flex;

				.MianPic {
					width: 296px;
					margin-right: 10px;
					height: 388px;

					img {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}

				.contentList {
					flex: 1;
					display: flex;
					flex-wrap: wrap;

					.RecruitItems {
						margin: 5px 10px;
						border-radius: 15px;
						width: 432px;
						height: 184px;
						background: white;
						cursor: pointer;

						.introduce {
							padding: 12px 20px;
							box-sizing: border-box;

							.JobTitle {
								display: flex;
								justify-content: space-between;
								margin-bottom: 10px;

								.jobPost {
									font-weight: bold;
									font-size: 16px;
									color: #000000;
								}

								.salary {
									font-weight: bold;
									font-size: 16px;
									color: #0076e8;
								}
							}

							.jobInfo {
								display: flex;
								align-items: center;
								justify-content: space-between;
								font-size: 14px;
								color: #64666d;

								.requirementBox {
									display: flex;

									.requirementItem {
										padding: 0 8px;
										border-right: 1px solid #e1e5e9;
									}

									.addres {
										display: flex;
										align-items: center;

										img {
											width: 14px;
											height: 14px;
										}
									}
								}
							}

							.jobTag {
								height: 35px;
								display: flex;
								overflow: hidden;
								align-items: center;

								.TagItem {
									flex-shrink: 0;
									margin: 0 18px 0 0;
									padding: 4px 8px;
									font-size: 12px;
									color: #64666d;
									border-radius: 3px;
									border: 1px solid #e5e5e5;
								}
							}
						}

						.companyBox {
							height: 76px;
							border-radius: 0px 0px 8px 8px;
							background: linear-gradient(
								90deg,
								rgba(51, 153, 252, 0.15) 0%,
								rgba(51, 153, 252, 0) 100%
							);
							display: flex;
							align-items: center;
							padding-left: 20px;

							img {
								width: 48px;
								height: 48px;
								margin-right: 10px;
							}

							.textBox {
								font-size: 14px;

								.name {
									color: #000000;
									margin-bottom: 4px;
								}

								.scale {
									color: #a0a4ab;
								}
							}
						}
					}
				}
			}
		}
	}

	.Journalism {
		display: flex;
		justify-self: center;

		.Journalism-body {
			width: 1200px;
			display: flex;
			position: relative;

			.MainImg {
				width: 220px;
				flex-shrink: 0;
				height: 400px;

				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}

			.JournalismNavBox {
				width: 1180px;
				position: absolute;
				top: 20px;
				left: 20px;
				height: 70px;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.MainBox {
					width: 690px;
					height: 100%;
					background-color: #0072e1;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.title {
						margin-left: 30px;
						font-size: 24px;
						color: #ffffff;
					}

					.TabFlexBox {
						display: flex;

						.TabItem {
							padding: 0px 25px;
							font-weight: bold;
							cursor: pointer;
							position: relative;
							/* 确保伪元素相对定位 */
							transition: all 0.3s ease;
							color: rgba(255, 255, 255, 0.8);

							/* 添加淡入淡出效果 */
							&::after {
								content: '';
								position: absolute;
								bottom: -15px;
								left: 50%;
								width: 0;
								/* 初始宽度为 0 */
								height: 3px;
								background-color: #ffffff;
								/* 边框颜色 */
								transform: translateX(-50%);
								/* 居中对齐 */
								transition: all 0.3s ease;
								/* 添加淡入淡出效果 */
							}

							&:hover {
								color: #ffffff !important;
							}

							&:hover::after {
								width: 30%;
								/* 鼠标移入时宽度变为 30% */
							}
						}

						.TabItemSelect {
							color: #ffffff !important;

							&::after {
								content: '';
								position: absolute;
								bottom: -15px;
								left: 50%;
								width: 30%;
								/* 初始宽度为 0 */
								height: 3px;
								background-color: #ffffff;
								/* 边框颜色 */
								transform: translateX(-50%);
								/* 居中对齐 */
								transition: all 0.3s ease;
								/* 添加淡入淡出效果 */
							}
						}
					}
				}

				.moreInfo {
					font-size: 16px;
					color: #9f9f9f;
					cursor: pointer;
				}
			}

			.NewsList {
				width: 980px;
				box-sizing: border-box;
				padding: 20px 20px 30px 20px;
				margin-top: 90px;
				background: url('./../../assets/images/homeVersion4/zyzxBg.png') no-repeat center;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;

				.NewsItem {
					display: flex;
					height: 120px;
					background: #ffffff;
					margin-bottom: 20px;
					cursor: pointer;

					border-radius: 8px;
					overflow: hidden;

					.imgContainer {
						width: 160px;
						height: 100%;

						img {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}
					}

					.infoBox {
						padding: 10px;
						box-sizing: border-box;
						width: 300px;
						height: 100%;

						.title {
							font-size: 15px;
							color: #000000;
							font-weight: bold;
							height: 40px;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							/* 最多显示两行 */
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.infos {
							margin: 2px 0;
							font-size: 14px;
							height: 40px;
							color: #a0a4ab;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							/* 最多显示两行 */
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.time {
							font-family: ArialMT;
							font-size: 14px;
							color: #64666d;
						}
					}
				}
			}
		}
	}
}

::v-deep .is-active {
	.el-carousel__button {
		width: 22px;
		height: 10px;
		border-radius: 6px;
		background: var(--brand-6, #0076e8);
	}
}

::v-deep .el-carousel__button {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #fff;
}
</style>
