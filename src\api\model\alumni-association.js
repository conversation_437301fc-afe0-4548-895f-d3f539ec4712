// 校友会相关接口
import request from '@/utils/treasure_request';

// 校友认证——————————————————————————————————————————
//获取校友认证状态
export function getUserAuth() {
	return request({
		url: `/ybzy/front/alumnaMember/getInfoByUserId`,
		method: 'get'
	});
}
//校友认证
export function userAuthentication(data) {
	return request({
		url: `/ybzy/front/alumnaMember/submit`,
		method: 'post',
		data
	});
}
//校友通讯录————————————————————————————————————————————>>>>>>
// 查询校友通讯录
export function getAddressBook() {
	return request({
		url: `/ybzy/front/alumnaCommunity/getListByUserId`,
		method: 'get'
	});
}
//校友通讯录————————————————————————————————————————————<<<<<<

//社区————————————————————————————————————————————>>>>>>
// 查询我创建的社区
export function getMySetupCommunity(params) {
	return request({
		url: `/ybzy/front/alumnaCommunity/queryPageByUserId`,
		method: 'get',
		params
	});
}
// 查询我加入的社区
export function getMyJoinCommunity(params) {
	return request({
		url: `/ybzy/front/alumnaCommunity/queryPageByMemberId`,
		method: 'get',
		params
	});
}
// 申请创建社区
export function setUpCommunity(data) {
	return request({
		url: `/ybzy/front/alumnaCommunity/save`,
		method: 'post',
		data
	});
}
// 修改社区
export function updateCommunity(data) {
	return request({
		url: `/ybzy/front/alumnaCommunity/update`,
		method: 'post',
		data
	});
}
// 修改社区-查询详情
export function getCommunityInfo(params) {
	return request({
		url: `/ybzy/front/alumnaCommunity/${params}`,
		method: 'get'
	});
}
//申请创建社区表单 社区标签
export function getLabelList() {
	return request({
		url: `/ybzy/front/alumnaCommunity/getLabelList`,
		method: 'get'
	});
}
//查询社区成员
export function getMember(params) {
	return request({
		url: `/ybzy/front/alumnaMember/listJson`,
		method: 'get',
		params
	});
}
//审核校友加入社区
export function auditMember(data) {
	return request({
		url: `/ybzy/front/alumnaCommunity/audit`,
		method: 'post',
		data
	});
}
//在社区中删除校友
export function delMember(data) {
	return request({
		url: `/ybzy/front/alumnaCommunity/deleteMember`,
		method: 'post',
		data
	});
}
//分页查询社区通知公告
export function getInform(params) {
	return request({
		url: '/ybzy/front/alumnaCommunityNotice/list',
		method: 'get',
		params
	});
}
//新增社区通知公告
export function addInform(data) {
	return request({
		url: '/ybzy/front/alumnaCommunityNotice/save',
		method: 'post',
		data
	});
}
//删除社区通知公告
export function delInform(data) {
	return request({
		url: `/ybzy/front/alumnaCommunityNotice/deleteById/?id=${data}`,
		method: 'post'
	});
}
//分页查询社区相册
export function getPhotos(params) {
	return request({
		url: '/ybzy/front/alumnaCommunityAlbum/list',
		method: 'get',
		params
	});
}
//分页查询社区相册里的照片
export function getPhoto(params) {
	return request({
		url: '/ybzy/front/alumnaCommunityPhoto/getAlbumPhotoPage',
		method: 'get',
		params
	});
}
//查询详情社区相册--修改回显用
export function getPhotoInfo(params) {
	return request({
		url: `/ybzy/front/alumnaCommunityAlbum/${params}`,
		method: 'get'
	});
}
//新增社区相册
export function addPhotos(data) {
	return request({
		url: '/ybzy/front/alumnaCommunityAlbum/save',
		method: 'post',
		data
	});
}
//上传相册图片
export function addPhoto(data) {
	return request({
		url: '/ybzy/front/alumnaCommunityPhoto/uploading',
		method: 'post',
		data
	});
}
//修改社区相册
export function updatePhoto(data) {
	return request({
		url: '/ybzy/front/alumnaCommunityAlbum/update',
		method: 'post',
		data
	});
}

//删除社区相册
export function delPhotos(data) {
	return request({
		url: `/ybzy/front/alumnaCommunityAlbum/deleteById?id=${data}`,
		method: 'post'
	});
}
//删除社区相册图片
export function delPhoto(data) {
	return request({
		url: `/ybzy/front/alumnaCommunityPhoto/deleteById?id=${data}`,
		method: 'post'
	});
}
//邀请成员详情
export function inviteInfo(params) {
	return request({
		url: `/ybzy/front/alumnaCommunity/${params.id}`,
		method: 'get',
		params: { inviter: params.inviter }
	});
}
//是否加入邀请链接
export function joinCommunity(data) {
	return request({
		url: `/ybzy/front/alumnaCommunity/join`,
		method: 'post',
		data
	});
}

//社区————————————————————————————————————————————<<<<<<

// 检查子系统是否登录
export function loginCheck() {
	return request({
		url: '/ybzy/platauth/front/loginCheck',
		method: 'get'
	});
}
//获取单点登录地址
export function getSsoUrl(data) {
	return request({
		url: '/ybzy/platapplication/front/getSsoUrl',
		method: 'post',
		data
	});
}
// 活动列表-我的预约
export function getSignList(data) {
	return request({
		url: '/museumcloud/activity/activitySign/signList',
		method: 'post',
		data: data,
		type: 'form',
		SSO_name: 'archivesManageOpeUser'
	});
}
// 活动列表-我的预约-取消预约
export function updateActivitySign(data) {
	return request({
		url: '/museumcloud/activity/activitySign/updateActivitySign',
		method: 'post',
		data: data,
		// type: 'form',
		SSO_name: 'archivesManageOpeUser'
	});
}
export function uploadFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/uploadOnlyOne',
		// url: '/main2/mecpfileManagement/uploadOnlyOne.json',
		method: 'post',
		data
	});
}
export function uploadMoreFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/upload',
		// url: '/main2/mecpfileManagement/uploadOnlyOne.json',
		method: 'post',
		data
	});
}

export function getFileInfo(params) {
	return request({
		url: '/ybzy/mecpfileManagement/front/getAdjunctFileInfos',
		method: 'get',
		params
	});
}
export function previewFile(data) {
	return request({
		url: '/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=' + data.adjunctId,
		// url: '/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=' + data.adjunctId,
		// headers: {
		// 	responseType: 'blob'
		// },
		responseType: 'blob',
		method: 'get'
	});
}
export function deleteFile(data) {
	return request({
		url:
			'/ybzy/mecpfileManagement/front/delAdjunct?id=' +
			data.id +
			'&documentId=' +
			data.id +
			'&userName=' +
			data.userName,
		// url: '/ybzy/mecpfileManagement/front/delAdjunct?id=' + data.id + '&userName=' + data.userName,
		method: 'post'
	});
}

// 活动列表
export function alumniActiveList(data) {
	return request({
		url: '/museumcloud/activity/activityItem/front/list',
		method: 'post',
		data: data,
		type: 'form'
	});
}

// 活动详情
export function alumniActiveListDetail(params) {
	return request({
		url: '/museumcloud/activity/activityItem/front/getObject',
		method: 'get',
		params
	});
}
// 校友风采
export function alumniShow(params) {
	return request({
		url: '/ybzy/front/alumnaCommunityPhoto/activeRecordsPage',
		method: 'get',
		params
	});
}
// 点赞收藏
export function likeOrCollect(params, url) {
	return request({
		url: `/ybzy/cmsinteract/front/${url}`,
		method: 'get',
		params
	});
}
