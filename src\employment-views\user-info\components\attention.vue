<!--
 @desc:个人中心 就业创业 我的关注
 @author: WH
 @date: 2023/9/5
 -->
<template>
	<div class="main">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="职位" name="0">
				<no-data
					v-if="recruitList.length == 0"
					:tips="{
						title: '暂无关注信息',
						detail: '你还没有关注信息，或者前往',
						clickText: '招聘信息'
					}"
					@noDataFn="noDataFn"
				/>
				<template v-else>
					<job-title-card
						v-for="(item, index) in recruitList"
						:key="index"
						:card-data="item"
						@handle="handle"
					/>
				</template>
			</el-tab-pane>
			<el-tab-pane label="公司" name="1">
				<no-data
					v-if="conpanyList.length == 0"
					:tips="{
						title: '暂无关注信息',
						detail: '你还没有关注信息，或者前往',
						clickText: '招聘信息'
					}"
					@noDataFn="noDataFn"
				/>
				<template v-else>
					<conpany-card
						v-for="(item, index) in conpanyList"
						:key="index"
						:card-data="item"
						@handle="handle"
					/>
				</template>
			</el-tab-pane>
			<!-- <el-tab-pane label="创业服务" name="2">创业服务</el-tab-pane>
			<el-tab-pane label="导师" name="3"></el-tab-pane>
			<el-tab-pane label="培训" name="4"></el-tab-pane> -->
			<!-- <el-tab-pane label="项目" name="5"></el-tab-pane> -->
		</el-tabs>

		<div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
	</div>
</template>

<script>
import JobTitleCard from './collect/job-title-card.vue';
import ConpanyCard from './collect/conpany-card.vue';
import NoData from './no-data.vue';
import { getDictionaryByCode } from '@/utils';

export default {
	components: { JobTitleCard, ConpanyCard, NoData },
	props: {
		epData: {
			type: Object,
			default() {
				return {};
			}
		}
		// failed: {
		// 	type: Boolean,
		// 	default: false
		// }
	},
	data() {
		return {
			activeName: '0', //0：招聘、1：公司、2：创业服务、3：导师、4：培训、5：项目
			recruitList: [],
			conpanyList: [],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10,
			peopleNumCodeList: [],
			corpIndustryList: []
		};
	},

	mounted() {
		this.getAttentionList();
		this.findSysCode();
	},
	methods: {
		handle(cardData) {
			this.cancelAttention(cardData.interact_object_id);
		},
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode(code, list) {
			const dicts = await getDictionaryByCode(['plat_enp_industry', 'post_company_scale']);
			this.peopleNumCodeList = dicts.post_company_scale || [];
			this.corpIndustryList = dicts.plat_enp_industry || [];
		},
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value && value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		},
		//取消收藏
		async cancelAttention(id) {
			try {
				this.loading = true;
				let { rCode, msg } = await this.$api.employment_api.cancelAttention({
					objectIds: id,
					type: this.activeName
				});
				if (rCode == 0) {
					this.getAttentionList();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		//获取列表
		async getAttentionList() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.getAttentionList({
					pageNum: this.page,
					pageSize: this.size,
					types: this.activeName
				});
				if (rCode == 0) {
					switch (this.activeName - 0) {
						case 0:
							this.recruitList = results.records;
							break;
						case 1:
							this.conpanyList = results.records;
							this.conpanyList.map((item, index) => {
								let industryField = item?.corp_industry;
								let peopleNumCode = item?.people_num_code;
								if (industryField) {
									item.corp_industry = this.enterpriseTypeStr(industryField, this.corpIndustryList);
								}
								if (peopleNumCode) {
									item.people_num_code = this.enterpriseTypeStr(
										peopleNumCode,
										this.peopleNumCodeList
									);
								}
							});
							break;
					}
					this.total = results.total;
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		noDataFn() {
			this.$router.push('/job-list');
		},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.page = 1;
			this.getAttentionList();
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.getAttentionList();
		},
		handleClick(tab, event) {
			this.getAttentionList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 0 20px;
	background: #fff;
	// .list-box {
	// 	width: 100%;
	// 	height: 680px;
	// 	padding: 20px;
	// 	overflow: auto;
	// }
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
.el-tab-pane {
	width: 100%;
	height: 680px;
	padding: 20px;
	border-radius: 4px;
	border: 1px solid #e8eaec;
	overflow: auto;
}
</style>
