<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="colletList">
			<div class="list">
				<div class="listTop">
					<div class="ele">序号</div>
					<div class="ele">课程名称</div>
					<div class="ele">收藏时间</div>
					<div class="ele">操作</div>
				</div>
				<div class="temp">
					<div v-for="(item, index) in listData" :key="index" class="lessons flex1">
						<div class="box box0">{{ index + 1 }}</div>
						<div class="box box1 flex1">
							<img :src="item.coverImg" alt="" />
							<div class="name">{{ item.name }}</div>
						</div>
						<div class="box box2">{{ item.createTime }}</div>
						<div class="box box5">
							<div class="btn1" @click="deleteColletFn(item.id)">取消收藏</div>
							<div class="btn2" @click="goDetail(item.id)">查看</div>
						</div>
					</div>
				</div>
			</div>
			<Empty v-if="listData.length == 0" :tips="'暂无数据'" />
			<div class="fenye">
				<el-pagination
					:current-page.sync="params.page"
					background
					:page-sizes="[10, 15, 20]"
					layout="total, sizes, prev, pager, next, jumper"
					:total="total"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				></el-pagination>
			</div>
		</div>
	</div>
</template>
<script>
import config from '@/config';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			params: {
				page: 1,
				pageNum: 10
			},
			total: 0,
			listData: []
		};
	},
	computed: {},
	async mounted() {
		// if (document.getElementsByClassName('commonHeader')[0]) {
		// 	document.getElementsByClassName('commonHeader')[0].style.display = 'none';
		// }
		if (!this._userinfo.id) {
			let queryString = '?';
			queryString += ['redirect', location.href].join('=');
			queryString = encodeURIComponent(queryString);
			localStorage.setItem(
				'scwl_homepage',
				`${config.domainUrl}${config.appList.valueAddedServices}`
			);
			window.location.href = `${config.domainUrl}scwl_user_main/#/login${queryString}`;
			return;
		} else {
			// if (!getLessonToken()) {
			// 	await this.loginFrontFn();
			// }
			this.getColletList();
		}
	},
	// beforeDestroy() {
	// 	document.getElementsByClassName('commonHeader')[0].style.display = 'block';
	// },
	methods: {
		// async loginFrontFn() {
		// 	const data = await this.$api.study_api.loginFront({
		// 		username: this._userinfo.username,
		// 		userId: this._userinfo.id,
		// 		nickname: this._userinfo.nickname
		// 	});
		// 	console.log('token', data.data.access_token);
		// 	setLessonToken(data.data.access_token);
		// },
		async getColletList() {
			const data = await this.$api.study_api.collectList(this.params);
			this.listData = data.data?.items || [];
			this.total = data.data?.total;
		},
		goDetail(id) {
			this.$router.push({ path: `/freecourses?id=${id}` });
			// window.open(
			// 	`${config.domainUrl}${config.appList.valueAddedServices}/#/lawLectureHall/freecourses?id=${id}`
			// );
		},
		deleteColletFn(courseId) {
			this.$confirm('确认取消收藏?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.study_api
						.collectCourse({
							courseId,
							type: 0
						})
						.then(res => {
							this.getColletList();
						});
					// console.log('生成订单', data);
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		handleSizeChange(val) {
			this.params.pageSize = val;
		},
		handleCurrentChange(val) {
			this.params.page = val;
		}
	}
};
</script>
<style scoped lang="scss">
.colletList {
	width: 1200px;
	margin: 0 auto;
	background: #fff;
	box-sizing: border-box;
	padding: 20px 15px 200px 15px;
	.list {
		.listTop {
			width: 100%;
			height: 40px;
			background: #f4f4f4;
			position: relative;
			.ele {
				font-size: 14px;

				color: #8c8c8c;
				position: absolute;
				top: 13px;
			}
			.ele:nth-child(1) {
				left: 20px;
			}
			.ele:nth-child(2) {
				left: 144px;
			}
			.ele:nth-child(3) {
				left: 596px;
			}
			.ele:nth-child(4) {
				left: 817px;
			}
		}
		.temp {
			.tempTop {
				margin-top: 15px;
				height: 40px;
				background: #f4f4f4;
				border: 1px solid #eeeeee;
				position: relative;
				.ele {
					top: 12px;
					position: absolute;
					color: #404040;
					span {
						font-size: 14px;

						color: #8c8c8c;

						top: 13px;
					}
				}
				.ele:nth-child(1) {
					left: 20px;
				}
				.ele:nth-child(2) {
					left: 276px;
				}
				.ele:nth-child(3) {
					left: 396px;
				}
			}
			.lessons {
				border: 1px solid #eeeeee;
				border-top: none;
				height: 94px;
				.box {
					border-left: 1px solid #eeeeee;
					height: 94px;
					box-sizing: border-box;
				}
				.box:last-child {
					border: none;
				}
				.box0 {
					text-align: center;
					font-size: 14px;
					line-height: 94px;
					color: #404040;
					width: 107px;
				}
				.box1 {
					padding: 0 14px 0 24px;
					width: 415px;
					img {
						width: 72px;
						height: 70px;
					}
					.name {
						margin-left: 12px;
						width: 210px;
						font-size: 16px;
						font-family: MicrosoftYaHei;
						color: #404040;
					}
				}
				.box2 {
					text-align: center;
					width: 208px;
					font-size: 14px;
					line-height: 94px;
					color: #404040;
				}

				.box5 {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 208px;
					font-size: 14px;
					font-family: MicrosoftYaHei;
					color: #404040;
					.btn1 {
						cursor: pointer;
						color: #f5222d;
					}
					.btn2 {
						cursor: pointer;
						color: #404040;
						margin-top: 8px;
					}
				}
			}
		}
	}
	.fenye {
		margin-top: 24px;
		text-align: right;
		// ::v-deep .el-pagination {
		// 	.btn-prev {
		// 		margin-left: 240px;
		// 	}
		// }
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #e8eaf0;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 40px;
	}
}
</style>
