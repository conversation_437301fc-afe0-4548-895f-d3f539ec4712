<template>
	<div class="paymentSounts">
		<div class="succeed">
			<div><img src="@/assets/shop-images/image-95.png" alt="" /></div>
			<div class="succeed-title">订单提交成功</div>
			<!-- <div v-show="$route.query.payMode != 8" class="succeed-dec">
				您的订单已提交成功，请尽快付款，付款完成后请尽快联系商家
			</div> -->
			<div v-show="$route.query.payMode == 8" class="succeed-dec">
				<span class="">
					商品订单提交完成，返回
					<a href="javascript:void(0)" @click="goLink">首页</a>
					挑选更多
				</span>
				<!-- <div v-show="count != 0">
					即将跳转至金融服务平台，倒计时
					<span class="count-down">{{ count }}s</span>
				</div> -->
			</div>
			<div v-show="$route.query.payMode != 8" class="succeed-link">
				<span>您可能会去：</span>
				<span><a href="javascript:void(0)" @click="onLink">点击前往订单中心 ></a></span>
			</div>
			<div v-show="$route.query.payMode == 8" class="succeed-link">
				<span>您可点击前往</span>
				<span>
					<a href="javascript:void(0)" class="underline" @click="onLink">个人中心-我的订单</a>
				</span>
				<span>查看已提交的订单详情</span>
			</div>
		</div>
	</div>
</template>

<script>
import config from '@/config';
import { Loading } from 'element-eoss';

export default {
	data() {
		return {
			type: 1,
			retailStep: 3,
			count: 5, // 倒计时默认5秒
			timer: null,
			iSopen: false
		};
	},
	mounted() {
		// if (this.$route.query.payMode == 8) {
		// 	// 启动计时器，每秒更新count值
		// 	this.timer = setInterval(() => {
		// 		if (this.count > 0) {
		// 			this.count--;
		// 		} else {
		// 			if (!this.iSopen) {
		// 				window.open(`${config.domainUrl}${config.appList.financeService}/#/financing/index`);
		// 			}
		// 			//scswl.eimm.wisesoft.net.cn:8099/jr/#/financing/index
		// 			clearInterval(this.timer); // 倒计时结束，停止计时器
		// 		}
		// 	}, 1000);
		// }
	},
	beforeDestroy() {
		// 组件销毁时清除计时器
		clearInterval(this.timer);
	},
	methods: {
		async isSeller() {
			let options = {
				target: 'app'
			};
			let loadingInstance = Loading.service(options);
			const res = await this.$api.shop_api.scswlLogin({ authType: 'onlyCheck' });
			if (res.code == 200) {
				if (res.isLogin && res.isSeller) {
					localStorage.setItem('scwl_homepage', `${config.domainUrl}${config.appList.scGoods}`);
					window.open(
						`${config.domainUrl}trip-seller/appSellerCenter/businessCenterInfo?auth=scswl`
					);
				} else if (res.isLogin) {
					if (!res.isEnterpriseAuth) {
						localStorage.setItem('scwl_homepage', `${config.domainUrl}${config.appList.scGoods}`);
						window.open(`${config.domainUrl}${config.appList.userCenter}/#/enterpriseAuth`);
					} else {
						this.$confirm('您还未认证商家资质，请前往个人中心进行所需商家资质的认证?', '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							closeOnPressEscape: false,
							closeOnClickModal: false,
							type: 'warning'
						})
							.then(() => {
								localStorage.setItem(
									'scwl_homepage',
									`${config.domainUrl}${config.appList.scGoods}`
								);
								window.open(`${config.domainUrl}${config.appList.userCenter}/#/dashboard`);
							})
							.catch(() => {});
					}
				} else {
					localStorage.setItem('scwl_homepage', `${config.domainUrl}${config.appList.scGoods}`);
					window.location.href = `${config.domainUrl}${config.appList.userCenter}/#/login?redirect=${window.location}`;
				}
			} else {
				this.$message.error(res.msg || '网络错误，请稍候再试！');
			}
			loadingInstance.close();
		},
		goLink() {
			this.$router.push({
				path: '/index'
			});
		},
		onLink() {
			this.$router.push({
				path: `/myOrder?isLay=${this.$route.query.isLaw == 1 ? '1' : '2'}`
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.paymentSounts {
	overflow: hidden;
}
.succeed {
	margin: 20px auto;
	width: 1200px;
	height: 608px;
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	text-align: center;
	img {
		margin-top: 86px;
		width: 200px;
		height: 200px;
	}
	&-title {
		font-size: 20px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 28px;
	}
	&-dec {
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #bfbfbf;
		line-height: 24px;
		margin-top: 10px;
		.count-down {
			height: 24px;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: var(--brand-6, '#ca3f3b');
			line-height: 24px;
		}
		span {
			height: 24px;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
		a {
			height: 24px;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: var(--brand-6, '#ca3f3b');
			line-height: 24px;
			text-decoration: underline;
		}
		div {
			height: 24px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 24px;
		}
	}
	&-link {
		margin-top: 80px;
		span {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
		}
		.underline {
			text-decoration: underline;
			margin: 0 5px;
		}
		a {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 22px;
		}
	}
}
</style>
