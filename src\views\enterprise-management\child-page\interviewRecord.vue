<template>
	<div class="main">
		<!-- 面邀记录 -->
		<h3 class="title">面邀记录</h3>
		<el-table
			v-loading="tableLoading"
			:data="list"
			style="width: 100%"
			align="center"
			header-align="center"
		>
			<el-table-column type="index" label="序号" width="50"></el-table-column>
			<el-table-column prop="postName" label="职位名称"></el-table-column>
			<el-table-column prop="userName" label="姓名"></el-table-column>
			<el-table-column prop="interviewDate" label="面邀时间"></el-table-column>
			<el-table-column prop="interviewTime" label="面试时间"></el-table-column>
			<el-table-column prop="interviewStatusDes" label="面试状态">
				<template #default="{ row }">
					<el-tag :type="{ 0: '', 1: 'success', 9: 'warning', 8: 'info' }[row.interviewStatus]">
						{{ row.interviewStatusDes || { 0: '待面试', 1: '已面试' }[row.interviewStatus] }}
					</el-tag>
				</template>
			</el-table-column>
			<!-- <el-table-column prop="desc" label="面试评价"></el-table-column> -->
			<el-table-column fixed="right" label="操作" width="150">
				<template slot-scope="scope">
					<el-button type="text" size="small" @click="handleOpenMsg(scope.row)">沟通</el-button>
					<el-button type="text" size="small" @click="biographicalView(scope.row)">
						查看简历
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="getList"
		/>
		<!-- 查看简历弹窗 -->
		<biographical-dialog ref="biographicalDialog" />
		<!--沟通-->
		<contact-message
			v-if="dialogMessageVisible"
			:base-info="contactDialogInfo"
			:dialog-form-visible="dialogMessageVisible"
		/>
	</div>
</template>

<script>
import biographicalDialog from './components/biographical.vue';
import contactMessage from '@/components/public/contactMessageEnterprise.vue';

export default {
	name: 'InterviewRecord',
	components: {
		contactMessage,
		biographicalDialog // 简历弹窗内容
	},
	data() {
		return {
			tableLoading: true,
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			list: [],
			contactDialogInfo: {},
			dialogMessageVisible: false
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 获取列表
		async getList() {
			try {
				this.tableLoading = true;
				const res = await this.$api.enterprise_center.getInterviewList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize
				});
				if (res.results) {
					this.list = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		handleOpenMsg(row) {
			this.contactDialogInfo = {
				SHOP_LOG: row.userHeadImg,
				SHOP_NAME: row.userName,
				SELLER_ID: row.userId,
				isGoods: true
			};

			this.dialogMessageVisible = true;
		},
		biographicalView(row) {
			this.$refs.biographicalDialog.show(row);
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}
.title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;
	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}
.del-btn {
	color: #de2d2d;
}
.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 20px;
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}
</style>
