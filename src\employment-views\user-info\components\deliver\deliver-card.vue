<!--
 @desc:投递卡片
 @author: WH
 @date: 2023/9/6
 -->
<template>
	<div class="card" @click="clickCard">
		<header>
			<div class="hr-info">
				<img
					:src="
						getHrPhotoUrl
							? getImgUrl(getHrPhotoUrl)
							: require('@/assets/shop-images/default-avatar.png')
					"
					alt=""
				/>
				<span>{{ hrName }}</span>
				<span>HR</span>
			</div>
			<p class="hr-state">{{ cardData.status || '未知状态' }}</p>
		</header>
		<article>
			<div class="left">
				<div class="title-box">
					<p>
						{{ getPostName }}
					</p>
					<span>{{ getPostAddress }}</span>
				</div>
				<div class="monye-box">
					<p>{{ getSalaryRange }}</p>
					<ul class="tags-box">
						<li v-for="(item, index) in getPostTags" :key="index">{{ item }}</li>
					</ul>
				</div>
			</div>
			<div class="right">
				<div class="img-box">
					<img src="@/assets/employment-images/tool-enterprise.png" alt="" />
				</div>
				<div class="company-info">
					<p>{{ getCompanyName }}</p>
					<ul class="tags-box">
						<li v-for="(item, index) in getCompanyTags" :key="index">
							{{ item }}
						</li>
					</ul>
				</div>
			</div>
		</article>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
import PreviewAdjunctMixin from '@/employment-views/mixin/previewAdjunct';
export default {
	mixins: [PreviewAdjunctMixin],
	props: {
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return { alumniUrl };
	},
	computed: {
		hrName() {
			if (!this.cardData?.hr) return '未知';
			let sex = this.cardData.hr.sex == 1 ? '先生' : '女士';
			return (this.cardData.hr.username || '未知').substring(0, 1) + sex;
		},
		getHrPhotoUrl() {
			return this.cardData?.hr?.photoUrl || '';
		},
		getPostName() {
			return this.cardData?.post?.name || '职位未知';
		},
		getPostAddress() {
			return this.cardData?.post?.address || '地址未知';
		},
		getSalaryRange() {
			const minMoney = this.cardData?.post?.minMoney;
			const maxMoney = this.cardData?.post?.maxMoney;
			if (!minMoney && !maxMoney) return '薪资面议';
			return `${minMoney || 0}~${maxMoney || 0}K`;
		},
		getPostTags() {
			return this.cardData?.post?.tag ? this.cardData.post.tag.split(',') : [];
		},
		getCompanyName() {
			return this.cardData?.enterprise?.corpName || '公司未知';
		},
		getCompanyTags() {
			return this.cardData?.enterprise?.tags ? this.cardData.enterprise.tags.split(',') : [];
		}
	},

	methods: {
		getImgUrl(url) {
			if (!url) return '';
			if (url.includes('http')) {
				return url;
			}
			return `${this.alumniUrl}${url}`;
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 100%;
	height: 150px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	border-bottom: 1px solid #e8eaf0;
	background: #ffffff;
	header {
		@include flexBox(space-between);
		margin-bottom: 22px;
		// border: 1px solid red;
		.hr-info {
			@include flexBox(flex-start);

			img {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				margin-right: 6px;
			}
			span {
				display: inline-block;
				font-size: 16px;
				color: #8390a3;
				padding: 0 10px;
				&:nth-child(2) {
					border-right: 1px solid #8390a3;
				}
			}
		}
		.hr-state {
			font-size: 16px;
			color: #333333;
		}
	}
	article {
		@include flexBox(space-between);
		.left {
			.title-box {
				@include flexBox(flex-start);
				margin-bottom: 20px;
				font-size: 16px;
				color: #0076e8;
				span {
					width: 300px;
					display: inline-block;
					font-size: 14px;
					margin-left: 10px;
					color: #999999;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
			.monye-box {
				@include flexBox(flex-start);
				font-size: 18px;
				color: #fe574a;
				p {
					margin-right: 16px;
				}
			}
		}
		.right {
			@include flexBox(space-between);
			width: 310px;
			.img-box {
				@include flexBox();
				width: 72px;
				height: 72px;
				border: 1px solid #f0f3f7;
				border-radius: 5px;
				img {
					width: 46px;
					height: 46px;
					border-radius: 6px;
				}
			}
			.company-info {
				p {
					font-size: 16px;
					color: #333333;
					margin-bottom: 24px;
				}
			}
		}
	}
}
.tags-box {
	@include flexBox(flex-start);
	li {
		margin-right: 10px;
		font-size: 14px;
		padding: 4px 10px;
		background: #f1f3f8;
		color: #999;
		border-radius: 2px;
	}
}
</style>
