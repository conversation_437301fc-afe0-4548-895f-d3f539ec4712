<template>
	<div class="Carousel" style="width: 827px; border-radius: 10px; overflow: hidden">
		<el-carousel :height="carouselHeight">
			<el-carousel-item v-for="(item, index) in list" :key="index">
				<img :src="$judgeFile(item.url)" />
			</el-carousel-item>
		</el-carousel>
	</div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
	name: 'Carousel',
	props: {
		carouselHeight: {
			type: String,
			default: '350px'
		},
		bannerList: {
			type: Array,
			default: () => []
		},
		isVertical: {
			type: Boolean,
			default: false
		},
		verticalHeight: {
			type: String,
			default: '48px'
		},
		imgHeight: {
			type: String,
			default: '106px'
		},
		imgWidth: {
			type: String,
			default: '311px'
		}
	},
	data() {
		return {
			list: []
		};
	},
	computed: {
		...mapGetters(['siteId'])
	},
	async mounted() {
		const { result } = await this.$api.shop_api.getAdvertsByCode({
			sysCode: 'pc_chuan_condiment',
			siteId: this.getSiteId() // 租户id
		});
		this.list = result.adData || [];
	}
};
</script>
<style lang="scss" scoped>
.Carousel {
	width: 100%;
}
.el-carousel__item img {
	height: 100%;
	width: 100%;
	object-fit: cover;
}

.el-carousel__item:nth-child(2n) {
	background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
	background-color: #d3dce6;
}
.img {
	margin-top: 7px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
}
.imgs {
	width: 100%;
	display: flex;
	justify-content: space-between;
}
.left {
	margin-right: 4px;
}
.bottom {
	width: 100%;
	margin-top: 9px;
	::v-deep .el-carousel__item {
		a {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
		}
	}
	::v-deep .el-carousel__indicators {
		display: none;
	}
	.link {
		font-size: 12px;
		font-family: Inter-Regular, Inter;
		font-weight: 400;
		color: #8c8c8c;
		padding: 15px;
		position: absolute;
		right: 0px;
		top: 0px;
	}
}
</style>
