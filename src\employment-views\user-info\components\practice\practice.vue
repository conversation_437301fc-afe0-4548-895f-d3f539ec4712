<!--
 @desc:个人中心 就业创业 我的实习报告 card
 @author: WH
 @date: 2023/9/23
 -->
<template>
	<div class="card">
		<header>
			<div class="title">
				<p>实习报告名称：{{ cardData.title }}</p>
				<p>实习公司名称：{{ cardData.enterpriseName }}</p>
			</div>
			<el-button type="danger" @click="handle">删除</el-button>
		</header>

		<article>
			<slot />
		</article>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			const TEXT = ['待面试', '已面试', '职位已关闭'];
			return TEXT[this.cardData.state - 0];
		}
	},
	methods: {
		handle() {
			this.$emit('handle', this.cardData);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	// @include flexBox();
	// flex-direction: column;
	width: 100%;
	// height: 200px;
	border-radius: 8px;
	margin-bottom: 20px;
	border: 1px solid #e8eaf0;
	background: #ffffff;

	header {
		@include flexBox(space-between);
		position: relative;
		width: 100%;
		height: 40px;
		padding: 0 20px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		background: #e8eaf0;
		.title {
			@include flexBox(flex-start);
		}
		p {
			margin-right: 30px;
			font-size: 14px;
			color: #666666;
		}
	}

	article {
		width: 100%;
		padding: 20px;
	}
}
</style>
