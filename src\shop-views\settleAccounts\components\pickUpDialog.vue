<template>
	<div v-loading="loading">
		<el-dialog
			width="600px"
			title="选择提货人"
			:visible.sync="dialogUserVisible"
			:destroy-on-close="true"
			:before-close="closeDialog"
		>
			<div>
				<el-form
					v-if="isAdd"
					ref="form"
					:model="form"
					:rules="rules"
					label-width="100px"
					class="form"
				>
					<el-form-item label="收货人" prop="contact">
						<el-input v-model="form.contact" placeholder="请输入收货人"></el-input>
					</el-form-item>
					<el-form-item label="所在地区" prop="addr">
						<el-cascader
							v-model="form.addr"
							:options="addresOptions"
							placeholder="请选择所在地区"
							:props="{ label: 'name', value: 'value' }"
						></el-cascader>
					</el-form-item>
					<el-form-item label="手机号码" prop="telNo">
						<el-input v-model="form.telNo" placeholder="请输入手机号码"></el-input>
					</el-form-item>
				</el-form>
				<div v-if="!isAdd">
					<div class="list">
						<div
							v-for="(item, index) of list"
							:key="index"
							:class="['list-item', item.active ? 'list-item_active' : '']"
							@click="onClickItem(item)"
						>
							<div v-show="item.active" class="img"></div>
							<img
								v-show="item.active"
								class="check-img"
								src="@/assets/shop-images/check.png"
								alt=""
							/>
							<div class="list-item-title">
								<div>{{ item.contact }}</div>
								<i class="el-icon-close" @click="handleClose(item.id)"></i>
							</div>
							<div class="list-item-dec">
								<div>
									手机号码：
									<span class="phone">{{ item.telno }}</span>
								</div>
								<div class="form-button">
									<span @click="updateSite(item, 1)">设为默认</span>
									<span @click="updateAddSite(item)">编辑</span>
								</div>
							</div>
						</div>
					</div>
					<div class="switch-user-from">
						<span>
							<i class="el-icon-plus"></i>
						</span>
						<a href="javascript:void(0)" class="add-location" @click="isAdd = true">
							<span>添加提货人</span>
						</a>
					</div>
				</div>
			</div>

			<span slot="footer" class="dialog-footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveUserAddr">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';
export default {
	name: 'PickUpDialog',
	props: {
		dialogUserVisible: {
			type: Boolean,
			default: false
		},
		arrList: {
			type: Array,
			default: () => []
		}
	},
	data() {
		let phone = (rule, value, callback) => {
			var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('联系人电话不能为空'));
			} else if (!reg.test(value)) {
				return callback(new Error('请输入正确的电话'));
			}
			callback();
		};
		return {
			isAdd: false,
			siteList: [],
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			form: {
				telNo: null
			},
			userId: getCookie('user_id'),
			loading: false,
			rules: {
				contact: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
				addr: [{ required: true, message: '请选择所在地区', trigger: 'blur' }],
				detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
				telNo: [{ required: true, validator: phone, trigger: 'blur' }]
			},
			list: []
		};
	},
	created() {
		this.list = this.arrList;
	},
	methods: {
		closeDialog() {
			this.$parent.dialogUserVisible = false;
		},
		onClickItem(item) {
			this.info = item;
			this.info.addressType = 1; //配送
			this.$emit('Address', this.info);
			this.list = this.list.map(event => {
				return {
					...event,
					active: item.id == event.id
				};
			});
		},
		handleClose(id) {
			this.$confirm('确定要删除当前提货人？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					this.$api.shop_api.delAddr({ id }).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '删除成功',
								type: 'success'
							});
							this.$parent.getUserAddrList();
						} else {
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
						this.loading = false;
					});
				})
				.catch(() => {});
		},
		updateAddSite(item) {
			let addr = [];
			addr.push(item.provinceid);
			addr.push(item.cityid);
			addr.push(item.countyid);
			this.form = { ...item, telNo: item?.telno || null, addr };
			this.isAdd = true;
		},
		updateSite(item, type) {
			let data = {};
			if (type) {
				data = {
					...item,
					provinceId: type ? item.provinceid : item.provinceId,
					cityId: type ? item.cityid : item.cityId,
					countyId: type ? item.countyid : item.countyId,
					telNo: item?.telNo || item.telno,
					type: type ? type : item.type,
					detailAddress: item?.detailAddress || item?.detailaddress || ' ',
					userId: this.userId
				};
			} else {
				data = {
					...item,
					telNo: item?.telNo || item.telno,
					type: type ? type : item.type,
					detailAddress: item?.detailAddress || item?.detailaddress || ' ',
					userId: this.userId
				};
			}
			this.$api.shop_api.saveUserAddr(data).then(res => {
				if (res.state) {
					this.$message.close();
					this.$message({
						message: res.msg || '添加成功',
						type: 'success'
					});
					this.dialogFormVisible = false;
					this.$parent.getUserAddrList();
				} else {
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 新增收货地址
		saveUserAddr() {
			if (!this.isAdd && !this.dialogFormVisible) {
				this.$parent.dialogUserVisible = false;
				this.$parent.list = this.list;
				return;
			}

			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					const { contact, id, telNo } = this.form;
					let provinceId = '';
					let cityId = '';
					let countyId = '';
					let type = 1;
					if (this.form.addr) {
						provinceId = this.form?.addr[0] || '';
						cityId = this.form?.addr[1] || '';
						countyId = this.form?.addr[2] || '';
					}
					if (this.form.type) {
						type = this.form.type;
					} else {
						type = this.list.length == 0 ? '1' : '2';
					}
					const data = {
						contact,
						id,
						detailAddress: this.form?.detailaddress || ' ',
						telNo,
						provinceId,
						cityId,
						countyId,
						userId: this.userId,
						type: type
					};
					this.$api.shop_api.saveUserAddr(data).then(res => {
						if (res.state) {
							this.$message.close();
							this.$message({
								message: res.msg || '添加成功',
								type: 'success'
							});
							this.$parent.dialogUserVisible = false;
							this.isAdd = false;
							this.$parent.getUserAddrList();
						} else {
							this.$message.close();
							this.$message.error(res.msg || '网络错误，请稍候再试！');
						}
						this.loading = false;
					});
				} else {
					return false;
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.logistics-info {
	background-color: #fff;
	padding: 16px 20px;
	margin-top: 16px;
	&-header {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-offline {
		margin-top: 15px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		padding: 16px 0px;
		.distribution {
			display: flex;
		}
		.distribution-mode {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid #d9d9d9;
			padding: 8px 18px;
			margin-right: 10px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 22px;
			overflow: hidden;
			position: relative;
			.check-img {
				position: absolute;
				bottom: 0px;
				right: 0px;
			}
			.img {
				position: absolute;
				height: 27px;
				width: 15px;
				bottom: -10px;
				right: -3px;
				background: var(--brand-6, '#ca3f3b');
				-moz-transform: rotate(45deg);
				-webkit-transform: rotate(45deg);
				-o-transform: rotate(45deg);
				transform: rotate(45deg);
			}
		}
		.distribution-active {
			background: #ffffff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			color: var(--brand-6, '#ca3f3b');
		}
		.site {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #262626;
			line-height: 20px;
		}
		.location {
			margin-left: 5px;
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
		}
		.default {
			margin-left: 16px;
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 20px;
		}
		.address {
			display: flex;
			align-items: center;
			padding: 5px 19px 5px 5px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid #eeeeee;
		}
		.address-active {
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid var(--brand-6, '#ca3f3b');
			i {
				color: var(--brand-6, '#ca3f3b');
			}
		}
		.add-location {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: var(--brand-6, '#ca3f3b');
			line-height: 20px;
		}
		.site-list {
			display: flex;
			&-item {
				width: 238px;
				height: 72px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid #d9d9d9;
				padding: 0px 20px;
				margin-right: 8px;
				&-nav {
					display: flex;
					align-items: center;
					i {
						color: #8c8c8c;
					}
					&-location {
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
			.site-list-item-active {
				border: 1px solid var(--brand-6, '#ca3f3b');
				::v-deep .el-checkbox__label {
					color: var(--brand-6, '#ca3f3b');
				}
				.site-list-item-nav {
					display: flex;
					align-items: center;
					i {
						color: var(--brand-6, '#ca3f3b');
					}
					.site-list-item-nav-location {
						color: var(--brand-6, '#ca3f3b');
						margin-left: 3px;
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						line-height: 20px;
						overflow: hidden; //超出的文本隐藏
						text-overflow: ellipsis; //溢出用省略号显示
						display: -webkit-box;
						-webkit-line-clamp: 1; // 超出多少行
						-webkit-box-orient: vertical;
					}
				}
			}
		}
	}
}
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
	background-color: var(--brand-6, '#ca3f3b');
	border-color: var(--brand-6, '#ca3f3b');
}
.user-address {
	border: none !important;
}
.switch-user {
	padding: 5px 8px;
	margin-left: 20px;
	height: 30px;
	background: #ffffff;
	line-height: 0px;
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	border: 1px solid var(--brand-6, '#ca3f3b');
	font-size: 12px;
	font-family: Source Han Sans SC-Normal, Source Han Sans SC;
	font-weight: 400;
	color: var(--brand-6, '#ca3f3b');
	span {
		margin-left: 4px;
	}
}
.switch-user-from {
	padding: 5px 8px;
	width: 110px;
	margin-top: 10px;
	background: #ffffff;
	border-radius: 3px 3px 3px 3px;
	opacity: 1;
	border: 1px solid var(--brand-6, '#ca3f3b');
	font-size: 12px;
	font-family: Source Han Sans SC-Normal, Source Han Sans SC;
	font-weight: 400;
	color: var(--brand-6, '#ca3f3b');
	span {
		margin-left: 4px;
	}
}
.list {
	max-height: 400px;
	overflow-y: auto;
	&-item_active {
		overflow: hidden;
		border: 1px solid var(--brand-6, '#ca3f3b') !important;
		position: relative;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		margin-bottom: 10px;
		.img {
			position: absolute;
			height: 46px;
			width: 20px;
			top: -15px;
			left: -7px;
			background: var(--brand-6, '#ca3f3b');
			transform: rotate(45deg);
		}
		.check-img {
			position: absolute;
			top: 0px;
			left: 0px;
			width: 16px;
			height: 16px;
		}
	}
	&-item {
		position: relative;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		margin-bottom: 10px;
		height: 93px;
		&-title {
			display: flex;
			justify-content: space-between;
			font-size: 18px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 32px;
			div {
				padding: 10px 20px;
			}
			i {
				margin-top: 12px;
				margin-right: 9px;
			}
		}
		&-dec {
			display: flex;
			padding: 10px 21px;
			justify-content: space-between;
			div {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #9da5b7;
				.phone {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
				}
			}
			.form-button {
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				cursor: pointer;
				span {
					padding: 0 20px;
				}
			}
		}
	}
}
.address-list {
	display: flex;
	align-items: center;
}
.rests-input {
	width: 416px;
}
</style>
