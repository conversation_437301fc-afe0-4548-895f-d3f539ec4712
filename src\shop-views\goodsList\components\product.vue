<template>
	<div class="product-content">
		<!-- <div class="product-title">产品基本信息</div> -->
		<div>
			<!-- <descriptions :data="productInfo" />
			<descriptions :data="keepGrainInfo" />
			<descriptions :data="supplierInfo" /> -->
			<descriptions :data="placeInfo" />
		</div>
	</div>
</template>

<script>
import Descriptions from './descriptions.vue';
export default {
	name: 'Product',
	components: { Descriptions },
	props: {
		info: {
			type: Object,
			default: () => {}
		}
	},
	data: () => {
		return {
			productInfo: {
				title: '商品参数',
				data: [
					{ key: '商品分类', val: '现象玉米' },
					{ key: '型号', val: '现象玉米103' },
					{ key: '规格', val: '吨' },
					{ key: '包装类型', val: '绵阳市' },
					{ key: '生产日期', val: '绵阳市' }
				]
			},
			keepGrainInfo: {
				title: '交收信息',
				data: [
					{ key: '提货方式', val: '成都一代仓储' },
					{ key: '装运条件', val: '6000m2' },
					{ key: '支付条款', val: '自营' },
					{ key: '付款方式', val: '成都温江区大棚玉米' }
				]
			},
			supplierInfo: {
				title: '联系人信息',
				data: [
					{ key: '联系人', val: '王金花' },
					{ key: '联系电话', val: '132122222' }
					// { key: '详细地址', val: '成都温江区创新路818号9栋1-4层' }
				]
			},
			placeInfo: {
				title: '商品介绍',
				html: true,
				htmlData: ''
			}
		};
	},
	mounted() {
		let skus = this.info.skus.map(item => {
			return item.SKU_NAME ? item.SKU_NAME : '';
		});
		skus = skus.toString();
		this.placeInfo.htmlData = this.info?.baseInfo?.DESC_M || '';
		this.productInfo.data.forEach(item => {
			if (item.key == '商品分类') item.val = this.info?.baseInfo?.typeNames || '';
			if (item.key == '型号') item.val = this.info?.baseInfo?.typeNames || '';
			if (item.key == '规格') item.val = skus || '';
			if (item.key == '包装类型') item.val = this.info?.baseInfo?.agriculturalPackageType || '';
			if (item.key == '生产日期') item.val = this.info?.baseInfo?.agriculturalYear || '';
		});
	}
};
</script>

<style lang="scss" scoped>
.product-content {
	padding: 10px 16px;
	background: #fff;
	min-height: calc(100% - 70px);
}
.product-title {
	width: 100%;
	height: 60px;
	line-height: 60px;
	padding: 0 14px;
	background: linear-gradient(270deg, #fafafc 0%, #fff6ef 100%);
	border-radius: 6px 6px 6px 6px;
	font-size: 20px;
	font-family: Source Han Sans SC-Regular, Source Han Sans SC;
	font-weight: 400;
	color: #000000;
}
.timeline {
	padding: 50px 26px 65px 26px;
	display: flex;
	::v-deep .el-timeline {
		padding-left: 13px;
		width: 264px;
		margin-right: 7px;
		&-item__tail {
			border-left: 2px solid #f0f0f0;
		}
		&-item__node {
			width: 24px;
			height: 24px;
			background-color: #fff;
			left: -7px;
			// border: 2px solid #6ac1ee;
			// background-color: #fff;
			&::after {
				content: '';
				display: inline-block;
				width: 12px;
				height: 12px;
				border-radius: 50px 50px 50px 50px;
				opacity: 1;
				border: 2px solid #6ac1ee;
				position: absolute;
				left: 50%;
				top: 50%;
				margin-left: -6px;
				margin-top: -6px;
			}
		}
		&-item__wrapper {
			top: 0px;
		}
		&-item__content {
			font-size: 16px;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #262626;
			line-height: 24px;
		}
		&-item__timestamp {
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 20px;
		}
	}
	&_title {
		font-size: 16px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #262626;
		line-height: 24px;
		margin-bottom: 4px;
	}
	&_time,
	&_list {
		font-size: 12px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 20px;
		margin-bottom: 4px;
	}
}
.sourcemap {
	flex: 1;
	height: 407px;
}
</style>
