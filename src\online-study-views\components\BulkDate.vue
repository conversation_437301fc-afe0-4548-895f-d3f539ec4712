<template>
	<div class="BulkDate">
		<div class="top">
			<div
				v-for="(item, index) of topTabs"
				:key="index"
				:class="[topActive === index + 1 ? 'top-tabs-active' : '', 'top-tabs']"
				@click="topChage(index)"
			>
				{{ item.name }}
			</div>
		</div>
		<div class="dynamic-state">
			<div class="top">
				<div class="title">价格动态</div>
				<div class="tabs">
					<div class="ups-downs ups-downs-active">涨幅榜</div>
					<div class="ups-downs">跌幅榜</div>
				</div>
				<div class="link">更多></div>
			</div>
			<div class="table">
				<el-table
					:data="tableData"
					show-header
					stripe
					style="width: 100%"
					:header-cell-style="{
						background: '#F4F4F4',
						color: '#404040'
					}"
				>
					<el-table-column prop="name" label="姓名" width="70"></el-table-column>
					<el-table-column prop="name" label="姓名"></el-table-column>
					<el-table-column prop="name" label="姓名"></el-table-column>
					<el-table-column prop="name" label="姓名" width="70"></el-table-column>
				</el-table>
			</div>
			<div class="bottom">
				<div class="tabs">
					<div class="title">库存统计</div>
					<div class="list">
						<el-tabs v-model="activeName" @tab-click="handleClick">
							<el-tab-pane label="玉米" name="first"></el-tab-pane>
							<el-tab-pane label="红豆" name="second"></el-tab-pane>
							<el-tab-pane label="大豆" name="third"></el-tab-pane>
							<el-tab-pane label="小麦" name="fourth"></el-tab-pane>
						</el-tabs>
					</div>
				</div>
				<div class="card">
					<div class="item">
						<div class="site">成都</div>
						<div class="price">25.04万/月</div>
						<div class="date">2023-02-10</div>
						<div class="downs">-0.39</div>
					</div>
					<div class="item">
						<div class="site">成都</div>
						<div class="price">25.04万/月</div>
						<div class="date">2023-02-10</div>
						<div class="ups">-0.39</div>
					</div>
					<div class="item">
						<div class="site">成都</div>
						<div class="price">25.04万/月</div>
						<div class="date">2023-02-10</div>
						<div class="ups">-0.39</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'BulkDate',
	data() {
		return {
			topActive: 1,
			topTabs: [
				{
					name: '农副产品'
				},
				{
					name: '农副产品'
				}
			],
			tableData: [
				{
					date: '2016-05-02',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1518 弄'
				},
				{
					date: '2016-05-04',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1517 弄'
				},
				{
					date: '2016-05-01',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1519 弄'
				},
				{
					date: '2016-05-03',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1516 弄'
				},
				{
					date: '2016-05-02',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1518 弄'
				},
				{
					date: '2016-05-02',
					name: '王小虎',
					address: '上海市普陀区金沙江路 1518 弄'
				}
			],
			activeName: 'second'
		};
	},
	methods: {
		topChage(index) {
			this.topActive = index + 1;
		},
		handleClick(tab, event) {
			console.log(tab, event);
		}
	}
};
</script>

<style lang="scss" scoped>
.BulkDate {
	width: 100%;
	height: 100%;
	padding: 19px 10px;
	.top {
		display: flex;

		.top-tabs {
			margin-right: 6px;
			text-align: center;
			width: 96px;
			height: 32px;
			background: #ffffff;
			border-radius: 36px 36px 36px 36px;
			opacity: 1;
			border: 1px solid #ca3f3b;
			font-size: 14px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #ca3f3b;
			line-height: 32px;
		}
		.top-tabs-active {
			margin-right: 6px;
			text-align: center;
			width: 96px;
			height: 32px;
			background: #ca3f3b;
			border-radius: 36px 36px 36px 36px;
			opacity: 1;
			border: 1px solid #ca3f3b;
			font-size: 14px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: rgba(255, 255, 255, 0.9);
			line-height: 32px;
		}
	}
	.dynamic-state {
		.top {
			margin-top: 20px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.title {
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #262626;
				line-height: 32px;
			}
			.tabs {
				display: flex;
				.ups-downs {
					width: 78px;
					height: 28px;
					background: #ffffff;
					border-radius: 0px 3px 3px 0px;
					opacity: 1;
					border: 1px solid #d9d9d9;
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #404040;
					text-align: center;
					line-height: 28px;
				}
				.ups-downs-active {
					background: #f2a665;
					border-radius: 3px 0px 0px 3px;
					opacity: 1;
					font-size: 14px;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #ffffff;
				}
			}
			.link {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
		}
		.table {
			margin-top: 10px;
			::v-deep .el-table td {
				padding: 8px 0 !important;
			}

			::v-deep.el-table th {
				padding: 6px 0;
			}
		}
		.bottom {
			.tabs {
				margin-top: 20px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title {
					font-size: 16px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #262626;
					line-height: 32px;
				}
				::v-deep .el-tabs__header {
					margin: 0;
				}
				::v-deep .el-tabs__nav-wrap::after {
					display: none;
				}
				::v-deep .el-tabs__item {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					padding: 0 20px;
				}
				::v-deep .el-tabs__active-bar {
					background: #ca3f3b;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				::v-deep .el-tabs__item.is-active {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #ca3f3b;
				}
			}
			.card {
				.item {
					margin-top: 6px;
					width: 364px;
					height: 60px;
					background: #ffffff;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
					border: 1px solid #eeeeee;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 21px 18px 17px;
					.site {
						font-size: 14px;
						font-family: Source Han Sans SC-Bold, Source Han Sans SC;
						font-weight: bold;
						color: #262626;
					}
					.price {
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
					}
					.date {
						font-size: 12px;
						font-family: Source Han Sans SC-Normal, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
					}
					.downs {
						font-size: 12px;
						font-family: Source Han Sans SC-Bold, Source Han Sans SC;
						font-weight: bold;
						color: #76bf6a;
						line-height: 20px;
					}
					.ups {
						font-size: 12px;
						font-family: Source Han Sans SC-Bold, Source Han Sans SC;
						font-weight: bold;
						color: #ca3f3b;
					}
				}
			}
		}
	}
}
</style>
