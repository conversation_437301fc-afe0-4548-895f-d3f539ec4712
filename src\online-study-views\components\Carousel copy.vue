<template>
	<div class="Carousel">
		<el-carousel :height="carouselHeight">
			<el-carousel-item v-for="item in 4" :key="item">
				<h3 class="small">{{ item }}</h3>
			</el-carousel-item>
		</el-carousel>
		<div class="imgs">
			<img
				:style="`width: ${imgWidth}; height: ${imgHeight}`"
				class="img left"
				src="@/assets/img/Group_1199.png"
				alt=""
			/>
			<img
				:style="`width: ${imgWidth}; height: ${imgHeight}`"
				class="img"
				src="@/assets/img/Group_1199.png"
				alt=""
			/>
		</div>
		<div v-if="isVertical" class="bottom">
			<el-carousel :height="verticalHeight" direction="vertical">
				<el-carousel-item v-for="item in 3" :key="item">
					<a class="medium">
						{{ item }}
					</a>
					<div class="link">
						<a href="">更多></a>
					</div>
				</el-carousel-item>
			</el-carousel>
		</div>
	</div>
</template>
<script>
export default {
	name: 'Carousel',
	props: {
		carouselHeight: {
			type: String,
			default: '284px'
		},
		isVertical: {
			type: Boolean,
			default: false
		},
		verticalHeight: {
			type: String,
			default: '48px'
		},
		imgHeight: {
			type: String,
			default: '106px'
		},
		imgWidth: {
			type: String,
			default: '311px'
		}
	}
};
</script>
<style lang="scss" scoped>
.Carousel {
	width: 100%;
}
.el-carousel__item h3 {
	color: #475669;
	font-size: 14px;
	opacity: 0.75;
	line-height: 150px;
	margin: 0;
}

.el-carousel__item:nth-child(2n) {
	background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
	background-color: #d3dce6;
}
.img {
	margin-top: 7px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
}
.imgs {
	width: 100%;
	display: flex;
	justify-content: space-between;
}
.left {
	margin-right: 4px;
}
.bottom {
	width: 100%;
	margin-top: 9px;
	::v-deep .el-carousel__item {
		a {
			font-size: 12px;
			font-family: Source Han Sans SC-Normal, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
		}
	}
	::v-deep .el-carousel__indicators {
		display: none;
	}
	.link {
		font-size: 12px;
		font-family: Inter-Regular, Inter;
		font-weight: 400;
		color: #8c8c8c;
		padding: 15px;
		position: absolute;
		right: 0px;
		top: 0px;
	}
}
</style>
