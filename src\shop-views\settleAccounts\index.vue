<template>
	<div v-loading="loading" class="attract-investment">
		<div v-show="retailStep == 1 || bulkStep == 1" class="conter">
			<!--实物商品的收货人-->
			<!-- v-show="!noShowMethods"   // 控制收货方式-->
			<logistics-info
				ref="logisticsInfo"
				:distribution-mode-list="distributionModeList"
				:rests-list="restsList"
				@Address="Address"
			/>
			<!--酒店、门票的联系人-->
			<sceneContacts
				v-if="['hotel', 'scene'].includes(shopType) && contacts.needContacts"
				ref="contacts"
				:need-show-info="contacts.contacts"
				select-type="radios"
				data-key="contacts"
				form-key="contactsArr"
				@selectContactsInfo="selectContactsInfo"
				@updateComponents="updateComponents"
			></sceneContacts>
			<!--进入酒店的时间-->
			<joinHotelTime
				v-if="shopType === 'hotel'"
				:skus="detail.skus"
				@changeSkuNum="changeSkuNum"
			></joinHotelTime>
			<!--酒店、门票的入住人/游玩人-->
			<sceneContacts
				v-if="['hotel', 'scene'].includes(shopType)"
				ref="user"
				:title-tip="shopType === 'hotel' ? '（每间只需填一人）' : ''"
				:need-show-info="passengerResult.passenger"
				select-type="multiple"
				:multiple-num="skuNum"
				data-key="user"
				form-key="userArr"
				:title="shopType === 'scene' ? '游客信息' : '住客资料'"
				@selectContactsInfo="selectContactsInfo"
				@updateComponents="updateComponents"
			></sceneContacts>
			<!--酒店入住须知-->
			<user-tip v-if="['hotel', 'scene'].includes(shopType)" :skus="detail.skus"></user-tip>
			<!--商品信息-->
			<settle-accounts-goods-info
				ref="settleAccountsGoodsInfo"
				:is-point-goods="isPointGoods"
				:shop-type="shopType"
				:start-date="startDate"
				:end-date="endDate"
				@totalPrice="totalPrice"
				@distributionMode="distributionMode"
				@getStoreList="getStoreList"
				@setRestsList="setRestsList"
				@transportFee="transportFee"
				@setInfo="setInfo"
			/>
			<!--支付信息-->
			<payment-mode ref="paymentMode" />
			<!--优惠券/购物卡-->
			<!-- <coupon
				v-if="type != 1"
				:shop-id="baseInfo.ID"
				:goods-list="list"
				:total-prices="totalPrices"
				:base-info="baseInfo"
				@changeCoupon="changeCoupon"
				@changeGift="changeGift"
			></coupon> -->
			<!--支付信息-->
			<aggregate-amount ref="aggregateAmount" :is-point-goods="isPointGoods" />
			<div class="button">
				<div>
					<span>应付金额：</span>
					<span class="price">
						¥{{
							distributionModeType == 2
								? Number(preferentialPrice).toFixed(2)
								: Number(totalPrices).toFixed(2)
						}}
					</span>
					<span v-if="isPointGoods">+{{ totalPoint }}积分</span>
				</div>
				<a href="javascript:void(0)">
					<div class="button-content" @click="submit">提交订单</div>
				</a>
			</div>
		</div>
		<!-- 支付 -->
		<div v-if="false" class="pay">
			<div class="pay-header">
				<div class="pay-header-title">
					订单提交成功，请尽快付款！订单号：{{ orderInfo.orderId }}
				</div>
				<div class="pay-header-dec">
					<div>
						商品信息：
						<span>{{ orderInfo.orderName }}</span>
						<span>25kg/袋*20000</span>
					</div>
					<div>
						订单金额：
						<span class="pay-price">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
					</div>
				</div>
			</div>
			<div class="pay-type">
				<div class="pay-type-title">选择支付方式</div>
				<div class="pay-type-content">
					<div class="pay-type-content-radio">
						<div class="pay-type-content-radio-item">
							<div class="pay-type-content-radio-item-nav">
								<div class="radio-img">
									<img class="img" src="@/assets/shop-images/check-circle-filled.png" alt="" />
								</div>
								<AlIcon name="icon-zhifubao" color="#06b4fd" size="22px"></AlIcon>
								<div>支付宝</div>
							</div>
							<div class="price">
								<span>支付：</span>
								<span class="price-item">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
							</div>
						</div>
						<div class="pay-type-content-radio-item">
							<div class="pay-type-content-radio-item-nav">
								<div class="radio-img">
									<img class="img" src="@/assets/shop-images/check-circle-filled.png" alt="" />
								</div>
								<AlIcon name="icon-weixin" color="#28c445" size="22px"></AlIcon>
								<div>微信</div>
							</div>
							<div class="price">
								<span>支付：</span>
								<span class="price-item">¥{{ orderInfo.otherParams.sumPrice.toFixed(2) }}</span>
							</div>
						</div>
					</div>
					<div class="divider"></div>
					<div class="qrcode"><img src="" alt="" /></div>
				</div>
			</div>
		</div>
		<!-- 订单提交成功 -->
	</div>
</template>

<script>
import LogisticsInfo from './components/logisticsInfo.vue';
import SettleAccountsGoodsInfo from './components/settleAccountsGoodsInfo.vue';
import PaymentMode from './components/paymentMode.vue';
import AggregateAmount from './components/aggregateAmount.vue';
import sceneContacts from './components/sceneContacts.vue';
import BigNumber from 'bignumber.js';
import { getCookie } from '@/utils/auth';
import userTip from '@/shop-views/settleAccounts/components/userTip';
import joinHotelTime from '@/shop-views/settleAccounts/components/joinHotelTime';
import dateInterval from '@/utils/dateInterval';
// import coupon from '@/shop-views/settleAccounts/components/coupon';
export default {
	components: {
		LogisticsInfo,
		SettleAccountsGoodsInfo,
		PaymentMode,
		AggregateAmount,
		sceneContacts,
		userTip,
		joinHotelTime
		// coupon
	},
	data() {
		return {
			detail: {},
			radio: 1,
			type: 1,
			ids: '',
			totalPrices: 0,
			preferentialPrice: 0,
			list: [],
			info: {},
			bulkStep: null,
			retailStep: null,
			distributionModeType: 1,
			distributionModeList: [],
			orderInfo: {},
			count: 5, // 初始值为5秒
			loading: false,
			offlineStore: {},
			isLaw: this.$route.query.isLaw,
			ckId: '',
			userId: '',
			restsList: [],
			transportFeeData: [],
			isPointGoods: false,
			totalPoint: 0,
			shopType: '', // 商品类型
			contactsArr: [], // 联系人选中信息
			userArr: [], // 游客选中信息
			keyArr: ['contacts', 'user'], //相同组件的key值
			startDate: '', // 开始时间
			endDate: '', // 结束时间
			skuId: '', // 门票skuId
			skuNum: 1, // 数量
			contacts: [], // 联系人动态信息
			passengerResult: [], // 游客动态信息
			baseInfo: {}, // 商品信息
			couponId: '', //优惠券
			giftId: '', // 购物卡
			couponInfo: {}, // 购物卡信息
			giftInfo: {}, // 优惠券信息
			noShowMethods: false // 不显示配送方式
		};
	},
	created() {
		/**门票酒店的开始结束时间*/
		let today = new Date();
		this.startDate = this.$route.query.startDate
			? new Date(this.$route.query.startDate).toLocaleDateString()
			: today.toLocaleDateString();
		this.endDate = this.$route.query.endDate
			? new Date(this.$route.query.endDate).toLocaleDateString()
			: new Date(today.getTime() + 24 * 60 * 60 * 1000).toLocaleDateString();
		/** 门票直接取skuId和Num，不用再去循环组装 */
		this.skuNum = this.$route.query.skuNum;
		this.skuId = this.$route.query.skuId || '';
		/**是否是积分商品*/
		this.isPointGoods = this.$route.query.isPointGoods === 'true';
		/**商品类型*/
		this.shopType = this.$route.query.shopType || '';
		this.ids = this.$route.query?.ids || '';
		this.skuNum = this.$route.query?.skuNum || 1;
		/**type===为购物车*/
		this.type = this.$route.query?.type || 1;
		if (this.type == 1) {
			this.retailStep = 1;
		} else {
			this.bulkStep = 1;
		}
		this.userId = this.isShopLogin();
	},
	methods: {
		/**选中优惠券*/
		changeCoupon(ids, info) {
			this.couponId = ids;
			this.couponInfo = info;
		},
		/**选中购物卡*/
		changeGift(id, info) {
			this.giftId = id;
			this.giftInfo = info;
		},
		/**针对酒店，商品数量发生变化*/
		changeSkuNum(skuNum, sDate, eDate) {
			this.skuNum = skuNum; // 房间数
			this.startDate = sDate;
			this.endDate = eDate;
			this.$nextTick(() => {
				this.$refs.settleAccountsGoodsInfo.resetPrice(skuNum);
			});
		},
		/**保存游客动态信息*/
		setInfo(info) {
			this.detail = info;
			this.contacts = info.contactsResult;
			this.passengerResult = info.passengerResult;
			this.baseInfo = info.baseInfo;
		},
		/**通知相同的组件重新加载渲染*/
		updateComponents(key) {
			this.keyArr.forEach(k => {
				if (k !== key) {
					this.$refs[k].getUserAddrList(); // 相同组件修改后重新请求
				}
			});
		},
		/**联系人选中信息*/
		selectContactsInfo(key, arr) {
			this[key] = arr.filter(item => {
				return item.active;
			});
		},
		getStoreList(id) {
			this.$refs.logisticsInfo.getStoreList(id);
		},
		/**计算价格*/
		totalPrice(list, isReset = false) {
			if (isReset) {
				this.totalPrices = 0;
				this.totalPoint = 0;
			}
			list.forEach(item => {
				this.totalPrices = BigNumber(this.totalPrices).plus(Number(item.totalPrices)).toString();
				this.totalPoint = BigNumber(this.totalPoint).plus(Number(item.totalPoint)).toString();
				// this.preferentialPrice = BigNumber(this.preferentialPrice)
				// 	.plus(Number(item.preferentialPrice))
				// 	.toString();
				this.preferentialPrice = this.totalPrices;
			});
			this.list = list;
			let transportFee = 0;
			if (list.length > 1) {
				this.distributionModeType = 2;
				this.$refs.aggregateAmount.show = true;
				list.forEach(item => {
					transportFee = BigNumber(transportFee).plus(Number(item.sumTransportFee)).toString();
					// this.preferentialPrice = BigNumber(this.preferentialPrice)
					// 	.plus(Number(item.preferentialPrice))
					// 	.toString();
				});
			}
			if (this.transportFeeData && this.transportFeeData.transportFee) {
				if (this.transportFeeData.addUp == 0) {
					transportFee = this.transportFeeData.transportFee;
				} else {
					transportFee = this.transportFeeData.transportFee * this.skuNum;
				}
			}
			// this.preferentialPrice += transportFee;

			this.$nextTick(() => {
				this.$refs.aggregateAmount.transportFee = transportFee;
				this.preferentialPrice = BigNumber(this.preferentialPrice)
					.plus(Number(transportFee))
					.toString();
				this.$refs.aggregateAmount.totalPrices = this.totalPrices;
				this.$refs.aggregateAmount.totalPoint = this.totalPoint;
			});
		},
		Address(info, type) {
			if (type == 1) {
				// 提货人
				this.ckId = info.id;
				this.offlineStore = info;
				return;
			}
			// this.offlineStore = {};
			this.info = info;
		},

		distributionMode(item) {
			console.log(item, '5555555555555555555555555555555555555555555');

			if (item) {
				this.distributionModeList = item;
				this.$nextTick(() => {
					this.$refs.logisticsInfo.formLabelAlign.distributionType = item[0];
				});
			} else {
				this.noShowMethods = true;
			}
		},
		setRestsList(arr) {
			this.restsList = arr;
			this.$nextTick(() => {
				this.$refs.logisticsInfo.restsList = arr;
			});
		},
		transportFee(t) {
			this.transportFeeData = t;
		},
		/**提交订单*/
		submit() {
			this.userId = this.isShopLogin();
			if (this.userId) {
				if (['scene', 'hotel'].includes(this.shopType)) {
					this.sceneCreatOrder();
				} else {
					this.addCreateOrderMult();
				}
			}
		},
		/**门票、酒店订单*/
		sceneCreatOrder() {
			// 游客信息
			let userInfo = this.userArr.map(item => {
				return {
					...item,
					userName: item.contact,
					phoneNum: item.telno
				};
			});
			// 联系人信息
			let contactInfo = this.contactsArr.map(item => {
				return {
					...item,
					phoneNum: item.telno,
					userName: item.contact
				};
			});
			/**校验联系人信息是否完整*/
			if (this.contacts && this.contacts.needContacts) {
				try {
					this.contacts.contacts.forEach(item => {
						if (!contactInfo[0][item.key]) {
							let err = `请完善联系人的${item.text}信息`;
							this.$message.error(err);
							throw new Error(err);
						}
					});
				} catch (err) {
					return;
				}
			}
			/**将每一条游客信息拿出来和后台配置的必传字段作对比*/
			if (this.passengerResult && this.passengerResult.passenger) {
				try {
					userInfo.forEach(user => {
						try {
							this.passengerResult.passenger.forEach(item => {
								if (!user[item.key]) {
									let err = `请完善游客 ${user.contact || user.userName} 的${item.text}信息`;
									this.$message.error(err);
									throw new Error(err);
								}
							});
						} catch (err) {
							throw new Error(err);
						}
					});
				} catch (err) {
					return;
				}
			}
			let data;
			/**门票的参数*/
			if (this.shopType === 'scene') {
				data = {
					// sumDays:'',
					couponCodeIds: this.couponId,
					// loginId::'',
					// shareUserId:'',
					giftCardIds: this.giftId,
					sDate: this.startDate.replace(/\//gi, '-'),
					eDate: this.endDate.replace(/\//gi, '-'),
					id: this.skuId,
					skuId: this.skuId,
					channel: 'ec',
					site: 2,
					userId: getCookie('user_id'), // 用户id
					orderNum: this.skuNum, // 商品数量
					phoneNum: contactInfo[0].telno, // 联系电话
					personAddress: contactInfo[0].detailaddress, // 联系地址
					contact: contactInfo[0].contact, // 联系人
					remark: this.$refs.settleAccountsGoodsInfo.remark, // 备注
					siteId: this.getSiteId(),
					otherParams: JSON.stringify({ ...contactInfo[0], payMode: 9 }), //其他信息-联系人信息
					passengers: JSON.stringify(userInfo)
				};
			} else if (this.shopType === 'hotel') {
				/**酒店的参数*/
				data = {
					id: this.skuId,
					channel: 'ec',
					site: 2,
					userId: getCookie('user_id'),
					skuId: this.skuId,
					orderNum: this.skuNum,
					sDate: this.startDate.replace(/\//gi, '-'),
					eDate: this.endDate.replace(/\//gi, '-'),
					otherParams: JSON.stringify({ ...contactInfo[0], payMode: 9 }), //其他信息-联系人信息
					passengers: JSON.stringify(userInfo),
					siteId: this.getSiteId(),
					remark: this.$refs.settleAccountsGoodsInfo.remark, // 备注
					contact: contactInfo[0].contact, // 联系人
					personAddress: contactInfo[0].detailaddress, // 联系地址
					phoneNum: contactInfo[0].telno, // 联系电话
					giftCardIds: this.giftId,
					couponCodeIds: this.couponId,
					sumDays: dateInterval(this.startDate, this.endDate),
					scode: this.totalPoint,
					info: {
						...this.detail,
						playMoney: this.totalPrices,
						type: 'hotel'
					}
				};
			}
			this.$api.shop_api.createOrder(data).then(res => {
				if (res.state) {
					this.$message.success('提交成功');
					this.$router.push({
						path: '/paymentSounts',
						query: {
							payMode: 9
						}
					});
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**提交实物订单*/
		addCreateOrderMult() {
			this.loading = true;
			let skus = this.list.map(item => {
				return {
					skuId: item.sku_id,
					skuNum: item.sku_num
				};
			});
			let distributionMode = '';
			distributionMode =
				this.info?.distributionType || this.$refs.logisticsInfo.formLabelAlign.distributionType;
			let otherParams = {};
			if (distributionMode == 1) {
				otherParams = {
					payMode: 9,
					provinceId: this?.offlineStore?.addrProvince || '',
					cityId: this?.offlineStore?.addrCity || '',
					countyId: this?.offlineStore?.addrCounty || ''
				};
			} else {
				otherParams = {
					payMode: 9,
					provinceId: this.info?.provinceid || '',
					cityId: this.info?.cityid || '',
					countyId: this.info?.countyid || ''
				};
			}
			if (this.$refs.paymentMode.formLabelAlign.distributionType == 3) {
				otherParams.payMode = 8;
				otherParams.financeData = { productId: this.$refs.paymentMode.financingActive.id };
			}
			let channel = this.$route.query.channel;
			if (channel == 'market') {
				otherParams.supplierId = this.$route.query.ids;
			} else {
				channel = 'ec';
			}
			otherParams.offlineStoreId = this.ckId;
			if (this.restsList.length) {
				this.restsList.forEach(item => {
					otherParams[item.key] = this.$refs.logisticsInfo.formLabelAlign[item.key];
				});
			}
			let data = {
				channel: channel,
				siteId: this.getSiteId(),
				userId: this.userId,
				skus: JSON.stringify(skus),
				contact: this.info.contact,
				phoneNum: this.info.telno,
				personAddress:
					distributionMode == 1 ? this?.offlineStore?.addrDetail : this.info?.detailaddress,
				distributionMode: distributionMode,
				remark: this.$refs.settleAccountsGoodsInfo.remark,
				otherParams: JSON.stringify(otherParams),
				couponCodeIds: this.couponId,
				giftCardIds: this.giftId
			};

			if (this.type == 2 && data.distributionMode == 1 && !this.ckId) {
				this.$message.close();
				this.$message.error('请选择自提地址');
				this.loading = false;
				return;
			}
			if (!data.personAddress) {
				this.$message.close();
				this.$message.error('请完善详细地址');
				this.loading = false;
				return;
			}
			if (
				this.$refs.paymentMode.formLabelAlign.distributionType == 3 &&
				!this.$refs.paymentMode.financingActive.id
			) {
				this.$message.close();
				this.$message.error('请先确定融资产品');
				this.loading = false;
				return;
			}
			this.$api.shop_api.createOrderMult(data).then(res => {
				if (res.state) {
					if (this.type == 1) {
						this.retailStep = 3;
						this.orderInfo = res.result;
					} else {
						this.bulkStep = 2;
					}
					this.$message.close();
					this.$message.success(res.msg);

					let ids = this.$route.query.ids;
					let type = this.$route.query.type;
					if (type == 1) {
						let params = {
							ids: ids,
							memberId: this.userId
						};
						this.$api.shop_api.batchDelete(params).then(res => {});
					}
					this.loading = false;
					this.$router.push({
						path: '/paymentSounts',
						query: {
							payMode: otherParams.payMode
						}
					});
					return;
				}
				this.$message.close();
				this.$message.error(res.msg);
				this.loading = false;
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.attract-investment {
	overflow: hidden;

	.header {
		height: 50px;
		background: #f2f4fa;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		&-conter {
			width: 1200px;
			margin: 0 auto;
			height: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.left {
				display: flex;
				align-items: center;
				.app {
					font-size: 12px;
					font-family: Source Han Sans SC-Normal, Source Han Sans SC;
					font-weight: 400;
					color: #9da5b7;
				}
				div {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #9da5b7;
					line-height: 22px;
				}
				.outLogin {
					margin-left: 52px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
				}
				.link {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
				}
			}
			.right {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #9aa3ba;
				line-height: 22px;
				margin-left: 37px;
			}
		}
	}
	.conter {
		width: 1200px;
		margin: 0 auto 60px;

		.button {
			margin-top: 15px;
			width: 100%;
			height: 70px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			span {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
			}
			.price {
				font-size: 24px;
				font-family: Rany-Medium, Rany;
				font-weight: 500;
				color: var(--brand-6, '#ca3f3b');
			}
			&-content {
				margin-left: 20px;
				padding: 24px 45px;
				background: var(--brand-6, '#ca3f3b');
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 22px;
			}
		}
	}
	.pay {
		width: 1200px;
		margin: 0 auto;
		&-header {
			margin-top: 26px;
			height: 98px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			padding: 23px 24px;
			&-title {
				font-size: 20px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #262626;
				line-height: 24px;
			}
			&-dec {
				display: flex;
				justify-content: space-between;
				align-items: center;
				div {
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
					span {
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #8c8c8c;
						line-height: 22px;
					}
					.pay-price {
						font-size: 24px;
						font-family: Rany-Medium, Rany;
						font-weight: 500;
						color: var(--brand-6, '#ca3f3b');
						line-height: 32px;
					}
				}
			}
		}
		&-type {
			margin-top: 16px;
			height: 564px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			padding: 32px 16px;
			&-title {
				font-size: 24px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #404040;
				line-height: 32px;
			}
			&-content {
				display: flex;
				&-radio {
					padding: 20px 30px;
					height: 355px;
					&-item {
						width: 712px;
						height: 64px;
						padding: 16px 20px;
						background: #ffffff;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
						border: 1px solid #d9d9d9;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top: 15px;
						.price {
							span {
								font-size: 14px;
								font-family: Source Han Sans SC-Regular, Source Han Sans SC;
								font-weight: 400;
								color: #404040;
								line-height: 22px;
							}
							&-item {
								font-size: 24px !important;
								font-family: Rany-Medium, Rany;
								font-weight: 500;
								color: var(--brand-6, '#ca3f3b') !important;
								line-height: 32px;
							}
						}
						&-nav {
							display: flex;
							align-items: center;
							div {
								font-size: 16px;
								font-family: Source Han Sans SC-Medium, Source Han Sans SC;
								font-weight: 500;
								color: #404040;
								line-height: 24px;
								margin-left: 10px;
							}
							img {
								width: 22px;
								height: 22px;
								opacity: 1;
							}
							.radio-img {
								width: 28px;
								height: 28px;
								background: #ffffff;
								opacity: 1;
								border-radius: 50%;
								border: 1px solid #d9d9d9;
								margin-right: 18px;
								position: relative;
								.img {
									position: absolute;
									left: -1px;
									bottom: -1px;
									width: 28px;
									height: 28px;
								}
							}
						}
					}
				}
				.divider {
					width: 1px;
					height: 355px;
					opacity: 1;
					border: 1px solid;
					border-image: linear-gradient(
							90deg,
							rgba(222, 225, 234, 0),
							rgba(222, 225, 234, 1),
							rgba(222, 225, 234, 0)
						)
						1 1;
				}
				.qrcode {
					margin-left: 63px;
					img {
						width: 212px;
						height: 248px;
						border-radius: 0px 0px 0px 0px;
						opacity: 1;
					}
				}
			}
		}
	}
}
</style>
