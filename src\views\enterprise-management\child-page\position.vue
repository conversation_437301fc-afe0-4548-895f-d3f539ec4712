<!-- eslint-disable vue/no-v-html -->
<template>
	<div class="main">
		<!-- 职位列表 -->
		<h3 class="form-title">
			职位列表
			<el-button class="new-btn" type="primary" @click="handlerNew">新增职位</el-button>
		</h3>
		<el-table
			ref="multipleTable"
			v-loading="tableLoading"
			:data="jobList"
			style="width: 100%"
			align="center"
			header-align="center"
			header-row-class-name="history-table"
			@selection-change="handleSelectionChange"
			@select="handleSelect"
		>
			<el-table-column v-if="jobChoiceMeeting" type="selection" width="55"></el-table-column>
			<el-table-column prop="name" label="职位名称" width="120"></el-table-column>
			<el-table-column prop="jobNature" label="工作性质">
				<template #default="{ row }">
					{{ row.jobNatureDes || getLabelByValue(jobNatureOptions, row.jobNature) }}
				</template>
			</el-table-column>
			<el-table-column prop="education" label="学历要求">
				<template #default="{ row }">
					{{ row.educationDes || getLabelByValue(educationOptions, row.education) }}
				</template>
			</el-table-column>
			<el-table-column prop="workExperience" label="工作经验" width="120">
				<template #default="{ row }">
					{{ row.workExperienceDes || getLabelByValue(workExperienceOptions, row.workExperience) }}
				</template>
			</el-table-column>
			<el-table-column prop="jobNature" width="120" label="薪资区间(千元)">
				<template #default="{ row }">
					{{ row.minMoney || 0 }}-{{ row.maxMoney || 999999999 }}
				</template>
			</el-table-column>
			<el-table-column prop="payment" width="120" label="薪资补充说明"></el-table-column>
			<el-table-column prop="postType" label="岗位类型">
				<template #default="{ row }">
					{{ row.postTypeDes || getLabelByValue(jobTypeOptions, row.postType) }}
				</template>
			</el-table-column>
			<el-table-column prop="tag" label="保障待遇" width="120">
				<template #default="{ row }">
					{{ row.tag || '-' }}
				</template>
			</el-table-column>
			<el-table-column prop="areaName" label="项目地点" width="120"></el-table-column>
			<el-table-column prop="lineType" label="招聘形式" width="100">
				<template #default="{ row }">
					<el-tag :type="row.lineType === 1 ? 'warning' : 'info'">
						{{ row.lineType === 1 ? '线下' : '线上' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="enterSchoolTime" label="进校时间" width="100">
				<template #default="{ row }">
					{{ row.enterSchoolTime || '-' }}
				</template>
			</el-table-column>
			<!-- <el-table-column label="审核状态" width="80">
				<template #default="{ row }">
					<el-tag :type="{ 0: '', 1: 'success', 9: 'info', 2: 'warning' }[row.auditStatus]">
						{{ auditStatusDict[row.auditStatus] || '待审核' }}
					</el-tag>
				</template>
			</el-table-column> -->
			<el-table-column label="发布状态" fixed="right" width="80">
				<template #default="{ row }">
					<el-tag :type="{ 0: '', 1: 'success' }[row.status]">
						{{ row.statusStr }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column v-if="!jobChoiceMeeting" fixed="right" label="操作" width="190">
				<template #default="{ row }">
					<el-button type="text" size="small" class="del-btn" @click="delClick(row)">
						删除
					</el-button>
					<el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
					<el-button type="text" size="small" @click="handleView(row)">查看</el-button>
					<el-button
						v-if="row.auditStatus === 1"
						type="text"
						size="small"
						@click="handleClose(row)"
					>
						{{ row.status ? '关闭' : '开启' }}
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			class="pagination"
			background
			layout="prev, pager, next,jumper"
			:total="paginationConfig.total"
			:current-page.sync="paginationConfig.pageNum"
			:page-size.sync="paginationConfig.pageSize"
			@current-change="handleCurrentChange"
		/>
		<!--新建/更新岗位-->
		<position-dialog ref="positionDialog" @positionCallback="positionCallback" />
		<!--查看岗位信息-->
		<el-dialog
			title="查看岗位信息"
			width="800px"
			:visible.sync="viewJobDialog.visible"
			:close-on-click-modal="false"
		>
			<base-description :column="2" :data-source="viewJobDialog.row">
				<template #lineType="{ row }">
					<el-tag :type="row.lineType === '1' ? 'warning' : 'info'">
						{{ row.lineType === '1' ? '线下' : '线上' }}
					</el-tag>
				</template>
				<template #enterSchoolTime="{ row }">
					{{
						row.lineType === '1'
							? row.enterSchoolTime
								? formatDate(row.enterSchoolTime)
								: '待定'
							: '-'
					}}
				</template>
			</base-description>
		</el-dialog>
	</div>
</template>

<script>
import { getDictionaryByCode } from '@/utils';
import BaseDescription from '@/components/description';
import PositionDialog from './components/positionDialog';
export default {
	name: 'Position',
	components: { BaseDescription, PositionDialog },
	props: {
		// 是否为双选会状态，双选会状态需要为多选，并且部分操作按钮无法操作
		jobChoiceMeeting: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			auditStatusDict: {
				0: '待审核',
				1: '已审核',
				2: '审核未通过',
				9: '草稿',
				11: '待招就处审核'
			},

			targetColSchMaj: [],
			tableLoading: true,
			orgCode: '',
			educationOptions: [],
			jobNatureOptions: [],
			jobTypeOptions: [],
			workExperienceOptions: [],
			addressOptions: [],
			paginationConfig: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			jobList: [], //职位列表
			viewJobDialog: {
				visible: false,
				row: []
			},
			setFlag: false, //是否进行主动设置的判断
			jobSelectedList: [], //回复选择的职位多选数据暂存
			jobSelectedIdsList: new Set(), //回复选择的职位多选数据暂存
			selectRowData: [], //表格多选时选中的数据
			selectRowIds: [] //表格多选时选中的数据id
		};
	},
	created() {
		this.getEducationList();
		this.getJobNatureList();
		this.getJobTypeList();
		this.getWorkExperienceList();
		this.getJobList();
		// this.getRegion();
		// this.getOrgListF(); //获取学校学院专业信息
	},
	methods: {
		// 格式化日期时间
		formatDate(dateString) {
			if (!dateString) return '-';
			const date = new Date(dateString);
			if (isNaN(date.getTime())) return dateString;

			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},
		// async getRegion() {
		// 	const { results } = await this.$api.employment_api.getSubRegion();
		// 	this.addressOptions = this.handleDealTree(results);
		// },
		// handleDealTree(nodes) {
		// 	return nodes.map(item => {
		// 		if (Array.isArray(item.children) && item.children.length > 0) {
		// 			item.children = this.handleDealTree(item.children);
		// 		} else {
		// 			delete item.children; // 删除空的 children 属性
		// 		}
		// 		return item;
		// 	});
		// },
		// validateMinMoney(rule, value, callback) {
		// 	if (Number(value) > Number(this.updateJobDialog.form.maxMoney)) {
		// 		callback(new Error('最低薪资不能大于最高薪资'));
		// 	}
		// 	callback();
		// },
		// validateMaxMoney(rule, value, callback) {
		// 	if (Number(value) < Number(this.updateJobDialog.form.minMoney)) {
		// 		callback(new Error('最高薪资不能小于最低薪资'));
		// 	}
		// 	callback();
		// },
		async getJobList() {
			this.tableLoading = true;
			this.stagingData();
			try {
				const res = await this.$api.enterprise_center.getJobList({
					pageNum: this.paginationConfig.pageNum,
					pageSize: this.paginationConfig.pageSize,
					status: this.jobChoiceMeeting ? '1' : null,
					collegeCode: this.orgCode ? this.orgCode : null
				});
				if (res.results) {
					this.jobList = res.results?.records;
					this.paginationConfig.total = res.results?.total || 0;
					this.$nextTick(() => {
						// 列表刷新之后需要手动触发一下设置选中数据的方法，回显选中状态
						this.setSelectTable([...this.jobSelectedIdsList], this.jobSelectedList);
					});
				} else {
					this.$message.error(res.msg);
				}
			} catch (error) {
				console.log(error);
			} finally {
				this.tableLoading = false;
			}
		},
		/**
		 * @description 删除操作
		 * */
		delClick(row) {
			this.$confirm('确认删除该职位？')
				.then(_ => {
					this.$api.enterprise_center.delJobInfo({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getJobList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		/**
		 * @description 新增操作
		 */
		handlerNew() {
			this.$refs.positionDialog.handlerNew();
		},
		/**
		 * @description 编辑操作
		 * */
		handleEdit(row, title) {
			this.$refs.positionDialog.handleEdit(row, title);
		},
		/**
		 * @description 查看操作
		 * */
		handleView(row) {
			this.handleEdit(row, '查看职位');
		},
		positionCallback() {
			this.getJobList();
			this.$emit('positionCallback');
		},
		// 处理职业面向工具
		// toolTargetColSchMaj() {
		// 	this.targetColSchMaj.forEach(item => {
		// 		if (item.targetCollege) {
		// 			let collegeItem = this.collegeList.filter((item, index) => {
		// 				return item.value == item.targetCollege;
		// 			});
		// 			this.getMajorList(collegeItem.code);
		// 		}
		// 	});
		// },
		/**
		 * @description 关闭职位	操作
		 * */
		handleClose(row) {
			let msg = row.status ? '确认关闭该职位' : '确认开启该职位';
			this.$confirm(msg)
				.then(_ => {
					this.$api.enterprise_center.updateJobStatus({ id: row.id }).then(res => {
						if (res.success) {
							this.$message.success('操作成功');
							this.getJobList();
						} else {
							this.$message.error(res.msg);
						}
					});
				})
				.catch(_ => {});
		},
		handleCurrentChange() {
			this.getJobList();
		},
		// 数据字典查询学历要求
		async getEducationList() {
			const dicts = await getDictionaryByCode(['post_education']);
			this.educationOptions = dicts.post_education;
		},
		// 数据字典查询工作性质
		async getJobNatureList() {
			const dicts = await getDictionaryByCode(['post_job_nature']);
			this.jobNatureOptions = dicts.post_job_nature;
		},
		// 数据字典查询岗位类型
		async getJobTypeList() {
			const dicts = await getDictionaryByCode(['post_job_type']);
			this.jobTypeOptions = dicts.post_job_type;
		},
		// 数据字典查询工作经验
		async getWorkExperienceList() {
			const dicts = await getDictionaryByCode(['post_work_experience']);
			this.workExperienceOptions = dicts.post_work_experience;
		},
		// 根据值去找label
		getLabelByValue(dict, value) {
			return dict.find(option => option.cciValue == value)?.shortName;
		},
		// 触发单个选择时的操作
		handleSelect(selection, row) {
			const isSelected = selection.some(item => item.id === row.id);
			if (isSelected) {
				// 选中的意思
			} else {
				// 取消选中的意思
				this.removeData(row.id);
			}
		},
		// 多选操作
		handleSelectionChange(val) {
			if (!this.jobChoiceMeeting) {
				return false;
			} else {
				console.log(val, '------多选操作-------');
				this.selectRowData = val;
				this.stagingData();
			}
		},
		// 表格设置选中项
		setSelectTable(rowIds, rows) {
			// 手动设置一波参数，主要是用于在列表刷新时（常见为弹窗第一次打开的时候，编辑之后列表刷新），接口响应未及时导致后续this.jobList.循环无法正常进入，需要重新进入时候的数据依赖，也算是一种数据暂存
			this.selectRowData = rows;
			this.jobSelectedList = rows;
			this.jobSelectedIdsList = new Set(rowIds);
			this.$refs.multipleTable && this.$refs.multipleTable.clearSelection();
			if (rowIds.length) {
				this.jobList.forEach(row => {
					if (rowIds.includes(row.id)) {
						this.$refs.multipleTable.toggleRowSelection(row);
						if (!this.jobSelectedIdsList.has(row.id)) {
							this.jobSelectedList.push(row);
							this.jobSelectedIdsList.add(row.id);
						}
					}
				});
			}
		},
		// 暂存数据操作，主要是处理表格翻页或者重新加载这种情况下保存已选表格数据
		stagingData() {
			this.jobSelectedIdsList = this.selectRowData.reduce((acc, cur) => {
				!acc.has(cur.id) && acc.add(cur.id) && this.jobSelectedList.push(cur);
				return acc;
			}, this.jobSelectedIdsList);
		},
		// 取消选中操作之后的数据更新，暂存一份当前的选中id集合，需要深拷贝一次，数据隔离
		removeData(id) {
			let idIndex = [...this.jobSelectedIdsList].indexOf(id);
			this.jobSelectedIdsList.delete(id);
			this.jobSelectedList.splice(idIndex, 1);
		},
		// 主动获取表单现在选中的数据
		getSelectedData() {
			// this.jobSelectedList = [...this.selectRowData];
			return this.jobSelectedList;
		},
		open(meetingData) {
			this.jobList = [];
			// 招就处发起的邀请， 职位没有限定
			if ('REO' !== meetingData.orgCode) {
				this.orgCode = meetingData.orgCode;
			} else {
				this.orgCode = '';
			}

			this.getJobList();
		},
		init() {
			this.getJobList();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 20px;
	background: #fff;
}

::v-deep .el-dialog__body {
	height: 80vh;
	overflow: auto;

	.business-info {
		.add {
			float: right;
			font-size: 18px;
			top: 6px;
			padding-top: 3px;
			color: var(--brand-5);
		}

		.el-select .el-tag {
			.el-select__tags-text {
				width: 137px !important;
				display: inline-block;
				overflow-x: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				cursor: pointer;
			}

			.el-tag__close {
				margin-bottom: 12px;
			}
		}
	}
}

.form-title {
	height: 28px;
	font-size: 20px;
	font-family: Source Han Sans SC-Bold, Source Han Sans SC;
	font-weight: bold;
	color: #404040;
	line-height: 28px;
	padding-left: 13px;
	position: relative;
	margin-top: 0;
	margin-bottom: 34px;

	&::after {
		display: inline-block;
		content: '';
		width: 6px;
		height: 20px;
		background: var(--brand-6, #0076e8);
		position: absolute;
		left: 0;
		top: 50%;
		margin-top: -10px;
	}
}

.del-btn {
	color: #de2d2d;
}

.space {
	display: inline-block;
	width: 1px;
	height: 10px;
	background: #dfdfdf;
	margin: 0 10px;
}

.new-btn {
	margin-left: 10px;
}

.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
}

.tips {
	font-size: 12px;
	color: #333;
	height: 20px;
	line-height: 20px;
	display: flex;
	align-items: center;
}

.info-save {
	text-align: center;
}

.el-form-item__content .el-input-group {
	vertical-align: middle;
}

::v-deep .el-input.is-disabled .el-input__inner {
	color: rgba(0, 0, 0, 0.4);
}
</style>
