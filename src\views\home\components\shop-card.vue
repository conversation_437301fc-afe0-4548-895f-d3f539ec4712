<!--
 @desc:技能商城卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<el-image
			class="banner"
			:src="changeImageSize($judgeFile(cardData.coverUrl), '800', '460')"
			lazy
			fit="cover"
		></el-image>
		<!-- <img src="@imgs/home/<USER>" alt="" /> -->
		<div class="content-box">
			<span>{{ cardData.productName }}</span>
			<!-- <p>{{ cardData.content ? cardData.content : '暂无简介' }}</p> -->
			<footer>
				<p class="money-box">
					¥
					<span>{{ cardData.sellPrice.toFixed(2) }}</span>
				</p>
				<!-- <p class="shop-name">
					<img src="@imgs/home/<USER>" alt="" />
					{{ cardData.shopName }}
				</p> -->
			</footer>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {
					coverUrl: '',
					productName: '宜宾燃面',
					content:
						'宜宾燃面是宜宾的传统面食之一，以炒面为主料，加入各种配菜和肉类。由于燃面的制作工艺独特，只能在燃烧的竹器上进行制作，所以也叫做“风味竹燃面”。宜宾燃面色香味俱佳，多种口感混合在一起，让人难以忘怀。',
					sellPrice: ' 35.00',
					shopName: '宜宾技状元数字化校园'
				};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		clickCard() {
			this.$emit('clickCard', { cardName: 'shop', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	width: 225px; // 400px;
	height: 215px; // 320px;
	border-radius: 8px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	.banner {
		width: 100%;
		height: 138px; // 230px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		background: #4887f4;
		object-fit: cover;
	}
	.content-box {
		@include flexBox(space-between, flex-start);
		flex-direction: column;
		padding: 5px 20px;
		height: calc(100% - 230px);
		& > span {
			font-size: 14px;
			font-weight: 400;
			color: #333333;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
			// height: 80px;
			flex: 0 0 36px;
		}
		& > p {
			font-size: 12px;
			font-weight: 400;
			color: #7a8392;
			width: 100%;
			// overflow: hidden;
			// text-overflow: ellipsis;
			// white-space: nowrap;
		}
		footer {
			@include flexBox(space-between);
			width: 100%;

			.money-box {
				font-size: 14px;
				font-weight: 400;
				color: #ff0000;
				margin-top: 5px;
				span {
					font-size: 16px;
				}
			}
			.shop-name {
				@include flexBox();

				font-size: 12px;
				font-weight: 400;
				color: #7a8392;
				img {
					width: 18px;
					height: 16px;
					margin-right: 6px;
				}
			}
		}
	}
}
</style>
