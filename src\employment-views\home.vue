<template>
	<div class="employment">
		<!-- 就业创业首页 -->
		<div class="carousel-box">
			<el-carousel height="400px">
				<el-carousel-item v-for="(item, index) in carouselList" :key="index">
					<img class="carousel-img" :src="$judgeFile(item.url)" alt="" />
				</el-carousel-item>
			</el-carousel>
		</div>
		<!-- 内容展示区域 -->
		<div class="main">
			<!-- 主要功能区域 -->
			<ul class="tool-box">
				<li v-for="(item, index) in toolList" :key="index" class="tool-item">
					<el-image class="item-img" fit="contain" :src="item.img" lazy></el-image>
					<div class="item-right">
						<p class="item-name">{{ item.name }}</p>
						<span class="item-nums">
							{{ item.nums }}
						</span>
						<span>{{ item.unit }}</span>
					</div>
				</li>
			</ul>
			<!-- 创业服务 -->
			<div class="services-box">
				<div class="services-left">
					<div class="title-box">
						<div class="box-left">
							<span class="title-name">创业服务</span>
							<span class="sub-title">创业从这里开始,轻松解决</span>
						</div>
						<div class="box-right">
							<span class="more-btn" @click="jumpPage(`/venture-services?typeNum=0`)">
								更多
								<i class="el-icon el-icon-d-arrow-right"></i>
							</span>
						</div>
					</div>
					<ul class="left-list-box">
						<li
							v-for="(item, index) in servicesLeftList"
							:key="index"
							class="list-item"
							@click="jumpPage(`/services-type-list?typeId=${item.id}`)"
						>
							<el-image class="item-img" :src="getImgUrl(item.icon)" lazy></el-image>
							<div class="list-right">
								<p class="list-right-name">{{ item.name }}</p>
								<span class="list-right-sub">{{ item.subName }}</span>
							</div>
						</li>
						<!-- 固定一个招聘位置 -->
						<li
							class="list-item"
							@click="jumpPage(`/independentPersonal/enterprise?type=position`)"
						>
							<img
								class="item-img"
								:src="require('@/assets/employment-images/recruitment-img.png')"
								alt=""
							/>
							<div class="list-right">
								<p class="list-right-name">发布招聘</p>
								<span class="list-right-sub">Post recruitment</span>
							</div>
						</li>
					</ul>
				</div>
				<div class="services-right">
					<div class="title-box">
						<div class="box-left">
							<span class="title-name">名师指导</span>
							<span class="sub-title">专业导师创业指导，让你在成功的路上，快人一步</span>
						</div>
						<div class="box-right">
							<span
								class="more-btn"
								@click="jumpPage('/mentor-list?nodeCode=pioneerMentorTeacher')"
							>
								更多
								<i class="el-icon el-icon-d-arrow-right"></i>
							</span>
						</div>
					</div>
					<ul v-if="servicesRightList.length" class="right-list-box">
						<li
							v-for="(item, index) in servicesRightList"
							:key="index"
							class="right-list-item"
							@click="jumpPage(`/services-detail?type=mentor&id=${item.id}`)"
						>
							<el-image
								class="item-img"
								fit="contain"
								:src="getYbzyImg(item.coverImg)"
								lazy
							></el-image>
							<p class="list-right-name">{{ item.title }}</p>
							<span class="list-right-desc over1">{{ item.abstract }}</span>
						</li>
					</ul>
					<Empty v-else :tips="'暂无数据'" />
				</div>
			</div>
			<!-- 推荐就业岗位 -->
			<div class="positions-box">
				<div class="title-box">
					<div class="box-left">
						<span class="title-name">推荐就业岗位</span>
						<span class="sub-title">高收入，好平台，新机遇</span>
					</div>
					<div class="box-right">
						<ul class="tabs-list">
							<li
								v-for="(item, index) in tabList.slice(0, 7)"
								:key="index"
								:class="['tabs-item', tabActive == item.cciValue ? 'item-active' : '']"
								@click="tabClick(item)"
							>
								{{ item.shortName }}
							</li>
						</ul>
						<div class="pagination-box">
							<span class="prev-btn"><i class="el-icon el-icon-arrow-left icon"></i></span>
							<span class="space-btn" @click="jumpPage('/job-list')">
								<i class="el-icon el-icon-more"></i>
							</span>
							<span class="next-btn"><i class="el-icon el-icon-arrow-right icon"></i></span>
						</div>
					</div>
				</div>
				<img
					class="positions-banner"
					src="@/assets/employment-images/positions-banner.png"
					alt=""
				/>
				<ul v-if="positionList.length" class="position-list">
					<li
						v-for="(item, index) in positionList"
						:key="index"
						class="list-item"
						@click="jumpPage(`/job-detail?id=${item.id}`)"
					>
						<p class="item-title">
							<span class="name over1">
								{{ item.name }}
								<img
									v-if="item.isDualSelect"
									class="tag-img"
									:src="require('@/assets/employment-images/tag-samll.png')"
									alt=""
								/>
							</span>
							<span class="price">
								<!-- {{ item.salaryStructure }} -->
								<span>{{ item.minMoney || 0 }}-{{ item.maxMoney || 0 }}k</span>
								<!-- <span v-if="item.payment">/{{ item.payment }}</span> -->
							</span>
						</p>
						<span class="item-position over1">
							{{ item.areaName }}
						</span>

						<p class="item-position">
							<el-tag v-if="item.lineType === 1" size="mini" type="success">线下</el-tag>
							<el-tag v-else size="mini" type="primary">线上</el-tag>
							<el-tooltip v-if="item.lineType === 1" effect="dark" placement="top">
								<div slot="content">
									进校时间：{{ item.enterSchoolTime || '-' }}
									<br />
									招聘地点：{{ item.offlinePlace || '-' }}
								</div>
								<span class="ellipsis">
									进校时间：{{ (item.enterSchoolTime || '-') + ' / ' }}招聘地点：{{
										item.offlinePlace || '-'
									}}
								</span>
							</el-tooltip>
						</p>
						<p class="over1">
							<span
								v-for="(labelItem, labelIndex) in item.tags"
								:key="labelIndex"
								class="item-labels"
							>
								{{ labelItem }}
							</span>
						</p>
						<div class="com-box">
							<img
								v-if="item.enterprise.logo"
								class="com-img"
								:src="getImgUrl(item.enterprise.logo)"
								alt=""
							/>
							<img
								v-else
								class="com-img"
								:src="require('@/assets/employment-images/com-defalut-img.jpg')"
								alt=""
							/>
							<span class="com-name over1">
								{{ item.enterprise.corpName }}
							</span>
							<span class="com-base over1">
								{{ item.enterprise.industryField | enterpriseTypeStr(corpIndustryList) }}
								<span v-if="item.enterprise.peopleNumCode">
									/{{ item.enterprise.peopleNumCode | enterpriseTypeStr(peopleNumCodeList) }}
								</span>
							</span>
						</div>
					</li>
				</ul>
				<Empty v-else :tips="'暂无数据'" />
			</div>
			<!-- 就业服务 -->
			<div class="employment-box">
				<div class="title-box">
					<div class="box-left">
						<span class="title-name">就业服务</span>
						<span class="sub-title">全方位就业服务，分析各行业未来可发展性，了解市场需求</span>
					</div>
					<div class="box-right">
						<span class="more-btn" @click="jumpPage('/venture-services?typeNum=1')">
							更多
							<i class="el-icon el-icon-d-arrow-right"></i>
						</span>
					</div>
				</div>
				<div class="employment-content-box">
					<div class="left-box">
						<div
							v-for="(item, index) in employment.slice(0, 2)"
							:key="index"
							class="item-box"
							@click="employmentJumpPage(item)"
						>
							<el-image class="item-img" fit="contain" :src="item.img" lazy></el-image>
							<div class="masker-box">
								<p class="item-name">{{ item.name }}</p>
								<p class="item-desc">
									<span>{{ item.desc }}</span>
									<i class="el-icon el-icon-d-arrow-right"></i>
								</p>
							</div>
						</div>
					</div>
					<div class="right-box">
						<div
							v-for="(item, index) in employment.slice(2, 4)"
							:key="index"
							class="item-box"
							@click="employmentJumpPage(item)"
						>
							<el-image class="item-img" fit="contain" :src="item.img" lazy></el-image>
							<div class="masker-box">
								<p class="item-name">{{ item.name }}</p>
								<p class="item-desc">
									<span>{{ item.desc }}</span>
									<i class="el-icon el-icon-d-arrow-right"></i>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 创业项目库 -->
			<div class="project-box">
				<div class="title-box">
					<div class="box-left">
						<span class="title-name">创业项目库</span>
						<span class="sub-title">优质项目案例，带你了解项目整个流程，“创业”也不是那么难</span>
					</div>
					<div class="box-right">
						<span class="more-btn" @click="jumpPage('/venture-services?type=project')">
							更多
							<i class="el-icon el-icon-d-arrow-right"></i>
						</span>
					</div>
				</div>
				<ul v-if="projectList && projectList.length" class="project-list">
					<li
						v-for="(item, index) in projectList"
						:key="index"
						class="project-item"
						@click="jumpPage(`/services-detail?type=project&id=${item.id}`)"
					>
						<el-image class="item-img" fit="contain" :src="getImgUrl(item.cover)" lazy></el-image>
						<div class="item-base">
							<p class="name over1">{{ item.name }}</p>
							<span class="desc over2">{{ item.profiles }}</span>
						</div>
					</li>
				</ul>
				<Empty v-else :tips="'暂无数据'" />
			</div>
		</div>
	</div>
</template>

<script>
import PreviewAdjunctMixin from './mixin/previewAdjunct';
import { getDictionaryByCode } from '@/utils';
import { baseUrl } from '@/config';
export default {
	filters: {
		enterpriseTypeStr(value, filterList) {
			let str = '';
			for (let item of filterList) {
				if (item.cciValue == value && value) {
					str = item.shortName;
				}
			}
			return str || value || '-';
		}
	},
	mixins: [PreviewAdjunctMixin],
	data() {
		return {
			carouselList: [], //轮播
			toolList: [
				{
					img: require('@/assets/employment-images/tool-enterprise.png'),
					name: '入驻企业',
					nums: '0',
					unit: '家'
				},
				{
					img: require('@/assets/employment-images/tool-interview.png'),
					name: '在线面试',
					nums: '0',
					unit: '+'
				},
				{
					img: require('@/assets/employment-images/tool-fund.png'),
					name: '创业基金',
					nums: '0',
					unit: 'W+'
				},
				{
					img: require('@/assets/employment-images/tool-master.png'),
					name: '创业导师',
					nums: '0',
					unit: '+'
				},
				{
					img: require('@/assets/employment-images/tool-positions.png'),
					name: '招聘岗位',
					nums: '0',
					unit: '个'
				}
			], // 分类数据集合数据
			servicesLeftList: [], // 创业服务
			employmentServiceList: [], //就业服务
			servicesRightList: [], // 名师指导
			positionList: [], // 推荐就业岗位
			tabList: [
				{
					shortName: '推荐',
					cciValue: ''
				}
			], //推荐就业类型选择数据
			peopleNumCodeList: [], //人数规模
			corpIndustryList: [], //行业
			tabActive: '',
			e_guide: '', //就业指导的typeId
			e_example: '', //就业案例的typeId
			employment: [
				{
					img: require('@/assets/employment-images/employment1.png'),
					name: '就业培训',
					url: '/course-list',
					desc: '专业导师培训，就业快人一步'
				},
				{
					img: require('@/assets/employment-images/employment2.png'),
					name: '就业指导',
					code: 'e_guide',
					url: `/services-type-list?typeNum=1&typeId=`,
					desc: '全方位就业指导，了解市场行业需求'
				},
				{
					img: require('@/assets/employment-images/employment3.png'),
					name: '就业案例解析',
					code: 'e_example',
					url: `/services-type-list?typeNum=1&typeId=`,
					desc: '多个就业案例，体会就业求职技巧'
				},
				{
					img: require('@/assets/employment-images/employment4.png'),
					name: '实习网点查询',
					url: '/internship-network',
					desc: '分析城市企业，了解各行业文化'
				}
			], //就业服务
			projectList: [] // 创业项目库
		};
	},
	mounted() {
		this.getAdvertsByCode();
		this.getHomeStatistics(); //获取顶部统计数据展示
		this.getFundInfo(); //
		this.getServiceTypeList();
		this.getInformation(); //名师指导
		// this.getMentorList();
		// this.getProjectList();
		this.findSysCode(); //岗位类型 //人数规模 //行业
		this.getJobCoPostList(); //职位列表分页接口
		this.getEmploymentServiceList(); //获取就业服务类型
		this.getProjectList(); //创业项目表分页接口
	},
	methods: {
		jump() {
			// /information-detail?id=${item.id}&code=${item.nodeCode}
		},
		/**
		 * @description 顶部轮播
		 * */
		async getAdvertsByCode() {
			const { result } = await this.$api.shop_api.getAdvertsByCode({
				sysCode: 'pc_commodity_top_top',
				siteId: this.getSiteId() // 租户id
			});
			this.carouselList = result.adData || [];
		},
		// 获取顶部统计数据展示
		getHomeStatistics() {
			this.$api.employment_api.getHomeStatistics().then(res => {
				let results = res.results;
				let listStr = ['enterpriseNum', 'interviewNum', 'moneyCount', 'mentorNum', 'postNum'];
				if (res.rCode == 0) {
					this.toolList.forEach((item, index) => {
						item.nums = results[listStr[index]] || 0;
					});
				}
			});
		},
		/**
		 * @description 获取创业基金数据
		 * */
		getFundInfo() {
			// 主应用在路由取参，子应用就取组件传参
			let params = {
				id: '7119576191144562688',
				tenantId: this._userinfo.tenantId || this.$tenantId,
				code: 'fundManage'
			};
			this.$api.information_api.detail(params).then(res => {
				this.toolList[2].nums = res.results?.amount || 0;
			});
		},
		/**
		 * @description 获取创业类型分页接口 0是创业 1是就业
		 * */
		getServiceTypeList() {
			let param = {
				pageNum: 1,
				pageSize: 7,
				type: 0
			};
			this.$api.employment_api.serviceTypeList(param).then(res => {
				this.servicesLeftList = res?.results?.records || [];
			});
		},
		/**
		 * @description 名师指导获取
		 * */
		getInformation() {
			let data = {
				nodeCode: 'pioneerMentorTeacher',
				tenantId: this._userinfo.tenantId || this.$tenantId,
				pageNum: 1,
				pageSize: 4
			};
			this.$api.information_api.paging(data).then(res => {
				this.servicesRightList = res?.results?.records || [];
			});
		},
		getYbzyImg(imgUrl) {
			if (imgUrl) {
				return `${baseUrl}/ybzyfile${imgUrl}`;
			}
		},
		/**
		 * @description 获取就业类型分页接口 0是创业 1是就业
		 * */
		getEmploymentServiceList() {
			let param = {
				pageNum: 1,
				pageSize: 8,
				type: 1
			};
			this.$api.employment_api.serviceTypeList(param).then(res => {
				// this.employmentServiceList = res?.results?.records || [];
				let list = res?.results?.records || [];
				list.map((item, index) => {
					if (item.code == 'e_guide' || item.code == 'e_example') {
						this[item.code] = item.id;
					}
				});
			});
		},
		/*
		 * @description 创业导师分页接口
		 * */
		getMentorList() {
			let param = {
				pageNum: 1,
				pageSize: 20
			};
			this.$api.employment_api.jobCoPioneerMentor(param).then(res => {});
		},
		/*
		 * @description 创业项目表分页接口
		 * */
		// getProjectList() {
		// 	let param = {
		// 		pageNum: 1,
		// 		pageSize: 8
		// 	};
		// 	this.$api.employment_api.jobStudentProjectList(param).then(res => {
		// 		this.projectList = res?.results?.records || [];
		// 	});
		// },
		/**
		 * @description 查询数据字典
		 * */
		async findSysCode() {
			// let param = {
			// 	sysAppCode: code
			// };
			// this.$api.employment_api.findSysCode(param).then(res => {
			// 	this[tabList] = this[tabList].concat(res?.results || []);
			// });
			const dicts = await getDictionaryByCode([
				'post_job_type',
				'post_company_scale',
				'post_industry_area'
			]);
			this.tabList = dicts.post_job_type || [];
			this.peopleNumCodeList = dicts.post_company_scale || [];
			this.corpIndustryList = dicts.post_industry_area || [];
		},
		/**
		 * @descrtiption 岗位分页接口
		 * */
		getJobCoPostList() {
			let param = {
				pageNum: 1,
				pageSize: 9,
				postType: this.tabActive,
				auditStatus: 1 //审核状态(0.待审核、1.审核通过、2.驳回)
			};
			this.$api.employment_api.jobCoPostList(param).then(res => {
				this.positionList = res?.results?.records || [];
			});
		},
		/*
		 * @description 创业项目表分页接口
		 * */
		getProjectList() {
			let param = {
				pageNum: 1,
				pageSize: 8,
				auditStatus: 1
			};
			this.$api.employment_api.jobStudentProject(param).then(res => {
				this.projectList = res?.results?.records || [];
			});
		},
		/**
		 * @description 推荐就业类型点击切换
		 * */
		tabClick(item) {
			this.tabActive = item.cciValue;
			this.getJobCoPostList();
		},
		// 就业服务专项跳转
		employmentJumpPage(item) {
			let url = item.url;
			if (item.code) {
				url += this[item.code];
			}
			this.jumpPage(url);
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>

<style lang="scss" scoped>
.employment {
	background: #f9f9f9;
}

.carousel-box {
	width: 100%;
	height: 400px;

	.carousel-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

::v-deep .el-carousel__button {
	width: 10px;
	height: 10px;
	background: #ffffff;
	border-radius: 50%;
}

::v-deep .el-carousel__indicators .is-active {
	.el-carousel__button {
		width: 22px;
		height: 10px;
		background: #0076e8;
		border-radius: 5px;
	}
}

.main {
	width: 1260px;
	margin: 0 auto;
	padding: 30px 0;
}

.title-box {
	display: flex;
	justify-content: space-between;

	.title-name {
		font-size: 24px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		color: #000000;
	}

	.sub-title {
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #999999;
		margin-left: 9px;
	}

	.box-right {
		display: flex;
		align-items: center;
	}

	.tabs-list {
		// border: 1px solid red;
		display: flex;

		.tabs-item {
			display: inline-block;
			padding: 12px 20px;
			background: #ffffff;
			border-radius: 18px;
			font-size: 14px;
			color: #747d85;
			margin-left: 10px;
			cursor: pointer;
		}

		.item-active {
			color: #0076e8;
		}
	}

	.pagination-box {
		display: flex;

		.prev-btn,
		.next-btn {
			display: flex;
			width: 36px;
			height: 36px;
			background: #ffffff;
			border-radius: 50%;
			color: #747d85;
			justify-content: center;
			align-items: center;
			margin-left: 10px;
			cursor: pointer;

			&:hover {
				color: #0076e8;
			}
		}

		.space-btn {
			width: 70px;
			height: 36px;
			background: #ffffff;
			border-radius: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #747d85;
			margin-left: 10px;
		}
	}

	.more-btn {
		display: inline-block;
		width: 70px;
		height: 36px;
		font-size: 14px;
		line-height: 36px;
		text-align: center;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #747d85;
		background: #ffffff;
		border-radius: 18px;
		cursor: pointer;

		&:hover {
			color: #0076e8;
		}
	}
}

.tool-box {
	width: 100%;
	height: 48px;
	display: flex;
	padding: 0 30px;
	justify-content: space-between;

	.tool-item {
		display: flex;
		flex-direction: row;
		font-size: 14px;
		font-weight: 400;
		color: #7a8392;
	}

	.item-img {
		width: 48px;
		height: 48px;
		object-fit: contain;
		margin-right: 10px;
	}

	.item-right {
		.item-nums {
			font-size: 24px;
			font-family: DINCond-Bold;
			font-weight: 400;
			color: #0076e8;
			margin-right: 6px;
		}
	}
}

.services-box {
	margin-top: 50px;
	display: flex;
	justify-content: space-between;

	.services-left,
	.services-right {
		width: 620px;
	}

	.left-list-box,
	.right-list-box {
		margin-top: 32px;
		display: flex;
		flex-wrap: wrap;
	}

	.list-item {
		width: 300px;
		height: 130px;
		background: #ffffff;
		border-radius: 5px;
		padding: 30px;
		margin-bottom: 20px;
		display: flex;
		align-items: center;
		cursor: pointer;

		.item-img {
			width: 69px;
			height: 69px;
			object-fit: contain;
			margin-right: 19px;
		}

		.list-right {
			&-name {
				font-size: 18px;
				color: #333333;
			}

			&-sub {
				font-size: 14px;
				color: #999999;
			}
		}

		&:nth-child(2n) {
			margin-left: 18px;
		}
	}

	.right-list-item {
		width: 300px;
		height: 280px;
		background: #ffffff;
		border-radius: 5px;
		padding: 30px 15px;
		margin-bottom: 20px;
		display: flex;
		flex-direction: column;
		align-items: center;
		cursor: pointer;

		.item-img {
			width: 150px;
			height: 150px;
			border-radius: 50%;
			overflow: hidden;
			object-fit: cover;
		}

		.list-right-name {
			font-size: 18px;
			color: #333333;
			margin-top: 17px;
		}

		.list-right-desc {
			font-size: 14px;
			color: #999999;
			margin-top: 18px;
		}

		&:nth-child(2n) {
			margin-left: 18px;
		}
	}
}

.positions-box {
	margin-top: 36px;

	.positions-banner {
		height: 86px;
		margin-top: 24px;
	}

	.position-list {
		display: flex;
		flex-wrap: wrap;
	}

	.list-item {
		width: 406px;
		height: 186px;
		background: #ffffff;
		border-radius: 8px;
		margin-right: 19px;
		margin-top: 20px;
		padding: 19px;
		position: relative;
		cursor: pointer;

		.item-title {
			display: flex;
			justify-content: space-between;

			.name {
				font-size: 18px;
				color: #333333;
				position: relative;
				padding-right: 52px;
				margin-right: 6px;
			}

			.tag-img {
				height: 24px;
				position: absolute;
				right: 0;
				top: 0;
			}

			.price {
				font-size: 16px;
				color: #fe574a;
				flex-shrink: 0;
			}
		}

		.item-position {
			font-size: 12px;
			color: #999999;
			display: inline-block;
			width: 100%;

			&:first-child {
				margin-top: 12px;
			}

			.ellipsis {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 321px;
				display: inline-block;
				line-height: 1;
				margin-left: 6px;
			}
		}

		.item-labels {
			display: inline-block;
			background: #f5f5f5;
			padding: 4px 10px;
			border-radius: 4px;
			margin-right: 10px;
			font-size: 12px;
			color: #666666;
			margin-top: 8px;
		}

		.com-box {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 60px;
			// background: #f9fcfb;
			background: url('~@/assets/employment-images/com-bg.png') center;
			background-size: cover;
			padding: 15px 20px;
			font-size: 12px;
			color: #666666;
			display: flex;
			align-items: center;

			.com-img {
				width: 30px;
				height: 30px;
				background: #f5f5f5;
				border-radius: 4px;
				margin-right: 13px;
				flex-shrink: 0;
				object-fit: cover;
			}

			.com-name {
				display: inline-block;
				width: 100%;
			}

			.com-base {
				flex-shrink: 0;
				margin-left: 5px;
			}
		}

		&:nth-child(3n) {
			margin-right: 0;
		}
	}
}

.employment-box {
	margin-top: 56px;

	.employment-content-box {
		margin-top: 32px;
		display: flex;
		justify-content: space-between;
	}

	.left-box,
	.right-box {
		width: 620px;
		height: 430px;
		display: flex;
		justify-content: space-between;
	}

	.item-box {
		position: relative;
	}

	.left-box {
		.item-box {
			width: 300px;
			height: 100%;
		}
	}

	.right-box {
		flex-direction: column;

		.item-box {
			width: 100%;
			height: 205px;
		}
	}

	.item-img {
		width: 100%;
		height: 100%;
	}

	.masker-box {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 80px;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 0px 0px 5px 5px;
		padding: 15px;
		font-size: 18px;
		font-weight: bold;
		color: #ffffff;
		cursor: pointer;

		.item-name {
			line-height: 18px;
		}

		.item-desc {
			font-size: 14px;
			margin-top: 20px;
			display: flex;
			justify-content: space-between;
		}
	}
}

.project-box {
	margin-top: 56px;

	.project-list {
		margin-top: 30px;
		display: flex;
		flex-wrap: wrap;
	}

	.project-item {
		width: 300px;
		height: 280px;
		background: #ffffff;
		border-radius: 5px;
		margin-right: 19px;
		margin-bottom: 20px;

		&:nth-child(4n) {
			margin-right: 0;
		}

		.item-img {
			width: 100%;
			height: 178px;
			background: #ffffff;
			object-fit: contain;
		}

		.item-base {
			padding: 17px 19px;

			.name {
				font-size: 18px;
				line-height: 18px;
				color: #333333;
			}

			.desc {
				font-size: 14px;
				line-height: 18px;
				color: #999999;
				margin-top: 12px;
			}
		}
	}
}
</style>
