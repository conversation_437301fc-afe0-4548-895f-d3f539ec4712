<template>
	<div v-loading="loading" class="GoodsInfo">
		<div class="left">
			<div class="img">
				<img v-if="skuActive.URL" :src="$judgeFile(skuActive.URL)" alt="" />
			</div>
			<div class="list">
				<div class="list-item">
					<div class="list-item-left swiper-button-prev">
						<div class="left">
							<i class="el-icon-arrow-left"></i>
						</div>
					</div>
					<swiper :options="swiperOption" @swiper="onSwiper" @slideChange="onSlideChange">
						<swiper-slide>
							<img
								v-if="skuActive.URL"
								:class="['list-img', active == 1 ? 'list-img_active' : '']"
								:src="$judgeFile(skuActive.URL)"
								alt=""
							/>
						</swiper-slide>
					</swiper>
					<div class="list-item-right swiper-button-next">
						<div class="right">
							<i class="el-icon-arrow-right"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="share">
				<a href="javascript:void(0)">
					<div
						@click="
							() => {
								collection ? cancelFollow() : addFollowShop();
							}
						"
					>
						<AlIcon v-if="collection" size="16" name="el-icon-star-on" color="#0076e8"></AlIcon>
						<AlIcon v-else name="el-icon-star-off" size="16" color="#959595"></AlIcon>
						<div class="share-text">收藏商品</div>
					</div>
				</a>
				<a href="javascript:void(0)">
					<div @click="copyAddress">
						<AlIcon name="el-icon-position" size="16" color="#959595"></AlIcon>
						<div class="share-text">分享商品</div>
					</div>
				</a>
			</div>
		</div>
		<div class="right">
			<div class="title">
				<span class="name">
					<span v-if="baseInfo.recommendNames" class="tags-list">
						<span v-for="event of getArr(baseInfo.recommendNames)" :key="event" class="tag">
							{{ event }}
						</span>
					</span>
					{{ baseInfo.PRODUCT_NAME }}
				</span>
			</div>
			<div class="tags">
				<div v-for="(item, index) of baseInfo.LABEL" :key="index">{{ item }}</div>
			</div>
			<div class="site">
				<div v-if="shopType === 'hotel'">
					<AlIcon name="el-icon-location"></AlIcon>
					{{ setAddress() }}
				</div>
				<div v-if="shopType !== 'hotel'">销量：{{ baseInfo.SELL_NUM }}</div>
				<div v-if="shopType !== 'hotel'">最少起购数量：{{ skuActive.minNum }}</div>
			</div>
			<div class="price">
				<div class="item">
					<div class="title-text">价格：</div>

					<div style="display: flex; align-items: center">
						<span v-if="isPointGoods">{{ skuActive.REAL_POINT || 0 }}积分+</span>
						<span>￥</span>
						<span class="unit-price">
							{{ skuActive.REAL_PRICE && skuActive.REAL_PRICE.toFixed(2) }}
						</span>
					</div>
					<div class="original-price">
						<div>￥{{ skuActive.ORI_PRICE && skuActive.ORI_PRICE.toFixed(2) }}</div>
					</div>
				</div>
				<div v-if="baseInfo.type2 !== 2" class="item">
					<div class="title-text">配送：</div>
					<div class="distribution">
						<span v-for="(item, index) of distributionMode" :key="index">
							{{ index != 0 ? '/' : '' }} {{ item == 1 ? `买家自提` : '卖家配送' }}
						</span>
					</div>
				</div>
			</div>
			<div v-if="shopType === 'hotel'" class="label">
				<div
					v-for="(item, index) of label"
					:key="index"
					:style="{ background: colorArr[index % 3].bg, color: colorArr[index % 3].color }"
					class="label-item"
				>
					{{ item }}
				</div>
			</div>
			<div v-if="shopType === 'scene'" style="margin: 32px 0 16px">
				<span class="date-title">入园日期：</span>
				<el-date-picker
					v-model="joinDate"
					style="margin-right: 30px"
					type="date"
					:picker-options="pickerOptions"
					placeholder="选择日期"
				></el-date-picker>
			</div>
			<div v-if="shopType === 'hotel'" class="date">
				<span class="date-title">入住日期：</span>
				<el-date-picker
					v-model="startDate"
					style="margin-right: 30px"
					type="date"
					:picker-options="pickerOptions"
					placeholder="选择日期"
				></el-date-picker>
				<span class="date-title">离店日期：</span>
				<el-date-picker
					v-model="endDate"
					type="date"
					placeholder="选择日期"
					:picker-options="pickerEndOptions"
				></el-date-picker>
			</div>
			<div v-if="shopType !== 'hotel'" class="specification">
				<div class="title-text">规格：</div>
				<div
					v-for="(item, index) of skus"
					:key="index"
					:class="['specification-item', item.ID == skuActive.ID ? 'specification-active' : '']"
					@click="clickSkuActive(item)"
				>
					{{ item.SKU_NAME }}
					<div v-show="item.ID == skuActive.ID" class="specification-item-img"></div>
					<img class="check-img" src="@/assets/shop-images/check.png" alt="" />
				</div>
			</div>

			<div v-if="shopType !== 'hotel'" class="num">
				<div class="title-text">数量：</div>
				<el-input-number
					v-model="num"
					:min="skuActive.minNum"
					:max="skuActive.NUMBER"
					size="medium"
					label="描述文字"
					@change="handleChange"
				></el-input-number>
			</div>
			<div v-if="shopType !== 'hotel'" class="num">
				<div class="title-text">库存：</div>
				<div class="NUMBER">{{ skuActive.NUMBER }}</div>
			</div>
			<div v-if="shopType !== 'hotel'" class="total">
				<div class="title-text">合计：</div>
				<div v-if="isPointGoods">{{ totalPoint }}积分+</div>
				<div>¥{{ totalPrice && Number(totalPrice).toFixed(2) }}</div>
			</div>
			<div class="button" :class="shopType === 'hotel' ? 'hotel-button' : ''">
				<!--type=1为零售商品、type2=2是酒店门票等非实物商品、isPointGoods为积分商品-->
				<el-button
					v-if="baseInfo.type2 !== 2 && baseInfo.type == 1 && isRetail && !isPointGoods"
					plain
					class="button-car"
					icon="el-icon-shopping-cart-2"
					@click="addCar"
				>
					加入购物车
				</el-button>
				<el-button v-if="shopType === 'hotel'" icon="el-icon-goods">
					<!-- @click="toPreShop" -->
					立即预订
				</el-button>
				<el-button v-else icon="el-icon-goods" @click="createOrderMult">立即购买</el-button>
				<!-- @click="createOrderMult" -->
			</div>
		</div>
		<!-- <el-dialog title="手机扫描二维码" :visible.sync="dialogVisible" width="500px">
			<div class="QR-box">
				<div id="qrCodeUrl"></div>
			</div>
		</el-dialog> -->
	</div>
</template>

<script>
import BigNumber from 'bignumber.js';
import { getCookie } from '@/utils/auth';
import isEmpty from '@/utils/isEmpty';
import QRCode from 'qrcodejs2';
import { alumniUrl } from '@/config';
export default {
	name: 'GoodsInfo',
	props: {
		baseInfo: {
			type: Object,
			default: () => {}
		},
		/**商品类型*/
		shopType: {
			type: String,
			default: () => {
				return '';
			}
		},
		distributionMode: {
			type: Array,
			default: () => []
		},
		skus: {
			type: Array,
			default: () => []
		},
		/**是否为积分商品*/
		isPointGoods: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			num: 1,
			active: 1,
			skuActive: {},
			type: null,
			loading: false,
			totalPrice: 0,
			collection: false,
			swiperOption: {
				slidesPerView: 3,
				spaceBetween: 30,
				initialSlide: 1,
				// centeredSlides: true,
				// 设置分页器
				pagination: {
					el: '.swiper-pagination',
					// 设置点击可切换
					clickable: false
				},
				// 设置前进后退按钮
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev'
				},
				// 设置自动轮播
				autoplay: {
					delay: 500000000 // 5秒切换一次
				},
				// 设置轮播可循环
				loop: false
			},
			userId: getCookie('user_id'),
			isRetail: false,
			totalPoint: 0,
			colorArr: [
				{ bg: 'rgba(0,118,232,0.1)', color: '#0076E8' },
				{ bg: 'rgba(254,129,14,0.1)', color: '#FE810E' },
				{ bg: 'rgba(249,95,85,0.1)', color: '#F95F55' }
			],
			startDate: '',
			endDate: '',
			pickerOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
				}
			},
			joinDate: '',
			alumniUrl,
			dialogVisible: false
		};
	},
	computed: {
		label() {
			return this.baseInfo.label && this.baseInfo.label.length
				? this.baseInfo.label
				: this.baseInfo.tesefuwu
				? this.baseInfo.tesefuwu.split(',')
				: '';
		},
		/**计算离店日期必须大于入住日期*/
		pickerEndOptions() {
			let startDate = new Date(this.startDate).getTime() + 24 * 60 * 60 * 1000;
			return {
				disabledDate(time) {
					return time.getTime() < startDate;
				}
			};
		}
	},
	watch: {
		/**监听入住日期改变，离店日期必须大于入住日期*/
		startDate(newVal) {
			if (new Date(newVal).getTime() >= new Date(this.endDate)) {
				this.endDate = new Date(
					new Date(newVal).getTime() + 24 * 60 * 60 * 1000
				).toLocaleDateString();
			}
		}
	},
	created() {
		this.distributionMode.forEach(item => {
			if (item == 2) {
				this.isRetail = true;
			}
		});
		if (this.userId) {
			this.isCollection();
		}
		this.type = this.$route.query.type;
		this.startDate = new Date().toLocaleDateString();
		this.endDate = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toLocaleDateString();
	},
	mounted() {
		this.skuActive = this.skus[0];
		this.num = this.skuActive.minNum;
		this.totalPrice = BigNumber(this.num).times(this.skuActive.REAL_PRICE).toString();
		this.totalPoint = BigNumber(this.num).times(this.skuActive.REAL_POINT).toString();
	},
	methods: {
		// 展示h5二维码
		showQrcode() {
			this.dialogVisible = true;
			let originUrl = this.alumniUrl + '/project-ybzy/ybzy_zjptH5/#/'; //二维码地址
			originUrl += `pages_common/pages/a_article-details/article-details?ID=${this.baseInfo.PRODUCT_ID}&type=${this.baseInfo.reg_code}`;
			console.log(originUrl, 'originUrloriginUrloriginUrloriginUrl');
			this.$nextTick(function () {
				document.getElementById('qrCodeUrl').innerHTML = '';
				let qrCodeUrl = new QRCode('qrCodeUrl', {
					width: 250,
					height: 250,
					text: originUrl,
					colorDark: 'black',
					colorLight: '#ffffff'
				});
				this.qrCode = qrCodeUrl;
			});
			// switch (code) {
			// 	// 根据商品类型不同跳转不同的详情
			// 	case 'native':
			// 		originUrl += `pages_common/pages/a_article-details/article-details?ID=32e5ee18acd643e6ae44d601751631b4&type=native`;
			// 		break;
			// 	case 'food':
			// 		originUrl += `pages_common/pages/a_delicious-food/goodsdetail?picId=6337662d59914f4caaeee66d994be500`;
			// 		break;
			// 	case 'culture':
			// 		originUrl += `pages_common/pages/a_article-details/article-details?ID=295615b9d9f5470abc20e80beb35a644&type=culture`;
			// 		break;
			// 	case 'heritage':
			// 		// this.$navigateTo({
			// 		// 	url: `/pages/a_article-details/article-details?ID=${i.ID}&type=${i.reg_code}`
			// 		// });
			// 		originUrl += `pages_common//pages/a_article-details/article-details?ID=${i.ID}&type=${i.reg_code}`;
			// 		break;
			// 	default:
			// 		break;
			// }
		},
		/**预定*/
		toPreShop() {
			let top = document.getElementById('content').offsetTop;
			window.scrollTo(0, top);
		},
		setAddress() {
			return this.baseInfo?.goodsPlace?.address || this.baseInfo.SHOP_DETAIL;
		},
		onSwiper(val) {
			console.log(val);
		},
		isCollection() {
			let data = {
				siteId: this.getSiteId(),
				targetId: this.$route.query?.id || '',
				userId: this.userId
			};
			this.$api.shop_api.isCollection(data).then(res => {
				this.collection = res.result.isCollection;
			});
		},
		addFollowShop() {
			this.loading = true;
			this.userId = this.isShopLogin();
			if (this.userId) {
				this.isCollection();
				let data = {
					siteId: this.getSiteId(),
					targetId: this.$route.query?.id || '',
					userId: this.userId,
					type: 2
				};
				this.$api.shop_api.followShop(data).then(res => {
					this.loading = false;

					if (res.state) {
						this.collection = true;
						this.$message.close();
						this.$message.success(res.msg);
						return;
					}
					this.$message.close();
					this.$message.error(res.msg);
				});
			}
		},
		copyAddress() {
			let copyInput = document.createElement('input'); // 创建元素
			copyInput.value = window.location.href;
			document.body.appendChild(copyInput); // 添加元素
			copyInput.select();
			document.execCommand('Copy'); // 执行浏览器复制命令
			document.body.removeChild(copyInput); // 移除刚创建的元素
			this.$message.close();
			this.$message.success('复制成功');
		},

		cancelFollow() {
			this.userId = this.isShopLogin();
			if (this.userId) {
				this.isCollection();
				this.loading = true;
				let data = {
					siteId: this.getSiteId(),
					id: this.$route.query?.id || '',
					userId: this.userId
				};
				this.$api.shop_api.cancelFollowShop(data).then(res => {
					this.loading = false;
					if (res.state) {
						this.collection = false;
						this.$message.close();
						this.$message.success(res.msg);
						return;
					}
					this.$message.close();
					this.$message.error(res.msg);

					// this.collection = res.result.isCollection;
				});
			}
		},
		onSlideChange(val) {
			console.log(val);
		},
		getArr(item) {
			let arr = [];
			arr = item.split(',');
			return arr;
		},
		clickSkuActive(item) {
			this.skuActive = item;
			this.num = 1;
			this.totalPrice = BigNumber(this.num).times(this.skuActive.REAL_PRICE).toString();
			this.totalPoint = BigNumber(this.num).times(this.skuActive.REAL_POINT).toString();
		},
		/**加入购物车*/
		addCar() {
			if (this.skuActive.NUMBER == 0) {
				this.$message.warning('库存不足');
				return;
			}
			this.userId = this.isShopLogin();
			if (this.userId) {
				this.loading = true;
				let data = {
					rentId: this.getSiteId(),
					memberId: this.userId,
					productId: this.baseInfo.PRODUCT_ID,
					skuId: this.skuActive.ID,
					skuNum: this.num
				};
				this.$api.shop_api.setShoppingCar(data).then(res => {
					this.loading = false;
					this.$message.close();
					if (res.state) {
						this.$emit('updateDate'); // 刷新购物车信息
						this.$message.success('加入购物车成功');
						return;
					}
					this.$message.close();
					this.$message.warning(res.msg);
					// this.$router.push({
					// 	path: `/shoppingCart?${this.$route.query.isLaw == 1 ? '&isLaw=1' : ''}${
					// 		this.$route.query.url ? '&url=' + this.$route.query.url : ''
					// 	}`
					// });
				});
			}
		},
		/**选择了规格-下单*/
		toBuy(id) {
			this.skuActive = this.skus.find(item => {
				return item.ID === id;
			});
			this.createOrderMult();
		},
		/**下单*/
		async createOrderMult() {
			// return this.showQrcode(); //展示h5得二维码
			if (this.skuActive.NUMBER == 0) {
				this.$message.warning('库存不足');
				return;
			}
			// 门票必须选择游园日期
			if (this.shopType === 'scene' && !this.joinDate) {
				this.$message.error('请选择游园日期');
				return;
			}
			// 酒店必须选择开始和结束日期
			if (this.shopType === 'hotel' && (isEmpty(this.startDate) || isEmpty(this.endDate))) {
				this.$message.error('请选择开始和结束日期');
				return;
			}
			this.userId = this.isShopLogin();
			if (this.userId) {
				let isLaw = 2;
				if (this.$route.query.isLaw == 1) {
					isLaw = 1;
				}
				let startDate, endDate;
				if (this.shopType === 'hotel') {
					startDate = this.startDate;
					endDate = this.endDate;
				} else {
					startDate = endDate = this.joinDate;
				}
				this.$router.push({
					path: `/settleAccountsIndex?ids=${this.baseInfo.PRODUCT_ID}&type=2&skuNum=${this.num}&skuId=${this.skuActive.ID}&isLaw=${isLaw}&value=${this.baseInfo.type}&shopType=${this.baseInfo.reg_code}&isPointGoods=${this.isPointGoods}&startDate=${startDate}&endDate=${endDate}`
				});
				return;
			}
		},
		handleChange() {
			this.totalPrice = BigNumber(this.num).times(this.skuActive.REAL_PRICE).toString();
			this.totalPoint = BigNumber(this.num).times(this.skuActive.REAL_POINT).toString();
		}
	}
};
</script>

<style lang="scss" scoped>
.GoodsInfo {
	width: 100%;
	height: 605px;
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	margin-top: 16px;
	padding: 22px 24px;
	display: flex;
	.left {
		width: 400px;
		.share {
			display: flex;
			margin-top: 50px;
			div {
				display: flex;
				align-items: center;
				margin-right: 37px;
				.share-text {
					margin-left: 3px;
					margin-right: 0px;
					height: 22px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #404040;
					line-height: 22px;
				}
			}
		}
		.img {
			img {
				width: 400px;
				height: 400px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				object-fit: contain;
			}
		}
		.list {
			width: 100%;
			.left {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 36px;
				height: 90px;
				background: #f0f0f0;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				i {
					font-size: 24px;
				}
			}
			.right {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 36px;
				height: 90px;
				background: #f0f0f0;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				margin: 0;
				i {
					font-size: 24px;
				}
			}
			height: 90px;
			&-item {
				height: 117px;
				width: 100%;
				overflow-x: auto;
				overflow-y: hidden;
				position: relative;
				display: flex;
				align-items: center;
				&-left {
					width: 36px;
					height: 90px;
					background: #f0f0f0;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				&-right {
					width: 36px;
					height: 90px;
					background: #f0f0f0;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				&-nav {
					padding-bottom: 17px;
					margin: 0 5px;
				}
				.swiper-container {
					margin: 0px 0 0 4px;
					width: 328px;
				}
				.swiper-button-prev,
				.swiper-button-next {
					width: 36px;
					position: initial;
					margin-top: -5px;
				}
				.swiper-button-prev:after,
				.swiper-button-next:after {
					display: none;
				}
				.list-img {
					width: 90px;
					height: 90px;
					object-fit: cover;
				}
				.list-img_active {
					border: 1px solid var(--brand-6, '#ca3f3b');
				}
			}
		}
	}
	.right {
		width: 740px;
		margin-left: 16px;
		position: relative;
		.title {
			padding-left: 10px;
			display: flex;
			align-items: center;
			.tag {
				background: #ffeeee;
				border-radius: 2px 2px 2px 2px;
				opacity: 1;
				font-size: 12px;
				font-family: Source Han Sans SC-Normal, Source Han Sans SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				margin-right: 8px;
				padding: 4px 8px;
				margin-bottom: 5px;
			}
			.tags-list {
				// display: flex;
			}
			.name {
				font-size: 24px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #262626;
				line-height: 32px;
			}
		}
		.tags {
			padding-left: 10px;
			margin-top: 10px;
			display: flex;
			div {
				margin-right: 5px;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid var(--brand-6, '#ca3f3b');
				padding: 0 8px;
				height: 24px;
				font-size: 12px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 23px;
			}
		}
		.site {
			padding-left: 10px;

			margin-top: 19px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 22px;
			display: flex;
			div {
				margin-right: 46px;
			}
		}
		.price {
			padding: 16px 10px;
			margin-top: 10px;
			width: 740px;
			//height: 100px;
			background: #fdf2e8;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			.item {
				display: flex;
				align-items: center;
				span {
					color: #f95f55;
					font-size: 16px;
				}
				.unit-price {
					font-size: 36px;
					font-family: Rany-Medium, Rany;
					font-weight: 500;
					line-height: 32px;
				}
				.distribution {
					span {
						height: 22px;
						font-size: 14px;
						font-family: Source Han Sans SC-Regular, Source Han Sans SC;
						font-weight: 400;
						color: #404040;
						line-height: 22px;
					}
				}
				.original-price {
					font-size: 12px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #bfbfbf;
					height: 100%;
					display: flex;
					align-items: flex-end;
					div {
						text-decoration: line-through;
						height: 100%;
						line-height: 50px;
					}
				}
			}
		}
		.label {
			display: flex;
			align-items: center;
			font-size: 12px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			line-height: 16px;
			margin: 24px 0;
			&-item {
				border-radius: 3px;
				margin-right: 8px;
				padding: 4px 8px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		.date {
			&-title {
				margin-right: 14px;
				font-size: 14px;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
			}
		}
		.specification {
			padding: 16px 10px;
			display: flex;
			align-items: center;
			&-item {
				position: relative;
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				padding: 8px 16px;
				border: 1px solid #bfbfbf;
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #8c8c8c;
				line-height: 22px;
				overflow: hidden;
				margin-right: 10px;
				&-img {
					position: absolute;
					height: 27px;
					width: 15px;
					bottom: -10px;
					right: -3px;
					background: var(--brand-6, '#ca3f3b');
					-moz-transform: rotate(45deg);
					-webkit-transform: rotate(45deg);
					-o-transform: rotate(45deg);
					transform: rotate(45deg);
					object-fit: cover;
				}
			}
			&-active {
				color: var(--brand-6, '#ca3f3b');
				background: #ffffff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid var(--brand-6, '#ca3f3b');
			}
			.check-img {
				position: absolute;
				bottom: 0px;
				right: 0px;
			}
		}
		.num {
			display: flex;
			padding: 16px 10px;
			.NUMBER {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
			}
			align-items: center;
		}
		.total {
			display: flex;
			padding: 16px 10px;
			align-items: center;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #f95f55;
			line-height: 22px;
		}
		.hotel-button {
			position: absolute;
			bottom: 40px;
		}
		.button {
			padding: 16px 10px;
			.el-button {
				width: 136px;
				height: 40px;
				background: var(--brand-6, '#ca3f3b');
				//background: #999;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				font-size: 16px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 24px;
				//cursor: not-allowed;
			}
			&-car {
				width: 152px !important;
				height: 40px !important;
				background: #ffffff !important;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				border: 1px solid var(--brand-6, '#ca3f3b');
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b') !important;
				cursor: pointer !important;
				// color: #999 !important;
			}
		}
		.title-text {
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 22px;
			margin-right: 14px;
		}
	}
}
::v-deep .el-input-number--medium {
	width: 168px;
	line-height: 42px;
	.el-input-number__decrease,
	.el-input-number__increase {
		width: 42px;
		font-size: 14px;
	}
	.el-input {
		.el-input__inner {
			height: 44px;
			line-height: 44px;
			padding-left: 44px;
			padding-right: 44px;
		}
	}
}
.left {
	i {
		color: #000000;
	}
}
</style>

<style lang="scss">
.el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
.el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
	border-color: var(--brand-6, '#ca3f3b') !important;
}
</style>
