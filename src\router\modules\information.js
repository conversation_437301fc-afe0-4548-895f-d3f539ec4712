/**
 * Name:子项目的路由
 * 目录：shop-views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */
export default {
	views: [],
	subViews: [
		{
			path: 'information',
			name: 'Information',
			component: () => import('@/information-views/home'),
			meta: { title: '教育资讯', noShowBreadCrumb: true }
		},
		{
			path: 'information-list',
			name: 'InformationList',
			component: () => import('@/information-views/list'),
			meta: { title: '资讯列表', noShowBreadCrumb: true }
		},
		{
			path: 'information-detail',
			name: 'InformationDetail',
			component: () => import('@/information-views/detail'),
			meta: { title: '资讯详情', noShowBreadCrumb: true }
		}
	]
};
