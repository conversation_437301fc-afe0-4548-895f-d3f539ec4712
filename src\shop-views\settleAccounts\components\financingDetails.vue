<template>
	<div class="FinancingDetails">
		<div class="title">
			<div class="logo"></div>
			<div class="title-text">大宗商品买断式金融-中国平安银行股份有限公司</div>
		</div>
		<div class="table">
			<div class="table-item">
				<div class="table-item-title">0-200万</div>
				<div class="table-item-dec">融资额度</div>
			</div>
			<div class="table-item">
				<div class="table-item-title">国内贸易/供应链金融</div>
				<div class="table-item-dec">产品分类</div>
			</div>
			<div class="table-item">
				<div class="table-item-title">1年</div>
				<div class="table-item-dec">融资期限</div>
			</div>
			<div class="table-item">
				<div class="table-item-title">1.5%-3.8%</div>
				<div class="table-item-dec">参考年利率</div>
			</div>
			<div class="table-item">
				<div class="table-item-title">2次</div>
				<div class="table-item-dec">申请次数</div>
			</div>
			<div class="table-item">
				<div class="table-item-title">企业</div>
				<div class="table-item-dec">适用主体</div>
			</div>
		</div>
		<div class="lable">
			<span class="lable-item">
				<span class="lable-item-title">担保方式：</span>
				<span class="lable-item-text">质押方式</span>
			</span>
			<span class="lable-item">
				<span class="lable-item-title">还款方式：</span>
				<span class="lable-item-text">先息后本，随借随还</span>
			</span>
			<span class="lable-item">
				<span class="lable-item-title">服务区域：</span>
				<span class="lable-item-text">成都市</span>
			</span>
		</div>
		<div class="logistics-info-header">
			<div class="icon"></div>
			<div class="title">产品介绍</div>
		</div>
		<div class="details">
			<div class="details-title">
				盘活存货，担保方式灵活；拓宽渠道，助力企业经营；扩大采购，加速资金周转。
			</div>
			<div class="details-dec">
				存货融是中国平安银行为有效破解企业融资难题，全力支持守信企业的融资，系统的对企业进行综合评分与信用评价，推出的货物质押融资服务。
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'FinancingDetails'
};
</script>

<style lang="scss" scoped>
.FinancingDetails {
	padding: 0px 22px;
}
.title {
	display: flex;
	align-items: center;
	.logo {
		width: 210px;
		height: 72px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
	}
	&-text {
		margin-left: 13px;
		height: 32px;
		font-size: 24px;
		font-family: Source Han Sans SC-Bold, Source Han Sans SC;
		font-weight: bold;
		color: #262626;
		line-height: 32px;
	}
}
.table {
	height: 93px;
	background: #f7f8fa;
	margin-top: 16px;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	display: flex;
	justify-content: space-around;
	align-items: center;
	text-align: center;
	&-item {
		&-title {
			height: 24px;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 24px;
		}
		&-dec {
			height: 18px;
			font-size: 14px;
			font-family: Noto Sans SC-Regular, Noto Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 18px;
		}
	}
}
.lable {
	margin-top: 16px;
	&-item {
		&-title {
			height: 22px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #9da5b7;
			line-height: 22px;
		}
		&-text {
			margin-top: 5px;
			height: 22px;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
		}
	}
}
.details {
	margin-top: 16px;
	&-title {
		height: 24px;
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #262626;
		line-height: 24px;
		margin-bottom: 4px;
	}
	&-dec {
		height: 22px;
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #8c8c8c;
		line-height: 22px;
	}
}
.logistics-info-header {
	margin-top: 32px;
	display: flex;
	align-items: center;
	.icon {
		margin-right: 5px;
		width: 6px;
		height: 20px;
		background: var(--brand-6, '#ca3f3b');
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
	}
	.title {
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #404040;
		line-height: 24px;
	}
}
</style>
