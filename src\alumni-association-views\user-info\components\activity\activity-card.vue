<!--
 @desc:个人中心 校友会 校友通讯录 我报名的校友活动卡片
 @author: WH
 @date: 2023/9/15
 -->
<template>
	<div class="card" @click="clickCard">
		<header>
			<!-- <p>活动订单编号：{{ cardData.number }}</p> -->
			<p>报名时间：{{ cardData.create_time }}</p>
			<!-- <p>支付方式：{{ cardData.payment }}</p> -->
			<div class="state">
				活动状态：
				<span>{{ stateText }}</span>
			</div>
		</header>

		<article>
			<img class="is-img" src="@/assets/shop-images/default-avatar.png" alt="" />
			<section>
				<p>{{ cardData.activity_title }}</p>
				<div class="info">
					<p>活动地点：{{ cardData.activity_address }}</p>
					<p>活动时间：{{ cardData.activity_start_time }}~{{ cardData.activity_end_date }}</p>
				</div>
				<p>{{ cardData.payment_amount ? '￥' + cardData.payment_amount : ' 免费' }}</p>
			</section>
			<el-button
				:class="{ 'is-btn': true, cancel: cardData.signstatus == 99 }"
				:disabled="cardData.signstatus == 99"
				type="primary"
				round
				@click="btnHandle"
			>
				{{ cardData.signstatus == 99 ? '已取消' : '取消报名' }}
			</el-button>
		</article>
	</div>
</template>

<script>
export default {
	props: {
		cardData: {
			type: Object,
			// required: true,
			default: () => {
				return {
					number: 'XYHD-20230825001',
					time: '2023.08.25 11:24',
					payment: '微信支付',
					name: '凝心聚力，同创未来120周年校友会',
					side: '四川省宜宾市翠屏区新村74号',
					runTime: '2023-07-01 10:30~2023-07-01 12:30',
					amount: '免费',
					state: 0
				};
			}
		}
	},
	data() {
		return {};
	},
	computed: {
		stateText() {
			const TEXT = ['预约中', '已结束', '已取消'];
			return TEXT[this.cardData.activitystatus - 0];
		}
	},

	methods: {
		btnHandle() {
			this.$emit('btnHandle', { btnName: 'esc', ...this.cardData });
		},

		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	// @include flexBox();
	// flex-direction: column;
	width: 100%;
	height: 200px;
	border-radius: 8px;
	margin-bottom: 20px;
	border: 1px solid #e8eaf0;
	background: #ffffff;

	header {
		@include flexBox(flex-start);
		position: relative;
		width: 100%;
		height: 40px;
		padding: 0 20px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		background: #e8eaf0;
		p {
			margin-right: 30px;
			font-size: 14px;
			color: #666666;
		}
		.state {
			position: absolute;
			right: 20px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 14px;
			span {
				color: #ff6600;
			}
		}
	}

	article {
		@include flexBox(flex-start);
		position: relative;
		width: 100%;
		height: 158px;
		padding: 20px;
		.is-img {
			width: 210px;
			height: 120px;
			border-radius: 8px;
			margin-right: 16px;
			box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.29);
			object-fit: cover;
		}
		section {
			@include flexBox(space-between, flex-start);
			height: 100%;
			flex-direction: column;
			& > p {
				&:nth-of-type(1) {
					font-size: 16px;
					color: #0076e8;
				}
				&:nth-of-type(2) {
					font-size: 18px;
					color: #ff0000;
				}
			}
			.info {
				font-size: 14px;
				color: #7a8392;
				line-height: 24px;
			}
		}
		.is-btn {
			position: absolute;
			right: 50px;
			top: 60px;
		}
	}
}
</style>
