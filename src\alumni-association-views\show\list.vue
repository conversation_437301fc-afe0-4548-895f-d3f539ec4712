<template>
	<div class="show-list">
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="list-box">
			<!-- 内容区域 -->
			<div class="list-card">
				<div v-for="(item, index) in list" :key="index" class="itemImageBox">
					<div class="item-box">
						<img :src="handelPicUrl(item.photoUrl)" alt="" />
						<div class="text-bottom">
							<div class="title">{{ item.title }}</div>
							<div class="handel">
								<div class="handel-item" @click="picHandelInterface('like', item)">
									<!-- <i class="el-icon-star-off" v-if="!item.isDig"></i>
									<i class="el-icon-star-on" v-else></i> -->
									<img v-if="!item.isDig" src="@/assets/images/alumniAssociation/like.png" alt="" />
									<img v-else src="@/assets/images/alumniAssociation/dislike.png" alt="" />
									点赞
								</div>
								<div class="handel-item" @click="picHandelInterface('collect', item)">
									<i v-if="!item.isCollect" class="el-icon-star-off"></i>
									<i v-else class="el-icon-star-on"></i>
									收藏
								</div>
								<div class="handel-item" @click="urlCopy(item)">
									<i class="el-icon-position"></i>
									分享
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div v-if="loadingIconShow" class="loadingIcon">
			<i class="el-icon-loading"></i>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import ElementUI from 'element-eoss';
export default {
	components: {
		subBreadcrumb
	},
	data() {
		return {
			pageNum: 1,
			loadingIconShow: false,
			imgTotle: 0,
			arrHeight: [],
			list: [],
			isFirst: true,
			startInfinity: false,
			infinity: true
		};
	},
	mounted() {
		window.addEventListener('scroll', () => {
			if (this.infinity) {
				return;
			}
			// 绑定scroll事件
			let clientHeight = document.documentElement.clientHeight; // 可见区域高度
			let scrollTop = document.documentElement.scrollTop; // 滚动区域顶部高度
			let scrollHeight = document.documentElement.scrollHeight; // 滚动区域总高度
			// console.log(clientHeight, '可见区域高度');
			// console.log(scrollTop, '滚动区域顶部高度');
			// console.log(scrollHeight, '滚动区域总高度');
			if (clientHeight + scrollTop > scrollHeight - 100) {
				this.infinity = true;
				// 当向下滑动至距离底部100px时，触发加载操作
				this.loadingIconShow = true;
				this.getInterfaceShowSend(this.pageNum, 20);
			}
		});
		this.getInterfaceShowFirst(this.pageNum, 20);
	},
	updated() {
		// fallsImg();
	},
	methods: {
		//分享
		urlCopy(item) {
			let url = this.handelPicUrl(item.photoUrl); //拿到想要复制的值
			let copyInput = document.createElement('input'); //创建input元素
			document.body.appendChild(copyInput); //向页面底部追加输入框
			copyInput.setAttribute('value', url); //添加属性，将url赋值给input元素的value属性
			copyInput.select(); //选择input元素
			document.execCommand('Copy'); //执行复制命令
			ElementUI.Message.success('复制成功！'); //弹出提示信息，不同组件可能存在写法不同
			//复制之后再删除元素，否则无法成功赋值
			copyInput.remove(); //删除动态创建的节点
			// this.$message.close();
			// this.$message.success('复制成功');
		},
		/**
		 * @description 图片(取消)点赞收藏接口
		 * */
		picHandelInterface(type, item) {
			let url = '';
			let message = '';
			if (type == 'like') {
				item.isDig = !item.isDig;
				if (item.isDig) {
					url = 'digg';
					message = '点赞成功';
				} else {
					url = 'unDigg';
					message = '取消点赞成功';
				}
			} else if (type == 'collect') {
				item.isCollect = !item.isCollect;
				if (item.isCollect) {
					url = 'collect';
					message = '收藏成功';
				} else {
					url = 'unCollect';
					message = '取消收藏成功';
				}
			}
			this.$message({
				message,
				type: 'success'
			});
			let data = {
				objectType: 'communityAlbum-photo',
				objectId: item.id
			};
			this.$api.alumni_association_api.likeOrCollect(data, url).then(res => {});
		},
		//处理图片路径
		handelPicUrl(url) {
			return url ? this.$judgeImg(url, true) : '';
		},
		/**
		 * @description 获取校友风采
		 * */
		getInterfaceShowFirst(pageNum, pageSize) {
			this.loadingIconShow = true;
			let data = {
				pageNum,
				pageSize
			};
			this.$api.alumni_association_api.alumniShow(data).then(res => {
				this.pageNum++;
				this.loadingIconShow = false;
				this.list.push(...res.results.records);
				let arrImageUrl = [];
				res.results.records.forEach(item => {
					arrImageUrl.push(this.handelPicUrl(item.photoUrl));
				});
				this.loadimages(arrImageUrl, res.results.records.length);
				// this.$nextTick(() => {
				// 	this.fallsImg();
				// });
				// setTimeout(() => {
				// 	this.fallsImg();
				// }, 500);
			});
		},

		loadimages(images, number) {
			let oldNumber = this.list.length - number;
			let imgList = images;
			let imgTags = document.getElementsByClassName('itemImageBox');
			let imgTotal = number; //所有图片的个数
			let promiseAll = [];
			for (var i = 0; i < imgTotal; i++) {
				promiseAll[i] = new Promise((resolve, reject) => {
					let imgTag = imgTags[i + oldNumber];
					imgTag = new Image();
					imgTag.src = imgList[i];
					imgTag.onload = function () {
						resolve(imgTag);
					};
				});
			}

			Promise.all(promiseAll).then(img => {
				setTimeout(() => {
					if (this.pageNum == 2) {
						this.fallsImg();
					} else {
						// this.getInterfaceShowSend(this.pageNum, 20);
						this.fallsImgAdd(number);
					}
				}, 50);
			});
		}, //判断图片是否全部加载完毕

		getInterfaceShowSend(pageNum, pageSize) {
			this.loadingIconShow = true;
			let data = {
				pageNum,
				pageSize
			};
			this.$api.alumni_association_api.alumniShow(data).then(res => {
				if (res.results.records.length == 0) {
					this.loadingIconShow = false;
					return;
				}

				this.pageNum++;
				this.loadingIconShow = false;
				this.list.push(...res.results.records);

				// let oldNumber = this.list.length;
				// let newNumber = res.results.records.length;
				let arrImageUrl = [];
				res.results.records.forEach(item => {
					arrImageUrl.push(this.handelPicUrl(item.photoUrl));
				});
				this.loadimages(arrImageUrl, res.results.records.length);

				// this.$nextTick(() => {
				// 	this.fallsImgAdd(oldNumber, newNumber);
				// });
			});
		},
		fallsImg() {
			this.isFirst = false;
			//1.获取主容器的宽度
			let content = document.getElementsByClassName('list-card')[0];
			let contentWidth = content.offsetWidth;

			//2.获取单个图片的宽度
			let imgs = content.children;
			let imgsWidth = imgs[0].offsetWidth;

			//3.第一行可以排列多少张图片
			let nums = Math.floor(contentWidth / imgsWidth);

			//4.收集第一行的初始高度
			for (let i = 0; i < imgs.length; i++) {
				if (i < nums) {
					this.arrHeight.push(imgs[i].offsetHeight);
				} else {
					//创建一个对象用来存储最少的高度的列
					let obj = {
						minH: this.arrHeight[0],
						minI: 0
					};
					let objMax = {
						maxH: this.arrHeight[0],
						maxI: 0
					};
					for (let j = 0; j < this.arrHeight.length; j++) {
						if (this.arrHeight[j] < obj.minH) {
							obj.minH = this.arrHeight[j];
							obj.minI = j;
						}
						if (this.arrHeight[j] > obj.minH) {
							objMax.maxH = this.arrHeight[j];
							objMax.maxI = j;
						}
					}
					//通过定位排列上去
					// imgs[j].style.display = 'none';
					imgs[i].style.position = 'absolute';
					imgs[i].style.left = imgs[obj.minI].offsetLeft + 'px';
					imgs[i].style.top = obj.minH + 'px';

					//每增加一张图片相对应的列高度就变化了
					this.arrHeight[obj.minI] = this.arrHeight[obj.minI] + imgs[i].offsetHeight;
				}

				content.style.height = Math.max(...this.arrHeight) + 'px';
			}
			this.infinity = false;
		},
		fallsImgAdd(newNumber) {
			let oldNumber = this.list.length - newNumber;
			let content = document.getElementsByClassName('list-card')[0];
			let imgs = content.children;
			for (let i = 0; i < newNumber; i++) {
				//创建一个对象用来存储最少的高度的列
				let obj = {
					minH: this.arrHeight[0],
					minI: 0
				};
				let objMax = {
					maxH: this.arrHeight[0],
					maxI: 0
				};
				for (let j = 0; j < this.arrHeight.length; j++) {
					if (this.arrHeight[j] < obj.minH) {
						obj.minH = this.arrHeight[j];
						obj.minI = j;
					}
					if (this.arrHeight[j] > obj.minH) {
						objMax.maxH = this.arrHeight[j];
						objMax.maxI = j;
					}
				}
				//通过定位排列上去
				imgs[i + oldNumber].style.position = 'absolute';
				imgs[i + oldNumber].style.left = imgs[obj.minI].offsetLeft + 'px';
				imgs[i + oldNumber].style.top = obj.minH + 'px';

				//每增加一张图片相对应的列高度就变化了
				this.arrHeight[obj.minI] = this.arrHeight[obj.minI] + imgs[i + oldNumber].offsetHeight;
			}
			content.style.height = Math.max(...this.arrHeight) + 'px';
			this.infinity = false;
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
* {
	padding: 0;
	margin: 0;
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
		padding: 0;
	}
}
.list-box {
	font-family: Microsoft YaHei;
	width: $max-width;
	margin: 20px auto 60px;
	.list-card {
		width: 100%;
		position: relative;
		.itemImageBox {
			float: left;
			padding: 15px;
			width: 315px;
			.item-box {
				background: #ffffff;
				border-radius: 8px;
				overflow: hidden;
				img {
					width: 100%;
				}
				.text-bottom {
					height: 100px;
					background: #ffffff;
					.title {
						text-align: center;
						height: 40px;
						line-height: 40px;
						font-size: 18px;
						font-family: Microsoft YaHei;
						font-weight: 400;
						color: #333333;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						padding: 0 21px;
					}
					.handel {
						height: 40px;
						line-height: 20px;
						padding: 0 21px;
						display: flex;
						.handel-item {
							cursor: pointer;
							height: 100%;
							flex: 1;
							font-size: 14px;
							font-family: Microsoft YaHei;
							font-weight: 400;
							color: #999999;
							display: flex;
							align-items: center;
							img {
								display: inline-block;
								width: 14px;
								height: 14px;
							}
						}
					}
				}
			}
		}
	}
}
.loadingIcon {
	line-height: 160px;
	color: #bbbbbb;
	font-size: 70px;
	width: 100%;
	height: 160px;
	// background: red;
	text-align: center;
}
::v-deep .el-icon-star-on {
	font-size: 18px;
}
</style>
