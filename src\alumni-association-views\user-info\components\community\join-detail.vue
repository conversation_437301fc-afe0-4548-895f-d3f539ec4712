<!--
 @desc:个人中心 校友会 校友社区 加入的社区详情
 @author: WH
 @date: 2023/9/13
 -->

<template>
	<div v-scroll-bottom="handleScroll" class="detail">
		<set-up-card :card-data="communityData" />
		<div v-show="showName == 'home'">
			<div class="mini-title">
				<span>社区成员</span>
				<p @click.stop="InviteFn" @click="InviteFn">
					<i class="el-icon-user"></i>
					邀请成员
				</p>
			</div>
			<div v-loading="memberLoading" class="card-box">
				<ab-card
					v-for="(item, index) in memberList"
					:key="item.id"
					:card-data="item"
					:class="{ 'mg-r0': (index + 1) % 4 == 0 }"
					class="shade-card"
				/>
				<p v-if="memberLoading.length == 0" class="no-data">暂无社区成员......</p>
			</div>
			<div class="mini-title">
				<span>社区通知</span>
			</div>
			<div class="inform-box">
				<InformCard
					v-for="(item, index) in informList"
					:key="index"
					:card-data="item"
					:show-btn="false"
				/>
				<p v-if="informList.length == 0" class="no-data">暂无社区通知......</p>
			</div>
			<div class="mini-title">
				<span>社区相册</span>
			</div>
			<div v-loading="photoLoading" class="card-box">
				<div
					v-for="(item, index) in photoList"
					:key="item.id"
					:class="{ 'mg-r0': (index + 1) % 4 == 0 }"
					class="photo-box"
				>
					<div class="photo">
						<img class="is-img" :src="getImgUrl(item.coverImg)" alt="" />
						<!-- <img class="is-img" src="@/assets/shop-images/default-avatar.png" alt="" /> -->
						<div class="shade"></div>
						<div class="btn-box">
							<div class="is-text" @click.stop="photoBtn('look', item.id)">
								<i class="el-icon-view"></i>
								<p>查看</p>
							</div>
						</div>
					</div>
					<p class="photo-info">{{ item.albumName }}</p>
				</div>
				<p v-if="photoList.length == 0" class="no-data">暂无社区照片......</p>
			</div>
		</div>
		<div v-show="showName == 'informControl'" class="inform-box">
			<InformCard v-for="(item, index) in informList" :key="index" :card-data="item" />
		</div>
		<photo-card v-if="showName == 'photoControl'" :photo-id="photoId" />

		<photo-dialog :visible.sync="photoVisible" :community-id="communityId" :photo-id="photoId" />
		<!-- @crudPhoto="crudPhoto" -->
		<invite-dialog :visible.sync="InviteVisible" :community-id="communityId" />
		<inform-dialog :visible.sync="InformVisible" :community-id="communityId" />
	</div>
</template>

<script>
import SetUpCard from './set-up-card.vue';
import AbCard from '../address-book/ab-card.vue';
import InviteDialog from './invite-dialog.vue';
import InformDialog from './inform-dialog.vue';
import InformCard from './inform-card.vue';
import PhotoCard from './photo-card.vue';
import PhotoDialog from './photo-dialog.vue';
import { alumniUrl } from '@/config';

export default {
	components: {
		SetUpCard,
		AbCard,
		InviteDialog,
		InformDialog,
		PhotoDialog,
		InformCard,
		PhotoCard
	},
	directives: {
		scrollBottom: {
			bind(el, binding) {
				el.addEventListener('scroll', () => {
					const scrollHeight = el.scrollHeight;
					const scrollTop = el.scrollTop;
					const clientHeight = el.clientHeight;

					if (scrollHeight - scrollTop === clientHeight) {
						binding.value(); // 调用绑定的方法
					}
				});
			}
		}
	},
	props: {
		communityId: {
			type: String,
			required: true
		},
		communityData: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			photoVisible: false,
			InviteVisible: false,
			InformVisible: false,
			showName: 'home',
			informList: [], //通知列表
			memberList: [], //成员列表
			memberLoading: false,
			memberId: '',
			photoList: [], //相册列表
			photoLoading: false,
			photoId: ''
		};
	},

	mounted() {
		this.getMember();
		this.getInform();
		this.getPhotos();
	},
	beforeDestroy() {
		// this.removeBoxscroll();
	},
	methods: {
		handleScroll() {
			// 触底逻辑
			console.log('滚动条触底了！');
		},
		//分页查询社区成员—————————————————
		async getMember() {
			this.memberLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getMember({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.memberList = results?.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.memberLoading = false;
			}
		},
		//分页查询社区相册—————————————————
		async getPhotos() {
			this.photoLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getPhotos({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.photoList = results?.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.photoLoading = false;
			}
		},
		//相册操作
		photoBtn(btnName, id = null) {
			this.photoId = id;
			switch (btnName) {
				case 'look':
					this.showName = 'photoControl';
					break;
			}
		},

		//分页查询社区通知公告—————————————————
		async getInform() {
			this.formLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getInform({
					pageNum: 1,
					pageSize: 20,
					communityId: this.communityId
				});
				if (rCode == 0) {
					this.informList = results.records;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.formLoading = false;
			}
		},

		btnHandle(cardData) {
			switch (cardData.btnName) {
				case 'addInform':
					this.InformVisible = true;
					break;
				case 'informControl':
					this.showName = 'informControl';
					this.getInform();
					break;

				default:
					break;
			}
		},
		InviteFn() {
			this.InviteVisible = true;
		},
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
@import '@/styles/alumni-association.scss';
.detail {
	height: 100%;
}
.card-box {
	@include flexBox(flex-start);
	flex-wrap: wrap;
}

.photo-box {
	width: 226px;
	height: 200px;
	background: #ffffff;
	border: 1px solid #e8eaec;
	border-radius: 4px;
	margin: 0 20px 20px 0;

	.photo {
		position: relative;
		&:hover {
			.shade {
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.8);
			}
			.btn-box {
				opacity: 1;
			}
		}
		.is-img {
			width: 226px;
			height: 150px;
			border-top-left-radius: 4px;
			border-top-right-radius: 4px;
			object-fit: cover;
		}

		.shade {
			position: absolute;
			z-index: 1;
			top: 1px;
			left: 1px;
		}
		.btn-box {
			@include flexBox();
			position: absolute;
			z-index: 3;
			top: 50%;
			margin-top: -30px;
			left: 1px;
			width: 228px;
			height: 60px;
			font-size: 14px;
			opacity: 0;
			color: #ffffff;
			.is-text {
				width: 80px;
				text-align: center;
				cursor: pointer;
			}
		}
	}
	.photo-info {
		// @include flexBox();
		width: 230px;
		height: 50px;
		padding: 0 20px;
		font-size: 14px;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #7a8392;
		line-height: 50px;
	}
}
.mg-r0 {
	margin-right: 0;
}
.no-data {
	width: 100%;
	min-height: 100px;
	line-height: 100px;
	text-align: center !important;
	color: #999;
}
</style>
