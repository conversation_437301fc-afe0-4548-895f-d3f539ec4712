<!--
 @desc:个人中心 就业创业 我报名的校友活动
 @author: WH
 @date: 2023/9/15
 -->
<template>
	<div v-loading="loading" class="main">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="全部活动" name="all"></el-tab-pane>
			<el-tab-pane label="待开始" name="0"></el-tab-pane>
			<el-tab-pane label="进行中" name="1"></el-tab-pane>
			<el-tab-pane label="已结束" name="2"></el-tab-pane>
			<el-tab-pane label="已取消" name="9"></el-tab-pane>
		</el-tabs>

		<div class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无校友活动',
					detail: '你还没有校友活动'
				}"
				@noDataFn="noDataFn"
			/>
			<template v-else>
				<activity-card
					v-for="(item, index) in list"
					:key="index"
					:card-data="item"
					@btnHandle="btnHandle"
				/>
			</template>
		</div>
		<div class="page">
			<el-pagination
				:current-page="pageNum"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<esc-activity-dialog :visible.sync="visible" />
	</div>
</template>

<script>
import ActivityCard from './activity/activity-card.vue';
import EscActivityDialog from './activity/esc-activity-dialog.vue';
import NoData from './no-data.vue';
import axios from 'axios';
import { parseTime } from '@/utils';
import { createNamespacedHelpers } from 'vuex';
const { mapMutations } = createNamespacedHelpers('ssoToken');
const { mapState } = createNamespacedHelpers('user');

export default {
	components: { ActivityCard, EscActivityDialog, NoData },

	data() {
		return {
			alumniActiveList: [], //校友活动数据
			list: [],
			loading: false,
			activeName: 'all',
			visible: false,
			pageNum: 1, // 页数
			pageSize: 10, // 条数
			total: 10
		};
	},
	computed: {
		...mapState(['userInfo'])
	},
	mounted() {
		this.getSignList();
	},

	methods: {
		...mapMutations(['SET_TOKEN']),

		//取消预约—————————————————
		async updateActivitySign(id, personId) {
			this.loading = true;
			try {
				let { code, message } = await this.$api.alumni_api.updateActivitySign({
					id,
					activitySignUserVO: [{ psersonId: personId }]
				});
				if (code == 200) {
					this.$message.warning(message);
					this.getSignList();
				} else {
					this.$message.warning(message);
				}
			} catch (error) {
				console.dir(error);
				//未登录子系统
				if (error.response.data.code == 401) {
					this.loginCheck();
				}
			} finally {
				this.loading = false;
			}
		},
		//分页查询活动—————————————————
		async getSignList() {
			this.loading = true;
			try {
				let data = {
					'pager.pageNumber': this.pageNum,
					'pager.pageSize': this.pageSize,
					'queryParams.tenementId': this.userInfo.tenantId,
					'queryParams.plateCode': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
					// 'queryParams.activityType': 'xyhd' //分类id：xyhd校友活动；sthd社团活动；ghhd工会活动；txhd退休活动；dekt第二课堂
				};
				if (this.activeName == 'all') {
					data['queryParams.status'] = '0,1,2,9'; //全部：0,1,2,9 ；0待开始； 1进行中；2 已结束；9已取消
				} else {
					data['queryParams.status'] = this.activeName;
				}
				let { code, message, data: results } = await this.$api.alumni_api.getSignList(data);
				if (code == 200) {
					this.list = results?.list.map(item => {
						return {
							...item,
							activity_start_time: parseTime(item.activity_start_time, '{y}-{m}-{d}'),
							activity_end_date: parseTime(item.activity_end_date, '{y}-{m}-{d}'),
							create_time: parseTime(item.create_time, '{y}-{m}-{d}')
						};
					});
					this.total = results.totalPage;
				} else {
					this.$message.warning(message);
				}
			} catch (error) {
				console.dir(error);
				//未登录子系统
				if (error.response.data.code == 401) {
					this.loginCheck();
				}
			} finally {
				this.loading = false;
			}
		},
		//检查子系统是否登录
		async loginCheck() {
			let { rCode, msg, results } = await this.$api.alumni_api.loginCheck();
			if (rCode == 0) {
				// this.list = results?.records;
				this.getSsoUrl(results.code);
			} else {
				this.$message.warning(msg);
			}
		},
		//获取单点登录地址
		async getSsoUrl(code) {
			let { rCode, msg, results } = await this.$api.alumni_api.getSsoUrl({
				appId: 'archivesManageOpeUser'
			});
			if (rCode == 0) {
				//archivesManageOpeUser子系统名字  后台配置
				this.postSso(results.archivesManageOpeUser, code);
			} else {
				this.$message.warning(msg);
			}
		},
		//根据code单点登录
		async postSso(url, codeId) {
			let newData = new FormData();
			newData.append('code', codeId);
			let { data } = await axios({
				method: 'post',
				url,
				data: newData,
				headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
				//   headers: {'X-Requested-With': 'XMLHttpRequest'},
			});
			let { code, message, data: results } = data;
			if (code == 200) {
				this.SET_TOKEN({ name: 'archivesManageOpeUser', token: results.token });
				this.$message.success(message);
				this.getSignList();
			} else {
				this.$message.warning(message);
			}
		},

		btnHandle(cardData) {
			switch (cardData.btnName) {
				case 'esc':
					// this.visible = true;
					this.updateActivitySign(cardData.id, cardData.personId);
					break;
			}
		},
		noDataFn() {},
		handleClick(tab, event) {
			this.getSignList();
		},
		// 条数
		handleSizeChange(i) {
			this.pageSize = i;
			this.pageNum = 1;
		},
		// 页数
		handleCurrentChange(i) {
			this.pageNum = i;
			this.getMember();
		}
	}
};
</script>

<style lang="scss" scoped>
.main {
	padding: 0 20px;
	background: #fff;
	height: 100%;
	.list-box {
		width: 100%;
		height: 680px;
		overflow: auto;
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
// .el-tab-pane {
// 	width: 100%;
// 	height: 680px;
// 	padding: 20px;
// 	border-radius: 4px;
// 	border: 1px solid #e8eaec;
// 	overflow: auto;
// }
</style>
