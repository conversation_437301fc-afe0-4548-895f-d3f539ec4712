<template>
	<div class="header">
		<div class="header-top">
			<div class="header-top-con">
				<div class="header-top-left">欢迎来到技状元数字化校园统一信息门户</div>
				<div class="header-top-right">
					<template v-for="(item, index) of topList">
						<div
							v-if="!item.notShow"
							:key="index"
							class="header-top-right-item"
							:style="{ color: topSelect === index ? '#0076E8' : '' }"
							@click="selectTop(item, index)"
						>
							{{ item.name }}
							<div v-if="index !== topList.length - 1" class="line"></div>
						</div>
					</template>
				</div>
			</div>
		</div>
		<div class="header-center">
			<div class="header-center-con">
				<div class="header-center-logo">
					<img class="img" src="@/assets/images/layout/header-logo.png" alt="" />
					<!-- <div class="line"></div>
					<div class="title">职教园区</div> -->
				</div>
				<div class="header-center-more">
					<div class="header-center-more-top">
						<div
							v-for="(item, index) of moreLIst"
							:key="index"
							:class="moreSelect === index ? 'select' : ''"
							class="item"
							@click="selectMore(index)"
						>
							{{ item }}
						</div>
					</div>
					<div class="header-center-more-bottom">
						<input
							class="search-input"
							placeholder="找服务技状元数字化校园平台，专业又轻松！"
							type="text"
						/>
						<div class="button">
							<img class="button-img" src="@/assets/images/home/<USER>" alt="" />
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="header-bottom">
			<div class="header-bottom-con">
				<div
					v-for="(item, index) of list"
					:key="index"
					class="header-bottom-item"
					:class="index === current ? 'select' : ''"
					@click="toRoute(item.url, index)"
				>
					{{ item.name }}
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getCookie } from '@/utils/auth';
import { mapMutations, mapState, mapGetters } from 'vuex';
import { alumniUrl } from '@/config';

export default {
	name: 'Header',
	data() {
		return {
			// topList: ['统一身份认证登录', '用户入驻','职教服务大厅', '联系我们', '移动应用'],
			topList: [
				{
					name: '统一身份认证登录',
					url: '/login'
				},
				{
					name: '用户入驻',
					url: '/register'
				},
				{
					name: '职教服务大厅',
					url: '/independentPersonal/vocational',
					notShow: true
				},
				{
					name: '联系我们',
					url: ''
				},
				{
					name: '移动应用',
					url: ''
				}
			],
			moreLIst: ['找技能', '找商品', '找工作', '找项目', '找课程'],
			list: [
				{
					name: '首页',
					url: '/home'
				},
				{
					name: '职教园区',
					url: '/VEhall'
				},
				{
					name: '技能宝库',
					url: '/skill-treasure'
				},
				{
					name: '技能商城',
					url: '/shopHome'
				},
				{
					name: '就业创业',
					url: '/employment-home'
				}
				// {
				// 	name: '在线学习',
				// 	url: '/online-study'
				// },
				// {
				// 	name: '职教大厅',
				// 	url: '/VEhall'
				// },
				// {
				// 	name: '教育资讯',
				// 	url: '/information'
				// },
				// {
				// 	name: '校友会',
				// 	url: '/alumniAssociation'
				// }
			],
			topSelect: 0,
			moreSelect: 0
		};
	},
	computed: {
		...mapGetters(['roles']),
		...mapState({
			subList: state => state.app.subList,
			current: state => state.app.current
		})
	},
	watch: {
		$route: {
			deep: true,
			handler(newVal) {
				this.computedCurrent();
			}
		}
	},
	created() {
		// 判断是否登录，展示文字不同
		if (getCookie('user_id')) {
			// this.topList.splice(0, 1, '个人中心');
			this.topList[0].name = '个人中心';
			this.topList[0].url = '/personal';
		}
		this.isRole(); //判断是否有教师角色，是否展示教职大厅
		// 将二级菜单缓存起来，以便面包屑好匹配
		this.UPDATE_SUB_LIST(this.list);
		// 刷新的时候路由没有变化，要进行当前tab索引的计算
		this.computedCurrent();
	},
	methods: {
		...mapMutations('app', ['UPDATE_SUB_LIST', 'SET_CURRENT']),
		/**计算当期tab的索引*/
		computedCurrent() {
			let currentIndex = this.subList.findIndex(item => {
				return item.url === this.$route.path;
			});
			if (currentIndex > -1) {
				currentIndex -= 1; // 要排除写死的个人中心
				this.SET_CURRENT(currentIndex);
			}
		},
		/**选择头部功能按钮*/
		selectTop(item, i) {
			this.topSelect = i;
			// if (i === 0) {
			// 	if (getCookie('user_id')) {
			// 		// this.$message.warning('开发中，敬请期待...');
			// 		this.$router.push('/personal'); // 个人中心
			// 	} else {
			// 		this.$router.push('/login'); // 登录
			// 	}
			// } else if (i === 1) {
			// 	this.$router.push('/register'); // 个人中心
			// } else if (i === 2) {
			// 	this.$router.push('/independentPersonal/vocational'); // 职教大厅
			// } else {
			// 	this.$message.warning('功能开发中，敬请期待...');
			// }
			if (item.url == '/independentPersonal/vocational') {
				window.open(
					`${alumniUrl}/project-ybzy/ybzy/index.html#/home?serverId=ybzyDtcSso&authType=6`,
					'_self'
				);
				return;
			}
			if (item.url) {
				this.$router.push(item.url);
			} else {
				this.$message.warning('功能开发中，敬请期待...');
			}
		},
		// 判断当前用户是否有教师角色，改变展示页面路径
		isRole() {
			let roleL = [1, 2]; // 老师的角色
			for (let role of this.roles) {
				if (roleL.includes(role)) {
					this.topList[2].notShow = false;
				}
			}
		},
		/**选择更多的按钮*/
		selectMore(i) {
			this.moreSelect = i;
		},
		/**选择更多的按钮*/
		toRoute(url, i) {
			if (url) {
				this.$router.push(url);
			} else {
				this.$message.warning('功能开发中，敬请期待...');
			}
			this.SET_CURRENT(i);
		}
	}
};
</script>

<style scoped lang="scss">
.header {
	&-top {
		height: 36px;
		background: #e8eaf0;
		display: flex;
		align-items: center;
		justify-content: center;
		align-items: center;
		&-con {
			width: 1260px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			align-items: center;
		}
		&-left {
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #333333;
		}
		&-right {
			display: flex;
			align-items: center;
			&-item {
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #404040;
				display: flex;
				align-items: center;
				cursor: pointer;
			}
			.line {
				height: 13px;
				width: 1px;
				background: #000;
				margin: 0 5px;
			}
		}
	}
	&-center {
		height: 100px;
		background: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		&-con {
			width: 1260px;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		&-logo {
			display: flex;
			align-items: center;
			.img {
				// width: 192px;
				height: 70px;
			}
			.line {
				width: 1px;
				height: 50px;
				background: #dfdfdf;
				margin: 0 16px 0 19px;
			}
			.title {
				font-size: 22px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: #333333;
				line-height: 46px;
			}
		}
		&-more {
			&-top {
				display: flex;
				align-items: center;
				.item {
					width: 60px;
					height: 26px;
					border-radius: 4px;
					font-size: 14px;
					font-family: Microsoft YaHei;
					font-weight: 400;
					color: var(--brand-6, #0076e8);
					line-height: 26px;
					text-align: center;
					cursor: pointer;
				}
				.select {
					background: var(--brand-6, #0076e8);
					color: #ffffff;
				}
			}
			&-bottom {
				width: 420px;
				height: 36px;
				background: #ffffff;
				border: 1px solid var(--brand-6, #0076e8);
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 12px 0 11px 9px;
				.search-input {
					outline: none;
					border: none;
					flex: 1;
					&::placeholder {
						color: #bfbfbf;
						font-size: 14px;
					}
				}
				.button {
					flex-shrink: 0;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 0 4px 4px 0;
					height: 36px;
					width: 36px;
					background: var(--brand-6, #0076e8);
					cursor: pointer;
					&-img {
						height: 20px;
						width: 20px;
					}
				}
			}
		}
	}
	&-bottom {
		height: 45px;
		background: var(--brand-6, #0076e8);
		display: flex;
		align-items: center;
		justify-content: center;
		&-con {
			width: 1260px;
			overflow: visible;
			white-space: nowrap;
		}
		&-item {
			display: inline-block;
			width: 157px;
			width: 20%;
			height: 45px;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: bold;
			color: #ffffff;
			line-height: 46px;
			cursor: pointer;
			text-align: center;
		}
		.select {
			background: #248ef0;
		}
	}
}
</style>
