import request from '@/utils/shop_request';
import Qs from 'qs';

export function getAllProduct(data) {
	return request({
		url: '/trip-api/api/product/getAllProduct',
		method: 'post',
		params: data
	});
}
export function getAllProductDetail(data) {
	return request({
		url: `/trip-api/api/product/detail`,
		method: 'post',
		type: 'form',
		data
	});
}
/**获取店铺的热卖商品*/
export function getShopTopProduct(params) {
	return request({
		url: `/trip-api/api/product/getShopTopProduct`,
		method: 'get',
		params
	});
}
/**获取到商品列表*/
export function getCommodityList(data) {
	return request({
		url: '/trip-api/yjy/product/paging',
		method: 'POST',
		params: data
	});
}
/**广告*/
export function getAdvertsByCode(data) {
	return request({
		url: '/trip-api/api/advert/getAdvertsByCode',
		method: 'POST',
		params: data
	});
}
/**获取分类*/
export function getProductTypeTree(params) {
	return request({
		url: '/trip-api/api/product/getProductTypeTree',
		method: 'get',
		params
	});
}
export function addMsg(data) {
	return request({
		url: '/trip-api/api/kf/sendKfMsg',
		method: 'post',
		params: data
	});
}
export function getMsgDetail(data) {
	return request({
		url: '/trip-api/api/kf/getMsgDetail',
		method: 'post',
		params: data
	});
}

export function getShopList(params) {
	return request({
		url: '/trip-api/api/product/getShopList',
		method: 'get',
		params
	});
}

/**店铺简要信息【通用】*/
export function getShopSimpleInfo(params) {
	return request({
		url: '/trip-api/api/product/getShopSimpleInfo',
		method: 'get',
		params
	});
}
/**获取信息列表*/
export function getInformation(params) {
	return request({
		url: '/trip-api/api/product/getInformation',
		method: 'get',
		params
	});
}
/**收藏商品*/
export function followShop(data) {
	return request({
		url: '/trip-api/api/favorite/collection',
		method: 'post',
		params: data
	});
}
/**是否收藏商品*/
export function isCollection(data) {
	return request({
		url: '/trip-api/api/favorite/isCollection',
		method: 'post',
		params: data
	});
}
/**取消收藏商品*/
export function cancelFollowShop(params) {
	return request({
		url: '/trip-api/api/favorite/delCollection',
		method: 'post',
		params
	});
}
/**获取企业等级*/
export function getEnterpriseLevel(params) {
	return request({
		url: '/scswl/platcreditevaluate/front/getEnterpriseLevel',
		method: 'get',
		params
	});
}
/**移动到收藏夹*/
export function moveToFavorite(data) {
	return request({
		url: '/trip-api/api/shopping/moveToFavorite',
		method: 'POST',
		params: data
	});
}
/**批量删除*/
export function batchDelete(data) {
	return request({
		url: '/trip-api/api/shopping/batchDelete',
		method: 'POST',
		params: data
	});
}

/**我的购物车*/
export function getShoppingCar(data) {
	return request({
		url: '/trip-api/api/shopping/list',
		method: 'POST',
		params: data
	});
}
/**修改购物车商品规格的数量*/
export function updateNum(data) {
	return request({
		url: '/trip-api/api/shopping/updateNum',
		method: 'POST',
		params: data
	});
}
/** 修改购物车商品规格*/
export function updateSkuId(data) {
	return request({
		url: '/trip-api/api/shopping/updateSkuId',
		method: 'POST',
		params: data
	});
}
/**获取评论数据-评论列表【通用】*/
export function getCommentListNew(params) {
	return request({
		url: `/trip-api/api/comment/getProductCommentList`,
		method: 'get',
		params
	});
}
/**积分兑换商品列表*/
export function productSku(params) {
	return request({
		url: `/api/integral-web/productSku/page`,
		method: 'get',
		params
	});
}
/**关注店铺*/
export function setFollowShop(params) {
	return request({
		url: '/trip-api/api/product/followShop',
		method: 'get',
		params
	});
}
/**取消关注店铺*/
export function setCancelFollowShop(params) {
	return request({
		url: '/trip-api/api/product/cancelFollowShop',
		method: 'get',
		params
	});
}
/**统一认证后用code登录电商*/
export function codeLoginShop(params) {
	return request({
		url: '/trip-seller/appApi/auth/login/ybzy',
		method: 'get',
		params
	});
}
/**商铺优惠券*/
export function getCoupons(params) {
	return request({
		url: '/api/coupon-web/api/coupon/validCouponOfRent',
		method: 'get',
		params
	});
}
/**加入购物车*/
export function setShoppingCar(data) {
	return request({
		url: '/trip-api/api/shopping/add',
		method: 'POST',
		params: data
	});
}
/**获取电商用户数据判断登录*/
export function scswlLogin(params) {
	return request({
		url: 'trip-seller/appApi/auth/scswlLogin',
		method: 'get',
		params
	});
}
/**表单请求*/
export function skuDetails(params) {
	return request({
		url: '/trip-api/api/product/getAppProductBySkuId',
		method: 'GET',
		params
	});
}
/**买家中心-报价详情*/
export function detailInfo(params) {
	return request({
		url: `/market-web/supplierQuoted/detail`,
		method: 'get',
		params
	});
}
/**获取选中的商品清单*/
export function getProductInfoByIds(data) {
	return request({
		url: '/trip-api/api/shopping/getProductInfoByIds',
		method: 'POST',
		params: data
	});
}
/**店铺自提点*/
export function getShopOfflineStoreList(params) {
	return request({
		url: `/trip-api/api/product/getShopOfflineStoreList`,
		method: 'get',
		params
	});
}
/**表单请求*/
export function doPostForm(data) {
	return request({
		url: '/api/market-web/xodb/doPostForm',
		method: 'POST',
		type: 'form',
		data
	});
}

/**json请求外部接口*/
export function doPostJson(data) {
	return request({
		url: `/market-web/xodb/doPostJson`,
		method: 'post',
		data
	});
}
/**确认收货*/
export function confirmReceipt(data) {
	return request({
		url: '/trip-api/api/orderExpress/confirmReceipt',
		method: 'post',
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
		data: Qs.stringify(data)
	});
}
/**取消订单*/
export function userCancelOrder(data) {
	return request({
		url: '/trip-api/api/order/userCancelOrder',
		method: 'post',
		type: 'form',
		data
	});
}
/**修改订单地址*/
export function updateAddress(data) {
	return request({
		url: '/trip-api/api/order/updateAddress',
		method: 'post',
		type: 'form',
		data
	});
}
/**获取收货地址列表*/
export function getUserAddrList(params) {
	return request({
		url: '/trip-api/api/userDeliveryAddress/getUserAddrList',
		method: 'post',
		params
	});
}
/**优惠券领取*/
export function getCoupon(params) {
	return request({
		url: `/api/coupon-web/api/coupon/getCoupon`,
		method: 'post',
		params
	});
}
/**获取地址选项*/
export function getAreaJson(params) {
	return request({
		url: '/trip-seller/commonUtil/getAreaJson',
		method: 'get',
		params
	});
}
/**新增修改收货地址*/
export function saveUserAddr(params) {
	return request({
		url: '/trip-api/api/userDeliveryAddress/saveUserAddr',
		method: 'post',
		params
	});
}
/**下单【通用】*/
export function createOrderMult(data) {
	return request({
		// url: '/api/market-web/mallBaseInfo/createOrderMult',
		url: '/trip-api/api/order/createOrderMult',
		method: 'POST',
		type: 'form',
		data
	});
}
/**获取订单列表*/
export function getUserOrderList(data) {
	return request({
		url: '/trip-api/api/order/getUserOrderList',
		method: 'post',
		type: 'form',
		data
	});
}
/**导出订单*/
export function exportUserOrderList(data) {
	return request({
		url: '/trip-api/api/order/exportUserOrderList',
		method: 'post',
		type: 'form',
		responseType: 'blob',
		data
	});
}
/**报错-支付凭证*/
export function saveOfflinePayedVoucher(data) {
	return request({
		url: '/trip-api/api/order/saveOfflinePayedVoucher',
		method: 'post',
		data
	});
}
/**物流信息*/
export function getExpressInfo(params) {
	return request({
		url: `/trip-api/api/orderExpress/getExpressInfo`,
		method: 'get',
		params
	});
}
/**获取订单详情*/
export function getOrderDetail(data) {
	return request({
		url: '/trip-api/api/order/getOrderDetail',
		method: 'post',
		type: 'form',
		data
	});
}
/**获取商品收藏列表*/
export function favoriteProduct(data) {
	return request({
		url: '/trip-api/api/product/favoriteProduct',
		method: 'post',
		type: 'form',
		data
	});
}
/**获取收藏店铺*/
export function getFollowShopList(params) {
	return request({
		url: '/trip-api/api/product/getFollowShopList',
		method: 'get',
		params
	});
}
/**批量取消关注商品*/
export function batchDelCollection(data) {
	return request({
		// url: '/trip-api/api/favorite/batchDelCollection',
		url: '/trip-api/api/product/batchCancelFollowShop',
		method: 'post',
		type: 'form',
		data
		// headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
		// data: Qs.stringify(data)
	});
}
/**申请开票*/
export function saveBillApply(data) {
	return request({
		url: '/trip-api/api/orderBill/saveBillApply',
		method: 'post',
		type: 'form',
		data
	});
}
/**申请退款*/
export function refund(data) {
	return request({
		url: '/trip-api/api/order/refund',
		method: 'post',
		type: 'form',
		data
	});
}
/**获取退款原因*/
export function getRefundReasonOption(params) {
	return request({
		url: `/trip-api/api/order/getRefundReasonOption`,
		method: 'get',
		params
	});
}
/**获取积分信息*/
export function getPointInfo(params) {
	return request({
		url: `/api/point-web/memberCurrent/info`,
		method: 'get',
		params
	});
}
/**获取积分列表*/
export function getPointList(params) {
	return request({
		url: `/api/point-web/memberPointLog/getList`,
		method: 'get',
		params
	});
}
/**删除收货地址*/
export function delAddr(data) {
	return request({
		url: '/trip-api/api/userDeliveryAddress/delAddr',
		method: 'post',
		type: 'form',
		data
	});
}
/**标准电商创建订单接口*/
export function createOrder(data) {
	return request({
		url: '/trip-api/api/order/createOrder',
		method: 'post',
		type: 'form',
		data
	});
}
/**获取优惠券*/
export function getGoodsCoupons(data) {
	return request({
		url: '/api/coupon-web/api/couponCode/getCouponListForCurOrder',
		method: 'post',
		data
	});
}
/**获取购物卡信息*/
export function getGoodsGift(data) {
	return request({
		url: '/api/coupon-web/api/giftCardRecord/getGiftCardListForCurOrder',
		method: 'post',
		data
	});
}
/**集供详情*/
export function getBillOrderList(data) {
	return request({
		url: `/trip-api/api/orderBill/getBillOrderList`,
		method: 'post',
		type: 'form',
		data
	});
}
/***查询发票详情*/
export function getBillApplyById(data) {
	return request({
		url: '/trip-api/api/orderBill/getBillApplyById',
		method: 'post',
		type: 'form',
		data
	});
}
/***获取店铺作品*/
export function getWorkList(params) {
	return request({
		url: '/api/activity-web/actStory/memberStoryList',
		method: 'get',
		params
	});
}
/**评价*/
export function comment(data) {
	return request({
		url: '/trip-api/api/comment/commentNew',
		method: 'post',
		type: 'form',
		data
	});
}
