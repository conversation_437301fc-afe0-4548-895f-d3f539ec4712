<template>
	<div class="baseInfo">
		<div class="main-page user-page">
			<div class="field">
				<span class="label">用户头像</span>
				<div class="value-area">
					<div class="img">
						<!--						<img :src="defaultAvatar" alt="" srcset="" />-->
						<headAvator
							ref="headerView"
							class="header-img"
							:own-id="_userinfo.id"
							@uploadPhotoUrl="uploadPhotoUrl"
						/>
					</div>
					<span class="cursor-span" @click="$refs.headerView.chooseImage()">去修改></span>
				</div>
			</div>
			<div class="field">
				<span class="label">用户昵称</span>
				<div class="value-area">
					<div v-if="!editName" class="middle">
						<p class="p1">{{ _userinfo.nickname }}</p>
						<p class="p2">昵称可用于对外沟通的展示</p>
					</div>
					<el-input
						v-if="editName"
						v-model="nickname"
						maxlength="15"
						show-word-limit
						clearable
						size="large"
						class="text"
					></el-input>

					<span v-if="!editName" class="cursor-span" @click="startEdit('nickname', 'editName')">
						去修改>
					</span>
					<span
						v-if="editName"
						class="cursor-span confirm-span"
						@click="changeInfo('nickname', 'editName')"
					>
						确定
					</span>
				</div>
			</div>
			<div class="field">
				<span class="label">绑定手机</span>
				<div class="value-area">
					<div class="middle">
						<p class="p1">{{ _userinfo.phone }}</p>
						<p class="p2">
							您在平台的注册手机号，此手机号将收到平台的业务短信通知，也可以作为您的登录凭证
						</p>
					</div>
				</div>
			</div>
			<div class="field">
				<span class="label">绑定邮箱</span>
				<div class="value-area">
					<div v-if="!editEmail" class="middle">
						<p class="p1">{{ _userinfo.email || '-' }}</p>
						<p class="p2">您在平台的绑定的邮箱号，此邮箱号可接收电子发票</p>
					</div>
					<el-input v-if="editEmail" v-model="email" clearable size="large" class="text"></el-input>
					<span v-if="!editEmail" class="cursor-span" @click="startEdit('email', 'editEmail')">
						去修改>
					</span>
					<span
						v-if="editEmail"
						class="cursor-span confirm-span"
						@click="changeInfo('email', 'editEmail')"
					>
						确定
					</span>
				</div>
			</div>
			<div class="field">
				<span class="label">身份绑定</span>
				<div class="value-area">
					<div class="middle">
						<p class="p1">{{ identityNames || '' }}</p>
						<p class="p2">身份作为您在平台的角色证明，请认真对待</p>
					</div>
				</div>
			</div>
			<!--			<div class="field">-->
			<!--				<span class="label">登录密码</span>-->
			<!--				<div class="value-area">-->
			<!--					<div class="middle">-->
			<!--						<p class="p1">{{ _userinfo.password || '******' }}</p>-->
			<!--						<p class="p2">请妥善保管您的登录密码</p>-->
			<!--					</div>-->
			<!--					<span class="cursor-span" @click="handlePwdUpdate">去修改></span>-->
			<!--				</div>-->
			<!--			</div>-->
			<div class="field">
				<span class="label">账号状态</span>
				<div class="value-area">
					<p class="text">{{ _userinfo.state == 1 ? '正常' : '禁用' }}</p>
				</div>
			</div>
			<!-- 守护地址 -->
			<el-dialog title="修改登录密码" :visible.sync="dialogFormVisible" width="550px">
				<div class="retrievePwd">
					<RetrieveForm ref="retrieveForm" plain @comp-change="compChange"></RetrieveForm>
				</div>
				<div slot="footer" class="dialog-footer">
					<el-button @click="dialogFormVisible = false">取 消</el-button>
					<el-button type="primary" @click="handlePwdSubmit">确 定</el-button>
				</div>
			</el-dialog>
		</div>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
import RetrieveForm from './form.vue';
import { mapGetters, mapMutations } from 'vuex';

export default {
	components: {
		RetrieveForm
	},
	data() {
		return {
			editName: false,
			nickname: '',
			email: '',
			editEmail: false,
			photoUrl: null,
			dialogFormVisible: false
		};
	},
	computed: {
		...mapGetters(['identity']),
		identityNames() {
			let arr = this.identity.map(item => {
				return item.name;
			});
			return arr.join('/');
		}
	},
	watch: {
		identity(newVal) {
			let names = [];
			if (newVal) {
				newVal.map(item => {
					return item.name;
				});
			}
			return names.join('/');
		}
	},
	methods: {
		...mapMutations('user', ['SET_USER_INFO']),
		/**开始编辑*/
		startEdit(field, ckey) {
			this[field] = this._userinfo[field];
			this[ckey] = true;
		},
		/**修改个人信息*/
		changeInfo(field, ckey) {
			if (field === 'email') {
				if (!/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(this[field])) {
					this.$message.error('邮箱格式错误！');
					return;
				}
			}
			const obj = {};
			obj[field] = this[field];
			// 昵称必填，没有昵称使用旧昵称
			if (!obj.nickname) {
				obj.nickname = this._userinfo.nickname;
			}
			this.$api.personal_api
				.changeUserInfo({
					id: this._userinfo.id,
					...obj
				})
				.then(res => {
					if (res.rCode === 0) {
						this.$message.success('修改成功');
						let newInfo = { ...this._userinfo };
						newInfo[field] = this[field];
						this.SET_USER_INFO(newInfo);
						this[ckey] = false;
					} else {
						this.$message.error('操作失败');
					}
				});
		},
		// 更新头像
		uploadPhotoUrl(id) {
			this.photoUrl = `${baseUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
			this.changeInfo('photoUrl', false);
		},
		// 修改密码
		handlePwdUpdate() {
			this.dialogFormVisible = true;
		},
		// 提交密码修改
		handlePwdSubmit() {
			// 提交数据
			this.$refs.retrieveForm.handleRetrieve();
		},
		// 提交成功后的回调
		compChange() {
			this.dialogFormVisible = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.baseInfo {
	background-color: #fff;
}
.user-page {
	padding: 0 20px;
	.field {
		display: flex;
		justify-content: space-around;
		.label {
			line-height: 100px;
			width: 120px;
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #495267;
		}
		.value-area {
			flex: 1;
			border-bottom: 1px solid #d9d9d9;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20px 0;

			.cursor-span {
				position: absolute;
				right: 10px;
				top: 40px;
				cursor: pointer;
				font-size: 14px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				color: var(--brand-6, #0076e8);
			}
			.confirm-span {
				color: #328fed;
			}
			.img {
				width: 48px;
				height: 48px;
				background: #f4f5f8;
				opacity: 1;
				border-radius: 50%;
				overflow: hidden;
				margin-top: 12px;
				.header-img {
					width: 100%;
					height: 100%;
				}
			}
			.middle {
				width: 80%;
				display: flex;
				flex-flow: column;
				.p1 {
					font-size: 16px;
					color: #333333;
					margin-bottom: 20px;
				}
				.p2 {
					font-size: 14px;
					color: #8390a3;
				}
			}
			.text {
				width: 92%;
			}
		}
	}
}
.retrievePwd {
	padding: 0px 20px;
	::v-deep .el-input {
		display: inline-block;
		height: 38px;
		width: 85%;

		input {
			background: transparent;
			border: 0px;
			border-radius: 0px;
			padding: 12px 5px 12px 15px;
			height: 38px;
			caret-color: #404040;
			&:focus {
				outline: none;
			}
			// &:-webkit-autofill {
			// 	box-shadow: 0 0 0px 1000px $bg inset !important;
			// 	-webkit-text-fill-color: $cursor !important;
			// }
		}
	}

	::v-deep .el-form-item {
		border: 1px solid rgba(255, 255, 255, 0.1);
		background: rgba(0, 0, 0, 0.1);
		border-radius: 5px;
		color: #454545;
	}
	::v-deep .el-input__inner:focus {
		box-shadow: none !important;
	}
	::v-deep .el-tabs__header {
		margin: 0 0 24px;
	}
	::v-deep .el-form-item {
		height: 40px;
		background: #ffffff;
		border-radius: 3px 3px 3px 3px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		&:hover {
			border-color: var(--brand-6, #0076e8);
		}
	}
	::v-deep .el-form-item__content {
		padding-left: 12px;
		line-height: 36px !important;
	}
	::v-deep .el-checkbox {
		display: flex;
		align-items: center;
	}
	::v-deep .el-checkbox__label {
		height: 14px;
		font-size: 14px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #404040;
		line-height: 14px;
	}
	::v-deep .el-button {
		padding: 7px 16px;
	}
	::v-deep .el-form-item {
		margin-bottom: 20px;
	}
	::v-deep .el-checkbox__inner {
		width: 16px;
		height: 16px;
		border-radius: 0;
	}
	::v-deep .loginCode .el-form-item {
		width: 330px;
	}
}
::v-deep .el-dialog__body {
	border-top: 1px solid #eee;
	border-bottom: 1px solid #eee;
}
</style>
