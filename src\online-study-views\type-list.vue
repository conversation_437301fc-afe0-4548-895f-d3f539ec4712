<template>
	<div class="lessonList">
		<!-- <FloatBtn ref="child" /> -->
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<div class="inner">
				<div class="chooseType">
					<div class="type flex1">
						<div class="label">分类筛选：</div>

						<ul class="flex1">
							<li :class="['all', allactive ? 'all-active' : '']" @click="allChoose">全部</li>
							<li
								v-for="(item, index) in typeList"
								:key="index"
								:class="{ liActive: findValue1(item, typeValue) }"
								@click="typeClick(item, 'typeValue', 'sign')"
							>
								{{ item.name }}
								<i class="iconfont icon-guanbi1" @click.stop="deleteValue(item, typeValue)"></i>
							</li>
						</ul>
					</div>
					<div class="type flex1">
						<div class="label">播放类型：</div>
						<div :class="['all', allactive1 ? 'all-active' : '']" @click="allChoose1">全部</div>
						<ul class="flex1">
							<li
								v-for="(item, index) in type2"
								:key="index"
								:class="{ liActive: findValue(item, type2Value) }"
								@click="typeClick(item, 'type2Value')"
							>
								{{ item }}
								<i class="iconfont icon-guanbi1" @click.stop="deleteValue(item, type2Value)"></i>
							</li>
						</ul>
					</div>
					<div class="input flex1">
						<div class="label">关键字：</div>
						<el-input v-model="form.name"></el-input>
						<el-button type="primary" class="search-btn" @click="handleSearch">搜索</el-button>
						<el-button class="reset-btn" @click="handleReset">重置</el-button>
					</div>
				</div>
				<div class="upDown flex2">
					<div class="left flex1">
						<ul class="flex1">
							<li
								v-for="(ele, index) in upList"
								:key="index"
								class="tUp flex1"
								:class="{ liActive: upValue == ele.name }"
								@click="upFn(ele)"
							>
								{{ ele.name }}
								<!-- <i class="el-icon el-icon-bottom iconfont"></i> -->
							</li>
						</ul>
					</div>
				</div>
				<div v-loading="loading" class="lesson">
					<div class="safeMain">
						<div
							v-for="(item, index) in lessonList"
							:key="index"
							class="temp"
							@click="goLessonInfo(item.id)"
						>
							<div class="imgBox">
								<img :src="item.coverImg" class="insureImg" />
								<span class="time-box">{{ item.time }}</span>
								<div class="mask-box">
									<img class="mask-icon" src="@/assets/study_images/play-icon.png" alt="" />
								</div>
							</div>

							<div class="infoBox">
								<div class="name over1">
									{{ item.name }}
								</div>
								<div class="desc over1">免费</div>
								<!-- <div class="star flex1">
									<el-rate v-model="item.score" disabled></el-rate>
									<div class="score">{{ Number(item.score).toFixed(2) }}分</div>
								</div> -->
							</div>
						</div>
					</div>
					<Empty v-if="lessonList.length == 0" :tips="'暂无数据'" />
				</div>
				<div v-if="total" class="page">
					<el-pagination
						background
						:current-page="form.pageNum"
						:page-size="12"
						layout="prev, pager, next, jumper"
						:total="total"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
// import FloatBtn from './components/floatBtn.vue';
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import config from '@/config';
export default {
	components: { subBreadcrumb },
	data() {
		return {
			isNo: false,
			typeList: [],
			typeValue: [],
			type2: ['直播', '录播'],
			type2Value: [],
			upList: [
				{
					name: '最新',
					code: '4'
				},
				{
					name: '销量',
					code: '2'
				}
				// {
				// 	name: '免费',
				// },
			],
			upValue: '',
			score: 5,
			allactive: false,
			allactive1: false,
			form: {
				name: '',
				courseClassId: '',
				courseClassIds: [],
				orderByColumn: 1, // 综合
				isAsc: 'desc',
				isFree: '',
				pageNum: 1,
				pageSize: 12
			},
			totalPage: 0,
			total: 0,
			lessonList: [],
			loading: true
		};
	},
	computed: {},
	mounted() {
		this.form.name = this.$route.query.searchValue;
		this.form.courseClassId = this.$route.query.childId;
		this.form.courseClassIds.push(this.$route.query.id);
		this.lessonTypeFn();
	},
	methods: {
		handleReset() {
			this.form.name = '';
			this.form.pageNum = 1;
			this.lessonTypeFn();
		},
		handleSearch() {
			this.form.pageNum = 1;
			this.lessonListFn();
		},
		checkLogin() {
			if (!this.userinfo.id) {
				let queryString = '?';
				queryString += ['redirect', location.href].join('=');
				queryString = encodeURIComponent(queryString);
				localStorage.setItem(
					'scwl_homepage',
					`${config.domainUrl}${config.appList.valueAddedServices}`
				);
				window.location.href = `${config.domainUrl}scwl_user_main/#/login${queryString}`;
				return;
			}
		},

		findValue(ele, group) {
			if (group.length == 0) return;
			let result = group.indexOf(ele) == '-1' ? false : true;
			return result;
		},
		findValue1(ele, group) {
			if (group.length == 0) return;
			let result = group.indexOf(ele) == '-1' ? false : true;
			return result;
		},
		typeClick(item, group, type) {
			this.form.courseClassId = item.id;
			this.form.pageNum = 1;
			if (this[group].indexOf(item) != '-1') return;
			if (type == 'sign') {
				this[group] = [];
			}
			this[group].push(item);
			this.lessonListFn();
		},
		deleteValue(item, group) {
			this.form.courseClassId = '';
			let index;
			for (let i = 0; i < group.length; i++) {
				if (item == group[i]) {
					index = i;
					break;
				}
			}
			group.splice(index, 1);
			this.lessonListFn();
		},
		// 快捷筛选条件选择
		upFn(item) {
			this.upValue = item.name;
			this.form.orderByColumn = item.code;
			this.form.isAsc = 'desc';
			this.lessonListFn();
		},
		handleSizeChange(val) {
			// console.log(`每页 ${val} 条`);
		},
		handleCurrentChange(val) {
			// console.log(`当前页: ${val}`);
			this.form.pageNum = val;
			// console.log(this.form);
			this.lessonListFn();
		},
		async lessonTypeFn() {
			const { data } = await this.$api.study_api.getCourseClassTree();
			// console.log(data);
			// 展示类型数据
			let list = data || [];
			list.map(item => {
				if (item.id == this.$route.query.id) {
					this.typeList = item?.children || [];
				}
			});
			// this.typeList = data;
			for (let item of this.typeList) {
				if (item.id == this.$route.query.childId) {
					this.typeValue.push(item);
				}
			}
			// this.form.courseClassId
			this.lessonListFn();
		},
		async lessonListFn() {
			this.loading = true;
			if (this.type2Value.length == 1) {
				if (this.type2Value[0] == '付费') {
					this.form.isFree = '0';
				} else {
					this.form.isFree = '1';
				}
			} else {
				this.form.isFree = '';
			}

			// this.form.courseClassIds = this.typeValue.map(item => {
			// 	return item.id;
			// });

			const { data } = await this.$api.study_api.pageList(this.form);
			this.lessonList = data.items;
			this.loading = false;
			this.total = data.total;
			this.totalPage = data.totalPage;
		},
		allChoose() {
			this.form.courseClassId = '';
			this.allactive = !this.allactive;
			if (this.allactive) {
				this.typeValue = [...this.typeList];
			} else {
				this.typeValue = [];
			}
			this.lessonListFn();
		},
		allChoose1() {
			this.allactive1 = !this.allactive1;
			if (this.allactive1) {
				this.type2Value = [...this.type2];
			} else {
				this.type2Value = [];
			}
			this.lessonListFn();
		},
		goLessonInfo(id) {
			this.$router.push({ path: '/freecourses', query: { id } });
		}
	}
};
</script>
<style scoped lang="scss">
.lessonList {
	background: #ffffff;
	padding-top: 20px;
	.imgBox {
		width: 100%;
		border-radius: 6px;
		position: relative;
		.mask-box {
			display: none;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.1);
			align-items: center;
			justify-content: center;
		}
		.mask-icon {
			width: 50px;
			height: 50px;
		}
		.time-box {
			position: absolute;
			right: 4px;
			bottom: 10px;
			padding: 2px 6px;
			border-radius: 4px;
			color: #fff;
			font-size: 12px;
			background: rgba(0, 0, 0, 0.4);
		}
	}
	.main {
		margin-top: 20px;
		.inner {
			margin: 0 auto;
			width: 1200px;
			.chooseType {
				min-height: 150px;
				background: #f7f9fb;
				border-radius: 10px;
				padding: 10px 20px 22px;
				.type {
					// min-height: 50px;
					box-sizing: border-box;
					align-items: baseline;
					margin-top: 20px;
					// border-bottom: 1px solid #e8eaf0;
					.label {
						font-size: 16px;
						font-weight: 400;
						color: #7b7b7b;
						flex-shrink: 0;
					}
					.all {
						padding: 2px 16px;
						color: #333333;
						font-size: 16px;
						cursor: pointer;
						margin: 0 10px;
						line-height: normal;
						height: auto;
					}
					.all-active {
						background: #4f85ff;
						border-radius: 14px;
						color: #ffffff;
						box-sizing: content-box;
						border: 1px solid #4f85ff;
					}
					ul {
						flex-wrap: wrap;
						li {
							font-weight: 400;
							cursor: pointer;
							color: #333333;
							font-size: 16px;
							margin: 0 10px;
							padding: 0 15px;
							height: 30px;
							line-height: 30px;
							border-radius: 4px;
							position: relative;
							overflow: hidden;
						}
						.liActive {
							color: #4f85ff;
						}
					}
				}
				.input {
					// height: 50px;
					margin-top: 20px;
					.label {
						font-size: 16px;
						font-weight: 400;
						color: #7b7b7b;
					}
					.el-input {
						margin: 0 10px 0 30px;
						width: 220px;
					}
					.search-btn {
						background: #4f85ff;
						border-radius: 16px;
					}
					.reset-btn {
						background: #707d99;
						color: #ffffff;
						border-radius: 16px;
					}
				}
			}
			.upDown {
				margin-top: 40px;
				.left {
					ul {
						li {
							margin-right: 25px;
							cursor: pointer;
							background: #f7f9fb;
							padding: 5px 12px;
							height: 26px;
							border-radius: 13px;
							font-size: 16px;
							color: #333333;
						}
						.liActive {
							font-weight: 400;
							color: #4f85ff;
							background: #f7f9fb;
							border: 1px solid #4f85ff;
							.iconfont {
								transform: rotate(180deg);
							}
						}
					}
					.money {
						margin-left: 15px;
						.el-input {
							width: 120px;
						}
						> span {
							color: #bbbbbb;
							padding: 0 8px;
						}
						.isno {
							color: #ff0000;
							margin-left: 10px;
						}
					}
				}
				.right {
					.line {
						width: 1px;
						height: 16px;
						background: #999;
						margin: 0 10px;
					}
					.page {
						margin-right: 22px;
					}
					.btn {
						cursor: pointer;
						width: 30px;
						height: 30px;
						background: #ffffff;
						border: 1px solid #e8eaf0;
						border-radius: 4px;
						text-align: center;
						line-height: 30px;
						color: #bbb;
						margin-right: 10px;
					}
				}
			}
			.lesson {
				box-sizing: border-box;
				margin-top: 30px;
				.safeMain {
					display: flex;
					flex-basis: row;
					flex-wrap: wrap;
					.temp {
						margin-right: 26px;
						position: relative;
						margin-bottom: 40px;
						width: 280px;
						height: 196px;
						background: #ffffff;
						.imgBox {
							height: 160px;
						}
						&:hover {
							// box-shadow: 0px 0px 14px 0px rgba(55, 62, 69, 0.08);
							.mask-box {
								display: flex;
							}
						}
						.insureImg {
							width: 100%;
							height: 160px;
							border-radius: 10px;
							overflow: hidden;
						}
						.infoBox {
							cursor: pointer;
							box-sizing: border-box;
							display: flex;
							flex-direction: row;
							justify-content: space-between;
							margin-top: 14px;
							.name {
								font-size: 16px;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #222222;
							}
							.desc {
								font-size: 16px;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #f33c3c;
								flex-shrink: 0;
							}
						}
					}
					.temp:nth-child(4n) {
						margin-right: 0;
					}
				}
			}
			.page {
				text-align: center;
				padding: 30px 0;
				::v-deep .btn-prev {
					background: #fff;
				}
				::v-deep .btn-next {
					background: #fff;
				}
			}
		}
	}
}
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	.sub-breadcrumb {
		width: 1240px !important;
		height: 40px;
	}
}
</style>
