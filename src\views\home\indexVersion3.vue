<template>
	<div class="app-wrapper">
		<div class="main-container">
			<video
				class="video"
				autoPlay
				loop
				muted
				:src="baseUrl + '/project-ybzy/picture/video/2.mp4'"
				height="100%"
			></video>
			<!-- // 视频静音 -->
			<!-- // 循环播放 -->
			<!-- // 自动播放 -->
			<div class="main-container-header">
				<div class="header_left">
					<el-image :src="require('@/assets/images/homeVersion3/dp_header_left.png')" />
				</div>
				<div class="header_right" @click="openWidos('1')">
					<span class="header_right_button_letter">统一信息门户</span>
					<el-image
						:src="require('@/assets/images/homeVersion3/dp_header_right_button_path.png')"
					/>
				</div>
			</div>
			<div class="body_title">
				<el-image
					class="title_image"
					:src="require('@/assets/images/homeVersion3/dp_body_title.png')"
				/>
			</div>
			<div class="main-container-body">
				<div class="body_content">
					<el-carousel trigger="click">
						<el-carousel-item v-for="(item, k) in adList" :key="item.id + k + ''" :autoplay="false">
							<div
								class="body_content_bg"
								:style="{ backgroundImage: `url(${$judgeFile(item.url)})` }"
								@click="openWidos(item.adUrl)"
							></div>
						</el-carousel-item>
					</el-carousel>
				</div>
			</div>
			<div class="main-footer">
				<div class="main-footer_botton">
					<i class="el-icon-caret-left" @click="onPage('up')"></i>
					<i class="el-icon-caret-right" @click="onPage('next')"></i>
					<div
						v-for="(item, index) in schoolItems.slice(currentI * 4, currentI * 4 + 4)"
						:key="index"
						class="botton_item"
					>
						<el-button round @click="openWidos(item.id)">
							<el-image
								class="botton_item_icon"
								:src="require(`@/assets/images/homeVersion3/${item.iconPath}.png`)"
							/>
							{{ item.name }}
						</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { baseUrl } from '@/config';
export default {
	name: 'IndexVersion',
	data() {
		return {
			baseUrl,
			adList: [
				// {
				// 	link: '',
				// 	img: require('@/assets/images/homeVersion3/ad-bg.png')
				// },
				// {
				// 	link: '',
				// 	img: require('@/assets/images/homeVersion3/ad-bg.png')
				// },
				// {
				// 	link: '',
				// 	img: require('@/assets/images/homeVersion3/ad-bg.png')
				// },
				// {
				// 	link: '',
				// 	img: require('@/assets/images/homeVersion3/ad-bg.png')
				// }
			],
			schoolItems: [
				{ id: '3', iconPath: 'yb_icon_2', name: '宜宾职业技术学院' },
				{ id: '4', iconPath: 'yb_icon_4', name: '南溪职业技术学校' },
				{ id: '5', iconPath: 'yb_icon_5', name: '高县职业技术学校' },
				{ id: '6', iconPath: 'yb_icon_3', name: '宜宾市职业技术学校' }
			],
			currentI: 0
		};
	},
	computed: {},
	mounted() {
		this.getAdvertsByCode();
	},
	methods: {
		//获取广告位
		async getAdvertsByCode() {
			const { result } = await this.$api.shop_api.getAdvertsByCode({
				sysCode: 'pc_certification_banner',
				siteId: this.getSiteId() // 租户id
			});
			this.adList = result.adData || [];
		},
		onPage(type) {
			const len = Math.trunc(this.schoolItems.length / 4);
			if (this.schoolItems.length === 4) return;
			if (type === 'up') {
				this.currentI <= 0 ? (this.currentI = len) : this.currentI--;
				return;
			}
			if (type === 'next') {
				this.currentI >= len ? (this.currentI = 0) : this.currentI++;
				return;
			}
		},
		openWidos(type) {
			if (type.includes('http')) {
				window.open(type);
			} else if (type === '1') {
				window.open(baseUrl + '/project-ybzy/ybzy_zhxy/index.html#/home');
			} else if (type === '3') {
				window.open(baseUrl + '/project-ybzy/ybzy_zhxy/index.html#/login');
			} else if (type === '4') {
				window.open(baseUrl + ':9802/project-ybzy/ybzy_zhxy_standard/index.html#/login');
			} else if (type === '5') {
				window.open(baseUrl + ':9801/project-ybzy/ybzy_zhxy_standard/index.html#/login');
			} else if (type === '6') {
				// window.open(baseUrl+"/project-ybzy/ybzy_zhxy/index.html#/home");
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.app-wrapper {
	&:after {
		visibility: hidden;
		display: block;
		font-size: 0;
		content: ' ';
		clear: both;
		height: 0;
	}
	position: relative;
	height: 100%;
	width: 100%;
	&.mobile.openSidebar {
		position: fixed;
		top: 0;
	}
}
.drawer-bg {
	background: #000;
	opacity: 0.3;
	width: 100%;
	top: 0;
	height: 100%;
	position: absolute;
	z-index: 999;
}

.fixed-header {
	position: fixed;
	top: 0;
	right: 0;
	z-index: 9;
	width: 100%;
	transition: width 0.28s;
}

.hideSidebar .fixed-header {
	width: 100%;
}

.mobile .fixed-header {
	width: 100%;
}

// 新增样式
.main-container {
	height: 80%;
	background-image: url('~@/assets/images/homeVersion3/dp_background.png');
	overflow: hidden;
	background-size: cover;
	.video {
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
	.main-container-header {
		height: 10%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 37px 78px 0 70px;
		position: relative;
		.header_left {
			height: 60px;
			width: 40%;
		}
		.header_right {
			height: 50px;
			width: 222px;
			background-repeat: round;
			background-image: url('~@/assets/images/homeVersion3/dp_header_right_button.png');
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			.header_right_button_letter {
				color: #7cc9f5;
				font-size: 16px;
				height: 50px;
				line-height: 50px;
				margin-right: 10px;
			}
		}
	}
	.body_title {
		// height: 10%;
		text-align: center;
		width: 40.52vw;
		// height: 5.37vh;
		// width: 778px;
		// height: 58px;
		margin: 86px auto 48px;
		.title_image {
			width: 100%;
			height: 100%;
		}
	}
	.main-container-body {
		height: 57vh;
		width: 77.33vw;
		margin: 0 auto;

		.body_content {
			height: 100%;
			border-radius: 12px;
			overflow: hidden;
			background-color: #fdfeff;
			display: flex;
			::v-deep .el-carousel {
				width: 100%;
				height: 100%;
				.el-carousel__container {
					height: 92%;
					box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
					z-index: 6;
					.el-carousel__item {
						display: flex;
					}
				}
				.el-carousel__indicators {
					// height: 73px;
					bottom: 0%;
					.el-carousel__indicator {
						.el-carousel__button {
							width: 60px;
							height: 6px;
							border-radius: 8px;
							width: 60px;

							background: #dae4f5;
							border-radius: 8px;
						}
						&.is-active {
							.el-carousel__button {
								width: 160px;
								background: #0175e8;
							}
						}
					}
				}
			}
			.body_content_bg {
				background-size: 100% 100%;
				width: 100%;
				height: 100%;
			}
		}
	}
	.main-footer {
		width: 100%;
		height: 16%;
		display: flex;
		align-items: center;
		justify-content: center;
		transform: translateY(-28%);
		// background: #fff;
		.main-footer_botton {
			width: 55%;
			height: 50%;
			flex-direction: row;
			display: flex;
			justify-content: center;
			align-items: center;
			// z-index: 1000;
			position: relative;
			.el-icon-caret-right,
			.el-icon-caret-left {
				position: absolute;
				display: inline-block;
				color: #c4dffa;
				font-size: 27px;
				top: 32%;
				cursor: pointer;
				transition: 0.5s;
				&:hover {
					color: #0175e8;
				}
			}
			.el-icon-caret-left {
				left: -25px;
			}
			.el-icon-caret-right {
				right: -25px;
			}
			.botton_item {
				width: 25%;
				text-align: center;
				::v-deep .el-button {
					width: 95%;
					height: 40px;
					font-size: 14px;
					font-weight: 600;
					&:hover {
						background: #0175e8;
						color: #ffffff;
					}
					.botton_item_icon {
						height: 15px;
						width: 15px;
						margin-right: 5px;
						position: relative;
						top: 3px;
					}
					.botton_item_content {
						line-height: 20px;
						height: 20px;
					}
				}
			}
		}
	}
}

.app-main {
	// margin-top: 50px;
	height: calc(100vh - 120px);
	min-height: calc(100vh - 120px);
	overflow: auto;
	padding-top: 0px !important;
	padding: 0px 20px 0px 20px;
	// position: relative;
	// top: 80px;
	margin-top: 80px;
	margin-bottom: 38px;
}
</style>
