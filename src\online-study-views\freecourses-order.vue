<template>
	<div v-loading="loading" class="attract-investment">
		<div class="conter">
			<!--商品信息-->
			<div class="course-info">
				<div class="course-info-header">
					<div class="icon"></div>
					<div class="title">商品清单</div>
				</div>
				<div style="margin-top: 15px">
					<el-table :data="list" style="width: 100%" :header-cell-style="{ background: '#F4F4F4' }">
						<el-table-column label="商品图片" width="180">
							<template slot-scope="scope">
								<img
									v-if="scope.row.coverImg"
									class="img"
									:src="$judgeFile(scope.row.coverImg)"
									alt=""
								/>
							</template>
						</el-table-column>
						<el-table-column prop="name" label="商品"></el-table-column>
						<el-table-column prop="currentPrice" label="单价" width="180">
							<template slot-scope="scope">
								<span>{{ (scope.row.currentPrice || 0).toFixed(2) }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="skuNum" label="数量" width="180"></el-table-column>
						<el-table-column prop="price" label="小计" width="180">
							<template>
								<div class="price">
									<span>¥{{ totalPrices }}</span>
								</div>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
			<!--支付方式-->
			<div class="pay-method-info">
				<div class="pay-method-info-header">
					<div class="icon"></div>
					<div class="title">支付方式</div>
				</div>
				<div class="pay-method-info-offline">
					<el-form label-width="100px">
						<el-form-item label="支付方式：">
							<div class="distribution">
								<div class="distribution-mode distribution-active">
									<span>线下支付</span>
									<div class="img"></div>
									<img class="check-img" src="@/assets/shop-images/check.png" alt="" />
								</div>
							</div>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<!-- 提交订单 -->
			<div class="button">
				<div>
					<span>应付金额：</span>
					<span class="price">¥{{ totalPrices }}</span>
				</div>
				<a href="javascript:void(0)">
					<div class="button-content" @click="submit">提交订单</div>
				</a>
			</div>
		</div>
		<el-dialog
			title="提交结果"
			:visible.sync="dialogVisible"
			width="800px"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:show-close="false"
		>
			<div class="pay-content">
				<div class="pay-img"><i class="iconfont icon-check-circle-filled"></i></div>
				<div class="pay-status">订单提交成功！</div>
				<div class="pay-info">订单号：{{ orderInfo.id }}，请尽快付款！</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="handleToMyCoursePage">前往我的课程</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import ElementUI from 'element-eoss';
export default {
	data() {
		return {
			loading: false,
			list: [],
			totalPrices: 0,
			dialogVisible: false,
			orderInfo: {}
		};
	},
	created() {
		this.getLessonInfo(this.$route.query.id);
	},
	methods: {
		/**
		 * @description 获取课程详情
		 * */
		async getLessonInfo(id) {
			const { data } = await this.$api.study_api.getCourseDetailInfo({ id });
			this.list = [{ ...data, skuNum: 1 }];
			this.totalPrices = data.currentPrice.toFixed(2);
		},
		// 提交订单
		async submit() {
			this.loading = true;
			const { code, data, msg } = await this.$api.study_api.createOrder({
				courseIds: this.list[0].id
			});
			this.loading = false;
			if (code == 200) {
				this.orderInfo = data;
				this.dialogVisible = true;
			} else {
				ElementUI.Message({
					type: 'warning',
					message: msg
				});
			}
		},
		// 前往我的课程
		handleToMyCoursePage() {
			this.dialogVisible = false;
			this.$router.push({
				path: '/personal',
				query: { type: 'shopPersonalIndex' }
			});
		}
	}
};
</script>

<style lang="scss" scoped>
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	margin: 16px auto;
	background: #fff;
	.sub-breadcrumb {
		width: 1200px !important;
		height: 50px;
	}
}

.attract-investment {
	overflow: hidden;

	.conter {
		width: 1200px;
		margin: 0 auto 60px;

		// 课程信息
		.course-info {
			background-color: #fff;
			padding: 16px 20px;

			&-header {
				display: flex;
				align-items: center;
				.icon {
					margin-right: 5px;
					width: 6px;
					height: 20px;
					background: var(--brand-6, '#ca3f3b');
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				.title {
					font-size: 16px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #404040;
					line-height: 24px;
				}
			}
			.price {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 22px;
			}
			.img {
				width: 70px;
				height: 70px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
			}
		}

		// 支付方式
		.pay-method-info {
			background-color: #fff;
			margin-top: 16px;
			padding: 16px 20px;
			&-header {
				display: flex;
				align-items: center;
				.icon {
					margin-right: 5px;
					width: 6px;
					height: 20px;
					background: var(--brand-6, '#ca3f3b');
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				.title {
					font-size: 16px;
					font-family: Source Han Sans SC-Medium, Source Han Sans SC;
					font-weight: 500;
					color: #404040;
					line-height: 24px;
				}
			}
			&-offline {
				margin-top: 15px;
				background: #ffffff;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				border: 1px solid #d9d9d9;
				padding: 16px 0px;
				.distribution {
					display: flex;
				}
				.distribution-mode {
					background: #ffffff;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					border: 1px solid #d9d9d9;
					padding: 8px 18px;
					margin-right: 10px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: #8c8c8c;
					line-height: 22px;
					overflow: hidden;
					position: relative;
					.check-img {
						position: absolute;
						bottom: 0px;
						right: 0px;
					}
					.img {
						position: absolute;
						height: 27px;
						width: 15px;
						bottom: -10px;
						right: -3px;
						background: var(--brand-6, '#ca3f3b');
						-moz-transform: rotate(45deg);
						-webkit-transform: rotate(45deg);
						-o-transform: rotate(45deg);
						transform: rotate(45deg);
					}
				}
				.distribution-active {
					background: #ffffff;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					border: 1px solid var(--brand-6, '#ca3f3b');
					color: var(--brand-6, '#ca3f3b');
				}
			}
		}

		// 提交订单按钮
		.button {
			margin-top: 15px;
			width: 100%;
			height: 70px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			span {
				font-size: 14px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #404040;
			}
			.price {
				font-size: 24px;
				font-family: Rany-Medium, Rany;
				font-weight: 500;
				color: var(--brand-6, '#ca3f3b');
			}
			&-content {
				margin-left: 20px;
				padding: 24px 45px;
				background: var(--brand-6, '#ca3f3b');
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 22px;
			}
		}
	}

	.pay {
		&-content {
			width: 633px;
			padding-top: 85px;
			padding-bottom: 111px;
			text-align: center;
			font-size: 16px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 24px;
			margin: 0 auto;
		}
		&-img i {
			font-size: 63px;
			color: #76bf6a;
		}
		&-status {
			font-size: 30px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #262626;
			line-height: 44px;
			margin: 13px 0 9px 0;
		}
		&-info {
			font-size: 16px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #8c8c8c;
			line-height: 24px;
		}
	}
}
</style>
