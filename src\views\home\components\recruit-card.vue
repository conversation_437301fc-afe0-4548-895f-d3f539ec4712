<!--
 @desc:名企招聘卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- {{cardData.tags}} -->
		<!-- <img
			class="tag-img"
			:src="cardData.img"
			alt=""
		/> -->
		<el-image :src="cardData.img" fit="cover" style="width: 100%; height: 240px"></el-image>

		<footer>
			<div class="footer-left">
				<p>{{ cardData.name }}</p>
			</div>
			<!-- <div class="footer-right">
				{{ cardData.enterprise.industryField }}&nbsp;/ &nbsp;{{ cardData.enterprise.peopleNumCode }}
			</div> -->
		</footer>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(space-between, flex-start);
	position: relative;
	flex-direction: column;
	width: 200px;
	height: 200px;
	border-radius: 8px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	// margin-right: 30px;
	// padding: 22px 20px 0 22px;
	&:nth-child(3n) {
		// margin-right: 0;
	}
	header {
		@include flexBox(space-between);
		padding: 22px 20px 0 20px;
		width: 100%;
		.title {
			display: flex;
			width: 100%;
		}
		p {
			font-size: 14px;
			font-weight: bold;
			color: #333333;
			position: relative;
			padding: 2px 2px 2px 0;
			margin-right: 6px;
		}
		.tag-img {
			height: 24px;
			position: absolute;
			right: 0;
			top: 0;
		}
		span {
			font-size: 16px;
			color: #fe574a;
		}
		.salary {
			flex-shrink: 0;
		}
	}
	.side-box {
		padding: 0 20px;
		font-size: 12px;
		color: #999999;
	}
	.tags-box {
		padding: 0 20px;
		@include flexBox(flex-start);
		width: 100%;
		li {
			margin-right: 10px;
			font-size: 12px;
			padding: 6px 16px;
			background: #f5f5f5;
			color: #666666;
			border-radius: 4px;
		}
	}

	footer {
		@include flexBox(space-between);
		width: 100%;
		height: 40px;
		line-height: 40px;
		padding: 0px 20px;
		font-size: 14px;
		color: #fff;
		position: absolute;
		width: 100%;
		left: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		// background: url('~@/assets/employment-images/com-bg.png') center;
		background-size: cover;
		justify-content: center;
		.footer-left {
			@include flexBox(flex-start);

			.img {
				width: 30px;
				height: 30px;
				margin-right: 14px;
				background: #503adf;
				object-fit: cover;
			}
			p {
				width: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}
</style>
