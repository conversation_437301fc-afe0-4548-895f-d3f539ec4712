<template>
	<div class="password">
		<div class="password-title">登录密码</div>
		<el-form
			ref="ruleForm"
			:model="loginForm"
			:rules="loginRules"
			auto-complete="on"
			label-position="left"
			label-suffix="："
		>
			<el-form-item prop="oldPassword" label="输入原密码" class="form-item">
				<el-input
					ref="oldPassword"
					v-model="loginForm.oldPassword"
					class="form-item-input"
					placeholder="请输入密码，8-12位数字字母特殊字符符号"
					name="oldPassword"
					type="password"
					tabindex="1"
					auto-complete="on"
					:show-password="true"
					:clearable="true"
				/>
			</el-form-item>
			<el-form-item prop="password" label="输入新密码" class="form-item">
				<el-input
					ref="password"
					v-model="loginForm.password"
					class="form-item-input"
					placeholder="请输入密码，8-12位数字字母特殊字符符号"
					:minlength="8"
					:maxlength="16"
					name="password"
					type="password"
					tabindex="1"
					auto-complete="on"
					:show-password="true"
					:clearable="true"
				/>
			</el-form-item>
			<el-form-item prop="isPassword" label="请确认密码" class="form-item">
				<el-input
					ref="isPassword"
					v-model="loginForm.isPassword"
					class="form-item-input"
					placeholder="请再次输入密码"
					:minlength="8"
					:maxlength="16"
					name="isPassword"
					type="password"
					tabindex="1"
					auto-complete="on"
					:show-password="true"
					:clearable="true"
				/>
			</el-form-item>
			<div class="password-tip">
				<div class="password-tip-item" :class="passwordLevel === 1 ? 'bad' : ''">弱</div>
				<div class="password-tip-item" :class="passwordLevel === 2 ? 'good' : ''">中</div>
				<div class="password-tip-item" :class="passwordLevel === 3 ? 'best' : ''">强</div>
			</div>
			<div class="password-button">
				<el-button
					:loading="loading"
					class="submit_btn"
					type="primary"
					@click.native.prevent="handleRetrieve"
				>
					确定
				</el-button>
				<el-button class="submit_btn close" @click.native.prevent="close">取消</el-button>
			</div>
		</el-form>
	</div>
</template>

<script>
import { validPassWord } from '@/utils/validate';
export default {
	name: 'RetrieveForm',
	props: {},
	data() {
		/**校验密码*/
		const validatePassword = (rule, value, callback) => {
			if (!validPassWord(value)) {
				callback(new Error('密码需为8~16位非空字符，并包含数字、大小写字母、特殊字符'));
			} else {
				// 判断密码弱中强
				if (value.length < 11) {
					this.passwordLevel = 1;
				} else if (value.length < 14) {
					this.passwordLevel = 2;
				} else {
					this.passwordLevel = 3;
				}
				callback();
			}
		};
		const validateIsPassword = (rule, value, callback) => {
			if (value != this.loginForm.password) {
				callback(new Error('密码输入不一致'));
			} else {
				callback();
			}
		};
		return {
			passwordLevel: 0,
			loginForm: {
				password: '', // 新密码
				isPassword: '', // 确认密码
				oldPassword: '' // 原密码
			},
			loginRules: {
				password: [{ required: true, trigger: 'change', validator: validatePassword }],
				isPassword: [{ required: true, trigger: 'blur', validator: validateIsPassword }],
				oldPassword: [{ required: true, trigger: 'blur', message: '请输入原密码' }]
			},
			loading: false,
			redirect: undefined
		};
	},
	watch: {
		$route: {
			handler: function (route) {
				this.redirect = route.query && route.query.redirect;
			},
			immediate: true
		}
	},
	methods: {
		/**取消*/
		close() {
			this.loginForm = {
				password: '', // 新密码
				isPassword: '', // 确认密码
				oldPassword: '' // 原密码
			};
		},
		/**重新设置密码*/
		handleRetrieve() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					this.loading = true;
					this.$api.personal_api
						.updatePassword({
							oldPassword: btoa(this.loginForm.oldPassword),
							password: btoa(this.loginForm.password)
						})
						.then(res => {
							if (res.rCode === 0) {
								this.$message({
									message: '修改成功',
									type: 'success'
								});
							} else {
								this.$message({
									message: res.msg,
									type: 'error'
								});
							}
							this.loading = false;
						})
						.catch(() => {
							this.loading = false;
						});
				} else {
					console.log('error submit!!');
					return false;
				}
			});
		}
	}
};
</script>
<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.password {
	background: #ffffff;
	padding: 20px;
	&-title {
		font-size: 18px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #333333;
		margin-bottom: 20px;
	}
	.form-item {
		display: flex;
		align-items: center;
		height: 34px;
		::v-deep .el-form-item__label {
			font-size: 14px;
			font-family: SimSun;
			font-weight: 400;
			color: #8390a3;
			line-height: 46px;
		}
		::v-deep .el-form-item__error {
			width: 400px;
		}
		&-input {
			width: 300px;
		}
	}
	&-button {
		margin-top: 40px;
		display: flex;
		align-items: center;
		margin-left: 177px;
		.submit_btn {
			width: 72px;
			height: 32px;
			border-radius: 6px;
		}
		.close {
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #7a8392;
			margin-left: 20px;
		}
	}
	&-tip {
		display: flex;
		align-items: center;
		margin-left: 107px;
		&-item {
			width: 98px;
			height: 24px;
			background: #ebecf0;
			margin-right: 3px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
		}
		.bad {
			background: #ff6552;
			color: #ffffff;
		}
		.good {
			background: var(--brand-6, #0076e8);
			color: #ffffff;
		}
		.best {
			background: #0bdc27;
			color: #ffffff;
		}
	}
}
</style>
