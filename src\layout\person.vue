<template>
	<div class="app-wrapper">
		<!--  个人中心的头部  -->
		<div :class="{ 'fixed-header': fixedHeader }">
			<PersonHeader></PersonHeader>
		</div>
		<!--  个人中心的路由页面  -->
		<app-main />
		<!--  底部文件  -->
		<SelfFooter />
	</div>
</template>

<script>
import { PersonHeader, AppMain, SelfFooter } from './components';
import ResizeMixin from './mixin/ResizeHandler';

export default {
	name: 'Person',
	components: {
		AppMain,
		PersonHeader,
		SelfFooter
	},
	mixins: [ResizeMixin],
	computed: {
		fixedHeader() {
			return this.$store.state.settings.fixedHeader;
		}
	},
	methods: {}
};
</script>

<style lang="scss" scoped></style>
