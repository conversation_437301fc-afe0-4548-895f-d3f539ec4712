<template>
	<div class="tabs">
		<el-tabs v-model="activeName" class="tabs-tab" @tab-click="handleClick">
			<el-tab-pane
				v-for="(item, index) of list"
				:key="index"
				:label="item.name"
				:name="item.type"
			></el-tab-pane>
		</el-tabs>
		<div class="line"></div>
		<div class="tabs-filter">
			<el-dropdown
				v-for="(item, index) of filters"
				:key="index"
				placement="bottom"
				class="tabs-filter-item"
			>
				<span class="el-dropdown-link">
					{{ item.name }}
					<i class="el-icon-arrow-down"></i>
				</span>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-for="(child, i) of item.list" :key="i * 999">
						<div
							:style="
								filters[0].name === child.label || filters[1].name === child.label
									? 'color:#1890ff'
									: ''
							"
							@click="selectFilter(item.type, child)"
						>
							{{ child.label }}
						</div>
					</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</div>
	</div>
</template>

<script>
export default {
	name: 'Tabs',
	data() {
		return {
			list: [
				{
					name: '全部',
					type: 'all'
				},
				// {
				// 	name: '门票',
				// 	type: 'scene'
				// },
				// {
				// 	name: '酒店',
				// 	type: 'hotel'
				// },
				{
					name: '特产',
					type: 'native'
				},
				{
					name: '文创',
					type: 'culture '
				},
				{
					name: '非遗',
					type: 'heritage '
				}
			],
			filters: [
				{
					value: '',
					name: '默认排序',
					type: 'default',
					list: [
						{
							label: '默认排序',
							value: ''
						},
						{
							label: '积分从低到高',
							value: 'sellPointAsc'
						},
						{
							label: '积分从高到低',
							value: 'sellPointDesc'
						},
						{
							label: '价格从低到高',
							value: 'sellPriceAsc'
						},
						{
							label: '价格从高到低',
							value: 'sellPriceDesc'
						},
						{
							label: '兑换量从高到低',
							value: 'sellNumDesc'
						}
					]
				},
				{
					value: '',
					name: '全部',
					type: 'allPoint',
					list: [
						{
							label: '全部',
							value: []
						},
						{
							label: '1-100',
							value: [1, 100]
						},
						{
							label: '101-1000',
							value: [101, 1000]
						},
						{
							label: '1001-3000',
							value: [1001, 3000]
						},
						{
							label: '3000以上',
							value: [3000]
						}
					]
				}
			],
			activeName: 'all'
		};
	},
	methods: {
		/**切换类型*/
		handleClick() {
			this.getList();
		},
		/**单击下拉选项*/
		selectFilter(type, item) {
			// 默认排序
			if (type === 'default') {
				this.filters[0].name = item.label;
				this.filters[0].value = item.value;
			}
			// 全部积分
			else {
				this.filters[1].name = item.label;
				this.filters[1].value = item.value;
			}
			this.getList();
		},
		/**获取数据*/
		getList() {
			let data = {
				categoryCode: this.activeName === 'all' ? '' : this.activeName, // 商品类型
				sortRule: this.filters[0].value, // 排序规则
				sellPointMin: this.filters[1].value[0] || '', //所需最少积分
				sellPointMax: this.filters[1].value[1] || '' //所需最多积分
			};
			this.$emit('getList', data);
		}
	}
};
</script>

<style scoped lang="scss">
.tabs {
	height: 100px;
	background: #ffffff;
	border-radius: 4px;
	padding-left: 20px;
	margin-top: 15px;
	display: flex;
	flex-direction: column;
	&-tab {
		height: 55px;
		display: flex;
		align-items: flex-end;
		::v-deep .el-tabs__header {
			margin-bottom: 0;
		}
		::v-deep .el-tabs__nav-wrap::after {
			display: none;
		}
	}
	.line {
		width: 1200px;
		height: 1px;
		margin-left: -20px;
		background: #f0f0f0;
	}
	&-filter {
		flex: 1;
		display: flex;
		align-items: center;
		font-size: 14px;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #404040;
		line-height: 14px;
		&-item {
			margin-right: 40px;
			cursor: pointer;
		}
	}
}
</style>
