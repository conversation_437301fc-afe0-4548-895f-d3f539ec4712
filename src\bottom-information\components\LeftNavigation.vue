<template>
	<div class="nav">
		<ul>
			<li
				v-for="(item, index) in list"
				:key="index"
				:class="active === item.code ? 'item active-item' : 'item'"
				@click="activeItem(item)"
			>
				<span class="dot"></span>
				<span class="title">
					{{ item.name }}
				</span>
				<div class="arrow-right">
					<i class="el-icon-arrow-right"></i>
				</div>
				<div v-if="active === index" class="bar"></div>
			</li>
		</ul>
	</div>
</template>

<script>
export default {
	data() {
		return {
			list: [
				{
					name: '关于我们',
					code: 'about-us'
				},
				{
					name: '用户协议',
					code: 'user-agreement'
				},
				{
					name: '免责声明',
					code: 'disclaimer'
				},
				{
					name: '隐私政策',
					code: 'privacy-policy'
				},
				{
					name: '版权声明',
					code: 'copyright-notice'
				},
				{
					name: '联系我们',
					code: 'contact-us'
				},
				{
					name: '帮助中心',
					code: 'help-center'
				},
				{
					name: '网站地图',
					code: 'website-map'
				}
			],
			active: ''
		};
	},
	watch: {
		$route: {
			handler: function (route) {
				this.active = route.query.code;
			},
			immediate: true
		}
	},
	mounted() {
		this.$emit('activeHandel', this.list[0]);
	},
	methods: {
		activeItem(item) {
			this.active = item.code;
			this.$emit('activeHandel', item);
		}
	}
};
</script>

<style lang="scss" scoped>
.nav {
	width: 220px;
	height: 930px;
	background: #fff;
	.item {
		height: 50px;
		line-height: 50px;
		position: relative;
		display: flex;
		align-items: center;
		.dot {
			margin-left: 19px;
			display: inline-block;
			width: 16px;
			height: 16px;
			background: #8590a3;
			border-radius: 50%;
		}
		.title {
			margin-left: 10px;
			display: inline-block;
			cursor: pointer;
			font-size: 14px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #8390a3;
		}
		.arrow-right {
			position: absolute;
			right: 19px;
			top: calc(50% - 15px);
			width: 30px;
			height: 30px;
			line-height: 30px;
			text-align: center;
		}
		.bar {
			position: absolute;
			top: 0;
			left: 0;
			width: 5px;
			height: 100%;
			background: #0076e8;
		}
	}
	.active-item {
		background: linear-gradient(-90deg, #ffffff 0%, #dfeaff 100%);
		.dot {
			background: #0076e8;
		}
		.title {
			color: #0076e8;
		}
		.arrow-right {
			color: #0076e8;
		}
	}
}
</style>
