<template>
	<div class="notice">
		<div
			v-for="(item, index) in alumnaNoticeList"
			:key="index"
			class="noticeContent"
			@click="toDetail(item.id)"
		>
			<div class="leftAboutTime">
				<div class="top">{{ item.publishTime | dateHandel('day') }}</div>
				<div class="bottom">{{ item.publishTime | dateHandel }}</div>
			</div>
			<div class="rightAboutNoticeContent">
				<div class="top">{{ item.title }}</div>
				<div class="bottom">
					{{ item.abstract }}
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	filters: {
		// 用于处理正常时间格式换成年月与日分开
		dateHandel(str, type) {
			let strTime = '';
			let dateArray = str.split(' ')[0].split('-');
			if (type == 'day') {
				strTime = dateArray[0];
			} else {
				strTime = dateArray[1] + '-' + dateArray[2];
			}
			return strTime;
		}
	},
	props: {
		alumnaNoticeList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {};
	},
	methods: {
		// 跳转详情页面
		toDetail(id) {
			this.jumpPage(`alumni-news-detail?updateTitle=通知公告详情页&id=${id}&code=alumnaNotice`);
		},
		jumpPage(url) {
			this.$router.push(url);
		}
	}
};
</script>
<style lang="scss" scoped>
.notice {
	width: 100%;
	height: 100%;
	overflow: hidden;
	.noticeContent {
		cursor: pointer;
		margin: 0 0 20px 0;
		height: 100px;
		width: 100%;
		display: flex;
		.leftAboutTime {
			width: 100px;
			height: 100px;
			border: 1px solid #0076e8;
			border-radius: 8px;
			overflow: hidden;
			.top {
				width: 100%;
				height: 50%;
				font-size: 18px;
				font-weight: 400;
				color: #0076e8;
				text-align: center;
				line-height: 50px;
			}
			.bottom {
				width: 100%;
				height: 50%;
				font-size: 18px;
				font-weight: 400;
				color: #ffffff;
				text-align: center;
				background: #0076e8;
				line-height: 50px;
			}
		}
		.rightAboutNoticeContent {
			flex: 1;
			height: 100%;
			padding: 0 0 0 10px;
			.top {
				padding: 6px 0 0 0;
				width: 286px;
				font-size: 14px;
				font-weight: bold;
				color: #333333;
				overflow: hidden;
				white-space: normal;
				text-overflow: ellipsis;
				overflow: hidden;
				display: -webkit-box;
				/* 限制在一个块元素显示的文本的行数 */
				/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
				-webkit-line-clamp: 2;
				/* 设置或检索伸缩盒对象的子元素的排列方式 */
				-webkit-box-orient: vertical;
			}
			.bottom {
				padding: 8px 0 0 0;
				font-size: 14px;
				font-weight: 400;
				color: #7a8392;
				line-height: 24px;
				white-space: normal;
				text-overflow: ellipsis;
				overflow: hidden;
				display: -webkit-box;
				/* 限制在一个块元素显示的文本的行数 */
				/* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
				-webkit-line-clamp: 2;
				/* 设置或检索伸缩盒对象的子元素的排列方式 */
				-webkit-box-orient: vertical;
			}
		}
	}
}
</style>
