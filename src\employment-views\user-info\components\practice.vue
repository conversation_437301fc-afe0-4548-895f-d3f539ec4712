<!--
 @desc:个人中心 就业创业 我的实习报告
 @author: WH
 @date: 2023/9/23
 -->
<template>
	<div v-loading="loading" class="user-info-box">
		<el-button type="primary" icon="el-icon-circle-plus-outline" @click="createPractice">
			创建实习报告
		</el-button>
		<div class="list-box">
			<Practice v-for="item in list" :key="item.id" :card-data="item" @handle="delReport">
				<ul>
					<li v-for="(file, index) in item.fileList" :key="index">
						<span>{{ file.docName }}</span>
						<p>
							<!-- <el-button icon="el-icon-view" circle></el-button> -->
							<i class="el-icon-download" @click="download(file.id)"></i>
							<!-- <el-button icon="el-icon-download" circle @click="download(file.id)"></el-button> -->
						</p>
					</li>
				</ul>
			</Practice>
		</div>
		<el-dialog title="上传实习报告" :visible.sync="visible">
			<el-form ref="form" :model="form" :rules="rules">
				<div class="business-info">
					<el-form-item label="实习报告标题" prop="title">
						<el-input v-model="form.title" type="input"></el-input>
					</el-form-item>
					<el-form-item label="实习公司名" prop="enterpriseName">
						<el-input v-model="form.enterpriseName" type="input"></el-input>
					</el-form-item>
					<el-form-item label="实习报告" prop="attachment">
						<uploadItem
							accept=".pdf, .doc, .docx"
							class="avatar-uploader"
							:file-number="10"
							:code="dataCode"
							:own-id="uploadId"
							@fileChange="fileChange"
						/>
					</el-form-item>
				</div>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" :loading="submitLoading" @click="submit">保 存</el-button>
				<el-button @click="visible = false">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import uploadItem from '@/alumni-association-views/user-info/components/uploadItem.vue';
import Practice from './practice/practice.vue';
import { v4 as uuidv4 } from 'uuid';
import { alumniUrl } from '@/config';

export default {
	components: { uploadItem, Practice },

	data() {
		return {
			dataCode: 'job_internship_report',
			uploadId: '',
			loading: false,
			submitLoading: false,
			form: {},
			rules: {
				title: [{ required: true, message: '请输入实习报告标题', trigger: 'blur' }],
				enterpriseName: [{ required: true, message: '请输入实习公司名', trigger: 'blur' }]
			},
			visible: false,
			list: []
		};
	},

	mounted() {
		this.getReportList();
		// this.handlerQueryDict();
		// this.getCurriculumInfo();
	},
	methods: {
		download(id) {
			window.open(
				`${alumniUrl}/ybzy/mecpfileManagement/front/downloadByAdjunctId.json?adjunctId=${id}`
			);
		},

		noDataFn() {},

		createPractice() {
			this.visible = true;
			this.uploadId = uuidv4();
		},
		// 提交数据
		submit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.form.id = this.uploadId;
					this.save();
				}
			});
		},

		async getReportList() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.employment_api.getReportList({
					asc: false,
					orderBy: 'createTime' //降序排列
				});
				if (rCode == 0) {
					if (results?.records.length > 0) {
						let list = results?.records;
						const requests = [];
						for (let i = 0; i < list.length; i++) {
							const item = list[i];
							this.uploadId = item.id;
							// item = item.attachment.join(',');
							requests.push(this.getFileInfo());
							this.list.push({
								...item
							});
						}
						const responses = await Promise.all(requests);
						this.responsesFn(responses);
					} else {
						this.list = [];
					}
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		responsesFn(arr) {
			arr.forEach((item, index) => {
				let arrSon = item.results.map(itemson => {
					this.previewFile(itemson.adjunctId);
					return { docName: itemson.originalName, id: itemson.adjunctId };
				});
				this.$set(this.list, index, { ...this.list[index], fileList: arrSon });
				// this.list[index].fileList=arrSon
			});
		},
		async delReport() {
			try {
				this.loading = true;
				let { rCode, msg } = await this.$api.employment_api.delReport({
					id: this.uploadId
				});
				if (rCode == 0) {
					this.list = [];
					this.getReportList();
					this.$message.success(msg);
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		async getFileInfo() {
			try {
				this.loading = true;
				let res = await this.$api.alumni_api.getFileInfo({
					code: this.dataCode,
					ownId: this.uploadId,
					userId: this.$store.state.user.userInfo
				});
				return res;
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		async previewFile(adjunctId) {
			let res = await this.$api.alumni_api.previewFile({
				adjunctId
			});

			return res;
		},

		async save() {
			try {
				this.loading = true;
				let { rCode, msg } = await this.$api.employment_api.saveReport(this.form);
				if (rCode == 0) {
					// this.communityTags = results;
					this.$message.success(msg);
					this.list = [];
					this.getReportList();
					this.visible = false;
					this.form = {};

					// this.getUserAuth();
				} else {
					this.$message.error(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.loading = false;
			}
		},
		fileChange(file) {
			if (file) {
				this.$nextTick(() => {
					let files = file.map(item => item?.adjunctId);
					this.$set(this.form, 'attachment', files?.join(',') || '');
					this.$refs.form.validateField('attachment');
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.user-info-box {
	padding: 20px;
	background: #fff;

	.el-input,
	.el-select {
		width: 300px;
	}
}
ul {
	// @include flexBox(flex-start);
	li {
		@include flexBox(space-between);
		cursor: pointer;
		color: #333;

		&:hover {
			color: #0076e8;
		}
		span {
			font-size: 14px;
		}
	}
}
.list-box {
	width: 100%;
	height: 680px;
	padding: 20px;
	border-radius: 4px;
	border: 1px solid #e8eaec;
	overflow: auto;
}
</style>
