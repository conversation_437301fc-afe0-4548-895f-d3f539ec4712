<template>
	<div v-loading="loading" class="GoodsScreen">
		<div class="list">
			<ul style="margin: 0; padding: 0">
				<li
					v-for="(item, index) of screenList"
					:key="index"
					:class="{ 'need-class-list_info': true, show: classIsShow[index] }"
				>
					<span class="need-class_name">{{ item.title }}</span>
					<ul class="need-class-list_val">
						<li
							v-for="(nav, i) of item.list"
							:key="i"
							:class="['text', nav.active ? 'action' : '']"
							@click="classifyClick(item, nav)"
						>
							{{ nav.name }}
						</li>
					</ul>
					<div v-if="item.title != '一级分类'">
						<span v-if="classIsShow[index]" class="more" @click="handleMore(index)">
							收起
							<i class="el-icon-arrow-up"></i>
						</span>
						<span v-else class="more" @click="handleMore(index)">
							更多
							<i class="el-icon-arrow-right"></i>
						</span>
					</div>
				</li>
			</ul>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GoodsScreen',
	props: {
		isTab: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			loading: false,
			classIsShow: [],
			screenList: [
				{
					title: '一级分类',
					list: []
				},
				{
					title: '二级分类',
					list: []
				}
			],
			currentId: '',
			params: {
				type: '',
				typeId: ''
			},
			default: {
				name: '全部',
				id: '',
				code: '',
				active: true,
				children: []
			}
		};
	},
	created() {
		this.currentId = this.$route.query.recommendId || '';
		this.handleList();
		this.$emit('getList', this.params);
	},
	methods: {
		/**克隆数据*/
		clone(obj) {
			return JSON.parse(JSON.stringify(obj));
		},
		/**处理类别结构*/
		handleList() {
			let arr = localStorage.getItem('shopType');
			if (arr) {
				let subArr = [this.clone(this.default)];
				arr = JSON.parse(arr);
				arr.unshift(this.clone(this.default));
				arr.forEach(item => {
					if (item.id === this.currentId) {
						this.params.type = item.code;
						item.active = true;
						subArr = subArr.concat(item.children || []);
					} else {
						item.active = false;
					}
					if (!this.currentId) {
						// “一级分类”选“全部”，“二级分类”应显示全部二级分类
						subArr = subArr.concat(item.children || []);
					}
				});
				this.screenList[0].list = arr;
				// 技术技能培训
				if (this.params.type === 'knowledge') {
					const knowledgeShopType = JSON.parse(localStorage.getItem('shopType-knowledge') || '[]');
					this.screenList[1].list = [this.clone(this.default)].concat(knowledgeShopType);
				} else {
					this.screenList[1].list = subArr;
				}
			}
		},
		/**点击单个分类*/
		classifyClick(item, nav) {
			// 如果是一级分类，改变二级分类的数据
			if (item.title === '一级分类') {
				this.currentId = nav.id;
				this.params.type = nav.code;
				this.params.typeId = ''; // 默认是全部
				if (this.currentId) {
					// 技术技能培训
					if (nav.code === 'knowledge') {
						const knowledgeShopType = JSON.parse(
							localStorage.getItem('shopType-knowledge') || '[]'
						);
						this.screenList[1].list = [this.clone(this.default)].concat(knowledgeShopType);
					} else {
						this.screenList[1].list = [this.clone(this.default)].concat(nav.children || []);
					}
				} else {
					// “一级分类”选“全部”，“二级分类”应显示全部二级分类
					this.screenList[1].list = [this.clone(this.default)];
					item.list.forEach(nav => {
						this.screenList[1].list = this.screenList[1].list.concat(nav.children || []);
					});
				}
				this.screenList[0].list.forEach(item => {
					if (item.id === this.currentId) {
						item.active = true;
					} else {
						item.active = false;
					}
				});
			} else {
				this.params.typeId = nav.id;
			}
			this.screenList[1].list = this.screenList[1].list.map(item => {
				if (item.id === this.params.typeId) {
					item.active = true;
				} else {
					item.active = false;
				}
				return item;
			});
			this.$emit('getList', this.params);
		},
		/**更多收起*/
		handleMore(index) {
			this.$set(this.classIsShow, index, !this.classIsShow[index]);
		}
	}
};
</script>

<style lang="scss" scoped>
.GoodsScreen {
	margin-top: 9px;
	background: #ffffff;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	width: 100%;
	padding: 17px 25px;
	.list {
		padding: 12px 0px;
		&-item {
			height: 50px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border-bottom: 1px solid #eeeeee;
			display: flex;
			align-items: center;
			.title {
				height: 24px;
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: #404040;
				line-height: 24px;
				padding-right: 45px;
			}
			&-nav {
			}
		}
	}
}
.need-class {
	margin-top: 18px;
	display: flex;
	background: #ffffff;
	&_name {
		width: 100px;
		line-height: 26px;
		height: 26px;
		font-size: 16px;
		font-family: Source Han Sans SC-Medium, Source Han Sans SC;
		font-weight: 500;
		color: #404040;
	}
	&-big {
		border-top: 1px solid #eeeeee;
		li {
			width: 126px;
			height: 51px;
			line-height: 51px;
			background: rgba(255, 255, 255, 0.5);
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid #eeeeee;
			border-top: none;
			border-right: none;
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			text-align: center;
			list-style: none;
			cursor: pointer;
			&.action {
				background: linear-gradient(271deg, #e3b3a4 0%, rgba(227, 168, 164, 0) 100%);
				color: var(--brand-6, '#ca3f3b');
				position: relative;
				&::after {
					content: '';
					display: inline-block;
					width: 4px;
					height: 100%;
					background: var(--brand-6, '#ca3f3b');
					position: absolute;
					right: 0;
				}
			}
			&:hover {
				color: var(--brand-6, '#ca3f3b');
			}
		}
	}
	&-list {
		flex: 1;
		font-size: 14px;
		font-family: Source Han Sans SC-Regular, Source Han Sans SC;
		font-weight: 400;
		color: #909090;
		border: 1px solid #eeeeee;
		border-left: none;
		padding-left: 8px;
		padding-right: 19px;
		border-left: 1px solid #eeeeee;
		&_info {
			overflow: hidden;
			height: 50px;
			padding-left: 8px;
			list-style: none;
			display: flex;
			padding-top: 12px;
			background: #ffffff;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border-bottom: 1px dashed #eeeeee;
			align-items: flex-start;
			position: relative;
			padding-right: 68px;
			transition: all 0.2s;
			&.show {
				height: auto;
			}
		}
		&_val {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			flex: 1;
			overflow: hidden;
			li {
				list-style: none;
				margin-right: 18px;
				color: #404040;
				line-height: 26px;
				margin-bottom: 12px;
				padding: 2px 16px;
				font-size: 14px;
				cursor: pointer;
				&:hover {
					color: var(--brand-6, '#ca3f3b');
				}
				&.action {
					padding: 2px 16px;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					background: var(--brand '', '#fff4f3');
					border-radius: 3px 3px 3px 3px;
					font-size: 14px;
					font-family: Source Han Sans SC-Regular, Source Han Sans SC;
					font-weight: 400;
					color: var(--brand-6, '#ca3f3b');
				}
			}
		}
	}
}
.more {
	font-size: 14px;
	font-family: Source Han Sans SC-Regular, Source Han Sans SC;
	font-weight: 400;
	color: #909090;
	line-height: 22px;
	height: 26px;
	display: inline-block;
	position: absolute;
	font-size: 14px;
	font-family: Source Han Sans SC-Regular, Source Han Sans SC;
	font-weight: 400;
	color: #909090;
	cursor: pointer;
	right: 0;
	display: flex;
	align-items: center;
	user-select: none;
	-ms-user-select: none; // IE
	-moz-user-select: none; // 火狐
	-khtml-user-select: none; // 早期浏览器
	-webkit-user-select: none;
	&:hover {
		color: var(--brand-6, '#ca3f3b');
	}
}
::v-deep .el-form-item__label {
	color: #8c8c8c !important;
}
</style>
