<template>
	<div v-loading="loading" class="add_need">
		<div v-if="true" class="head">
			<i class="el-icon-s-home icon" @click="$router.push({ path: '/' })"></i>
			<el-breadcrumb separator-class="el-icon-arrow-right">
				<el-breadcrumb-item v-if="$route.query.iscenter != 1" :to="{ path: '/personal/home' }">
					买家中心
				</el-breadcrumb-item>
				<el-breadcrumb-item v-if="$route.query.iscenter != 1">
					<span class="cur" @click="$router.go(-1)">我的需求</span>
				</el-breadcrumb-item>
				<el-breadcrumb-item v-if="$route.query.iscenter == 1">
					<span class="cur" @click="$router.go(-1)">求购市场</span>
				</el-breadcrumb-item>
				<el-breadcrumb-item>
					<span class="red">{{ $route.query.id ? '编辑' : '发布' }}需求</span>
				</el-breadcrumb-item>
			</el-breadcrumb>
		</div>
		<div class="main" style="width: 1200px">
			<div class="big-title">{{ $route.query.id ? '编辑' : '发布' }}需求</div>
			<div v-if="!$route.query.id" class="tab-box">
				<div class="tab" :class="tabIndex == 0 ? 'act' : ''" @click="handleTab(0)">单个发布</div>
				<div class="tab" :class="tabIndex == 1 ? 'act' : ''" @click="handleTab(1)">批量发布</div>
			</div>
			<Title title="填写采购内容"></Title>
			<div class="mini-title">商品参数：填写采购商品信息</div>
			<el-form
				v-if="tabIndex == 0"
				ref="singleForm"
				:model="singleForm"
				:rules="rules"
				inline
				label-width="110px"
				class="single-form"
				label-position="left"
			>
				<el-form-item label="商品分类：" prop="goodsClassify">
					<div class="inp-box">
						<el-cascader
							v-model="singleForm.goodsClassify"
							:options="goodsOptions"
							placeholder="请选择商品分类"
							:props="{ label: 'name', value: 'id', checkStrictly: true }"
						></el-cascader>
					</div>
				</el-form-item>
				<!-- <el-form-item label="商品品类：" prop="goodsCategory">
					<div class="inp-box">
						<el-cascader
							v-model="singleForm.goodsCategory"
							:options="categoryOptions"
							placeholder="请选择商品品类"
							:props="{ label: 'name', value: 'id', checkStrictly: true }"
						></el-cascader>
					</div>
				</el-form-item> -->
				<el-form-item label="提货方式：" prop="pickUpType">
					<div class="inp-box">
						<el-select v-model="singleForm.pickUpType" placeholder="请选择提货方式">
							<el-option label="买家自提" value="1"></el-option>
							<el-option label="物流配送" value="2"></el-option>
						</el-select>
					</div>
				</el-form-item>
				<el-form-item label="采购数量：" prop="quantity">
					<div class="inp-box">
						<el-input-number
							v-model="singleForm.quantity"
							:min="1"
							:controls="false"
							placeholder="请输入采购数量"
						></el-input-number>
					</div>
				</el-form-item>
				<el-form-item label="期望单价:" prop="expectedPrice">
					<div class="inp-box">
						<el-input-number
							v-model="singleForm.expectedPrice"
							:min="1"
							:controls="false"
							placeholder="请输入期望单价"
						></el-input-number>
					</div>
				</el-form-item>
				<el-form-item label="计量单位：" prop="units">
					<div class="inp-box">
						<el-input v-model="singleForm.units" placeholder="请输入计量单位"></el-input>
					</div>
				</el-form-item>
				<el-form-item label="期望货源地：" prop="expectedAddr">
					<div class="inp-box">
						<el-cascader
							v-model="singleForm.expectedAddr"
							:options="addresOptions"
							placeholder="请选择期望货源地"
							:props="{ label: 'name', value: 'value' }"
						></el-cascader>
					</div>
				</el-form-item>
				<el-form-item v-if="singleForm.pickUpType != 1" label="收货地：" prop="receiveAddr">
					<div class="inp-box">
						<el-cascader
							v-model="singleForm.receiveAddr"
							:options="addresOptions"
							placeholder="请选择收货地"
							:props="{ label: 'name', value: 'value' }"
						></el-cascader>
					</div>
				</el-form-item>
				<el-form-item v-if="singleForm.pickUpType != 1" label="收货地址：" prop="receiveAddrDetail">
					<div class="inp-box">
						<el-input
							v-model="singleForm.receiveAddrDetail"
							placeholder="请输入收货地址"
						></el-input>
					</div>
				</el-form-item>
				<el-form-item label="补充说明：">
					<div class="inp-box1 textarea-box" :style="{ width: '950px' }">
						<el-input
							v-model="singleForm.illustrate"
							type="textarea"
							placeholder="请输入内容"
							:rows="4"
						></el-input>
					</div>
				</el-form-item>
			</el-form>
			<div v-else class="box">
				<el-form
					v-for="(item, index) in formList"
					:key="index"
					:ref="item.name"
					:model="item"
					:rules="rules"
					inline
					label-width="105px"
					class="single-form bg"
					:class="item.name"
					label-position="left"
				>
					<div v-if="index !== 0" class="del" @click="handleDel(index)">删除</div>
					<el-form-item label="商品分类：" prop="goodsClassify">
						<div class="inp-box">
							<el-cascader
								v-model="item.goodsClassify"
								:options="goodsOptions"
								placeholder="请选择商品分类"
								:props="{ label: 'name', value: 'id', checkStrictly: true }"
							></el-cascader>
						</div>
					</el-form-item>
					<!-- <el-form-item label="商品品类：" prop="goodsCategory">
					<div class="inp-box">
						<el-cascader
							v-model="singleForm.goodsCategory"
							:options="categoryOptions"
							placeholder="请选择商品品类"
							:props="{ label: 'name', value: 'id', checkStrictly: true }"
						></el-cascader>
					</div>
				</el-form-item> -->
					<el-form-item label="提货方式：" prop="pickUpType">
						<div class="inp-box">
							<el-select v-model="item.pickUpType" placeholder="请选择提货方式">
								<el-option label="买家自提" value="1"></el-option>
								<el-option label="物流配送" value="2"></el-option>
							</el-select>
						</div>
					</el-form-item>
					<el-form-item label="采购数量：" prop="quantity">
						<div class="inp-box">
							<el-input-number
								v-model="item.quantity"
								:min="1"
								:controls="false"
								placeholder="请输入采购数量"
							></el-input-number>
						</div>
					</el-form-item>
					<el-form-item label="期望单价:" prop="expectedPrice">
						<div class="inp-box">
							<el-input-number
								v-model="item.expectedPrice"
								:min="1"
								:controls="false"
								placeholder="请输入期望单价"
							></el-input-number>
						</div>
					</el-form-item>
					<el-form-item label="计量单位：" prop="units">
						<div class="inp-box">
							<el-input v-model="item.units" placeholder="请输入计量单位"></el-input>
						</div>
					</el-form-item>
					<el-form-item label="期望货源地：" prop="expectedAddr">
						<div class="inp-box">
							<el-cascader
								v-model="item.expectedAddr"
								:options="addresOptions"
								placeholder="请选择期望货源地"
								:props="{ label: 'name', value: 'value' }"
							></el-cascader>
						</div>
					</el-form-item>
					<el-form-item label="收货地：" prop="receiveAddr">
						<div class="inp-box">
							<el-cascader
								v-model="item.receiveAddr"
								:options="addresOptions"
								placeholder="请选择收货地"
								:props="{ label: 'name', value: 'value' }"
							></el-cascader>
						</div>
					</el-form-item>
					<el-form-item label="收货地址：" prop="receiveAddrDetail">
						<div class="inp-box">
							<el-input v-model="item.receiveAddrDetail" placeholder="请输入收货地址"></el-input>
						</div>
					</el-form-item>
					<el-form-item label="补充说明：">
						<div class="inp-box1 textarea-box" :style="{ width: '950px' }">
							<el-input
								v-model="item.illustrate"
								type="textarea"
								placeholder="请输入内容"
								:rows="4"
							></el-input>
						</div>
					</el-form-item>
				</el-form>
			</div>
			<div v-if="tabIndex == 1" class="add-btn">
				<span class="el-icon-circle-plus-outline" @click="addFormList"></span>
				<span @click="addFormList">新增产品</span>
			</div>
			<div class="mini-title mt45">采购人信息：填写采购基本信息</div>
			<el-form
				ref="contactInfo"
				:model="contactInfo"
				:rules="rules"
				inline
				label-width="105px"
				class="single-form"
				label-position="left"
			>
				<el-form-item label="发布人：" prop="publisher">
					<div class="inp-box">
						<el-input v-model="contactInfo.publisher" placeholder="请输入发布人"></el-input>
					</div>
				</el-form-item>
				<el-form-item label="联系人：" prop="contactPerson">
					<div class="inp-box">
						<el-input v-model="contactInfo.contactPerson" placeholder="请输入联系人"></el-input>
					</div>
				</el-form-item>
				<el-form-item label="联系电话：" prop="contactPhone">
					<div class="inp-box">
						<el-input v-model="contactInfo.contactPhone" placeholder="请输入联系电话"></el-input>
					</div>
				</el-form-item>
				<el-form-item label="有效期：" prop="validity">
					<div class="inp-box">
						<el-date-picker
							v-model="contactInfo.validity"
							format="yyyy-MM-dd"
							value-format="yyyy-MM-dd"
							type="date"
							placeholder="选择日期"
						></el-date-picker>
					</div>
				</el-form-item>
				<div class="line"></div>
			</el-form>
			<div class="btn-box">
				<el-button @click="$router.go(-1)">
					{{ $route.query.id ? '取消编辑' : '取消发布' }}
				</el-button>
				<el-button type="primary" @click="requirementSubmit()">
					{{ $route.query.id ? '确认修改' : '立即发布' }}
				</el-button>
			</div>
		</div>
	</div>
</template>

<script>
import Title from './../components/title.vue';
import { personal } from '@/api/index';
import { Loading } from 'element-eoss';
import { setCookie1, delCookie, getCookie } from '@/utils/auth';
export default {
	name: 'AddNeed',
	components: {
		Title
	},
	data() {
		let phone = (rule, value, callback) => {
			var reg = /^1[3456789]\d{9}$/;
			if (!value) {
				return callback(new Error('联系人电话不能为空'));
			} else if (!reg.test(value)) {
				return callback(new Error('请输入正确的电话'));
			}
			callback();
		};
		return {
			tabIndex: 0,
			rules: {
				goodsClassify: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
				goodsCategory: [{ required: true, message: '请选择商品品类', trigger: 'change' }],
				pickUpType: [{ required: true, message: '请选择提货方式', trigger: 'change' }],
				quantity: [{ required: true, message: '请输入采购数量', trigger: 'blur' }],
				expectedAddr: [{ required: true, message: '请选择期望货源地', trigger: 'blur' }],
				receiveAddr: [{ required: true, message: '请选择收货地', trigger: 'blur' }],
				publisher: [{ required: true, message: '请选择发布人', trigger: 'blur' }],
				contactPerson: [{ required: true, message: '请选择联系人', trigger: 'blur' }],
				expectedPrice: [{ required: true, message: '请输入期望单价', trigger: 'blur' }],
				contactPhone: [{ required: true, validator: phone, trigger: 'blur' }],
				validity: [{ required: true, message: '请选择有效期', trigger: 'blur' }],
				units: [{ required: true, message: '请选择输入计量单位', trigger: 'blur' }],
				receiveAddrDetail: [{ required: true, message: '请输入收货地址', trigger: 'blur' }]
			},
			singleForm: {},
			contactInfo: {},
			addresOptions: JSON.parse(sessionStorage.getItem('siteList')),
			goodsOptions: JSON.parse(sessionStorage.getItem('bulkClassifyList')),
			categoryOptions: JSON.parse(sessionStorage.getItem('retailClassifyList')),
			loading: false,
			formList: [{ name: 'listName0' }],
			id: ''
		};
	},
	async created() {
		if (this.$route.query.id) {
			this.id = this.$route.query.id;
			this.needDetail();
		}
		this.isShopLogin();
		if (getCookie('isEnterpriseAuth') && JSON.parse(getCookie('isEnterpriseAuth'))) {
			return;
		} else {
			let options = {
				target: 'app'
			};
			let loadingInstance = Loading.service(options);
			const res = await personal.scswlLogin({ authType: 'onlyCheck' });
			loadingInstance.close();
			if (res.isSeller) {
				setCookie1('isSeller', 'true');
			} else {
				delCookie('isSeller');
			}
			if (res.isEnterpriseAuth) {
				setCookie1('isEnterpriseAuth', 'true');
			} else {
				delCookie('isEnterpriseAuth');
			}
			if (res.isEnterpriseAuth) {
				return;
			} else {
				this.$confirm('必须完成企业认证，才能发布需求, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {})
					.catch(() => {});
			}
		}
	},
	methods: {
		// 获取详情
		needDetail() {
			this.loading = true;
			personal.needDetail({ id: this.id }).then(res => {
				if (res.code == 200) {
					this.singleForm = res.results;
					let classfly = [];
					classfly[0] = this.singleForm.goodsClassify;
					if (this.singleForm.goodsCategory) {
						classfly.push(this.singleForm.goodsCategory);
					}
					if (this.singleForm.goodsType) {
						classfly.push(this.singleForm.goodsType);
					}
					this.singleForm.goodsClassify = classfly;
					let address = [];
					let address1 = [];
					address[0] = this.singleForm.expectedAddrProvince;
					address[1] = this.singleForm.expectedAddrCity;
					address[2] = this.singleForm.expectedAddrCounty;
					address1[0] = this.singleForm.receiveAddrProvince;
					address1[1] = this.singleForm.receiveAddrCity;
					address1[2] = this.singleForm.receiveAddrCounty;
					this.singleForm.expectedAddr = address;
					this.singleForm.receiveAddr = address1;
					this.contactInfo = {
						publisher: this.singleForm.publisher,
						contactPerson: this.singleForm.contactPerson,
						contactPhone: this.singleForm.contactPhone,
						validity: this.singleForm.validity
					};
				} else {
					this.$message.close();
					this.$message.error(res.msg || '网络错误，请稍候再试！');
				}
				this.loading = false;
			});
		},
		// 删除list
		handleDel(i) {
			this.formList.splice(i, 1);
			this.$forceUpdate();
		},
		//  增加list
		addFormList() {
			this.formList.push({ name: `listName${this.formList.length}` });
			this.$forceUpdate();
		},
		// 发布
		requirementSubmit() {
			let params = { requirements: [], contactInfo: {} };
			if (this.tabIndex == 0) {
				this.$refs.singleForm.validate(valid => {
					if (valid) {
						this.$refs.contactInfo.validate(valid1 => {
							if (valid1) {
								this.loading = true;
								let expectedAddrProvince = this.singleForm.expectedAddr[0];
								let expectedAddrCity = this.singleForm.expectedAddr[1];
								let expectedAddrCounty = this.singleForm.expectedAddr[2];
								let receiveAddrProvince = '';
								let receiveAddrCity = '';
								let receiveAddrCounty = '';
								if (this.singleForm.receiveAddr && this.singleForm.receiveAddr.length > 0) {
									receiveAddrProvince = this.singleForm.receiveAddr[0];
									receiveAddrCity = this.singleForm.receiveAddr[1];
									receiveAddrCounty = this.singleForm.receiveAddr[2];
								}
								const {
									pickUpType,
									quantity,
									units,
									receiveAddrDetail,
									illustrate,
									expectedPrice,
									validity
								} = this.singleForm;
								let goodsClassify = this.singleForm.goodsClassify[0];
								let goodsCategory =
									this.singleForm.goodsClassify.length > 1 ? this.singleForm.goodsClassify[1] : '';
								let goodsVariety =
									this.singleForm.goodsClassify.length > 2 ? this.singleForm.goodsClassify[2] : '';
								let item = {
									expectedAddrCity,
									expectedAddrCounty,
									receiveAddrProvince,
									receiveAddrCity,
									receiveAddrCounty,
									pickUpType,
									quantity,
									units,
									receiveAddrDetail,
									illustrate,
									expectedAddrProvince,
									goodsClassify,
									goodsCategory,
									goodsVariety,
									expectedPrice,
									validity,
									id: this.id
								};
								params.requirements.push(item);
								params.contactInfo = this.contactInfo;
								personal.requirementSubmit(params).then(res => {
									if (res.code == 200) {
										this.loading = false;
										this.$message.close();
										this.$message({
											message: res.msg,
											type: 'success'
										});
										this.$router.go(-1);
									} else {
										this.loading = false;
										this.$message.close();
										this.$message.error(res.msg || '网络错误，请稍候再试！');
									}
								});
							} else {
								return false;
							}
						});
					} else {
						return false;
					}
				});
			} else {
				for (let i = 0; i < this.formList.length; i += 1) {
					if (
						!this.formList[i].goodsClassify ||
						!this.formList[i].pickUpType ||
						!this.formList[i].units ||
						!this.formList[i].quantity ||
						!this.formList[i].expectedAddr ||
						!this.formList[i].receiveAddr ||
						!this.formList[i].receiveAddrDetail ||
						!this.formList[i].expectedPrice
					) {
						this.$message.close();
						this.$message.error('请完善资料');
						return;
					}
					let expectedAddrProvince = this.formList[i].expectedAddr[0];
					let expectedAddrCity = this.formList[i].expectedAddr[1];
					let expectedAddrCounty = this.formList[i].expectedAddr[2];
					let receiveAddrProvince = '';
					let receiveAddrCity = '';
					let receiveAddrCounty = '';
					if (this.formList[i].receiveAddr && this.formList[i].receiveAddr.length > 0) {
						receiveAddrProvince = this.formList[i].receiveAddr[0];
						receiveAddrCity = this.formList[i].receiveAddr[1];
						receiveAddrCounty = this.formList[i].receiveAddr[2];
					}

					const {
						validity,
						pickUpType,
						quantity,
						units,
						receiveAddrDetail,
						illustrate,
						expectedPrice
					} = this.formList[i];
					let goodsClassify = this.formList[i].goodsClassify[0];
					let goodsCategory =
						this.formList[i].goodsClassify.length > 1 ? this.formList[i].goodsClassify[1] : '';
					let goodsVariety =
						this.formList[i].goodsClassify.length > 2 ? this.formList[i].goodsClassify[2] : '';
					let item = {
						expectedAddrCity,
						expectedAddrCounty,
						receiveAddrProvince,
						receiveAddrCity,
						receiveAddrCounty,
						pickUpType,
						quantity,
						units,
						receiveAddrDetail,
						illustrate,
						expectedAddrProvince,
						goodsClassify,
						goodsCategory,
						goodsVariety,
						expectedPrice,
						validity
					};
					params.requirements.push(item);
				}
				this.$refs.contactInfo.validate(valid1 => {
					if (valid1) {
						this.loading = true;
						params.contactInfo = this.contactInfo;
						personal.requirementSubmit(params).then(res => {
							if (res.code == 200) {
								this.loading = false;
								this.$message.close();
								this.$message({
									message: res.msg,
									type: 'success'
								});
								this.$router.go(-1);
							} else {
								this.loading = false;
								this.$message.close();
								this.$message.error(res.msg || '网络错误，请稍候再试！');
							}
						});
					} else {
						return false;
					}
				});
			}
		},
		// 切换tab
		handleTab(i) {
			if (this.tabIndex !== i) {
				this.tabIndex = i;
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.add_need {
	width: 100%;
	.head {
		width: 1200px;
		display: flex;
		align-items: center;
		margin: 22px auto;
		.icon {
			color: #9aa3ba;
			font-size: 17px;
			margin-right: 8px;
			cursor: pointer;
		}
		.cur {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.45);
			transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		}
		.cur:hover {
			color: var(--brand-6, '#ca3f3b');
		}
		.red {
			color: var(--brand-6, '#ca3f3b');
		}
	}
	.main {
		width: 1200px;
		background: #fff;
		padding: 28px 0 54px 0;
		margin: 0 auto 60px auto;
		.big-title {
			padding-left: 32px;
			font-size: 24px;
			font-weight: 500;
			color: #404040;
			line-height: 28px;
		}
		.tab-box {
			margin-top: 65px;
			justify-content: center;
			display: flex;
			.tab {
				width: 213px;
				height: 48px;
				border: 1px solid var(--brand-6, '#ca3f3b');
				box-sizing: border-box;
				font-size: 18px;
				text-align: center;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 48px;
				cursor: pointer;
			}
			.act {
				background: var(--brand-6, '#ca3f3b');
				border: none;
				color: #fff;
			}
		}
		::v-deep .title {
			margin: 64px 0 0 58px;
			font-size: 20px;
		}
		.mini-title {
			margin-top: 22px;
			width: 100%;
			font-size: 18px;
			font-weight: 400;
			color: #404040;
			line-height: 32px;
			margin-bottom: 21px;
			padding: 0 65px 0 70px;
		}
		.mt45 {
			margin-top: 45px;
		}
		.single-form {
			margin-top: 22px;
			padding: 0 65px 0 70px;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			::v-deep.el-form-item__label {
				font-size: 14px;
				font-weight: 400;
				color: #404040;
			}
			.inp-box {
				width: 390px;
				::v-deep .el-cascader,
				.el-input-number,
				.el-select,
				.el-date-editor {
					width: 100%;
				}
				::v-deep .el-input-number {
					.el-input__inner {
						text-align: left;
					}
				}
			}
			.textarea-box {
				width: 950px;
			}
			.line {
				margin-top: 40px;
				width: 100%;
				height: 1px;
				background: #eeeeee;
			}
		}
		.bg {
			background: #f4f5f8;
			padding: 0;
			margin: 0 65px 10px 70px;
			padding: 47px 33px 0 33px;
			position: relative;
			.del {
				font-size: 14px;
				cursor: pointer;
				font-weight: 400;
				color: var(--brand-6, '#ca3f3b');
				line-height: 22px;
				position: absolute;
				right: 44px;
				top: 15px;
			}
			.inp-box {
				width: 350px;
			}
			.textarea-box {
				width: 884px;
			}
		}
		.btn-box {
			margin: 39px 0 0 0;
			display: flex;
			justify-content: center;
		}
	}
	.add-btn {
		height: 60px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		display: flex;
		margin: 0 65px 0 70px;
		align-items: center;
		padding-left: 19px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 400;
		color: var(--brand-6, '#ca3f3b');
		span {
			cursor: pointer;
			margin-right: 4px;
		}
	}
}
</style>
