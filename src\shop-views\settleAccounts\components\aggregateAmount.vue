<template>
	<div class="logistics-info">
		<div class="logistics-info-header">
			<div class="icon"></div>
			<div class="title">应付金额信息</div>
		</div>
		<div class="logistics-info-offline">
			<div class="item">
				<div class="lable">商品总额：</div>
				<div class="price content">¥{{ Number(totalPrices).toFixed(2) }}</div>
			</div>
			<div v-if="isPointGoods" class="item">
				<div class="lable">消耗积分：</div>
				<div class="price content">{{ totalPoint }}</div>
			</div>
			<div v-show="show" class="item">
				<div class="lable">运费：</div>
				<div class="price content">¥{{ Number(transportFee).toFixed(2) }}</div>
			</div>
			<!-- <div class="item">
				<div class="lable">优惠金额：</div> -->
			<!-- <div class="content">¥{{ preferentialPrice }}</div> -->
			<!-- <div class="content">¥0</div>
			</div> -->
		</div>
	</div>
</template>

<script>
export default {
	name: 'AggregateAmount',
	props: {
		isPointGoods: {
			type: Boolean,
			default: () => {
				return false;
			}
		}
	},
	data() {
		return {
			formLabelAlign: {
				distributionType: 1
			},
			show: false,
			type: 1,
			totalPrices: 0,
			transportFee: 0,
			preferentialPrice: 0,
			totalPoint: 0
		};
	},
	created() {
		this.type = this.$route.query.type;
	},
	mounted() {},
	methods: {
		distributionClick(item) {
			this.formLabelAlign.distributionType = item;
		}
	}
};
</script>
<style lang="scss" scoped>
.logistics-info {
	background-color: #fff;
	margin-top: 16px;
	padding: 16px 20px;
	&-header {
		display: flex;
		align-items: center;
		.icon {
			margin-right: 5px;
			width: 6px;
			height: 20px;
			background: var(--brand-6, '#ca3f3b');
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
		}
		.title {
			font-size: 16px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #404040;
			line-height: 24px;
		}
	}
	&-offline {
		margin-top: 15px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		border: 1px solid #d9d9d9;
		text-align: right;
		padding: 15px 20px;

		.item {
			display: flex;
			justify-content: flex-end;
			font-size: 14px;
			font-family: Source Han Sans SC-Regular, Source Han Sans SC;
			font-weight: 400;
			color: #404040;
			line-height: 22px;
			.content {
				width: 100px;
			}
			.price {
				font-size: 16px;
				font-family: Source Han Sans SC-Medium, Source Han Sans SC;
				font-weight: 500;
				color: var(--brand-6, '#ca3f3b');
				line-height: 24px;
			}
		}
	}
}
</style>
