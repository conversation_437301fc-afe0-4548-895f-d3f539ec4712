/**
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-09-18 08:45:47
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-09-18 09:12:53
 * @FilePath: /ybzy-zhxy-pc/src/store/getters.js
 * @Description:
 */
const getters = {
	sidebar: state => state.app.sidebar,
	device: state => state.app.device,
	token: state => state.user.token,
	avatar: state => state.user.avatar,
	name: state => state.user.name,
	/**用户信息*/
	userinfo: state => {
		// 如果项目刷新，从本地缓存中取
		if (Object.keys(state.user.userInfo).length === 0) {
			let info = localStorage.getItem('userInfo');
			try {
				state.user.userInfo = info ? JSON.parse(info) : {};
			} catch (error) {
				state.user.userInfo = {};
			}
		}
		return state.user.userInfo;
	},
	/**用户身份权限*/
	roles: state => {
		// 如果项目刷新，从本地缓存中取
		if (state.user.roles.length === 0) {
			let roles = localStorage.getItem('roles');
			state.user.roles = roles ? JSON.parse(roles) : [];
		}
		return state.user.roles;
	},
	/**用户身份信息*/
	identity: state => {
		// 如果项目刷新，从本地缓存中取
		if (state.user.identity.length === 0) {
			let identity = localStorage.getItem('identity');
			state.user.identity = identity ? JSON.parse(identity) : [];
		}
		return state.user.identity;
	}
};
export default getters;
