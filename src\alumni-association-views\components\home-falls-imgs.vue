<template>
	<div class="home-falls-imgs">
		<div class="left">
			<div class="pic1 item-box">
				<el-image
					ref="preview"
					class="imgItem backgroundImg"
					:src="handelPicUrl(pic0.photoUrl)"
					:preview-src-list="[handelPicUrl(pic0.photoUrl)]"
				></el-image>

				<div class="imgText">
					{{ pic0.title ? pic0.title : '' }}
				</div>
			</div>
			<div class="bottom">
				<div class="left-left item-box">
					<el-image
						ref="preview"
						class="imgItem backgroundImg"
						:src="handelPicUrl(pic1.photoUrl)"
						:preview-src-list="[handelPicUrl(pic1.photoUrl)]"
					></el-image>
					<div class="imgText">
						{{ pic0.title ? pic1.title : '' }}
					</div>
				</div>
				<div class="left-right item-box">
					<el-image
						ref="preview"
						class="imgItem backgroundImg"
						:src="handelPicUrl(pic2.photoUrl)"
						:preview-src-list="[handelPicUrl(pic2.photoUrl)]"
					></el-image>
					<div class="imgText">
						{{ pic0.title ? pic2.title : '' }}
					</div>
				</div>
			</div>
		</div>
		<div class="right">
			<div class="top">
				<div class="top-left item-box">
					<el-image
						ref="preview"
						class="imgItem backgroundImg"
						:src="handelPicUrl(pic3.photoUrl)"
						:preview-src-list="[handelPicUrl(pic3.photoUrl)]"
					></el-image>
					<div class="imgText">
						{{ pic0.title ? pic3.title : '' }}
					</div>
				</div>
				<div class="top-right item-box">
					<el-image
						ref="preview"
						class="imgItem backgroundImg"
						:src="handelPicUrl(pic4.photoUrl)"
						:preview-src-list="[handelPicUrl(pic4.photoUrl)]"
					></el-image>
					<div class="imgText">
						{{ pic0.title ? pic4.title : '' }}
					</div>
				</div>
			</div>
			<div class="bottom">
				<div class="bottom-left">
					<div class="bottom-left-top item-box">
						<el-image
							ref="preview"
							class="imgItem backgroundImg"
							:src="handelPicUrl(pic5.photoUrl)"
							:preview-src-list="[handelPicUrl(pic5.photoUrl)]"
						></el-image>
						<div class="imgText">
							{{ pic0.title ? pic5.title : '' }}
						</div>
					</div>
					<div class="bottom-left-bottom item-box">
						<el-image
							ref="preview"
							class="imgItem backgroundImg"
							:src="handelPicUrl(pic6.photoUrl)"
							:preview-src-list="[handelPicUrl(pic6.photoUrl)]"
						></el-image>
						<div class="imgText">
							{{ pic0.title ? pic6.title : '' }}
						</div>
					</div>
				</div>
				<div class="bottom-right item-box">
					<el-image
						ref="preview"
						class="imgItem backgroundImg"
						:src="handelPicUrl(pic7.photoUrl)"
						:preview-src-list="[handelPicUrl(pic7.photoUrl)]"
					></el-image>
					<div class="imgText">
						{{ pic0.title ? pic7.title : '' }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	data() {
		return {
			pic0: {},
			pic1: {},
			pic2: {},
			pic3: {},
			pic4: {},
			pic5: {},
			pic6: {},
			pic7: {}
		};
	},
	mounted() {
		this.getInterfaceShow(8);
	},
	methods: {
		backgroundUrl(url) {
			return `background-image: url('${url}')`;
		},

		//处理图片路径
		handelPicUrl(url) {
			return url ? this.$judgeImg(url, true) : '';
		},
		/**
		 * @description 获取校友风采
		 * */
		getInterfaceShow(pageSize) {
			let data = {
				pageNum: 1,
				pageSize
			};
			this.$api.alumni_association_api.alumniShow(data).then(res => {
				res.results.records.forEach((item, index) => {
					this[`pic${index}`] = item;
				});
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.home-falls-imgs {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 870px;
	display: flex;
	.left {
		width: 645px;
		height: 100%;
		padding: 0 30px 0 0;
		.pic1 {
			height: 540px;
			width: 100%;
		}
		.bottom {
			margin: 40px 0 0 0;
			display: flex;
			overflow: hidden;
			.left-left {
				width: 323px;
				height: 250px;
				margin: 0 31px 0 0;
				overflow: hidden;
			}
			.left-right {
				width: 292px;
				height: 250px;
			}
		}
	}
	.right {
		width: 615px;
		height: 100%;
		.top {
			height: 250px;
			width: 100%;
			display: flex;
			.top-left {
				width: 292px;
				height: 250px;
				margin: 0 31px 0 0;
			}
			.top-right {
				width: 292px;
				height: 250px;
			}
		}
		.bottom {
			margin: 40px 0 0 0;
			height: 540px;
			width: 100%;
			display: flex;
			.bottom-left {
				width: 292px;
				height: 100%;
				margin: 0 31px 0 0;
				.bottom-left-top {
					width: 100%;
					height: 250px;
				}
				.bottom-left-bottom {
					margin: 40px 0 0 0;
					width: 100%;
					height: 250px;
				}
			}
			.bottom-right {
				width: 292px;
				height: 100%;
			}
		}
	}
}
.item-box {
	background: #fff;
	border-radius: 8px;
	overflow: hidden;
}
.imgItem {
	width: 100%;
	height: calc(100% - 60px);
	overflow: hidden;
}
.imgText {
	text-align: center;
	width: 100%;
	height: 60px;
	line-height: 60px;
	font-size: 18px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 0 21px;
}
.backgroundImg {
	// background-image: url('https://img0.baidu.com/it/u=937072262,2445742246&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800');
	background-size: cover; /* 让图片填充整个盒子 */
	background-position: center center; /* 图片居中 */
}
</style>
