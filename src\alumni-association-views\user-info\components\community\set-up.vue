<!--
 @desc:个人中心 校友会 校友社区 我创建的社区
 @author: WH
 @date: 2023/9/12
 -->
<template>
	<div class="main">
		<div v-loading="loading" v-scroll-bottom="handleScroll" class="list-box">
			<no-data
				v-if="list.length == 0"
				:tips="{
					title: '暂无社区',
					detail: '你还没有创建相关社区信息，或者',
					clickText: '申请创建社区'
				}"
				@noDataFn="setUpBtn"
			/>
			<div v-else-if="list.length !== 0 && !showDetail">
				<p class="btn" @click="setUpBtn">
					<i class="el-icon-circle-plus"></i>
					申请创建社区
				</p>
				<set-up-card
					v-for="(item, index) in list"
					:key="index"
					:card-data="item"
					show-btn="invite"
					@btnHandle="btnHandle"
					@clickCard="clickCard"
				/>
			</div>
			<div v-else>
				<svg
					t="1706757369916"
					class="icon"
					viewBox="0 0 1024 1024"
					version="1.1"
					xmlns="http://www.w3.org/2000/svg"
					p-id="35434"
					width="20"
					height="20"
					@click="showDetail = false"
				>
					<path
						d="M585.216 255.104H231.168l118.528-118.528a41.792 41.792 0 0 0 0-59.2 41.792 41.792 0 0 0-59.2 0L104.704 263.104c-8.96 8.96-12.8 20.992-12.032 32.64a41.472 41.472 0 0 0 12.032 32.768l185.664 185.664a41.728 41.728 0 0 0 59.264 0 41.792 41.792 0 0 0 0-59.2L233.472 338.88h346.432c146.304 0 269.568 115.2 272.896 261.504a267.84 267.84 0 0 1-267.52 273.728h-53.568l-1.92-0.128H170.304l-1.92 0.128a41.856 41.856 0 0 0-39.36 35.008v13.76c3.2 19.2 19.392 34.048 39.168 35.008h410.816c192.64 0 353.984-152.384 357.504-344.896a351.744 351.744 0 0 0-351.36-357.888z"
						fill="#0076e8"
						p-id="35435"
					></path>
				</svg>
				<set-up-detail
					:community-id="clickCommunityID"
					:community-data="clickCommunityData"
					@btnHandle="btnHandle"
					@clickCard="clickCard"
				/>
			</div>
		</div>
		<!-- <div class="page">
			<el-pagination
				:current-page="page"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div> -->

		<el-dialog :title="dialogInfo.name" :visible.sync="dialogInfo.visible">
			<el-form
				ref="form"
				v-loading="formLoading"
				:rules="rules"
				:model="formData"
				label-width="140px"
			>
				<el-form-item label="社区主题名称" prop="communityName">
					<el-input v-model="formData.communityName" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="社区容纳人数" prop="capacityNum">
					<el-input v-model="formData.capacityNum" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="社区标签" prop="labelids">
					<el-checkbox-group v-model="formData.labelids">
						<el-checkbox
							v-for="item in communityTags"
							:key="item.value"
							:label="item.value"
							name="type"
						>
							{{ item.label }}
						</el-checkbox>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item label="社区封面图" prop="communityLogo">
					<uploadItem
						class="avatar-uploader"
						:file-number="1"
						:code="dataCode"
						:own-id="uploadId"
						@fileChange="fileChange"
					/>
				</el-form-item>

				<el-form-item label="社区介绍" prop="introduce">
					<el-input
						v-model="formData.introduce"
						autocomplete="off"
						type="textarea"
						:rows="2"
					></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogInfo.visible = false">取 消</el-button>
				<el-button type="primary" @click="educationSubmit">
					{{ dialogInfo.type == 'add' ? '确 定' : '修 改' }}
				</el-button>
			</div>
		</el-dialog>
		<invite-dialog :visible.sync="InviteVisible" :community-id="clickCommunityID" />
	</div>
</template>

<script>
import SetUpCard from './set-up-card.vue';
import SetUpDetail from './set-up-detail.vue';
import InviteDialog from './invite-dialog.vue';
import NoData from '../no-data.vue';
import uploadItem from '../uploadItem.vue';
import { v4 as uuidv4 } from 'uuid';
export default {
	components: { SetUpCard, InviteDialog, SetUpDetail, NoData, uploadItem },
	directives: {
		scrollBottom: {
			bind(el, binding) {
				const scrollHandler = () => {
					const scrollHeight = el.scrollHeight;
					const scrollTop = el.scrollTop;
					const clientHeight = el.clientHeight;

					if (scrollHeight - scrollTop === clientHeight) {
						binding.value(); // 调用绑定的方法
					}
				};

				el._scrollHandler = scrollHandler; // 保存事件处理函数的引用

				el.addEventListener('scroll', scrollHandler);
			},
			//移除了滚动事件的监听器，并删除了保存的事件处理函数的引用
			unbind(el) {
				el.removeEventListener('scroll', el._scrollHandler);
				delete el._scrollHandler;
			}
		}
	},
	data() {
		return {
			clickCommunityID: '',
			clickCommunityData: '',
			dataCode: 'alumna_community_cover',
			uploadId: '',
			showDetail: false,
			InviteVisible: false,
			formLoading: false,
			formData: {
				communityName: '',
				capacityNum: '',
				communityLogo: '',
				labelids: [],
				introduce: ''
			},
			communityTags: [],
			rules: {
				communityName: [{ required: true, message: '请输入社区主题名称', trigger: 'blur' }],
				capacityNum: [{ required: true, message: '请输入社区容纳人数', trigger: 'blur' }],
				communityLogo: [{ required: true, message: '请上传社区封面图片', trigger: 'change' }]
			},
			file: '',
			list: [],
			page: 1, // 页数
			loading: false,
			size: 10, // 条数
			total: 10,
			dialogInfo: {
				visible: false,
				name: '创建社区',
				tupe: 'add'
			}
		};
	},

	mounted() {
		this.getMySetupCommunity();
	},
	beforeDestroy() {
		// 在组件销毁之前解绑指令
		const box = this.$el.querySelector('[v-scroll-bottom]');
		if (box) {
			box.removeEventListener('scroll', box._scrollHandler);
			delete box._scrollHandler;
		}
	},
	methods: {
		handleScroll() {
			// 触底逻辑
			console.log('滚动条触底了！');
		},
		clickCard(cardData, index) {
			this.showDetail = !this.showDetail;
			this.clickCommunityID = cardData?.id || '';
			this.clickCommunityData = cardData || {};
		},
		btnHandle({ btnName, cardData }) {
			switch (btnName) {
				case 'Invite':
					if (cardData.isAudit != 1) {
						return this.$message.warning('社区未通过审核');
					}
					this.clickCommunityID = cardData.id;
					this.InviteVisible = true;
					break;
				case 'update':
					this.setDialogInfo('修改社区', 'update');
					this.getLabelList(); //社区标签  新增跟修改会用到
					this.getCommunityInfo(cardData.id);
					break;

				default:
					break;
			}
		},
		fileChange(file) {
			if (file?.length) {
				this.$nextTick(() => {
					// this.file = file;
					this.formData.communityLogo = file[0]?.adjunctId;
					// this.$set(this.formData, 'communityLogo', file.adjunctId);
					this.$refs.form.validateField('communityLogo');
				});
			}
		},
		setDialogInfo(name, type) {
			this.dialogInfo.name = name;
			this.dialogInfo.type = type;
			this.dialogInfo.visible = true;
		},
		//获取社区标签——————————————————————————————
		async getLabelList() {
			if (this.communityTags?.length > 0) return; //请求过用缓存
			try {
				let { rCode, results } = await this.$api.alumni_api.getLabelList();
				if (rCode == 0) {
					this.communityTags = results;
				}
			} catch (error) {
				console.error('>>>error', error);
				this.communityTags = [];
			}
		},
		//申请创建社区——————————————————————————————
		async setUpCommunity(data) {
			this.formLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.setUpCommunity(data);
				if (rCode == 0) {
					this.$message.success(msg);
					this.dialogInfo.visible = false;
					this.getMySetupCommunity();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.error();
			} finally {
				this.formLoading = false;
			}
		},
		//修改社区—查询详情—————————————————
		async getCommunityInfo(id) {
			this.formLoading = true;
			try {
				let { rCode, msg, results } = await this.$api.alumni_api.getCommunityInfo(id);
				if (rCode == 0) {
					this.formData = results;
					this.formData.labelids = results.labelids.split(',');
					this.uploadId = id;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
			} finally {
				this.formLoading = false;
			}
		},
		//修改社区——————————————————————————————
		async updateCommunity(data) {
			this.formLoading = true;
			try {
				let { rCode, msg } = await this.$api.alumni_api.updateCommunity(data);
				if (rCode == 0) {
					this.$message.success(msg);
					this.formData = {
						labelids: []
					};
					this.dialogInfo.visible = false;
					this.getMySetupCommunity();
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.error();
			} finally {
				this.formLoading = false;
			}
		},
		//查询我创建的社区——————————————————————————————
		async getMySetupCommunity() {
			try {
				this.loading = true;
				let { rCode, msg, results } = await this.$api.alumni_api.getMySetupCommunity({
					asc: true,
					orderBy: 'id',
					pageNum: this.page,
					pageSize: this.size
				});
				if (rCode == 0) {
					this.list = results.records || [];
					this.total = results.total || 0;
				} else {
					this.$message.warning(msg);
				}
			} catch (error) {
				console.error('>>>error', error);
				this.$message.error();
			} finally {
				this.loading = false;
			}
		},
		setUpBtn() {
			this.formData = {
				labelids: []
			};
			this.uploadId = uuidv4();
			this.setDialogInfo('创建社区', 'add');
			this.getLabelList(); //社区标签  新增跟修改会用到
		},
		educationSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					let labelids = [...this.formData.labelids].join(',');
					let type = this.dialogInfo.type;
					// debugger;
					if (type === 'update') {
						this.updateCommunity({ ...this.formData, labelids });
					} else if (type === 'add') {
						this.setUpCommunity({ ...this.formData, labelids, id: this.uploadId });
					}
				}
			});
		},
		noDataFn() {},
		// 条数
		handleSizeChange(i) {
			this.size = i;
			this.$emit('sizeChange', this.size);
		},
		// 页数
		handleCurrentChange(i) {
			this.page = i;
			this.$emit('pageChange', this.page);
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';

.main {
	padding: 20px;
	background: #fff;
	.list-box {
		width: 100%;
		height: 680px;
		overflow: auto;
		position: relative;
		.icon {
			position: absolute;
			top: 0px;
			right: 20px;
			z-index: 6;
		}
		.btn {
			@include flexBox();
			width: 160px;
			height: 36px;
			border-radius: 4px;
			font-size: 14px;
			border: 1px solid #dcdfe6;
			color: #ffffff;
			background: #0076e8;
			cursor: pointer;
			i {
				margin-right: 8px;
				font-size: 18px;
			}
		}
	}
	.page {
		text-align: right;
		margin-top: 20px;
		// ::v-deep .el-pagination {
		// 	display: flex;
		// 	.btn-prev {
		// 		margin-left: auto;
		// 	}
		// }
	}
}
</style>
