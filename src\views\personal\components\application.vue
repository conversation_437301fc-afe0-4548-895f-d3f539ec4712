<template>
	<div class="list">
		<div
			v-for="(app, index) in appList"
			:key="index"
			:class="{
				item: true,
				noRole: !app.url
			}"
			@click="handleHref(app)"
		>
			<div class="item-left">
				<div class="info">
					<p class="name">{{ app.name }}</p>
					<p class="des">{{ app.remark ? app.remark : `提供${app.name}服务` }}</p>
				</div>
				<!-- 应用 logo -->
				<div class="img">
					<el-image class="el-image" :src="app.logo ? app.logo : app.icon" fit="cover" />
				</div>
			</div>
			<!-- 认证按钮 -->
			<div class="noRole-auth">
				<el-button type="primary" @click="handleShowAuth(app.enterpriseTypeList)">去认证</el-button>
			</div>
		</div>
		<!-- 缺省组件 -->
		<el-empty v-if="appList.length == 0" class="empty" description="暂无应用"></el-empty>
		<el-dialog title="应用认证" :visible.sync="dialogVisible" width="800px">
			<div>
				<div class="auth-tips">请选择希望申请的认证类型：</div>
				<div class="auth">
					<el-radio-group v-model="authType">
						<div
							v-for="list in enterpriseTypeList"
							:key="list.id"
							:class="{ 'auth-list': true, action: list.code == authType }"
						>
							<div class="auth-radio">
								<el-radio :label="list.code">
									<div class="auth-box">
										<div class="auth-img">
											<el-image
												style="width: 100%; height: 100%"
												:src="getImgUrl(list.logo)"
												fit="cover"
											></el-image>
										</div>
										<div class="auth-info">
											<span class="auth-info-title">{{ list.title }}</span>
											<div class="auth-info-text">{{ list.remark }}</div>
										</div>
									</div>
								</el-radio>
							</div>
						</div>
					</el-radio-group>
					<el-empty
						v-if="enterpriseTypeList.length == 0"
						description="暂未开通其他认证方式"
					></el-empty>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleToAuthPage">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	name: 'Application',
	data() {
		return {
			appList: [],
			enterpriseTypeList: [],
			// 认证类型
			authType: null,
			dialogVisible: false
		};
	},
	created() {
		this.handleGetExapp();
	},
	methods: {
		/**跳转到认证页面*/
		handleToAuthPage() {
			const authType = this.authType;
			if (authType) {
				this.$router.push({
					path: '/auth-center/authInfo',
					query: {
						code: authType
					}
				});
			} else {
				this.$message.warning('您暂时不能进行该项认证');
			}
		},
		/**获取应用*/
		handleGetExapp() {
			this.$api.personal_api.allAppList().then(res => {
				this.appList = res.results;
			});
		},
		/**跳转app链接*/
		handleHref(app) {
			if (app.url) {
				window.open(app.url);
			} else {
				return;
			}
		},
		/**打开认证列表弹窗*/
		handleShowAuth(data) {
			if (this._userinfo.hasEnpCert) {
				this.dialogVisible = true;
				this.enterpriseTypeList = data;
			} else {
				this.$message.warning('请先完成企业认证！');
			}
		}
	}
};
</script>

<style scoped lang="scss">
.list {
	background: #f5f6fb;
	margin-top: 20px;
	padding: 20px;
	height: auto;
	width: 100%;
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	.item {
		cursor: pointer;
		width: 200px;
		height: 83px;
		background: #ffffff;
		border-radius: 5px;
		padding: 14px;
		margin: 0 20px 20px 0;
		transition: all 0.2s;
		position: relative;
		overflow: hidden;
		&:hover {
			background: #f5fafe;
			box-shadow: 0px 3px 16px 0px rgba(66, 89, 158, 0.15);
			border-radius: 6px 6px 6px 6px;
		}
		&-left {
			display: flex;
			align-items: center;
			.info {
				flex: 1;
				overflow: hidden;
				.name {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					color: #262626;
					font-size: 14px;
					margin-bottom: 12px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.des {
					font-size: 12px;
					color: #9da5b7;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
			.img {
				display: block;
				flex-shrink: 0;
				width: 56px;
				height: 56px;
				border-radius: 5px;
				img {
					width: 100%;
					height: 100%;
				}
			}
		}
		.noRole-auth {
			display: none;
		}
		&.noRole {
			.info,
			.img {
				filter: grayscale(100%);
			}
			background: #f1f2f2;
			.noRole-auth {
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
				background: rgba(54, 60, 67, 0.4);
				justify-content: center;
				align-items: center;
			}
			&:hover {
				.noRole-auth {
					display: flex;
				}
			}
		}
	}
}
.empty {
	margin: 0 auto;
}
</style>
