<!--
 @desc:名企招聘卡片
 @author: WH
 @date: 2023/8/21
 -->
<template>
	<div class="card" @click="clickCard">
		<!-- {{cardData.tags}} -->
		<header>
			<p class="u-line-1">
				{{ cardData.name }}
				<img
					v-if="cardData.isDualSelect"
					class="tag-img"
					:src="require('@/assets/employment-images/tag-samll.png')"
					alt=""
				/>
			</p>
			<span class="salary">
				<!-- {{ cardData.salaryStructure }} -->
				{{ cardData.minMoney || 0 }}-{{ cardData.maxMoney || 0 }}k
			</span>
		</header>
		<p class="side-box">{{ cardData.areaName }}</p>
		<ul class="tags-box u-line-1">
			<li v-for="(item, index) in cardData.tags" :key="index">{{ item }}</li>
		</ul>
		<footer>
			<div class="footer-left">
				<el-image
					v-if="cardData.enterprise.logo"
					class="img"
					:src="getImgUrl(cardData.enterprise.logo)"
					lazy
					fit="cover"
				>
					<img
						slot="error"
						class="img"
						:src="require('@/assets/employment-images/com-defalut-img.jpg')"
						alt=""
					/>
				</el-image>
				<p>{{ cardData.enterprise.corpName }}</p>
			</div>
			<div class="footer-right">
				{{ cardData.enterprise.industryField }}&nbsp;/ &nbsp;{{ cardData.enterprise.peopleNumCode }}
			</div>
		</footer>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';
export default {
	props: {
		cardData: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {};
	},

	methods: {
		/**获取logo*/
		getImgUrl(id) {
			return `${alumniUrl}/ybzy/mecpfileManagement/front/previewAdjunct.json?adjunctId=${id}`;
		},
		clickCard() {
			this.$emit('clickCard', { cardName: 'recruit', ...this.cardData });
		}
	}
};
</script>

<style lang="scss" scoped>
@import '@/styles/public.scss';
.card {
	@include flexBox(space-between, flex-start);
	flex-direction: column;

	width: 400px;
	height: 186px;
	border-radius: 8px;
	margin-bottom: 20px;
	cursor: pointer;
	font-family: Microsoft YaHei;
	background: #ffffff;
	margin-right: 30px;
	// padding: 22px 20px 0 22px;
	&:nth-child(3n) {
		margin-right: 0;
	}
	header {
		@include flexBox(space-between);
		padding: 22px 20px 0 20px;
		width: 100%;
		.title {
			display: flex;
			width: 100%;
		}
		p {
			font-size: 14px;
			font-weight: bold;
			color: #333333;
			position: relative;
			padding: 2px 52px 2px 0;
			margin-right: 6px;
		}
		.tag-img {
			height: 24px;
			position: absolute;
			right: 0;
			top: 0;
		}
		span {
			font-size: 16px;
			color: #fe574a;
		}
		.salary {
			flex-shrink: 0;
		}
	}
	.side-box {
		padding: 0 20px;
		font-size: 12px;
		color: #999999;
	}
	.tags-box {
		padding: 0 20px;
		@include flexBox(flex-start);
		width: 100%;
		li {
			margin-right: 10px;
			font-size: 12px;
			padding: 6px 16px;
			background: #f5f5f5;
			color: #666666;
			border-radius: 4px;
		}
	}

	footer {
		@include flexBox(space-between);
		width: 100%;
		height: 60px;
		padding: 16px 20px;
		font-size: 12px;
		color: #666;
		background: url('~@/assets/employment-images/com-bg.png') center;
		background-size: cover;
		.footer-left {
			@include flexBox(flex-start);
			.img {
				width: 30px;
				height: 30px;
				margin-right: 14px;
				background: #503adf;
				object-fit: cover;
			}
			p {
				width: 106px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}
</style>
