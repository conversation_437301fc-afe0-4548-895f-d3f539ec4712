<template>
	<div class="body">
		<div
			v-for="(item, index) in BtnList"
			:key="index"
			class="items"
			@click="selectTop(item, index)"
		>
			<img class="items-img" :src="require(`@/assets/images/homeVersion4/${item.icon}`)" alt="" />
			<p>{{ item.text }}</p>
		</div>
	</div>
</template>

<script>
import { alumniUrl } from '@/config';

export default {
	name: 'SuspendedWindow',
	components: {},
	data() {
		return {
			BtnList: [
				{ text: '用户入驻', icon: 'yhrz.png', url: '/register' },
				{ text: '联系我们', icon: 'lxwm.png', url: '' }
			],

			topSelect: 0
		};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		selectTop(item, i) {
			this.topSelect = i;
			// if (i === 0) {
			// 	if (getCookie('user_id')) {
			// 		// this.$message.warning('开发中，敬请期待...');
			// 		this.$router.push('/personal'); // 个人中心
			// 	} else {
			// 		this.$router.push('/login'); // 登录
			// 	}
			// } else if (i === 1) {
			// 	this.$router.push('/register'); // 个人中心
			// } else if (i === 2) {
			// 	this.$router.push('/independentPersonal/vocational'); // 职教大厅
			// } else {
			// 	this.$message.warning('功能开发中，敬请期待...');
			// }
			if (item.url == '/independentPersonal/vocational') {
				window.open(
					`${alumniUrl}/project-ybzy/ybzy/index.html#/home?serverId=ybzyDtcSso&authType=6`,
					'_self'
				);
				return;
			}
			if (item.url) {
				this.$router.push(item.url);
			} else {
				this.$message.warning('功能开发中，敬请期待...');
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.body {
	top: 65vh;
	right: 0;
	padding: 5px 0;
	width: 72px;
	background: #ffffff;
	box-shadow: 0px 0px 8px 0px rgba(172, 182, 191, 0.5);
	border-radius: 12px;
	display: flex;
	align-items: center;
	flex-direction: column;
	position: fixed;
	z-index: 99;

	.items {
		display: flex;
		cursor: pointer;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		font-size: 12px;
		color: #767676;
		margin: 8px 0;

		.items-img {
			width: 24px;
			height: 24px;
			margin-bottom: 4px;
		}
	}
}
</style>
