/* 获取URL指定参数值 */
function getParams(name) {
	var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
	var r = window.location.search
		? decodeURI(window.location.search).substr(1).match(reg)
		: decodeURI(window.location.hash)
				.slice(decodeURI(window.location.hash).indexOf('?') + 1)
				.match(reg);
	if (r != null) {
		// return unescape(r[2]);
		return r[2];
	} else {
		return null;
	}
}

export { getParams };
