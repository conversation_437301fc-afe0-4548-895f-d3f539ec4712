/**
 * Name:子项目的路由
 * 目录：shop-views
 * @Params views 全屏展示的页面
 * @Params subViews 嵌套在公共头部下面的子页面
 * */

export default {
	views: [],
	subViews: [
		{
			path: 'shopHome',
			name: 'ShopHome',
			component: () => import('@/shop-views/home/<USER>'),
			meta: { title: '技能商城', noShowBreadCrumb: true }
		},
		{
			path: 'shopGoodsList',
			name: 'ShopGoodsList',
			component: () => import('@/shop-views/goodsList/index'),
			meta: { title: '商品列表' }
		},
		{
			path: 'shopDetail',
			name: 'ShopDetail',
			component: () => import('@/shop-views/goodsList/details'),
			meta: { title: '商品详情' }
		},
		{
			path: 'pointsMall',
			name: 'PointsMall',
			component: () => import('@/shop-views/pointsMall/index'),
			meta: { title: '积分商城' }
		},
		{
			path: 'shopHomePage',
			name: 'ShopHomePage',
			component: () => import('@/shop-views/shopHome/index'),
			meta: { title: '店铺首页' }
		},
		{
			path: 'collectList',
			name: 'CollectList',
			component: () => import('@/shop-views/personal/buyer/collect_list'),
			meta: { title: '我的收藏' }
		},
		{
			path: 'drawback',
			name: 'Drawback',
			component: () => import('@/shop-views/personal/buyer/drawback'),
			meta: { title: '申请退款' }
		},
		{
			path: 'myOrder',
			name: 'MyOrder',
			component: () => import('@/shop-views/personal/buyer/my_order'),
			meta: { title: '我的订单' }
		},
		{
			path: 'orderDetails',
			name: 'OrderDetails',
			component: () => import('@/shop-views/personal/buyer/order_details'),
			meta: { title: '订单详情' }
		},
		{
			path: 'billList',
			name: 'BillList',
			component: () => import('@/shop-views/personal/buyer/bill_list'),
			meta: { title: '开票记录' }
		},
		{
			path: 'billDetails',
			name: 'BillDetails',
			component: () => import('@/shop-views/personal/buyer/bill_details'),
			meta: { title: '开票详情' }
		},
		{
			path: 'shoppingCart',
			name: 'ShoppingCart',
			component: () => import('@/shop-views/shoppingCart/index'),
			meta: { title: '购物车' }
		},
		{
			path: 'settleAccountsIndex',
			name: 'settleAccountsIndex',
			meta: {
				title: '订单结算'
			},
			component: () => import('@/shop-views/settleAccounts/index')
		},
		{
			path: 'shippingAddress',
			name: 'ShippingAddress',
			meta: {
				title: '收货地址'
			},
			component: () => import('@/shop-views/personal/buyer/shipping_address')
		},
		{
			path: 'pay',
			name: 'Pay',
			meta: {
				title: '支付'
			},
			component: () => import('@/shop-views/settleAccounts/pay')
		},
		{
			path: 'paymentSounts',
			name: 'PaymentSounts',
			meta: {
				title: '支付成功'
			},
			component: () => import('@/shop-views/settleAccounts/payment-sounts')
		},
		{
			path: 'appraise',
			name: 'Appraise',
			component: () => import('@/shop-views/personal/buyer/appraise'),
			meta: { title: '评价' }
		}
	]
};
