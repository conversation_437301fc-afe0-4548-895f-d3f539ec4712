<template>
	<div>
		<div class="sub-breadcrumb-box">
			<subBreadcrumb
				:is-main="false"
				icon="el-icon-location"
				text="当前所在位置："
				background="transparent"
				class="sub-breadcrumb"
			></subBreadcrumb>
		</div>
		<div class="main">
			<!-- 筛选区域 -->
			<filter-box
				:filter-list="filterList"
				:filter-form="filterForm"
				@filterChange="filterChange"
			></filter-box>
			<!-- 列表数据区域 -->
			<ul class="list-box">
				<li
					v-for="(item, index) in list"
					:key="index"
					class="list-item"
					@click="jumpPage('/services-detail')"
				>
					<img class="item-img" :src="item.img" alt="" />
					<div class="item-base">
						<p class="name over1">{{ item.name }}</p>
						<span class="desc over2">{{ item.desc }}</span>
						<div class="star flex1">
							<el-rate v-model="item.score" disabled></el-rate>
							<div class="score">{{ Number(item.score).toFixed(2) }}</div>
							<span class="study-num">{{ item.nums }}人学习</span>
						</div>
					</div>
				</li>
				<!-- 分页 -->
				<el-pagination
					class="pagination"
					background
					layout="prev, pager, next,jumper"
					:total="1000"
					@current-change="handleCurrentChange"
				></el-pagination>
			</ul>
		</div>
	</div>
</template>

<script>
import subBreadcrumb from '@/components/subBreadcrumb/sub-breadcrumb';
import filterBox from '../components/filter-box.vue';
export default {
	components: {
		subBreadcrumb,
		filterBox
	},
	data() {
		return {
			filterList: [
				{
					label: '创业项目培训：',
					porp: 'typeValue',
					typeList: [
						{
							name: '培训分类名字',
							code: '1'
						},
						{
							name: '培训分类名字',
							code: '2'
						},
						{
							name: '培训分类名字',
							code: '3'
						},
						{
							name: '培训分类名字',
							code: '4'
						},
						{
							name: '培训分类名字',
							code: '5'
						},
						{
							name: '培训分类名字',
							code: '6'
						}
					]
				}
			], //
			filterForm: {
				typeValue: []
			},
			name: '',
			pageNum: 1, //当前页码
			list: [
				{
					name: '工商服务',
					desc: '免费人工核名查询,30分钟内知晓查询结果,可提供注册地址,全程专人办理',
					score: 4.5,
					nums: 16
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				},
				{
					name: '资金服务',
					desc: '公司验资 选中企缘.提供专业高效审计,评估,代理记帐,公司验资.中国注册税务管...',
					score: 4.3,
					nums: 56
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				},
				{
					name: '法律服务',
					desc: '法规汇编专业的辅导,优质的课程服务,制定学习计划,全面讲解重难点,专业答疑法律',
					score: 3.12,
					nums: 34
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				},
				{
					name: '可可饮品店',
					desc: '公司已于2016年成立，首家门店已经在通辽市科尔沁区开始营业。本公司已于2016年成立，首家门店已经在通辽市科尔沁区开始营业。',
					score: 4.5,
					nums: 6
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				},
				{
					name: '六羡阁茶道',
					desc: '优美且富有意境的环境和亲民的价格吸引了许多人，具有稳定的客源，是办公',
					score: 4.0,
					nums: 1
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				},
				{
					name: '御品源农产品贸易',
					desc: '公司已于2016年成立，首家门店已经在通辽市科尔沁区开始营业。本公司已于2016年成立，首家门店已经在通辽市科尔沁区开始营业。',
					score: 5,
					nums: 113
					// img: require('@/assets/employment-images/BUSINESS-img.png')
				}
			] //列表数据
		};
	},
	methods: {
		/**
		 * @description 分页切换时列表数据重新请求
		 * */
		handleCurrentChange(val) {
			this.pageNum = val;
		},
		/**
		 * @description 点击跳转对应页面
		 * */
		jumpPage(url) {
			this.$router.push(url);
		},
		findValue1(ele, group) {
			if (group.length == 0) return;
			let result = group.indexOf(ele) == '-1' ? false : true;
			return result;
		},
		/**
		 * @description 类型点击事件
		 */
		typeClick(item, group) {
			if (group.indexOf(item) != '-1') return;
			group.push(item);
		},
		/**
		 * @description 类型删除事件
		 */
		deleteValue(item, group) {
			// this.form.courseClassId = '';
			let index;
			for (let i = 0; i < group.length; i++) {
				if (item == group[i]) {
					index = i;
					break;
				}
			}
			group.splice(index, 1);
			// this.lessonListFn();
		}
	}
};
</script>

<style lang="scss" scoped>
$max-width: 1260px;
// 导航栏
.sub-breadcrumb-box {
	width: 100%;
	height: 40px;
	background: #ffffff;
	.sub-breadcrumb {
		width: $max-width !important;
		height: 40px;
	}
}
.main {
	width: $max-width;
	margin: 20px auto 60px;
}
.filter-box {
	width: 100%;
	height: 102px;
	padding: 0 20px;
	background: #ffffff;
	.type {
		height: 50px;
		box-sizing: border-box;
		border-bottom: 1px solid #e8eaf0;
		.all {
			padding: 2px 16px;
			color: #333333;
			font-size: 16px;
			cursor: pointer;
			margin: 0 10px;
		}
		.all-active {
			background: #4f85ff;
			border-radius: 14px;
			color: #ffffff;
			box-sizing: content-box;
			border: 1px solid #4f85ff;
		}
		ul {
			li {
				font-weight: 400;
				cursor: pointer;
				color: #333333;
				font-size: 16px;
				margin: 0 10px;
				padding: 0 15px;
				height: 30px;
				line-height: 30px;
				border-radius: 4px;
				position: relative;
				overflow: hidden;
				position: relative;
			}
			.liActive {
				color: #0076e8;
				border: 1px solid #0076e8;
				border-radius: 4px;
				// width: 20px;
				// height: 20px;
			}
			.item-icon {
				position: absolute;
				top: -6px;
				right: -6px;
				width: 20px;
				height: 20px;
				border-radius: 50%;
				background: #0076e8;
				display: inline-block;
				&::before {
					content: 'X';
					position: absolute;
					z-index: 3;
					color: #fff;
					left: 4px;
					top: -3px;
					font-size: 10px;
				}
			}
		}
	}
	.label {
		font-size: 14px;
		font-weight: bold;
		color: #666666;
	}
	.input {
		height: 50px;
		.el-input {
			margin: 0 10px 0 30px;
			width: 220px;
		}
		.search-btn {
			background: #0076e8;
			border-radius: 4px;
		}
		.reset-btn {
			background: #ffffff;
			color: #666666;
			border-radius: 4px;
		}
	}
}
.list-box {
	width: $max-width;
	margin: 20px auto 60px;
	// margin-top: 30px;
	display: flex;
	flex-wrap: wrap;
	.list-item {
		width: 236px;
		height: 280px;
		// background: #ffffff;
		border-radius: 5px;
		margin-right: 20px;
		margin-bottom: 20px;
		&:nth-child(5n) {
			margin-right: 0;
		}
		.item-img {
			width: 100%;
			height: 140px;
			background: #ffffff;
			border-radius: 8px;
		}
		.item-base {
			padding: 5px 1px;
			.name {
				font-size: 14px;
				line-height: 18px;
				color: #333333;
			}
			.desc {
				font-size: 12px;
				line-height: 18px;
				color: #7a8392;
				margin-top: 12px;
			}
			.star {
				margin-top: 15px;
				font-size: 12px;
				color: #7a8392;
				display: flex;
				align-items: center;
			}
			.study-num {
				display: inline-block;
				padding-left: 8px;
				margin-left: 8px;
				border-left: 1px solid #bcc1c8;
			}
			::v-deep.el-rate {
				height: auto;
				margin-right: 6px;
			}
			::v-deep.el-rate__icon {
				margin: 0;
			}
		}
	}
}
.pagination {
	width: 100%;
	text-align: center;
	margin-top: 20px;
	::v-deep.btn-prev,
	::v-deep.btn-next {
		width: 70px;
		height: 40px;
		line-height: 40px;
		background: #ffffff;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		> span {
			line-height: 40px;
		}
	}
	::v-deep.el-pager {
		.number,
		.btn-quickprev,
		.btn-quicknext {
			background: #ffffff;
			border: 1px solid #e9e9e9;
			padding: 0px 12px;
			height: 40px;
			line-height: 40px;
			border-radius: 4px;
		}
	}
	::v-deep.el-pagination__jump {
		height: 40px;
		line-height: 40px;
	}
}
</style>
