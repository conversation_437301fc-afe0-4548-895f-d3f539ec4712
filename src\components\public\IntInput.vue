<template>
	<el-input
		v-model="modelValue"
		:size="size"
		:disabled="disabled"
		:readonly="readonly"
		@blur="handleBlur"
		@change="handleChange"
	>
		<template v-if="prepend" slot="prepend">{{ prepend }}</template>
		class="pend"
		<template v-if="append" slot="append" class="pend">{{ append }}</template>
	</el-input>
</template>

<script>
/* eslint-disable */
import debounce from '@fe/common/lib/debounce.js';

export default {
	name: 'IntInput',
	props: {
		value: {
			require: true
		},
		max: {
			type: Number
		},
		min: {
			type: Number
		},
		prepend: {
			type: String
		},
		append: {
			type: String
		},
		size: {
			type: String
		},
		required: {
			type: Boolean,
			default: false
		},
		default: {
			type: Number,
			default: 0
		},
		disabled: {
			type: Boolean,
			default: false
		},
		readonly: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			value_: '',
			exceedHandler: null
		};
	},
	computed: {
		modelValue: {
			get() {
				return this.value_;
			},
			set(newVal) {
				// e.value = e.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
				// e.value = e.value.replace(/^\./g, ""); //验证第一个字符是数字而不是
				// e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
				// e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
				// e.value = e.value.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3'); //只能输入一个小数
				// e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数

				const notNum = /[^0-9]/gi;
				let num = parseInt(newVal.replace(notNum, ''));

				if (isNaN(num) || num === 0) {
					num = '';
				} else if (this.max && num > this.max) {
					num = this.max;
					if (this.exceedHandler) {
						this.exceedHandler();
					} else {
						this.exceedHandler = debounce(() => this.$message.info('输入超过最大允许值'), 300);
						this.exceedHandler();
					}
				} else if (this.min && num < this.min) {
					num = this.min;
				}

				this.value_ = num;
			}
		}
	},
	watch: {
		value(newVal) {
			this.value_ = newVal;
		}
	},
	created() {
		this.value_ = this.value;
	},
	methods: {
		handleChange(newVal) {
			this.$emit('input', newVal);
		},
		handleBlur() {
			if (this.required && !this.value) {
				this.value_ = this.default;
				this.handleChange(this.default);
			}
		}
	}
};
</script>
