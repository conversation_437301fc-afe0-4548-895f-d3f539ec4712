<template>
	<div class="boutique-venues">
		<div class="header">
			<div class="store">
				<store-header
					v-if="!loading"
					:shop-info="shopInfo"
					class="header-class"
					:class="`type${styleType3}`"
					@setFollowState="setFollowState"
				/>
			</div>

			<img
				v-if="styleType === 1"
				class="header-img"
				src="@/assets/shop-images/home-title-bg1.png"
				alt=""
			/>
			<img
				v-else-if="styleType === 2"
				class="header-img"
				src="@/assets/shop-images/home-title-bg2.png"
				alt=""
			/>
			<img
				v-else-if="styleType === 3"
				class="header-img"
				src="@/assets/shop-images/home-title-bg3.png"
				alt=""
			/>
			<img
				v-else-if="styleType === 4"
				class="header-img"
				src="@/assets/shop-images/home-title-bg4.png"
				alt=""
			/>
			<img v-else class="header-img" src="@/assets/shop-images/home-title-bg.png" alt="" />

			<img v-if="styleType2 === 1" class="gj-img" src="@/assets/shop-images/guajian1.png" alt="" />
			<img
				v-else-if="styleType2 === 2"
				class="gj-img"
				src="@/assets/shop-images/guajian2.png"
				alt=""
			/>
			<img
				v-else-if="styleType2 === 3"
				class="gj-img"
				src="@/assets/shop-images/guajian3.png"
				alt=""
			/>
			<img
				v-else-if="styleType2 === 4"
				class="gj-img"
				src="@/assets/shop-images/guajian4.png"
				alt=""
			/>
		</div>
		<div class="tabs" :class="`type${styleType1}`">
			<div class="tabs-tab">
				<a v-for="(item, index) of tabList" :key="index" href="javascript:void(0)">
					<div
						:class="['tabs-tab-text', active === index ? 'tabs-tab-text_active' : '']"
						@click="tabActive(index)"
					>
						<div>{{ item }}</div>
					</div>
				</a>
			</div>
		</div>
		<!-- 首页 -->
		<div v-show="active === 0" class="boutique-venues-index">
			<!-- 轮播 -->
			<div v-if="Advertising.length" class="swiper">
				<el-carousel height="768px">
					<el-carousel-item v-for="(item, index) of Advertising" :key="index">
						<a href="javascript:void(0)">
							<img v-if="item.IMAGE_URL" class="swiper-img" :src="item.IMAGE_URL" alt="" />
						</a>
					</el-carousel-item>
				</el-carousel>
			</div>
			<!-- 商品 -->
			<div class="goods-list">
				<goods
					v-loading="shopLoading"
					:is-pagination="false"
					:list="choicenessList"
					:title-text="'本店精选'"
				/>
				<Empty v-show="!choicenessList.length" :tips="'暂无商品信息'" />
			</div>
		</div>
		<!-- 全部商品 -->
		<div v-if="active === 1" class="all-goods">
			<div class="all-goods-text">全部商品</div>
			<goods-screen @getList="getList" />
			<goods-search @search="getList" />
			<Goods
				v-show="list.length"
				ref="allGoods"
				v-loading="listLoading"
				:list="list"
				:total="total"
				@paginationChange="paginationChange"
			/>
			<Empty v-show="!list.length" :tips="'暂无商品信息'" />
		</div>
		<works
			v-if="active === 2"
			v-loading="workLoading"
			:list="workList"
			:total="workTotal"
			@getWorkList="getWorkList"
		></works>
		<coupons
			v-if="active === 3"
			v-loading="couponLoading"
			:coupons-list="couponsList"
			:total="couponTotal"
			@getCouponList="getCounpons"
		></coupons>
	</div>
</template>

<script>
import GoodsScreen from './components/goods-screen.vue';
import GoodsSearch from './components/goods-search.vue';
import Goods from './components/goods.vue';
import storeHeader from './components/store-header.vue';
import works from '@/shop-views/shopHome/components/works.vue';
import coupons from '@/shop-views/shopHome/components/coupons.vue';
import { getCookie } from '@/utils/auth';

export default {
	name: 'BoutiqueVenues',
	components: { storeHeader, Goods, GoodsScreen, GoodsSearch, works, coupons },
	data() {
		return {
			styleType: 0,
			styleType1: 0,
			styleType2: 0,
			styleType3: 0,
			tabList: ['主页', '商品', '作品', '优惠券'],
			active: 0,
			shopId: '',
			shopInfo: {},
			loading: false,
			userId: getCookie('user_id'),
			Advertising: [], // 轮播广告
			/**店铺精选*/
			choicenessList: [],
			shopLoading: false,
			psize: 12,
			/**获取全部商品的参数*/
			searchInfo: {
				type1: 1,
				type: '',
				typeId: '',
				orderRule: 'all',
				minPrice: '',
				maxPrice: '',
				keywords: '',
				psize: 8,
				offset: 0
			},
			list: [],
			total: 0,
			listLoading: false,
			/**作品请求参数和数据*/
			worksParams: {
				pageNum: 1,
				pageSize: 10
			},
			workList: [],
			workTotal: 0,
			workLoading: false,
			/**优惠券相关的数据*/
			couponParams: {
				pageNum: 1,
				pageSize: 10
			},
			couponsList: [],
			couponTotal: 0,
			couponLoading: false
		};
	},
	async created() {
		this.shopId = this.$route.query?.id || '';
		await this.getInfo();
		await this.getChoicenessList();
		// await this.getList();
		await this.getAdvertising();
	},
	methods: {
		/**分页数据*/
		paginationChange(item) {
			if (item.type == 1) {
				this.searchInfo.offset = 0;
			} else {
				this.searchInfo.offset = this.searchInfo.psize * (item.value - 1);
			}
			this.getList();
		},
		/**获取店铺信息*/
		async getInfo() {
			// setInterval(() => {
			// 	const num = this.styleType + 1
			// 	this.styleType = num % 4
			// }, 2000)
			this.loading = true;
			let data = {
				userId: this.userId,
				shopId: this.shopId
			};
			let res = await this.$api.shop_api.getShopSimpleInfo(data);
			this.shopInfo = res?.result || '';
			this.styleType = this.shopInfo.decorateMould ? Number(this.shopInfo.decorateMould) : 0;
			this.styleType1 = this.shopInfo.decorateColour ? Number(this.shopInfo.decorateColour) : 0;
			// 装修挂件
			this.styleType2 = this.shopInfo.decoratePendant ? Number(this.shopInfo.decoratePendant) : 0;
			// 文字大小
			this.styleType3 = this.shopInfo.decorateWord ? Number(this.shopInfo.decorateWord) : 0;
			// this.Advertising = res?.result?.images || [];
			this.loading = false;
		},
		/**切换tab*/
		tabActive(index) {
			this.active = index;
			if (index === 1) {
				this.getList();
			} else if (index === 2) {
				this.getWorkList();
			} else if (index === 3) {
				this.getCounpons();
			}
		},
		/**获取作品*/
		getWorkList(page, size) {
			let params = {
				pageNum: page || this.worksParams.pageNum,
				pageSize: size || this.worksParams.pageSize,
				rentId: this.getSiteId(),
				shopId: this.shopId || ''
			};
			this.workLoading = true;
			this.$api.shop_api.getWorkList(params).then(res => {
				this.workLoading = false;
				if (res.code === 200) {
					this.workList = res.results.records || [];
					this.workTotal = res.results.total || 0;
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**获取优惠券*/
		getCounpons(page, size) {
			let data = {
				rentId: this.getSiteId(),
				shopId: this.$route.query?.id || '',
				pageNum: page || this.couponParams.pageNum,
				pageSize: size || this.couponParams.pageSize
			};
			this.couponLoading = true;
			this.$api.shop_api.getCoupons(data).then(res => {
				this.couponLoading = false;
				if (res.code === 200) {
					this.couponsList = res.results.records || [];
				} else {
					this.$message.error(res.msg);
				}
			});
		},
		/**获取商品*/
		getList(data) {
			this.listLoading = true;
			// 有搜索数据，赋值搜索数据
			if (data) {
				this.searchInfo.offset = 0;
				this.searchInfo.psize = 8;
				this.$refs.allGoods.reset(); // 重置分页组件
				Object.keys(this.searchInfo).forEach(key => {
					if (data[key] !== undefined) {
						this.$set(this.searchInfo, key, data[key]);
					}
				});
			}
			let params = {
				siteId: this.getSiteId(),
				sellerId: this.shopInfo?.sellerId || '',
				...this.searchInfo
			};
			this.$api.shop_api.getAllProduct(params).then(res => {
				this.listLoading = false;
				this.list = res?.result || [];
				this.total = Number(res.totalNum);
			});
		},
		/**获取店铺精选*/
		async getChoicenessList() {
			let shopId = this.$route.query?.id || '';
			let data = {
				shopId,
				offset: 0,
				noType1: 3,
				psize: this.psize
			};
			this.shopLoading = true;
			let res = await this.$api.shop_api.getShopTopProduct(data);
			this.shopLoading = false;
			this.choicenessList = res?.result || [];
		},
		/**收藏店铺*/
		setFollowState(item) {
			let shopId = this.$route.query?.id || '';
			let data = {
				userId: this.userId,
				shopId: shopId
			};
			if (item) {
				let params = {
					userId: this.userId,
					shopId: shopId
				};
				this.$api.shop_api.setCancelFollowShop(params).then(res => {
					if (res.state) {
						this.getInfo();
						this.$message.close();
						this.$message.success('取消收藏店铺成功');
						return;
					}
					this.$message.close();
					this.$message.warning(res.msg);
				});
			} else {
				this.$api.shop_api.setFollowShop(data).then(res => {
					if (res.state) {
						this.getInfo();
						this.$message.close();
						this.$message.success('收藏店铺成功');
						return;
					}
					this.$message.close();
					this.$message.warning(res.msg);
				});
			}
		},
		/**获取广告*/
		getAdvertising() {
			let data = {
				siteId: this.getSiteId(),
				columnCode: 'sellerAdvert',
				offset: 0,
				psize: 10,
				sellerId: this.shopInfo?.sellerId
			};
			this.$api.shop_api.getInformation(data).then(res => {
				this.Advertising = res?.result || [];
			});
		}
	}
};
</script>
<style lang="scss">
.header-class {
	&.StoreHeader {
		position: relative;
		z-index: 10;
		.left .name {
			color: #fff !important;
		}
		.left .tags .lable {
			color: #fff !important;
		}
		.left .tags span {
			color: #fff !important;
		}
	}
	&.type1 {
		.name-title {
			font-size: 16px !important;
		}
	}
	&.type2 {
		.name-title {
			font-size: 18px !important;
		}
	}
	&.type3 {
		.name-title {
			font-size: 20px !important;
		}
	}
	&.type4 {
		.name-title {
			font-size: 24px !important;
		}
	}
}
</style>
<style lang="scss" scoped>
.boutique-venues {
	overflow: hidden;
	.gj-img {
		position: absolute;
		width: 60px;
		top: 10px;
		left: 450px;
	}
	.header {
		width: 100%;
		height: 100px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		position: relative;
		&-img {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
			width: 100%;
			object-fit: cover;
		}
		.store {
			width: 1200px;
			height: 100px;
			margin: 0 auto;
		}
	}
	.tabs {
		width: 100%;
		height: 60px;
		background: var(--brand-10, '#03325F');
		&.type1 {
			background: #2b9347;
			.tabs-tab-text_active {
				background: #55a62f !important;
			}
		}
		&.type2 {
			background: #3e405b;
			.tabs-tab-text_active {
				background: #de7a5f !important;
			}
		}
		&.type3 {
			background: #2e2b55;
			.tabs-tab-text_active {
				background: #484686 !important;
			}
		}
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		&-tab {
			width: 1200px;
			margin: 0px auto;
			height: 100%;
			display: flex;
			align-items: center;
			&-text {
				font-size: 18px;
				font-family: Source Han Sans SC-Regular, Source Han Sans SC;
				font-weight: 400;
				color: #ffffff;
				line-height: 32px;
				padding: 14px 20px;
				div {
					min-width: 80px;
					text-align: center;
				}
			}
			&-text_active {
				background: var(--brand-6, '#0076e8');
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
			}
		}
	}
	&-index {
		.goods-list {
			width: 1200px;
			margin: 0 auto;
			background-color: #fff;
			.empty {
				padding-bottom: 100px;
			}
		}
	}
	.all-goods {
		width: 1200px;
		margin: 0 auto;
		.empty {
			padding-bottom: 100px;
		}
		.all-goods-text {
			padding: 20px 0;
			font-size: 30px;
			font-family: Source Han Sans SC-Medium, Source Han Sans SC;
			font-weight: 500;
			color: #000000;
			line-height: 44px;
		}
	}
	.store-album {
		width: 1200px;
		margin: 0 auto;
	}
	.el-carousel__item img {
		width: 1920px;
		height: 768px;
		object-fit: cover;
	}
}
</style>
